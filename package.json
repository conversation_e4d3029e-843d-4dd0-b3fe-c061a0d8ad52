{"name": "mop", "version": "0.1.0", "private": true, "engines": {"node": ">=20"}, "volta": {"node": "20.13.1"}, "scripts": {"build": "bazel build //...", "test": "bazel test //...", "format": "npm run lint:fix && npm run prettier:fix", "lint": "npm run lint:js && npm run lint:css", "lint:fix": "npm run lint:js:fix && npm run lint:css:fix", "lint:js": "npx eslint \"./ui/**/*.{js,jsx,ts,tsx}\"", "lint:js:fix": "npm run lint:js -- --fix", "lint:css": "stylelint \"./ui/**/*.scss\"", "lint:css:fix": "npm run lint:css -- --fix", "lint:json": "npx eslint \"./{ui,jsonschema}/**/*.json\"", "lint:json:fix": "npm run lint:json -- --fix", "prettier": "npx prettier \"./ui/**/*.{js,jsx,ts,tsx,scss,css,json}\" --check", "prettier:fix": "npm run prettier -- --write", "type-check": "tsc --noEmit"}, "dependencies": {"@popperjs/core": "^2.11.6", "@types/golang-wasm-exec": "^1.15.2", "@types/pako": "^2.0.0", "apexcharts": "^4.5.0", "bootstrap": "^5.3.0", "chart.js": "^4.4.8", "clsx": "^2.1.1", "i18next": "^25.0.2", "i18next-browser-languagedetector": "^8.0.5", "i18next-http-backend": "^3.0.2", "pako": "^2.0.4", "tippy.js": "^6.3.7", "tsx-vanilla": "^1.0.0", "uuid": "^9.0.1", "yalps": "^0.5.6"}, "devDependencies": {"@bufbuild/buf": "1.35.0", "@protobuf-ts/plugin": "2.9.1", "@protobuf-ts/plugin-framework": "2.9.1", "@protobuf-ts/protoc": "2.9.1", "@protobuf-ts/runtime": "2.9.1", "@types/bootstrap": "^5.2.0", "@types/eslint": "^8.56.10", "@types/glob": "^8.1.0", "@types/node": "^18.16.1", "@types/uuid": "^9.0.8", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "eslint": "^8.56.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-import": "^2.28.1", "eslint-plugin-json": "^3.1.0", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-simple-import-sort": "^10.0.0", "eslint-plugin-unused-imports": "^2.0.0", "glob": "^8.1.0", "http-server": "13.0.1", "postcss-scss": "^4.0.9", "prettier": "3.0.3", "sass": "^1.64.2", "stylelint": "^15.10.3", "stylelint-prettier": "^4.0.2", "stylelint-scss": "^5.2.1", "terser": "^5.14.2", "typescript": "5.2.2", "typescript-formatter": "^7.2.2", "vite": "^6.3.5", "vite-plugin-checker": "^0.6.4"}}