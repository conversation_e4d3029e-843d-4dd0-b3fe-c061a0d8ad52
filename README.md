# WoW Mists of Pandaria Classic Simulator

Welcome to the WoW Mists of Pandaria Classic simulator! If you have questions or are thinking about contributing, [join our discord](https://discord.gg/jJMPr9JWwx) to chat!

The primary goal of this project is to provide a framework that makes it easy to build a DPS sim for any class/spec, with a polished UI and accurate results. Each community will have ownership / responsibility over their portion of the sim, to ensure accuracy and that their community is represented. By having all the individual sims on the same engine, we can also have a combined 'raid sim' for testing raid compositions.

This project is licensed with MIT license. We request that anyone using this software in their own project to make sure there is a user visible link back to the original project.

[Live sims can be found here.](https://wowsims.github.io/mop 'https://wowsims.github.io/mop')

[Support our devs via Patreon.](https://www.patreon.com/wowsims)

## Downloading Sim

Links for latest Sim build:

-   [Windows Sim](https://github.com/wowsims/mop/releases/latest/download/wowsimmop-windows.exe.zip)
-   [MacOS Sim](https://github.com/wowsims/mop/releases/latest/download/wowsimmop-amd64-darwin.zip)
-   [Linux Sim](https://github.com/wowsims/mop/releases/latest/download/wowsimmop-amd64-linux.zip)

Then unzip the downloaded file, then open the unzipped file to open the sim in your browser!

Alternatively, you can choose from a specific relase on the [Releases](https://github.com/wowsims/mop/releases) page and click the suitable link under "Assets"

## Documentation

- [Installation Guide](docs/installation.md)
- [Development Commands](docs/commands.md)
- [Adding a New Sim](docs/adding_sim.md)
- [Internationalization](docs/i18n_guide.md)
