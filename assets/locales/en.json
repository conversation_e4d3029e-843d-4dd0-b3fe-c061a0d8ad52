{"landing": {"navigation": {"home": "Home", "simulations": "Simulations", "about": "About", "toggle": "Toggle navigation"}, "simulations": {"full_raid": "Full Raid Sim"}, "home": {"title": "WoWSims - Mists of Pandaria", "description": "A powerful simulation tool for World of Warcraft: Mists of Pandaria", "welcomeDescription": "Welcome to WoWSims - Mists of Pandaria! This is a community-driven project to provide class and raid simulations for World of Warcraft® Mists of Pandaria Classic together with the leading theorycrafters and class representatives."}, "header": {"wowsims": "WoWSims", "expansion": "Mists of Pandaria", "supportDevs": "Support our devs"}}, "common": {"phases": {"1": "Phase 1 (T14)", "2": "Phase 2 (T15)", "3": "Phase 3 (T16)"}, "status": {"unlaunched": "Not Yet Supported", "alpha": "Alpha", "beta": "Beta", "launched": "Launched"}, "classes": {"death_knight": "Death Knight", "druid": "Druid", "hunter": "<PERSON>", "mage": "Mage", "monk": "<PERSON>", "paladin": "<PERSON><PERSON><PERSON>", "priest": "Priest", "rogue": "Rogue", "shaman": "Shaman", "warlock": "<PERSON><PERSON>", "warrior": "Warrior"}, "specs": {"death_knight": {"blood": "Blood", "frost": "<PERSON>", "unholy": "Unholy"}, "druid": {"balance": "Balance", "feral": "Feral", "guardian": "Guardian", "restoration": "Restoration"}, "hunter": {"beast_mastery": "Beast Mastery", "marksmanship": "Marksmanship", "survival": "Survival"}, "mage": {"arcane": "<PERSON><PERSON>", "fire": "Fire", "frost": "<PERSON>"}, "monk": {"brewmaster": "Brewmaster", "mistweaver": "Mistweaver", "windwalker": "Windwalker"}, "paladin": {"holy": "Holy", "protection": "Protection", "retribution": "Retribution"}, "priest": {"discipline": "Discipline", "holy": "Holy", "shadow": "Shadow"}, "rogue": {"assassination": "Assassination", "combat": "Combat", "subtlety": "Subtlety"}, "shaman": {"elemental": "Elemental", "enhancement": "Enhancement", "restoration": "Restoration"}, "warlock": {"affliction": "Affliction", "demonology": "Demonology", "destruction": "Destruction"}, "warrior": {"arms": "Arms", "fury": "Fury", "protection": "Protection"}}, "stats": {"strength": "Strength", "agility": "Agility", "stamina": "Stamina", "intellect": "Intellect", "spirit": "Spirit", "spell_hit": "Spell Hit", "spell_crit": "Spell Crit", "spell_haste": "Spell Haste", "expertise": "Expertise", "dodge": "Dodge", "parry": "<PERSON>", "mastery": "Mastery", "attack_power": "Attack Power", "ranged_attack_power": "Ranged Attack Power", "spell_power": "Spell Power", "pvp_resilience": "PvP Resilience", "pvp_power": "PvP Power", "armor": "Armor", "bonus_armor": "Bonus Armor", "health": "Health", "mana": "<PERSON><PERSON>", "mp5": "MP5", "main_hand_dps": "Main Hand DPS", "off_hand_dps": "Off Hand DPS", "ranged_dps": "Ranged DPS", "block": "Block", "melee_speed_multiplier": "Melee Speed Multiplier", "ranged_speed_multiplier": "Ranged Speed Multiplier", "cast_speed_multiplier": "Cast Speed Multiplier", "melee_haste": "<PERSON><PERSON>", "ranged_haste": "Ranged Haste", "melee_hit": "<PERSON><PERSON>", "melee_crit": "<PERSON><PERSON>"}}, "sim": {"title": "Mists of Pandaria {spec} {class} simulator", "description": "{spec} {class} simulations for World of Warcraft® Mists of Pandaria Classic."}, "gear": {"title": "Gear"}, "settings": {"title": "Settings"}, "talents": {"title": "Talents"}, "rotation": {"title": "Rotation"}, "results": {"title": "Results"}, "import": {"title": "Import"}, "export": {"title": "Export"}, "sidebar": {"iterations": "Iterations", "buttons": {"simulate": "Simulate", "stat_weights": "Stat Weights", "suggest_reforges": "Suggest Reforges"}, "header": {"title": "WoWSims - Mists of Pandaria", "phase": "{{phase}} - {{status}}"}, "character_stats": {"title": "Stats", "melee_crit_cap": "<PERSON>ee Crit Cap", "tooltip": {"base": "Base:", "gear": "Gear:", "talents": "Talents:", "buffs": "Buffs:", "consumes": "Consumes:", "bonus": "Bonus:", "total": "Total:", "glancing": "Glancing:", "suppression": "Suppression:", "to_hit_cap": "To Hit Cap:", "to_exp_cap": "To Exp Cap:", "spec_offsets": "Spec Offsets:", "final_crit_cap": "Final Crit Cap:", "can_raise_by": "Can Raise By:"}, "crit_cap": {"exact": "Exact", "over_by": "Over by", "under_by": "Under by"}, "bonus_prefix": "Bonus", "points_suffix": "Points", "percent_suffix": "%"}}}