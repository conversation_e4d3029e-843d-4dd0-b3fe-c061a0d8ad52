{"type": "TypeAPL", "prepullActions": [{"action": {"castSpell": {"spellId": {"otherId": "OtherActionPotion"}}}, "doAtValue": {"const": {"val": "-1s"}}}, {"action": {"activateAura": {"auraId": {"spellId": 1784}}}, "doAtValue": {"const": {"val": "-1s"}}}], "priorityList": [{"action": {"castSpell": {"spellId": {"spellId": 2825, "tag": -1}}}}, {"action": {"condition": {"not": {"val": {"dotIsActive": {"spellId": {"spellId": 1943}}}}}, "castSpell": {"spellId": {"spellId": 1943}}}}, {"action": {"condition": {"and": {"vals": [{"cmp": {"op": "OpLe", "lhs": {"dotRemainingTime": {"spellId": {"spellId": 1943}}}, "rhs": {"const": {"val": "2"}}}}, {"cmp": {"op": "OpGe", "lhs": {"currentComboPoints": {}}, "rhs": {"const": {"val": "4"}}}}]}}, "castSpell": {"spellId": {"spellId": 1943}}}}, {"action": {"condition": {"not": {"val": {"auraIsActive": {"auraId": {"spellId": 5171}}}}}, "castSpell": {"spellId": {"spellId": 5171}}}}, {"action": {"condition": {"or": {"vals": [{"auraIsActive": {"sourceUnit": {"type": "<PERSON><PERSON><PERSON><PERSON>"}, "auraId": {"spellId": 79140}}}, {"cmp": {"op": "OpLe", "lhs": {"remainingTime": {}}, "rhs": {"const": {"val": "30s"}}}}]}}, "autocastOtherCooldowns": {}}}, {"action": {"condition": {"and": {"vals": [{"auraIsActive": {"auraId": {"spellId": 5171}}}, {"dotIsActive": {"spellId": {"spellId": 1943}}}, {"cmp": {"op": "OpGe", "lhs": {"currentComboPoints": {}}, "rhs": {"const": {"val": "5"}}}}]}}, "castSpell": {"spellId": {"spellId": 126734}}}}, {"action": {"condition": {"and": {"vals": [{"auraIsActive": {"auraId": {"spellId": 5171}}}, {"dotIsActive": {"spellId": {"spellId": 1943}}}, {"cmp": {"op": "OpGe", "lhs": {"currentComboPoints": {}}, "rhs": {"const": {"val": "5"}}}}, {"or": {"vals": [{"cmp": {"op": "OpLe", "lhs": {"remainingTime": {}}, "rhs": {"const": {"val": "60s"}}}}, {"isExecutePhase": {"threshold": "E35"}}, {"cmp": {"op": "OpGe", "lhs": {"remainingTime": {}}, "rhs": {"const": {"val": "90s"}}}}]}}]}}, "castSpell": {"spellId": {"spellId": 79140}}}}, {"action": {"condition": {"and": {"vals": [{"auraIsActive": {"auraId": {"spellId": 5171}}}, {"dotIsActive": {"spellId": {"spellId": 1943}}}, {"or": {"vals": [{"cmp": {"op": "OpLe", "lhs": {"remainingTime": {}}, "rhs": {"const": {"val": "60s"}}}}, {"auraIsActive": {"sourceUnit": {"type": "<PERSON><PERSON><PERSON><PERSON>"}, "auraId": {"spellId": 79140}}}]}}]}}, "castSpell": {"spellId": {"spellId": 121471}}}}, {"action": {"condition": {"cmp": {"op": "OpGe", "lhs": {"currentComboPoints": {}}, "rhs": {"const": {"val": "5"}}}}, "castSpell": {"spellId": {"spellId": 32645}}}}, {"action": {"condition": {"and": {"vals": [{"cmp": {"op": "OpEq", "lhs": {"currentComboPoints": {}}, "rhs": {"const": {"val": "0"}}}}, {"not": {"val": {"auraIsActive": {"auraId": {"spellId": 1784}}}}}, {"auraIsActive": {"auraId": {"spellId": 5171}}}]}}, "castSpell": {"spellId": {"spellId": 137619}}}}, {"action": {"condition": {"and": {"vals": [{"or": {"vals": [{"cmp": {"op": "OpLe", "lhs": {"currentComboPoints": {}}, "rhs": {"const": {"val": "4"}}}}, {"and": {"vals": [{"auraIsKnown": {"auraId": {"spellId": 114015}}}, {"cmp": {"op": "OpLe", "lhs": {"auraNumStacks": {"auraId": {"spellId": 114015}}}, "rhs": {"const": {"val": "4"}}}}]}}]}}, {"or": {"vals": [{"isExecutePhase": {"threshold": "E35"}}, {"auraIsActive": {"auraId": {"spellId": 121153}}}]}}]}}, "castSpell": {"spellId": {"spellId": 111240}}}}, {"action": {"condition": {"and": {"vals": [{"auraIsActive": {"sourceUnit": {"type": "<PERSON><PERSON><PERSON><PERSON>"}, "auraId": {"spellId": 79140}}}, {"gcdIsReady": {}}, {"cmp": {"op": "OpLe", "lhs": {"currentEnergy": {}}, "rhs": {"const": {"val": "60"}}}}, {"cmp": {"op": "OpLe", "lhs": {"currentComboPoints": {}}, "rhs": {"const": {"val": "4"}}}}, {"not": {"val": {"auraIsActive": {"auraId": {"spellId": 121153}}}}}]}}, "castSpell": {"spellId": {"spellId": 1856}}}}, {"action": {"condition": {"and": {"vals": [{"or": {"vals": [{"cmp": {"op": "OpLe", "lhs": {"currentComboPoints": {}}, "rhs": {"const": {"val": "4"}}}}, {"and": {"vals": [{"auraIsKnown": {"auraId": {"spellId": 114015}}}, {"cmp": {"op": "OpLe", "lhs": {"auraNumStacks": {"auraId": {"spellId": 114015}}}, "rhs": {"const": {"val": "4"}}}}]}}]}}, {"not": {"val": {"isExecutePhase": {"threshold": "E35"}}}}]}}, "castSpell": {"spellId": {"spellId": 1329}}}}, {"action": {"condition": {"not": {"val": {"spellIsReady": {"spellId": {"spellId": 1856}}}}}, "castSpell": {"spellId": {"spellId": 14185}}}}, {"action": {"condition": {"const": {"val": "false"}}, "castSpell": {"spellId": {"spellId": 57934}}}}]}