{"type": "TypeAPL", "prepullActions": [{"action": {"castSpell": {"spellId": {"spellId": 122278}}}, "doAtValue": {"const": {"val": "-30s"}}, "hide": true}, {"action": {"castSpell": {"spellId": {"spellId": 115069}}}, "doAtValue": {"const": {"val": "-20s"}}}, {"action": {"castSpell": {"spellId": {"otherId": "OtherActionPotion"}}}, "doAtValue": {"const": {"val": "-.5s"}}}], "priorityList": [{"action": {"autocastOtherCooldowns": {}}}, {"action": {"condition": {"or": {"vals": [{"const": {"val": "false"}}, {"cmp": {"op": "OpGe", "lhs": {"spellTimeToReady": {"spellId": {"spellId": 123904}}}, "rhs": {"const": {"val": "30"}}}}, {"not": {"val": {"spellIsKnown": {"spellId": {"spellId": 123904}}}}}]}}, "castSpell": {"spellId": {"spellId": 126734}}}}, {"action": {"condition": {"or": {"vals": [{"not": {"val": {"spellIsKnown": {"spellId": {"spellId": 123904}}}}}, {"spellIsKnown": {"spellId": {"itemId": 76090}}}]}}, "castSpell": {"spellId": {"otherId": "OtherActionPotion"}}}}, {"action": {"condition": {"const": {"val": "false"}}, "castSpell": {"spellId": {"spellId": 115213}}}}, {"hide": true, "action": {"condition": {"const": {"val": "false"}}, "castSpell": {"spellId": {"spellId": 126456}}}}, {"hide": true, "action": {"condition": {"const": {"val": "false"}}, "castSpell": {"spellId": {"spellId": 122278}}}}, {"action": {"condition": {"cmp": {"op": "OpLe", "lhs": {"currentHealthPercent": {}}, "rhs": {"const": {"val": "50%"}}}}, "castSpell": {"spellId": {"spellId": 124507}}}}, {"action": {"condition": {"and": {"vals": [{"cmp": {"op": "OpGe", "lhs": {"auraNumStacks": {"auraId": {"spellId": 128938}}}, "rhs": {"const": {"val": "6"}}}}, {"auraIsInactiveWithReactionTime": {"auraId": {"spellId": 115308}}}]}}, "castSpell": {"spellId": {"spellId": 115308}}}}, {"action": {"condition": {"or": {"vals": [{"and": {"vals": [{"cmp": {"op": "OpLe", "lhs": {"monkCurrentChi": {}}, "rhs": {"const": {"val": "2"}}}}, {"and": {"vals": [{"cmp": {"op": "OpGe", "lhs": {"spellTimeToReady": {"spellId": {"spellId": 121253}}}, "rhs": {"const": {"val": "2s"}}}}, {"cmp": {"op": "OpLe", "lhs": {"spellTimeToReady": {"spellId": {"spellId": 121253}}}, "rhs": {"const": {"val": "7s"}}}}]}}, {"cmp": {"op": "OpLe", "lhs": {"currentEnergy": {}}, "rhs": {"math": {"op": "OpSub", "lhs": {"maxEnergy": {}}, "rhs": {"math": {"op": "OpMul", "lhs": {"energyRegenPerSecond": {}}, "rhs": {"const": {"val": "2"}}}}}}}}]}}, {"cmp": {"op": "OpEq", "lhs": {"monkCurrentChi": {}}, "rhs": {"const": {"val": "0"}}}}]}}, "castSpell": {"spellId": {"spellId": 115399}}}}, {"hide": true, "action": {"condition": {"and": {"vals": [{"cmp": {"op": "OpGt", "lhs": {"auraRemainingTime": {"auraId": {"spellId": 115307}}}, "rhs": {"const": {"val": "2s"}}}}, {"cmp": {"op": "OpGe", "lhs": {"monkCurrentChi": {}}, "rhs": {"math": {"op": "OpSub", "lhs": {"monkMaxChi": {}}, "rhs": {"const": {"val": "1"}}}}}}, {"auraIsActive": {"auraId": {"spellId": 118636}}}, {"cmp": {"op": "OpGe", "lhs": {"auraNumStacks": {"auraId": {"spellId": 120267}}}, "rhs": {"const": {"val": "100000"}}}}]}}, "castSpell": {"spellId": {"spellId": 115295}}}}, {"action": {"condition": {"and": {"vals": [{"cmp": {"op": "OpLe", "lhs": {"spellTimeToReady": {"spellId": {"spellId": 121253}}}, "rhs": {"const": {"val": "1.5s"}}}}, {"cmp": {"op": "OpGe", "lhs": {"monkCurrentChi": {}}, "rhs": {"math": {"op": "OpSub", "lhs": {"monkMaxChi": {}}, "rhs": {"const": {"val": "1"}}}}}}]}}, "castSpell": {"spellId": {"spellId": 100784, "tag": 1}}}}, {"action": {"castSpell": {"spellId": {"spellId": 121253}}}}, {"action": {"castSpell": {"spellId": {"spellId": 116847}}}}, {"hide": true, "action": {"condition": {"and": {"vals": [{"cmp": {"op": "OpLe", "lhs": {"auraRemainingTime": {"auraId": {"spellId": 115307}}}, "rhs": {"const": {"val": "2s"}}}}, {"cmp": {"op": "OpLe", "lhs": {"monkCurrentChi": {}}, "rhs": {"const": {"val": "1"}}}}, {"cmp": {"op": "OpGe", "lhs": {"numberTargets": {}}, "rhs": {"const": {"val": "3"}}}}]}}, "castSpell": {"spellId": {"spellId": 101546}}}}, {"hide": true, "action": {"condition": {"and": {"vals": [{"cmp": {"op": "OpLe", "lhs": {"auraRemainingTime": {"auraId": {"spellId": 115307}}}, "rhs": {"const": {"val": "2s"}}}}, {"cmp": {"op": "OpLe", "lhs": {"monkCurrentChi": {}}, "rhs": {"const": {"val": "1"}}}}, {"cmp": {"op": "OpLt", "lhs": {"currentHealthPercent": {}}, "rhs": {"const": {"val": "85%"}}}}]}}, "castSpell": {"spellId": {"spellId": 115072}}}}, {"hide": true, "action": {"condition": {"and": {"vals": [{"cmp": {"op": "OpLe", "lhs": {"auraRemainingTime": {"auraId": {"spellId": 115307}}}, "rhs": {"const": {"val": "2s"}}}}, {"cmp": {"op": "OpLe", "lhs": {"monkCurrentChi": {}}, "rhs": {"const": {"val": "1"}}}}]}}, "castSpell": {"spellId": {"spellId": 100780}}}}, {"action": {"condition": {"or": {"vals": [{"cmp": {"op": "OpLe", "lhs": {"auraRemainingTime": {"auraId": {"spellId": 115307}}}, "rhs": {"const": {"val": "1.5s"}}}}, {"and": {"vals": [{"cmp": {"op": "OpLe", "lhs": {"spellTimeToReady": {"spellId": {"spellId": 121253}}}, "rhs": {"const": {"val": "2s"}}}}, {"cmp": {"op": "OpGe", "lhs": {"monkCurrentChi": {}}, "rhs": {"math": {"op": "OpSub", "lhs": {"monkMaxChi": {}}, "rhs": {"const": {"val": "1"}}}}}}]}}]}}, "castSpell": {"spellId": {"spellId": 100784, "tag": 1}}}}, {"action": {"condition": {"or": {"vals": [{"and": {"vals": [{"spellIsReady": {"spellId": {"spellId": 123904}}}, {"spellIsKnown": {"spellId": {"itemId": 76090}}}]}}, {"auraIsActive": {"auraId": {"itemId": 76089}}}, {"auraIsActive": {"auraId": {"spellId": 126734}}}]}}, "castSpell": {"spellId": {"spellId": 123904}}}}, {"action": {"condition": {"or": {"vals": [{"spellIsReady": {"spellId": {"spellId": 123904}}}, {"spellIsReady": {"spellId": {"itemId": 76089}}}, {"spellIsKnown": {"spellId": {"spellId": 126734}}}]}}, "strictSequence": {"actions": [{"castSpell": {"spellId": {"otherId": "OtherActionPotion"}}}, {"castSpell": {"spellId": {"spellId": 126734}}}, {"castSpell": {"spellId": {"spellId": 123904}}}]}}}, {"action": {"condition": {"or": {"vals": [{"spellIsReady": {"spellId": {"spellId": 123904}}}, {"spellIsReady": {"spellId": {"itemId": 76089}}}]}}, "strictSequence": {"actions": [{"castSpell": {"spellId": {"otherId": "OtherActionPotion"}}}, {"castSpell": {"spellId": {"spellId": 123904}}}]}}}, {"action": {"condition": {"or": {"vals": [{"spellIsReady": {"spellId": {"spellId": 123904}}}, {"spellIsReady": {"spellId": {"spellId": 126734}}}]}}, "strictSequence": {"actions": [{"castSpell": {"spellId": {"spellId": 126734}}}, {"castSpell": {"spellId": {"spellId": 123904}}}]}}}, {"hide": true, "action": {"condition": {"and": {"vals": [{"cmp": {"op": "OpGe", "lhs": {"brewmasterMonkCurrentStaggerPercent": {}}, "rhs": {"const": {"val": "10%"}}}}, {"cmp": {"op": "OpGe", "lhs": {"auraRemainingTime": {"auraId": {"spellId": 124255, "tag": 1}}}, "rhs": {"const": {"val": "5s"}}}}]}}, "castSpell": {"spellId": {"spellId": 119582}}}}, {"hide": true, "action": {"condition": {"and": {"vals": [{"cmp": {"op": "OpGe", "lhs": {"currentEnergy": {}}, "rhs": {"math": {"op": "OpSub", "lhs": {"maxEnergy": {}}, "rhs": {"math": {"op": "OpMul", "lhs": {"const": {"val": "1.5"}}, "rhs": {"energyRegenPerSecond": {}}}}}}}}, {"cmp": {"op": "OpGe", "lhs": {"numberTargets": {}}, "rhs": {"const": {"val": "3"}}}}]}}, "castSpell": {"spellId": {"spellId": 101546}}}}, {"hide": true, "action": {"condition": {"and": {"vals": [{"cmp": {"op": "OpGe", "lhs": {"currentEnergy": {}}, "rhs": {"math": {"op": "OpSub", "lhs": {"maxEnergy": {}}, "rhs": {"math": {"op": "OpMul", "lhs": {"const": {"val": "1.5"}}, "rhs": {"energyRegenPerSecond": {}}}}}}}}, {"cmp": {"op": "OpLt", "lhs": {"currentHealthPercent": {}}, "rhs": {"const": {"val": "85%"}}}}]}}, "castSpell": {"spellId": {"spellId": 115072}}}}, {"hide": true, "action": {"condition": {"cmp": {"op": "OpGe", "lhs": {"currentEnergy": {}}, "rhs": {"math": {"op": "OpSub", "lhs": {"maxEnergy": {}}, "rhs": {"math": {"op": "OpMul", "lhs": {"const": {"val": "1.5"}}, "rhs": {"energyRegenPerSecond": {}}}}}}}}, "castSpell": {"spellId": {"spellId": 100780}}}}, {"action": {"condition": {"cmp": {"op": "OpLe", "lhs": {"auraRemainingTime": {"auraId": {"spellId": 125359}}}, "rhs": {"const": {"val": "1.5s"}}}}, "castSpell": {"spellId": {"spellId": 100787}}}}, {"action": {"castSpell": {"spellId": {"spellId": 115098}}}}, {"action": {"castSpell": {"spellId": {"spellId": 123986}}}}, {"action": {"castSpell": {"spellId": {"spellId": 124081}}}}, {"action": {"condition": {"cmp": {"op": "OpGe", "lhs": {"monkCurrentChi": {}}, "rhs": {"math": {"op": "OpSub", "lhs": {"monkMaxChi": {}}, "rhs": {"const": {"val": "1"}}}}}}, "castSpell": {"spellId": {"spellId": 100784, "tag": 1}}}}, {"action": {"castSpell": {"spellId": {"spellId": 100787}}}}]}