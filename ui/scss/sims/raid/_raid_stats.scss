:root {
	--raid-stats-bg: var(--bs-gray-800-alpha-50);
}

.raid-stats {
	.raid-stats-section {
		margin-bottom: var(--section-spacer);

		.raid-stats-section-label {
			font-weight: bold;
		}

		.raid-stats-section-content {
			padding: var(--spacer-2);
			border: var(--border-default);
			display: grid;
			grid-template-columns: repeat(4, 1fr);
			gap: var(--block-spacer);
			background-color: var(--raid-stats-bg);

			@include media-breakpoint-down(md) {
				grid-template-columns: repeat(3, 1fr);
			}

			@include media-breakpoint-down(sm) {
				grid-template-columns: repeat(2, 1fr);
			}

			.raid-stats-category-root {
				.raid-stats-category {
					display: inline-flex;
					color: var(--bs-white);

					.raid-stats-category-label {
						filter: brightness(0.4);
					}

					.raid-stats-category-counter,
					&:hover .raid-stats-category-label {
						filter: brightness(0.6);
					}

					&.active .raid-stats-category-label {
						filter: brightness(0.8);
					}

					&.active .raid-stats-category-counter,
					&.active:hover .raid-stats-category-label {
						filter: brightness(1);
					}

					.raid-stats-category-counter {
						min-width: 16px;
						text-align: center;
					}
				}
			}
		}
	}
}

.tippy-box[data-theme='raid-stats-category-tooltip'] {
	max-width: unset;

	.tippy-content {
		text-align: left;
		font-weight: bold;
	}

	.raid-stats-effect {
		display: flex;
		align-items: center;

		&:not(:last-child) {
			margin-bottom: var(--spacer-1);
		}

		& > :not(:last-child) {
			margin-right: var(--spacer-1);
		}

		.raid-stats-effect-counter {
			min-width: 16px;
			text-align: center;
		}

		.raid-stats-effect-icon {
			width: var(--icon-size-sm);
			height: var(--icon-size-sm);
		}
	}
}
