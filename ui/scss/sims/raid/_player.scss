:root {
	--player-bg: var(--bs-gray-800-alpha-50);
}

.player {
	width: 100%;
	height: 2.5rem;
	padding: calc(var(--spacer-1) - 1px) var(--spacer-1);
	border: var(--border-default);
	display: flex;
	justify-content: space-between;
	overflow-x: hidden;
	color: var(--bs-white);

	/* stylelint-disable-next-line plugin/stylelint-bem-namics */
	&:not([class*='bg-']) {
		background: var(--player-bg);
	}

	.player-label {
		margin-right: var(--spacer-2);
		display: flex;
		align-items: center;
		flex-grow: 1;
		overflow: hidden;
		line-height: 1rem;

		.player-details {
			display: flex;
			flex-direction: column;
			overflow: hidden;

			.player-name {
				height: 1rem;
				padding: 0;
				border: none;
				outline: none;
				background: transparent;
				text-overflow: ellipsis;
			}
		}
	}
}

.player-icon {
	width: 2rem;
	height: 2rem;
	margin-right: var(--spacer-1);
	border-radius: 100%;
}
