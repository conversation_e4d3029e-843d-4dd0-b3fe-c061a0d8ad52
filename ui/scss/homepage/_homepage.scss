.homepage-image {
	position: fixed;
	width: 100%;
	height: 100%;
	background-image: url('/mop/assets/img/mop.jpg');
	background-repeat: no-repeat;
	background-size: cover;
	background-position: center;
	z-index: -1;
	opacity: 0.3;
}

#homepage {
	height: 100%;
	display: flex;
	flex-direction: column;

	.homepage-header-container,
	.homepage-content-container,
	.homepage-footer-container {
		@include media-breakpoint-down(md) {
			padding-top: var(--spacer-3);
			padding-bottom: var(--spacer-3);
		}
	}

	.homepage-header {
		.homepage-header-container {
			display: flex;
			padding-top: var(--section-spacer);

			@include media-breakpoint-down(md) {
				display: flex;
			}

			.navbar-brand-container {
				display: flex;

				@include media-breakpoint-down(md) {
					width: 100%;
					justify-content: space-between;
					align-items: flex-end;
				}

				.wowsims-logo {
					width: 6rem;
					margin-right: var(--spacer-3);

					@include media-breakpoint-down(md) {
						width: 48px;
					}
				}

				.wowsims-title {
					@extend .display-1;
					@extend .fw-bold;
					color: var(--bs-brand);
					margin: 0;
					line-height: 1;
				}
			}

			.homepage-header-collapse {
				padding-top: var(--spacer-3);
				padding-bottom: var(--spacer-3);
				align-items: flex-end;
				justify-content: flex-end;
			}
		}

		.expansion-title {
			color: var(--bs-expansion);
			margin: 0;
		}
	}

	.homepage-content-container {
		padding-top: var(--container-padding);
		padding-bottom: var(--container-padding);
		flex-direction: column;

		@include media-breakpoint-down(md) {
			margin-bottom: var(--spacer-3);
		}

		.info-container {
			margin-bottom: var(--container-padding);
			display: flex;
			flex-direction: column;

			@include media-breakpoint-down(md) {
				margin-bottom: var(--spacer-3);
			}

			.wowsims-info {
				@extend .fs-4;
				width: 75%;
				margin: 0;

				@include media-breakpoint-down(lg) {
					width: 100%;
				}
			}
		}

		.sim-links-container {
			display: flex;
			flex-direction: column;

			@include media-breakpoint-down(lg) {
				margin-left: calc(var(--spacer-3) * -1);
				margin-right: calc(var(--spacer-3) * -1);
			}

			.sim-links {
				display: flex;
				flex-wrap: wrap;

				@include media-breakpoint-down(lg) {
					margin-bottom: 0 !important;
				}

				&:not(:last-child) {
					margin-bottom: var(--spacer-5);
				}

				& > .sim-link,
				& > .sim-link-dropdown {
					max-width: 25%;
					flex: 1 25%;
					margin-right: 25%;
				}

				.sim-link,
				.sim-link-dropdown {
					width: 100%;

					@include media-breakpoint-down(lg) {
						max-width: 50%;
						flex: 1 50%;
						margin-right: 0 !important;
					}

					@include media-breakpoint-down(md) {
						flex: 1 100% !important;
					}
				}

				.sim-link {
					@include media-breakpoint-down(md) {
						max-width: none;
					}
				}

				.sim-link-dropdown {
					.dropdown-menu {
						@include media-breakpoint-down(lg) {
							position: relative !important;
							inset: 0 !important;
							transform: none !important;
						}
					}
				}

				.raid-sim-link {
					flex-grow: 0 !important;
				}
			}
		}
	}
}
