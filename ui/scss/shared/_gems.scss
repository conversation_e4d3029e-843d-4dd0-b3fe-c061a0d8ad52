.gem-socket-container {
	--gem-width: 1.25rem;

	position: relative;
	width: var(--gem-width);
	height: var(--gem-width);
	flex-shrink: 0;

	&:not(:last-child) {
		margin-right: 1px;
	}

	.gem-icon,
	.socket-icon {
		position: absolute;
	}
}

.gem-icon {
	@include wowhead-background-icon;
	width: calc(4 * var(--gem-width) / 5);
	height: calc(4 * var(--gem-width) / 5);
	inset: calc(var(--gem-width) / 10);
	border-radius: 100%;
	z-index: 1;
}

.socket-icon {
	@include wowhead-background-icon;
	width: 100%;
	height: 100%;
	inset: 0;
}
