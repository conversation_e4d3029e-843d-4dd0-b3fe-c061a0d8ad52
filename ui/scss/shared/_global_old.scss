:root {
	--theme-background-color: <PERSON><PERSON><PERSON><PERSON><PERSON>;
	--theme-background-image: linear-gradient(var(--bs-black), var(--bs-black));
	--theme-background-opacity: 0.9;
	--main-bg-color: Dark<PERSON>late<PERSON><PERSON>;
	--main-secondary-color: var(--bs-black);
	--sidebar-text-color: var(--bs-white);
	--sidebar-highlight-color: var(--bs-crit);
}

@font-face {
	font-family: SimDefaultFont;
	src: url(/mop/assets/fonts/PlusJakartaSans-Regular.woff2);
}

@font-face {
	font-family: SimDefaultFont;
	src: url(/mop/assets/fonts/PlusJakartaSans-Bold.woff2);
	font-weight: bold;
}

* {
	font-family: SimDefaultFont;
	letter-spacing: 1px;
}

.loader {
	border: 16px solid #f3f3f3; /* Light grey */
	border-top: 16px solid #3498db; /* Blue */
	border-radius: 50%;
	width: 120px;
	height: 120px;
	animation: spin 1s linear infinite;
}

@keyframes spin {
	0% {
		transform: rotate(0deg);
	}

	100% {
		transform: rotate(360deg);
	}
}

.dropdown-root {
	position: relative;
	display: inline-block !important;
	white-space: nowrap;
}

.dropdown-root:hover .dropdown-panel {
	display: grid !important;
}

.dropdown-button {
	cursor: pointer;
	background-color: var(--bs-gray);
}

.dropdown-panel {
	display: none;
	position: absolute;
	z-index: 1;
	box-shadow: 2px 4px 20px 5px var(--bs-black);
}

.dropdown-option-container {
	background-color: var(--bs-gray);
}

.dropdown-option {
	display: flex;
	align-items: center;
	cursor: pointer;
}

*:not(.dropdown-button) > * > .dropdown-option:not(:hover) {
	opacity: 0.7;
}

.safe,
.positive {
	color: var(--bs-success) !important;
}

.danger,
.negative {
	color: var(--bs-danger) !important;
}

.warning {
	color: var(--bs-partial);
	text-shadow:
		0 0 var(--spacer-2) var(--bs-danger),
		0 0 var(--spacer-2) var(--bs-danger),
		0 0 var(--spacer-2) var(--bs-danger);
}

.bold {
	font-weight: bold;
}

a {
	text-decoration: none;
}

// Override Bootstrap styling to keep inline with fieldset border
legend {
	float: none;
	width: auto;
}
