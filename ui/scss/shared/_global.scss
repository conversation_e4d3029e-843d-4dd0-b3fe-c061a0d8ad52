/* stylelint-disable plugin/stylelint-bem-namics */
:root {
	--bs-body-font-size: 14px;
	font-size: var(--bs-body-font-size);

	@include media-breakpoint-up(1080p) {
		--bs-body-font-size: 16px;
	}

	// We want to apply only to 1440p monitors, NOT 1080p Ultrawide
	// @media (min-width: map-get($grid-breakpoints, 1440p)) and (max-aspect-ratio: 16/9) {
	// 	--bs-body-font-size: 20px;
	// }

	// @include media-breakpoint-up(4k) {
	// 	--bs-body-font-size: 24px;
	// }

	@include media-breakpoint-up(lg) {
		--section-spacer: var(--section-spacer-lg) !important;
		--container-padding: var(--container-padding-lg) !important;
	}

	@include media-breakpoint-up(xxl) {
		--section-spacer: var(--section-spacer-xxl) !important;
		--container-padding: var(--container-padding-xxl) !important;
	}
}

* {
	letter-spacing: 1px;
}

html {
	height: 100%;
	color-scheme: dark;
	font-size: var(--bs-body-font-size);
}

body {
	height: 100%;
	// Bootstrap adds padding-right when Modals open for some reason, causing popping
	padding-right: 0 !important;
	// Prevent browser scrollbars from adjusting page contents
	margin-right: calc(-1 * (100vw - 100%));
	overflow-x: hidden;
	opacity: 0;
	transition: opacity 0.25s linear;

	&.ready {
		opacity: 1;
	}
}

button {
	display: inline-block;
	border: 0;
	background-color: transparent;
	padding: 0;
	color: rgba(var(--bs-link-color-rgb), var(--bs-link-opacity, 1));
	text-decoration: none;
	outline: none;
	width: auto;
	text-align: inherit;
	font-weight: inherit;
	transition: $btn-transition;

	&:hover {
		--bs-link-color-rgb: var(--bs-link-hover-color-rgb);
	}
}

a,
button[class*='btn-link'] {
	cursor: pointer;
	border: 0;
	padding: 0;
	outline: none;
	background-color: transparent;
	transition: color 0.15s ease-in-out;

	&.link-alt {
		color: var(--bs-white);

		&:hover,
		&:focus,
		&.active {
			color: var(--bs-brand);
		}
	}
}

hr {
	border-top: 1px solid;
	border-bottom: 1px solid;
}

label {
	font-weight: bold;
}

h1,
h2,
h3,
h4,
h5,
h6 {
	font-weight: bold;
}

p {
	margin-bottom: var(--block-spacer);
}

kbd {
	background: var(--bs-body-bg);
	color: var(--bs-white);
}

.list-reset {
	list-style: none;
	padding: 0;
	margin: 0;
}

.dragto:not(.dragfrom) {
	filter: brightness(0.5);
}

.interactive {
	cursor: pointer;
	user-select: none;
}

.hide {
	display: none !important;
}

.icon-sm {
	display: inline-block;
	min-width: var(--icon-size-sm);
	width: var(--icon-size-sm);
	height: var(--icon-size-sm);
	background-size: cover;
}

.icon-md {
	display: inline-block;
	min-width: var(--icon-size-md);
	width: var(--icon-size-md);
	height: var(--icon-size-md);
	background-size: cover;
}

.p-gap {
	@extend .px-gap;
	@extend .py-gap;
}

.pl-gap {
	padding-left: var(--gap-width);
}

.pr-gap {
	padding-right: var(--gap-width);
}

.pt-gap {
	padding-top: var(--gap-width);
}

.pb-gap {
	padding-bottom: var(--gap-width);
}

.px-gap {
	@extend .pl-gap;
	@extend .pr-gap;
}

.py-gap {
	@extend .pt-gap;
	@extend .pb-gap;
}

@each $label, $value in $item-quality-colors {
	.item-quality-#{$label} {
		color: var(--bs-#{$label}) !important;
	}
}

@each $label, $value in $resource-colors {
	.resource-#{$label} {
		color: var(--bs-#{$label}) !important;
	}
}

@each $label, $value in $damage-colors {
	.damage-#{$label} {
		color: var(--bs-#{$label}) !important;
	}
}

@each $label, $value in $faction-colors {
	.faction-#{$label} {
		color: var(--bs-#{$label}) !important;
	}
}

@each $label, $value in $spell-school-colors {
	.spell-school-#{$label} {
		color: var(--bs-#{$label}) !important;
	}
	.bg-spell-school-#{$label} {
		background: var(--bs-#{$label}) !important;
	}
}

@each $label, $value in $multi-spell-school-colors {
	.spell-school-#{$label} {
		color: var(--bs-primary);
		background-image: $value;
		background-clip: text;
		-webkit-text-fill-color: transparent;
	}

	.bg-spell-school-#{$label} {
		background: $value;
	}
}

[contenteditable='true']:active,
[contenteditable='true']:focus {
	border: none;
	outline: none;
}

[draggable='true'] {
	cursor: grab;
}

.notices-banner {
	width: 100%;
	background: color-mix(in srgb, var(--bs-body-bg), var(--bs-white) 5%);
}

.blurred {
	filter: blur(2px);
	overflow: hidden;
	pointer-events: none;
}
