/* stylelint-disable plugin/stylelint-bem-namics */
$gray-800: #323232;

@font-face {
	font-family: SimDefaultFont;
	src: url(/mop/assets/fonts/PlusJakartaSans-Regular.woff2);
}

@font-face {
	font-family: SimDefaultFont;
	src: url(/mop/assets/fonts/PlusJakartaSans-Bold.woff2);
	font-weight: bold;
}

:root {
	--bs-body-font-family: SimDefaultFont;
	--bs-body-line-height: 1.25;
}

.btn-close {
	background: none;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: var(--link-transition);

	&:hover {
		color: var(--bs-white);
	}
}

.btn-reset {
	@include button-variant(
		$background: transparent,
		$border: transparent,
		$color: map-get($link-colors, 'link-danger'),
		$hover-background: transparent,
		$hover-border: transparent,
		$hover-color: $body-color,
		$active-background: transparent,
		$active-border: transparent,
		$active-color: map-get($link-colors, 'link-danger'),
		$disabled-background: transparent,
		$disabled-border: transparent,
		$disabled-color: rgba($body-color, 0.6)
	);
}

.container {
	height: 100%;
	display: flex;

	@include media-breakpoint-down(lg) {
		width: 100%;
		max-width: 100%;
		margin-left: 0;
		margin-right: 0;
	}
}

.dropdown-menu {
	&[data-bs-popper] {
		margin: 0 !important;
	}
}

.form-check {
	justify-content: flex-start !important;

	&.form-check-reverse {
		justify-content: space-between !important;

		.form-label {
			margin-left: 0;
			text-align: left;
		}
	}

	.form-label {
		margin-left: var(--spacer-2);
		margin-bottom: 0;
	}
}

.form-check-input {
	margin-top: 0;

	&:focus {
		box-shadow: 0 0 0.25rem var(--bs-primary);
	}

	&:checked[type='checkbox'] {
		background-color: var(--bs-primary);
		background-image: var(--bs-form-check-box-bg-image);
	}

	&:checked[type='radio'] {
		background-color: var(--bs-primary);
		background-image: var(--bs-form-check-radio-bg-image);
	}
}

.link-warning {
	color: var(--bs-link-warning) !important;
}

.link-danger {
	color: var(--bs-link-danger) !important;
}

.link-success {
	color: var(--bs-success) !important;
}

.modal {
	.modal-dialog {
		.modal-header,
		.modal-footer {
			padding-left: 0;
			padding-right: 0;
			margin-left: $modal-header-padding;
			margin-right: $modal-header-padding;
		}

		.modal-header {
			align-items: flex-start;
		}

		.modal-body {
			display: flex;
			flex-direction: column;
		}
	}
}

.modal-header {
	.btn-close {
		margin-right: calc(-1 * var(--spacer-1));
		z-index: 1000;

		&.position-fixed {
			top: calc(var(--bs-modal-margin) + 1px + var(--gap-width));
			right: calc(2.5vw + 1px + var(--gap-width));
		}
	}
}

.navbar {
	.navbar-toggler {
		border: 0;
		box-shadow: none;
		color: var(--bs-white);

		@include media-breakpoint-down(md) {
			position: absolute;
			top: 0;
			right: 0;
		}
	}

	.nav-link {
		display: flex;
		align-items: center;
		white-space: nowrap;

		@include media-breakpoint-down(md) {
			padding-bottom: var(--spacer-3);
		}

		&:first-of-type {
			@include media-breakpoint-down(md) {
				padding-top: var(--spacer-2);
			}
		}
	}

	.navbar-nav {
		@include media-breakpoint-down(md) {
			position: relative;
			align-items: flex-start;
		}
	}

	.navbar-collapse {
		@include media-breakpoint-down(md) {
			position: fixed;
			top: 0;
			bottom: 0;
			left: 0;
			right: 0;
			padding: 1rem;
			background: rgba(0, 0, 0, 0.9);
			z-index: 1000;
		}
	}
}

.nav-tabs {
	.nav-item {
		display: flex;
		align-items: center;

		.nav-link {
			height: 100%;
			margin-bottom: 0;
			border: 0;
			display: flex;
			align-items: center;
			font-weight: bold;
			white-space: nowrap;
			cursor: pointer;

			&.active {
				position: relative;

				&::after {
					content: '';
					position: absolute;
					height: 2px;
					background: $nav-tabs-link-active-color;
					bottom: 0;
					left: 0;
					right: 0;
				}
			}
		}
	}
}

@each $color, $value in $class-colors {
	// Dampened class themes
	.bg-#{$color}-dampened {
		background: mix($black, $value, 75%);
	}
}

.toast {
	--icon-color: var(--bs-warning);

	.toast-header {
		border-bottom: 0;
		padding-bottom: 0;

		i {
			line-height: 1;
			color: var(--icon-color);
		}
	}

	.btn-close {
		i {
			color: var(--bs-toast-color);
		}
	}
}

.toast--success {
	--icon-color: var(--bs-success);
}

.toast--error {
	--icon-color: var(--bs-danger);
}
