.sim-link {
	display: flex;

	&:hover,
	&.show {
		background-color: var(--bs-black-alpha-50);
	}

	.sim-link-content {
		padding: var(--spacer-2);
		display: flex;
		align-items: center;

		.sim-link-icon {
			width: 3rem;
			height: 3rem;
			margin-right: var(--spacer-2);
			border-width: 2px;
			border-style: solid;
			border-radius: 100%;
		}

		.sim-link-label,
		.launch-status-label {
			font-size: 0.75rem;
			font-weight: normal;
			white-space: nowrap;
		}

		.sim-link-title {
			font-size: 1.25rem;
			font-weight: bold;
			white-space: nowrap;
		}
	}
}

.sim-link-dropdown {
	.dropdown-menu {
		background: var(--bs-black-alpha-50);

		@include media-breakpoint-down(lg) {
			position: relative !important;
			inset: 0 !important;
			transform: none !important;
		}
	}
}
