@use 'sass:map';

// Define and overwrite Bootstrap Variables
// See bootstrap/scss/_variables.scss for a complete list of definitions, or
// individual component pages in the Bootstrap 5.2 docs for relevant variable usage.

//////////////////////////
//// Custom variables ////
//////////////////////////

// Custom theme color mapping
$expansion: #23c88c;
$brand: #e0a335;
$success: #1eff00;

$death-knight: rgb(194, 46, 70);
$druid: rgb(255, 125, 10);
$hunter: rgb(171, 212, 115);
$mage: rgb(105, 204, 240);
$monk: rgb(0, 255, 152);
$paladin: rgb(245, 140, 186);
$priest: rgb(255, 255, 255);
$rogue: rgb(255, 245, 105);
$shaman: rgb(36, 89, 255);
$warlock: rgb(148, 130, 201);
$warrior: rgb(199, 156, 110);

$class-colors: (
	death-knight: $death-knight,
	druid: $druid,
	hunter: $hunter,
	mage: $mage,
	monk: $monk,
	paladin: $paladin,
	priest: $priest,
	raid: $brand,
	rogue: $rogue,
	shaman: $shaman,
	warlock: $warlock,
	warrior: $warrior,
);
$theme-colors: map-merge($theme-colors, $class-colors);

$spell-school-colors: (
	physical: #e5cc80,
	arcane: #8ff2ff,
	fire: #eb4561,
	frost: #4a80ff,
	holy: #ffff8f,
	nature: #d1fa99,
	shadow: #b8a8f0,
);

$multi-spell-school-colors: (
	astral: linear-gradient(90deg, map-get($spell-school-colors, nature), map-get($spell-school-colors, arcane)),
	shadowflame: linear-gradient(90deg, map-get($spell-school-colors, shadow), map-get($spell-school-colors, fire)),
	spellfire: linear-gradient(90deg, map-get($spell-school-colors, fire), map-get($spell-school-colors, arcane)),
	spellfrost: linear-gradient(90deg, map-get($spell-school-colors, arcane), map-get($spell-school-colors, frost)),
	frostfire: linear-gradient(90deg, map-get($spell-school-colors, frost), map-get($spell-school-colors, fire)),
	shadowfrost: linear-gradient(90deg, map-get($spell-school-colors, shadow), map-get($spell-school-colors, frost)),
	plague: linear-gradient(90deg, map-get($spell-school-colors, shadow), map-get($spell-school-colors, nature)),
	firestorm: linear-gradient(90deg, map-get($spell-school-colors, fire), map-get($spell-school-colors, nature)),
);

$theme-colors: map-merge($theme-colors, $spell-school-colors);

$custom-colors: (
	expansion: $expansion,
	brand: $brand,
	primary: $primary,
	success: $success,
	off-white: #f0f0f0,
);
$theme-colors: map-merge($theme-colors, $custom-colors);

$custom-alpha: (
	black-alpha-30: rgba($black, 0.3),
	black-alpha-50: rgba($black, 0.5),
	black-alpha-75: rgba($black, 0.75),
	gray-800-alpha-50: rgba($gray-800, 0.5),
);
$theme-colors: map-merge($theme-colors, $custom-alpha);

$custom-breakpoints: (
	xxxl: 1600px,
	// Intentionally 1 higher than standard resolution
	1080p: 1921px,
	1440p: 2561px,
	4k: 3841px,
);

// Spacer variables
:root {
	--spacer-0: #{map-get($spacers, 0)};
	--spacer-1: #{map-get($spacers, 1)};
	--spacer-2: #{map-get($spacers, 2)};
	--spacer-3: #{map-get($spacers, 3)};
	--spacer-4: #{map-get($spacers, 4)};
	--spacer-5: #{map-get($spacers, 5)};
}

// Borders
$border-color: $gray-600;
:root {
	--border-default: 1px solid var(--bs-border-color);
}

// item qualites
$item-quality-junk: #9d9d9d;
$item-quality-common: #ffffff;
$item-quality-uncommon: #1eff00;
$item-quality-rare: #0070dd;
$item-quality-epic: #a335ee;
$item-quality-legendary: #ff8000;
$item-quality-artifact: #e5cc80;
$item-quality-heirloom: #0cf;

$item-quality-colors: (
	junk: $item-quality-junk,
	common: $item-quality-common,
	uncommon: $item-quality-uncommon,
	rare: $item-quality-rare,
	epic: $item-quality-epic,
	legendary: $item-quality-legendary,
	artifact: $item-quality-artifact,
	heirloom: $item-quality-heirloom,
);
$theme-colors: map-merge($theme-colors, $item-quality-colors);

// Resources
$health-color: #22ba00;
$mana-color: #2e93fa;
$energy-color: #ffd700;
$rage-color: #ff0000;
$focus-color: #cd853f;
$chi-color: #00ff98;
$combo-points-color: #ffa07a;
$lunar-energy-color: #4a8aff;
$solar-energy-color: #d2952b;
$holy-power-color: #eed98a;

$resource-colors: (
	chi: $chi-color,
	combo-points: $combo-points-color,
	energy: $energy-color,
	focus: $focus-color,
	health: $health-color,
	mana: $mana-color,
	rage: $rage-color,
	holy-power: $holy-power-color,
);
$theme-colors: map-merge($theme-colors, $resource-colors);

// Damage types
$damage-hit: green;
$damage-crit: blue;
$damage-partial: yellow;
$damage-miss: red;
$damage-dps: #ed5653;
$damage-threat: #b56d07;

$damage-colors: (
	dps: $damage-dps,
	threat: $damage-threat,
	hit: $damage-hit,
	crit: $damage-crit,
	partial: $damage-partial,
	miss: $damage-miss,
);
$theme-colors: map-merge($theme-colors, $damage-colors);

// Factions
$faction-colors: (
	horde: red,
	alliance: blue,
);
$theme-colors: map-merge($theme-colors, $faction-colors);

// Talents
$talent-colors: (
	talent-full: #ffd100,
	talent-partial: #40bf40,
);
$theme-colors: map-merge($theme-colors, $talent-colors);

// Links
$link-colors: (
	link-danger: #ef9eaa,
	link-warning: #faf07f,
);

$theme-colors: map-merge($theme-colors, $link-colors);

// Tables
$table-colors: (
	table-row-even-bg: #18191e,
	table-row-odd-bg: #222328,
);

$theme-colors: map-merge($theme-colors, $table-colors);

// Spacers

:root {
	--table-cell-padding: 0.5rem;

	--container-padding-sm: var(--gap-width-sm);
	--container-padding-lg: 3rem;
	--container-padding-xxl: 4rem;
	--container-padding: var(--gap-width-sm);

	--section-spacer-sm: 2rem;
	--section-spacer-lg: calc(var(--container-padding) / 2);
	--section-spacer-xxl: calc(var(--container-padding) / 2);
	--section-spacer: var(--section-spacer-sm);

	--block-spacer: 0.75rem;
	--gap-width: 1.5rem;
	--gap-width-sm: 0.5rem;
}
// Sizing
:root {
	// Make Bootstrap font sizes available as CSS vars
	--h1-font-size: #{$h1-font-size};
	--h2-font-size: #{$h2-font-size};
	--h3-font-size: #{$h3-font-size};
	--h4-font-size: #{$h4-font-size};
	--h5-font-size: #{$h5-font-size};
	--h6-font-size: #{$h6-font-size};

	--content-font-size: 0.75rem;
	--icon-size-sm: 1rem;
	--icon-size-md: 2.5rem;
}

// Z-index variables
:root {
	--header-z-index: 100;
	--sidebar-z-index: 200;
	--toast-z-index: 9999;
}

/////////////////////////////////////////////
//// Global Bootstrap variable overrides ////
/////////////////////////////////////////////

$grid-breakpoints: map-merge($grid-breakpoints, $custom-breakpoints);

$body-bg: #15171e;
$body-color: $white;
$font-size-root: 16px;
$grid-gutter-width: 0.75rem;

$custom-font-sizes: (
	body: 0.875rem,
);
$font-sizes: map-merge($font-sizes, $custom-font-sizes);

// This option doesn't seem to be working for some reason. Carets hidden manually instead
// $enable-caret: false;
$enable-negative-margins: true;
$enable-rounded: false;

$link-color: #a5b1d6;
$link-decoration: none;
$link-hover-color: $white;
:root {
	--link-transition: color 0.15s ease-in-out;
}

// Button
$btn-close-color: $link-color;
$btn-close-focus-shadow: none;
$btn-close-hover-opacity: 1;
$btn-close-opacity: 1;
$btn-hover-bg-shade-amount: 30%;
$btn-font-size: 0.875rem;
$btn-padding-y: 0.5rem;

:root {
	--btn-transition: #{$btn-transition};
	// Add custom variable to reuse paddings
	--btn-padding-y: #{$btn-padding-y};
	--btn-padding-x: #{$btn-padding-x};
	--btn-font-size: #{$btn-font-size};
}

// Input
$input-bg: #1e2633;
$input-border-color: #373d57;
$input-color: $white;
$input-disabled-bg: color-mix(in srgb, $body-bg, $white 20%);
$input-focus-bg: $input-bg;
$input-focus-border-color: color-mix(in srgb, $input-border-color, $white 20%);
$input-focus-box-shadow: 0 0 0.25rem color-mix(in srgb, $body-bg, $white 20%);
$input-focus-color: $white;
$input-font-size: var(--content-font-size);
$input-placeholder-color: $border-color;

:root {
	--input-padding-y: #{$input-padding-y};
	--input-padding-x: #{$input-padding-x};
}

// Dropdown
$dropdown-bg: color-mix(in srgb, $body-bg, $white 5%);
$dropdown-border-color: $input-border-color;
$dropdown-color: $white;
$dropdown-link-active-bg: $dropdown-bg;
$dropdown-link-color: $white;
$dropdown-link-hover-color: $white;
$dropdown-link-hover-bg: color-mix(in srgb, $body-bg, $black 30%);
$dropdown-padding-y: 0;

:root {
	// Add custom variable to reuse outside dropdowns
	--dropdown-bg: #{$dropdown-bg};
	--dropdown-border-width: #{$dropdown-border-width};
	--dropdown-color: #{$dropdown-color};
	--dropdown-link-active-bg: #{$dropdown-link-active-bg};
	--dropdown-link-color: #{$dropdown-link-color};
	--dropdown-link-hover-color: #{$dropdown-link-hover-color};
	--dropdown-link-hover-bg: #{$dropdown-link-hover-bg};
	--dropdown-padding-y: #{$dropdown-padding-y};
}

// Form
$form-check-input-bg: $input-bg;
$form-check-input-border: 1px solid $input-border-color;
$form-check-input-width: 2rem;
$form-check-input-focus-border: $input-focus-border-color;
$form-check-input-focus-box-shadow: 0 0 0.25rem $primary;
$form-check-input-checked-bg-color: $primary;
$form-check-input-checked-bg-image: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'><path fill='none' stroke='#{color-contrast($primary)}' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='m6 10 3 3 6-6'/></svg>");
$form-check-input-checked-border-color: var(--bs-primary);
$form-check-margin-bottom: 0;
$form-check-padding-start: $form-check-input-width;

$form-label-margin-bottom: 0.25rem;
$form-label-font-size: var(--content-font-size);
$form-label-font-weight: normal;

$form-select-bg: $input-bg;
$form-select-border-color: $input-border-color;
$form-select-color: $white;
$form-select-disabled-bg: color-mix(in srgb, $body-bg, $white 20%);
$form-select-focus-border-color: $input-focus-border-color;
$form-select-focus-box-shadow: 0 0 0.25rem color-mix(in srgb, $body-bg, $white 20%);
$form-select-font-size: var(--content-font-size);
$form-select-indicator: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'><path fill='none' stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/></svg>");

$form-text-color: $gray-600;
$form-text-font-size: var(--content-font-size);

// Modal
$modal-content-bg: $body-bg;
$modal-content-border-color: $input-border-color;
$modal-footer-margin-between: 0px;
$modal-header-border-color: $border-color;
$modal-header-padding: 1.25rem;
$modal-inner-padding: 1.25rem;

// Nav
$nav-link-font-size: 0.875rem;
$nav-link-padding-y: var(--spacer-3);
$nav-link-transition: var(--link-transition);

$nav-tabs-border-color: $border-color;
$nav-tabs-link-active-bg: transparent;
$nav-tabs-link-active-color: $white;

// Tooltip
$tooltip-bg: color-mix(in srgb, $body-bg, $white 5%);
$tooltip-border: $input-border-color;
$tooltip-body-color: $body-color;
$tooltip-max-width: 15vw;
$tooltip-max-width-lg: 25vw;
$tooltip-max-width-sm: 75vw;
$tooltip-body-padding-x: var(--spacer-2);
$tooltip-body-padding-y: var(--spacer-2);

:root {
	--bs-tooltip-bg: #{$tooltip-bg};
	--bs-tooltip-border: #{$tooltip-border};
	--bs-tooltip-body-color: #{$tooltip-body-color};
	--bs-tooltip-max-width: #{$tooltip-max-width};
	--bs-tooltip-max-width-lg: #{$tooltip-max-width-lg};
	--bs-tooltip-max-width-sm: #{$tooltip-max-width-sm};
	--bs-tooltip-body-padding-x: #{$tooltip-body-padding-x};
	--bs-tooltip-body-padding-y: #{$tooltip-body-padding-y};
}

// Toast
$toast-background-color: $tooltip-bg;
$toast-border-color: $input-border-color;
$toast-border-radius: 0;
$toast-color: $body-color;
$toast-header-color: $toast-color;
$toast-header-background-color: $toast-background-color;
$toast-padding-x: var(--spacer-3);
$toast-padding-y: var(--spacer-3);
