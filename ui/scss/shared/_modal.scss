.modal {
	.modal-dialog {
		max-height: calc(100vh - (2 * var(--bs-modal-margin)));
		display: flex;

		@include media-breakpoint-down(lg) {
			max-width: calc(100vw - (2 * var(--bs-modal-margin)));
		}

		&.modal-overflow-scroll {
			.modal-content {
				max-height: calc(100vh - (2 * var(--bs-modal-margin)));
			}

			.modal-body {
				overflow: auto;
			}
		}
	}

	.modal-scroll-table {
		flex: 1;
		overflow-y: auto;

		th {
			position: sticky;
			top: 0;
			background: var(--bs-body-bg);
		}
	}
}

.modal-body {
	gap: var(--bs-modal-padding);
}
