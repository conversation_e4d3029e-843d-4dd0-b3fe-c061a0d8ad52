.tippy-box[data-theme='tooltip-quick-swap'] {
	opacity: 1;
	width: 220px;
	cursor: default;

	.tippy-content {
		padding: 0;
	}

	ul {
		padding-left: 0;
		list-style: none;
		margin-bottom: 0;
		gap: 2px;
	}

	li {
		&:not(:last-child) {
			border-bottom: var(--border-default);
		}

		&:nth-child(odd) {
			.tooltip-quick-swap__anchor {
				background-color: var(--bs-table-row-odd-bg);
			}
		}
	}

	.tooltip-quick-swap__icon {
		--gem-width: 2.5rem;
		position: unset;
		margin-right: var(--spacer-1);
		border-radius: 0;
	}
}

.tooltip-quick-swap__title,
.tooltip-quick-swap__footer,
.tooltip-quick-swap__empty {
	padding-left: var(--spacer-2);
	padding-right: var(--spacer-2);
}

.tooltip-quick-swap__title,
.tooltip-quick-swap__footer {
	padding-top: var(--spacer-2);
	padding-bottom: var(--spacer-2);
	margin-bottom: 0;
}

.tooltip-quick-swap__list {
	max-height: 206px;
	overflow-y: auto;
}

.tooltip-quick-swap__anchor {
	cursor: pointer;
	background-color: var(--bs-table-row-even-bg);
	transition: var(--link-transition);
	border: 1px solid transparent;
	transition-property: background-color, color, border;

	@media (pointer: fine) {
		&:hover {
			background-color: hsla(var(--bs-table-row-even-bg-hsl), 0.9) !important;
		}
	}

	&.active {
		border-color: var(--bs-success);
	}
}

.tooltip-quick-swap__label {
	text-overflow: ellipsis;
	white-space: nowrap;
	overflow: hidden;
}

.tooltip-quick-swap__empty {
	margin-bottom: 0;
	color: var(--bs-link-danger);
}
