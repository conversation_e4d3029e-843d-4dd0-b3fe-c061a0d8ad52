.list-picker-root {
	flex-direction: column;
	align-items: center;
	gap: var(--block-spacer);

	.list-picker-title {
		width: 100%;
		border-bottom: var(--border-default);
		margin-bottom: var(--block-spacer);
		font-size: 1rem;
		font-weight: bold;
	}

	.list-picker-items {
		width: 100%;
		display: flex;
		flex-direction: column;
		gap: var(--block-spacer);

		.list-picker-item-container {
			border: var(--border-default);

			&.dragfrom {
				background-color: color-mix(in srgb, var(--bs-body-bg) 80%, transparent);
				filter: opacity(0.5);
				cursor: move;
			}

			&.draggable:has(> .list-picker-item-header .list-picker-item-popover.hover) {
				background-color: color-mix(in srgb, var(--bs-primary) 5%, transparent);
			}

			&.inline {
				display: flex;

				.list-picker-item-header {
					padding: 0;
					border: 0;
					margin: 0;
				}
			}

			&:not(.inline) {
				padding: var(--spacer-3);

				.list-picker-item-header {
					.list-picker-item-action:first-of-type {
						margin-left: auto;
					}
				}
			}

			.list-picker-item-header {
				padding-bottom: var(--block-spacer);
				border-bottom: var(--border-default);
				margin-bottom: var(--block-spacer);
				display: flex;
				align-items: center;
				flex: 1;
				--list-picker-item-spacing: var(--spacer-2);
				gap: var(--list-picker-item-spacing);

				.list-picker-item-title {
					margin-bottom: 0;
				}

				.list-picker-item-action {
					&.list-picker-item-move {
						cursor: move;
					}
				}

				.list-picker-item-popover:popover-open {
					@extend .tippy-content;

					inset: unset;
					position: relative;
					display: flex;
					gap: var(--list-picker-item-spacing);
				}
			}
		}
	}

	.list-picker-new-button {
		width: 100%;
	}

	&.horizontal {
		flex-direction: row;
		align-items: center;

		.list-picker-items {
			flex-direction: row;
			flex-wrap: wrap;
			align-items: center;
		}
	}

	&.list-picker-compact {
		.list-picker-item-container {
			border: 0;
			padding: 0 !important;
		}

		.list-picker-item-header {
			display: none !important;
		}

		&:not(:has(.list-picker-items > *)) {
			display: none;
		}
	}
}
