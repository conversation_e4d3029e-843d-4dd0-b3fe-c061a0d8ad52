.encounter-picker-root {
	display: flex;
	flex-wrap: wrap;
	gap: var(--block-spacer);

	.execute-group {
		width: 100%;
		flex-direction: column;
		flex-wrap: nowrap;
	}

	.advanced-button {
		min-width: calc(50% - 0.5rem);

		@include media-breakpoint-down(xxl) {
			width: 100%;
		}
	}

	.list-picker-compact {
		.input-root {
			margin-bottom: 0;
		}
	}
}

.advanced-encounter-picker-modal {
	.modal-body {
		overflow: auto;
		gap: var(--block-spacer);
	}

	.encounter-header {
		display: flex;
		flex-direction: column;
		gap: var(--block-spacer);
	}

	.encounter-picker {
		width: 33.33%;

		@include media-breakpoint-down(sm) {
			width: 50%;
		}
	}

	.execute-group {
		flex-direction: row;
	}

	.target-picker-root {
		display: grid;
		gap: var(--block-spacer);
		grid-template-columns: 1fr;

		@include media-breakpoint-up(xl) {
			grid-template-columns: 1fr 1fr 1fr;
		}

		.input-root:is(:only-child),
		.input-root:is(:last-child) {
			margin-bottom: 0;
		}
	}
}

.hide-threat-metrics {
	.advanced-encounter-picker-modal {
		// Use visibility to maintain thirds nicely for the modal layout
		.target-picker-section3.threat-metrics {
			display: block !important;
			visibility: hidden;

			@include media-breakpoint-down(xl) {
				display: none !important;
			}

			.input-root {
				display: none;
			}
		}
	}
}
