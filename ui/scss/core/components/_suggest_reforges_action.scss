.tippy-box[data-theme='reforge-optimiser-popover'] {
	min-width: 240px;

	.saved-data-manager-root {
		margin-bottom: 0;
	}
}

.tippy-box[data-theme='suggest-reforges-softcaps'] {
	max-height: 40vh;
	overflow: auto;

	table {
		width: 100%;
		table-layout: auto;

		tr {
			> *:first-child {
				width: 0;
			}
		}
	}
}

.suggest-reforges-settings-group {
	--settings-button-width: 36px;
	position: relative;
	order: 3;
}

.suggest-reforges-action-button {
	padding-left: calc(var(--btn-padding-x) + var(--settings-button-width));
	padding-right: calc(var(--btn-padding-x) + var(--settings-button-width));
}

.reforge-optimizer-stat-cap-table {
	border-collapse: collapse;
	border-spacing: 0;
	padding: 0;
	width: 100%;

	tr {
		td,
		th {
			padding-bottom: var(--spacer-1);
		}
	}

	th,
	td {
		padding: 0;

		&:not(:first-child):not(:only-child) {
			padding-left: calc(var(--spacer-1) / 2);
		}

		&:not(:last-child):not(:only-child) {
			padding-right: calc(var(--spacer-1) / 2);
		}

		&:first-child:not(:only-child) {
			padding-right: var(--spacer-2);
		}
	}

	tbody {
		td:nth-child(2) {
			width: 75px;
		}

		td:nth-child(3) {
			width: 60px;
		}
	}

	.form-control {
		padding-left: var(--spacer-2);
		padding-right: var(--spacer-2);
		text-align: right;
	}
}

.reforge-optimizer-stat-cap-item {
	td:first-child {
		width: 0;
	}
}

.reforge-optimizer-stat-cap-item-label {
	white-space: nowrap;
}

.suggest-reforges-button-settings {
	position: absolute;
	top: 0;
	right: 0;
	bottom: 0;
	background-color: transparent;
	border-color: transparent;
	width: var(--settings-button-width);
}
