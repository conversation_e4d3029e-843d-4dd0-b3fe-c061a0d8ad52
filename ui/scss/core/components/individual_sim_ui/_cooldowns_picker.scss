.cooldowns-picker-root {
	display: flex;
	flex-flow: column;

	.cooldown-picker {
		margin-bottom: var(--block-spacer);
		display: flex;
		align-items: center;
		justify-content: space-between;

		&.add-cooldown-picker {
			.cooldown-timings-picker,
			.delete-cooldown {
				visibility: hidden;
			}
		}

		& > :not(:last-child) {
			margin-right: var(--spacer-2);
		}

		.cooldown-picker-label {
			overflow: hidden;
			text-overflow: ellipsis;
		}

		.cooldown-timings-picker {
			margin-bottom: 0;
			margin-left: auto;

			input {
				width: 100%;
			}
		}
	}
}
