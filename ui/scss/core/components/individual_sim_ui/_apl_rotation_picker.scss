@import '../dropdown_picker';

@import './apl_helpers';

:root {
	--apl-spacer: var(--spacer-2);
}

.apl-rotation-picker-root {
	display: flex;
	flex-direction: column;
	gap: var(--section-spacer);

	.apl-list-item-picker,
	.apl-prepull-action-picker {
		flex-wrap: wrap;
		align-items: flex-start !important;

		.list-picker-title {
			border: 0;
			margin: 0;
		}

		& > .list-picker-new-button {
			width: 12rem;
			margin: 0;
		}

		.list-picker-items {
			gap: var(--apl-spacer);
		}

		> * > .list-picker-item-container {
			background-color: color-mix(in srgb, var(--bs-body-bg) 80%, transparent);
			padding: var(--apl-spacer);

			.list-picker-item-header {
				align-items: flex-start;
				justify-content: flex-end;
			}

			& > .list-picker-item {
				flex-grow: 1;
				padding-right: var(--apl-spacer);
			}
		}

		.form-label {
			margin-bottom: 0;
		}
	}

	.apl-prepull-action-picker {
		.list-picker-item-container {
			align-items: center;
		}
	}

	.apl-list-item-picker .apl-prepull-actions-only,
	.apl-prepull-action-picker .apl-priority-list-only {
		display: none;
	}

	.adaptive-string-picker-root > input {
		text-align: center;
	}

	.boolean-picker-input {
		cursor: pointer;
		width: 1.5rem;
		height: 1.5rem;
	}

	.apl-prepull-actions-doat {
		width: unset;
		margin: 0;
	}

	.apl-validation-information {
		color: var(--bs-off-white) !important;
		text-shadow: 0 0 var(--spacer-2) var(--bs-link-color);
	}

	.apl-validation-warning {
		color: var(--bs-link-warning) !important;
		text-shadow:
			0 0 var(--spacer-2) var(--bs-danger),
			0 0 var(--spacer-2) var(--bs-danger),
			0 0 var(--spacer-2) var(--bs-danger);
	}

	.apl-validation-error {
		color: var(--bs-danger) !important;
		text-shadow: 0 0 var(--spacer-2) var(--bs-off-white);
	}

	.list-picker-item-container {
		gap: var(--apl-spacer);
	}
}

.apl-list-item-picker-root {
	display: flex;
	align-items: center;
	flex-direction: row;
	margin: 0;

	& > :not(:last-child) {
		margin-right: var(--spacer-2);
	}

	.form-label {
		font-size: var(--btn-font-size);
		line-height: 1.5;
	}

	.form-control {
		padding: calc(var(--input-padding-y) / 2) calc(var(--input-padding-x) / 2);
	}

	.number-picker-root {
		flex-direction: row;
	}

	.dropdown-picker-button {
		padding: 0;
		border: 0;
	}

	.dropdown-menu {
		background-color: var(--bs-table-row-odd-bg);
	}
}

.apl-action-picker-root,
.apl-value-picker-root {
	flex-direction: row;
	margin: 0;
	gap: var(--apl-spacer);

	.input-root {
		margin: 0;
		width: auto;
		border: none;
	}

	.list-picker-new-button {
		width: unset;
		font-size: var(--content-font-size);
		line-height: 1.5rem;
	}
}

.apl-list-item-picker-root > .apl-action-picker-root {
	flex-direction: column;
}

.apl-picker-builder-root {
	flex-wrap: wrap;
	gap: var(--apl-spacer);

	.apl-picker-builder-multi {
		padding-left: var(--apl-spacer);
		border-left: 1px solid var(--bs-border-color);
	}

	.list-picker-item-header {
		.list-picker-item-title {
			display: none !important;
		}
	}

	.list-picker-root {
		align-items: flex-start;

		& > .list-picker-new-button {
			margin: 0;
		}

		.list-picker-item-container {
			padding: 0 !important;
			border: 0 !important;
			margin-bottom: 0 !important;
			display: flex;

			.list-picker-item-header {
				order: 1;
				line-height: var(--spacer-4);
			}
		}
	}
}

.apl-action-picker-action {
	display: flex;
	flex-direction: row;
	gap: var(--apl-spacer);
}

.hide-picker-root {
	width: unset;
	margin: 0;
}

.apl-picker-builder-root.apl-action-sequence .apl-action-condition {
	display: none;
}

/* stylelint-disable-next-line plugin/stylelint-bem-namics */
.apl-picker-builder-root.apl-action-strictSequence .apl-action-condition {
	display: none;
}

.apl-action-schedule {
	.apl-action-condition {
		display: none;
	}

	.adaptive-string-picker-root {
		.form-control {
			width: 10rem;
			max-width: 100%;
			text-align: left;
		}
	}
}
