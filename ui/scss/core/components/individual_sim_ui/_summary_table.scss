.summary-table-root {
	@include media-breakpoint-down(md) {
		width: 100%;
	}
}

.summary-tables-container {
	display: grid;
	grid-gap: var(--section-spacer);
	grid-template-columns: repeat(2, 1fr);
	grid-auto-flow: row;

	@include media-breakpoint-down(md) {
		grid-template-columns: 1fr;
	}

	.content-block-header {
		.summary-table-reset-button {
			margin-left: auto;
		}
	}

	.content-block-body {
		gap: var(--spacer-1);
	}
}

.summary-table-row {
	.gem-icon {
		--gem-width: 2rem;
		position: unset;
		margin-right: var(--spacer-1);
		border-radius: 0;
		border: var(--border-default);
	}

	:last-child {
		margin-left: auto;
	}
}

.summary-table-link {
	display: flex;
	align-items: center;
}

.summary-table-footer {
	.positive {
		color: var(--bs-success);
	}

	.negative {
		color: var(--bs-danger);
	}
}
