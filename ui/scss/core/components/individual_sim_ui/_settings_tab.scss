@use 'sass:map';

@import './consumes_picker';
@import './cooldowns_picker';

.settings-tab {
	.tab-pane-content-container {
		.tab-panel-left {
			grid-template-columns: 2fr 2fr 3fr;
		}
	}

	.rotation-settings,
	.other-settings {
		.input-root {
			label {
				width: 60%;
				padding-right: 0.5rem;
			}

			input:not(.form-check-input),
			select,
			.picker-group {
				min-width: 40%;
			}
		}
	}

	.cooldown-settings {
		.cooldown-timings-picker {
			min-width: 40%;
			width: 40%;
		}
	}

	.buffs-settings,
	.debuffs-settings {
		.content-block-header{
			flex-direction: column;
		}

		.content-block-body {
			display: grid;
			grid-template-columns: repeat(3, minmax(0, 1fr));
			grid-gap: var(--block-spacer);

			@include media-breakpoint-down(md) {
				grid-template-columns: repeat(2, minmax(0, 1fr));
			}

			& > .icon-picker {
				.form-label {
					white-space: normal;
				}
			}
		}
	}
}

@include media-breakpoint-down(xxl) {
	.settings-tab {
		.tab-pane-content-container {
			flex-direction: column;
		}
	}
}

@include media-breakpoint-down(xl) {
	.settings-tab {
		.tab-pane-content-container {
			.tab-panel-left {
				flex-wrap: wrap;

				.tab-panel-col {
					flex-basis: 100%;
				}
			}
		}
	}
}
