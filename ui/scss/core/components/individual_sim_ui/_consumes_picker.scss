.consumes-picker-root {
	display: grid;
	gap: var(--block-spacer);

	@include media-breakpoint-down(lg) {
		grid-template-columns: repeat(3, 1fr);
	}

	@include media-breakpoint-down(md) {
		grid-template-columns: repeat(1, 1fr);
	}

	.consumes-row-inputs {
		justify-content: flex-end;
	}

	.elixir-space {
		width: var(--icon-size-md);
		display: flex;
		align-items: center;
		justify-content: center;
	}
}
