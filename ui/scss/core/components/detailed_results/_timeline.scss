:root {
	--timeline-tick-bg: maroon;
	--timeline-cast-bg: rgb(229, 204, 128, 0.7);
	--timeline-aura-bg: rgba(0, 0, 255, 0.52);
	--timeline-travel-time-bg: rgb(249, 192, 255, 0.6);
}

.threat .series-color,
.threat.series-color {
	color: var(--bs-threat);
}

.health .series-color,
.health.series-color {
	color: var(--bs-health);
}

.mana .series-color,
.mana.series-color {
	color: var(--bs-mana);
}

.energy .series-color,
.energy.series-color {
	color: var(--bs-energy);
}

.rage .series-color,
.rage.series-color {
	color: var(--bs-rage);
}

.focus .series-color,
.focus.series-color {
	color: var(--bs-focus);
}

.combo-points .series-color,
.combo-points.series-color {
	color: var(--bs-combo-points);
}

.solar-energy .series-color,
.solar-energy.series-color {
	color: var(--bs-solar-energy);
}

.lunar-energy .series-color,
.lunar-energy.series-color {
	color: var(--bs-lunar-energy);
}

.holy-power .series-color,
.holy-power.series-color {
	color: var(--bs-holy-power);
}

.timeline-root {
	display: flex;
	height: 100%;
	flex-direction: column;
}

.timeline-disclaimer {
	display: flex;
	align-items: flex-start;

	.timeline-chart-picker {
		width: 8rem;
		margin-left: auto;
	}
}

.timeline-warning {
	margin-right: var(--bs-spacer-2);
}

.timeline-plots-container {
	flex-grow: 1;
}

.timeline-plot > div {
	margin: auto;
}

// The timeline is generated by ApexCharts which sets height manually. In order to scale it properly, we need to apply several
// height: 100% attributes on these containers
.timeline-content {
	height: 100%;

	.dr-row {
		height: 100%;
		padding: 0;

		.timeline {
			height: 100%;
		}
	}
}

.timeline-plot {
	&.dps-resources-plot {
		height: 32rem;
	}

	.apexcharts-tooltip.apexcharts-theme-dark {
		z-index: 1000;
		box-shadow: none;
		border-radius: 0;
		background-color: var(--bs-tooltip-bg);
		border: var(--bs-tooltip-border) 1px solid;
		color: var(--bs-tooltip-body-color);
		padding: var(--bs-tooltip-body-padding-y) var(--bs-tooltip-body-padding-x);
	}
}

.timeline-tooltip {
	display: flex;
	flex-direction: column;
	gap: var(--spacer-3);

	ul {
		display: flex;
		flex-direction: column;
		gap: var(--spacer-1);
		padding-left: 0;
		list-style: none;
		margin-bottom: 0;
	}

	li {
		display: flex;
		flex-wrap: wrap;
		align-items: center;
		gap: var(--spacer-2);
	}
}

.timeline-tooltip-header {
	border-bottom: 1px solid var(--bs-white);
}

.timeline-tooltip-body {
	display: flex;
	flex-direction: column;
	gap: var(--spacer-3);
}

.timeline-tooltip-body-row {
	display: flex;
	align-items: center;
}

.timeline-tooltip-icon {
	width: 20px;
	height: 20px;
}

.series-color {
	font-weight: bold;
}

.timeline-tooltip-auras {
	border-top: 1px solid var(--bs-white);
}

.rotation-container {
	color: var(--bs-white);
	display: flex;
}

.rotation-hidden-ids {
	margin: 10px !important;
}

.rotation-row {
	height: 28px;
	margin: 0.25rem;
}

.rotation-labels {
	display: inline-block;
	white-space: nowrap;
}

.rotation-label {
	font-size: 13px;
	font-weight: bold;
	display: flex;
	align-items: center;
	color: var(--bs-white);
}

.rotation-label .fas {
	margin: 1px;
	cursor: pointer;
	color: var(--bs-white);
}

.rotation-label .fas:hover {
	color: var(--bs-link-danger);
}

.rotation-label-hidden {
	display: inline-flex;
	border: 1px solid var(--bs-white);
	margin: 3px;
}

.rotation-label-icon {
	@include wowhead-background-icon;
	height: 24px;
	width: 24px;
	margin: 2px;
}

.rotation-label-text {
	margin: 2px;
}

.rotation-label-header,
.rotation-timeline-header {
	height: 30px;
}

.rotation-timeline {
	display: inline-block;
	overflow-x: scroll;
}

.rotation-timeline-row {
	position: relative;
	border-bottom: 1px solid var(--bs-gray);
}

.rotation-timeline-resource {
	position: absolute;
	height: 100%;
	border-left: 1px solid var(--bs-gray);
	border-right: 1px solid var(--bs-gray);
	display: flex;
	align-items: center;
	font-weight: bold;
	overflow-x: hidden;
}

.rotation-timeline-resource-fill {
	position: absolute;
	bottom: 0;
	left: 0;
	width: 10px;

	&.focus {
		background-color: var(--bs-orange);
	}

	&.energy {
		background-color: var(--bs-partial);
	}

	&.lunar-energy {
		background-color: var(--bs-lunar-energy);
		width: 24px;
	}

	&.solar-energy {
		background-color: var(--bs-solar-energy);
		top: 0;
		width: 24px;
	}

	&.holy-power {
		background-color: var(--bs-holy-power);
		width: 24px;
	}
}

.rotation-timeline-cast {
	position: absolute;
	background-color: var(--timeline-cast-bg);
	z-index: 1;
	display: flex;
}

.rotation-timeline-travel-time {
	position: absolute;
	height: 24px;
	background-color: var(--timeline-travel-time-bg);
}

.rotation-timeline-tick {
	position: absolute;
	width: 5px;
	background-color: var(--timeline-tick-bg);
	height: 20px;
	top: 2px;
	z-index: 2;
}

.rotation-timeline-aura {
	position: absolute;
	background-color: var(--timeline-aura-bg);
	opacity: 0.5;
	height: 20px;
	top: 2px;
}

.rotation-timeline-stacks-change {
	position: absolute;
	height: 20px;
	background-color: transparent;

	border-left: 1px solid var(--bs-gray);
	border-right: 1px solid var(--bs-gray);
	display: flex;
	align-items: center;
	font-weight: bold;
	overflow-x: hidden;
}

.rotation-timeline-separator {
	width: 100%;
	border: 1px solid var(--bs-white);
	margin: 10px 0;
}

.rotation-timeline-cast-icon {
	@include wowhead-background-icon;
	height: 24px;
	width: 24px;
	border-bottom: 3px solid var(--bs-gray);
}

.rotation-timeline-cast.outcome-miss .rotation-timeline-cast-icon {
	border-color: var(--bs-miss);
}

.rotation-timeline-cast.outcome-partial .rotation-timeline-cast-icon {
	border-color: var(--bs-partial);
}

.rotation-timeline-cast.outcome-hit .rotation-timeline-cast-icon {
	border-color: var(--bs-hit);
}

.rotation-timeline-cast.outcome-crit .rotation-timeline-cast-icon {
	border-color: var(--bs-crit);
}
