.ep-weights-menu {
	.ep-weights-options {
		margin-bottom: var(--block-spacer);
	}

	.ep-reference-options {
		margin-bottom: var(--block-spacer);

		span {
			font-weight: bold;
		}

		p {
			margin-top: var(--block-spacer);
		}
	}

	.results-ep-table-container {
		position: relative;

		.results-ep-table {
			width: 100%;

			&.stats-type-ep .type-weight,
			&.stats-type-weight .type-ep {
				display: none;
			}

			th,
			td {
				&[class*='type-'] {
					text-align: right;
				}
			}

			th {
				padding-top: var(--spacer-2);
				padding-bottom: var(--spacer-2);
				padding-right: var(--spacer-2);
				text-align: left;
			}

			.ep-ratios {
				background-color: var(--bs-table-row-even-bg);

				td {
					padding: var(--table-cell-padding);
				}

				.input-root {
					align-items: flex-end;
				}

				input {
					text-align: right;
					max-width: 100px;
				}

				.compute-ep {
					white-space: nowrap;

					.not-tiny {
						display: inline;
					}

					.fas {
						display: inline;
						vertical-align: middle;
					}
				}
			}

			tbody {
				tr:nth-child(even) {
					background-color: var(--bs-table-row-even-bg);
				}

				tr:nth-child(odd) {
					background-color: var(--bs-table-row-odd-bg);
				}

				td {
					padding: var(--table-cell-padding);

					.fas {
						display: inline;
					}
				}
			}

			.input-root {
				margin-bottom: 0;
			}

			.results-avg.notapplicable {
				padding-right: 25px;
				font-weight: bold;
			}

			.results-stdev {
				font-size: 0.625rem;
			}

			.col-action {
				margin-left: var(--spacer-1);
			}
		}
	}
}

.ep-weights-sidebar {
	min-width: 170px;

	.saved-data-create-container {
		max-width: 200px;
	}
}

.unused-ep {
	color: var(--bs-gray-500) !important;
}

// Style overrides for the EP UI in tank mode
.ep-weights-menu:not(.hide-threat-metrics) {
	// Compact rendering of EP compute button
	@mixin compact-button {
		.results-ep-table-container {
			.results-ep-table {
				.not-tiny {
					display: none !important;
				}
			}
		}
	}

	// Compact rendering of number pickers
	@mixin compact-input {
		input {
			text-align: center;
			padding-left: 0;
			padding-right: 3px;
		}
	}

	// Compact table display by shrinking padding
	@mixin compact-table {
		.results-ep-table-container {
			.results-ep-table {
				padding-right: 0;

				td {
					padding-left: 0;
				}
			}
		}
	}

	// Shorten the compute EP button on very small devices
	@media (max-width: 471px) {
		@include compact-button;
	}

	// More compact EP table on mobile viewports
	@include media-breakpoint-only(xs) {
		@include compact-table;

		.ep-ratios {
			@include compact-input;
		}
	}

	// Padding obscures part of the value on these sizes
	@include media-breakpoint-between(sm, lg) {
		.ep-ratios {
			@include compact-input;
		}
	}

	@include media-breakpoint-only(lg) {
		.results-ep-table {
			th {
				padding-right: 0;
			}
		}
	}

	// Avoid awkward line wrapping of table headers
	@include media-breakpoint-down(lg) {
		@include compact-button;
		@include compact-table;

		.results-ep-table {
			@include compact-input;

			th[class*='type-'] {
				text-align: center;
			}
		}
	}
}

.results-pending-overlay {
	position: absolute;
	inset: 0;
	display: flex;
	align-items: center;
	justify-content: center;
	z-index: 10000;
	background: var(--bs-black-alpha-50);
}
