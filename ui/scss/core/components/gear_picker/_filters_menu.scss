.filters-menu {
	.modal-body {
		gap: var(--block-spacer);
	}

	.menu-section {
		display: flex;

		@include media-breakpoint-down(sm) {
			display: block;
		}

		&.general-section {
			.menu-section-content {
				gap: var(--block-spacer);
			}
		}

		.menu-section-header {
			flex: 1;
		}

		.menu-section-content {
			display: flex;
			flex-direction: column;
			flex: 3;

			.ilvl-filters {
				display: grid;
				grid-template-columns: 1fr 0 1fr;
				grid-column-gap: var(--bs-modal-padding);

				.ilvl-filters-separator {
					padding: $input-padding-y 0;
					display: flex;
					justify-content: center;
					align-self: flex-end;
				}
			}

			&[class*='filters-menu-section-'] {
				display: grid;
				grid-template-columns: repeat(2, 1fr);
				grid-column-gap: var(--bs-modal-padding);
			}
		}

		.filters-menu-section-bool-list {
			grid-row-gap: var(--spacer-1);
		}
	}
}
