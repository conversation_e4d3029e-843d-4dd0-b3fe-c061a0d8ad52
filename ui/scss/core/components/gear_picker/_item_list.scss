:root {
	--ilvl-cell-width: 3rem;
	--source-cell-width: 16rem;
	--ep-cell-width: 6rem;
	--icon-cell-width: 2rem;
}

.selector-modal-search {
	max-width: 12rem;
}

.selector-modal-phase-selector {
	min-width: 7rem;

	.phase-selector {
		margin-bottom: 0;
	}
}

.selector-modal-remove-button {
	margin-left: auto;
}

.selector-modal-list-labels {
	display: flex;
	padding-right: 0.5rem;
	margin-right: var(--spacer-3);
	font-size: 1.125rem;
	justify-content: space-between; // Ensures labels are evenly spaced
	gap: var(--spacer-3);

	@include media-breakpoint-down(xl) {
		margin-right: 0;
	}

	.item-label {
		flex: 1;
		margin-right: 0.5rem;
	}

	.ilvl-label {
		width: var(--ilvl-cell-width);
	}

	.source-label {
		width: var(--source-cell-width);
	}

	.ep-label {
		width: var(--ep-cell-width);
		display: flex;
		align-items: center;
		float: right;
	}

	.favorite-label,
	.compare-label {
		width: var(--icon-cell-width);
	}
}

.selector-modal-list {
	width: 100%;
	max-height: 60vh;
	overflow-y: scroll;
	overflow-x: hidden;
	padding: 0;
	margin-bottom: 0;

	&.hide-ep {
		.selector-modal-list-item-ep {
			display: none;
		}
	}
}

// TODO: Move all of these to a shared file, as they're used in other places too
.selector-modal-list-item {
	padding: 0.5rem;
	display: flex;
	align-items: center;
	background-color: var(--bs-gray-900);
	gap: var(--spacer-3);

	&:nth-child(2n) {
		background: var(--bs-table-row-even-bg);
	}

	&:nth-child(2n + 1) {
		background: var(--bs-table-row-odd-bg);
	}

	&:hover {
		background: var(--bs-gray-800);
	}

	&.active {
		.selector-modal-list-item-icon {
			outline: 2px solid var(--bs-success);
		}
	}

	&.reforge {
		display: flex;
		justify-content: space-between; /* Adjusts children to start and end */
		width: 100%; /* Ensure it fills its container */
		color: var(--bs-success);

		.loss,
		.gain {
			flex-shrink: 0; /* Prevents shrinking */
		}

		.loss {
			color: var(--bs-danger);
		}

		.gain {
			margin-left: 5px;
			color: var(--bs-success);
		}

		.ep {
			margin-left: auto; /* Pushes the EP to the far right */
			padding-left: 20px; /* Ensures some space between it and any preceding content */
			color: var(--bs-crit); /* Example color */
		}
	}

	&.reforge.selected {
		// Style for the selected reforge item
		opacity: 1; // Reset opacity to full for visibility
		border: 1px solid var(--bs-off-white); // Off-white border
	}

	.selector-modal-list-item-link {
		display: flex;
		align-items: center;
		min-width: 10vw;
	}

	.selector-modal-list-label-cell {
		flex: 1;
		display: flex;
		align-items: center;
	}

	.selector-modal-list-item-icon {
		@include wowhead-background-icon;
		width: 3rem;
		height: 3rem;
		border: var(--border-default);
	}

	.selector-modal-list-item-name {
		font-size: 1.125rem;
		margin-left: var(--spacer-2);
		letter-spacing: normal;
		font-weight: normal;
		cursor: pointer;
	}
}

.selector-modal-list-item-ilvl-container {
	width: var(--ilvl-cell-width);
	text-align: center;
}

.selector-modal-list-item-source-container {
	width: var(--source-cell-width);
}

.selector-modal-list-item-favorite,
.selector-modal-list-item-compare {
	width: var(--icon-cell-width);
}

.selector-modal-list-item-ep {
	width: var(--ep-cell-width);
	display: flex;
	align-items: center;

	& > span {
		display: inline-block;
		color: var(--bs-white);
		text-align: right;
	}

	.selector-modal-list-item-ep-delta {
		margin-left: var(--spacer-1);
		font-size: 0.8rem;
	}
}

/***
 * Tab Overrides
 */

#gear-picker-selector-modal-upgrades {
	.selector-modal-list-item-name {
		display: flex;
	}
}
