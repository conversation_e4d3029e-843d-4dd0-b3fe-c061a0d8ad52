@import './detailed_results/dps_histogram';
@import './detailed_results/log_runner';
@import './detailed_results/player_damage';
@import './detailed_results/resource_metrics';
@import './detailed_results/results_filter';
@import './detailed_results/source_chart';
@import './detailed_results/timeline';
@import './detailed_results/topline_results';

.detailed-results-manager-root {
	display: flex;
	flex-direction: column;

	& > * {
		min-height: 0;
	}
}

.detailed-results-controls-div {
	display: flex;
	margin-bottom: var(--block-spacer);

	.detailed-results-new-tab-button {
		margin-right: var(--block-spacer);
	}
}

.all-players .single-player-only {
	display: none;
}

.single-player .all-players-only {
	display: none;
}

.individual-sim .single-player-only {
	display: initial;
}

.individual-sim .all-players-only {
	display: none;
}

.dr-root {
	display: flex;
	flex-direction: column;

	&.dr-no-results {
		.dr-row {
			display: none;
		}
	}

	&:not(.dr-no-results) {
		#noResultsTab {
			display: none;
		}
	}

	> .tab-content {
		padding-top: var(--gap-width);
	}

	> .tab-content > .tab-pane.dr-tab-content {
		padding-top: 0;
		padding-bottom: 0;
	}
}

.dr-toolbar {
	position: sticky;
	top: var(--sim-header-height);
	padding-left: var(--container-padding);
	padding-right: var(--container-padding);
	margin-left: calc(-1 * var(--container-padding));
	margin-right: calc(-1 * var(--container-padding));
	display: flex;
	transition: background-color 0.15s ease-in-out;
	// This needs to be less than var(--header-z-index) otherwise the toolbar overlaps the import/export dropdowns
	z-index: calc(var(--header-z-index) - 1);

	&::after {
		content: ' ';
		position: absolute;
		bottom: -1px;
		left: 0;
		right: 0;
		width: calc(100% - 2 * var(--container-padding));
		height: 1px;
		margin-left: auto;
		margin-right: auto;
		background-color: var(--bs-border-color);
		transition: width 0.15s ease-in-out;
	}

	&.stuck {
		background: rgba(var(--theme-background-color), var(--theme-background-opacity));

		&::after {
			width: 100%;
		}
	}

	& > * {
		min-height: 0;
	}

	.nav-tabs {
		border-bottom: 0;
	}

	.input-root {
		margin-bottom: 0;
	}
}

#noResultsTab {
	display: flex;
	justify-content: center;
	align-items: center;
	padding: var(--gap-width);
	font-size: 1rem;
}

.results-filter {
	display: flex !important;
	align-items: center;
}

.individual-sim .player-filter-root {
	display: none !important;
}

.tabs-filler {
	flex-grow: 1;
}

.other-metrics {
	display: flex;
	width: 100%;
}

.metrics-table {
	table-layout: auto;

	td,
	th {
		width: 0;
	}

	th {
		white-space: nowrap;
	}

	td {
		@include media-breakpoint-up(1080p) {
			white-space: nowrap;
		}
	}

	.metrics-table-cell--primary-metric {
		width: 400px;
	}
}

.damage-content .metrics-table,
.threat-content .metrics-table,
.healing-content .metrics-table,
.tippy-box[data-theme='metrics-table'] {
	font-size: 12px;
}

.tippy-box[data-theme='metrics-table'],
.metrics-table {
	--bs-border-default: rgba(var(--bs-white-rgb), 0.2);
	width: 100%;

	th,
	td {
		border-left: 1px solid var(--bs-border-default);
		border-right: 1px solid var(--bs-border-default);

		&:first-child {
			border-left: 0;
		}

		&:last-child {
			border-right: 0;
		}
	}
}

.metrics-table-header-row {
	border-bottom: 2px solid rgba(var(--bs-white-rgb), 0.8);
}

.metrics-table-footer-row {
	border-top: 2px solid rgba(var(--bs-white-rgb), 0.8);
}

.metrics-table-header-cell {
	padding: var(--spacer-1) var(--spacer-2);
	cursor: pointer;
	text-align: center;
}

.metrics-table-body tr {
	border-bottom: 1px solid var(--bs-border-default);

	&:nth-child(even) {
		background-color: rgba(var(--bs-white-rgb), 0.05);
	}
}

.metrics-table-body tr:hover {
	background-color: var(--bs-black-alpha-50);
}

.metrics-table-body,
.metrics-table-footer {
	td {
		padding: var(--spacer-1) var(--spacer-2);
	}
}

.metrics-table-body td:first-child,
.metrics-table-footer td:first-child {
	text-align: left;
}

.metrics-table-body td:not(:first-child),
.metrics-table-footer td:not(:first-child) {
	text-align: right;
}

.metrics-action {
	display: flex;
	align-items: center;
	gap: var(--spacer-2);
	white-space: initial;

	.expand-toggle {
		margin-left: auto;
	}
}

.metrics-action-icon {
	@include wowhead-background-icon;
	height: 24px;
	width: 24px;
	vertical-align: middle;
	margin-right: 4px;
}

.parent-metric {
	cursor: pointer;
}

.child-metric > *:first-child {
	padding-left: 20px;
}

tr:not(.parent-metric) .expand-toggle {
	display: none;
}

.parent-metric.expand .fa-caret-right,
.parent-metric:not(.expand) .fa-caret-down {
	display: none;
}

.metrics-total {
	--total-percentage-width: 7ch;
	--total-amount-width: 7ch;
	--total-bar-min-width: 50px;
	min-width: calc(var(--total-percentage-width) + var(--total-amount-width) + var(--total-bar-min-width));
}

.metrics-total-percentage,
.metrics-total-damage {
	flex-shrink: 0;
}

.metrics-total-percentage {
	width: var(--total-percentage-width);
}

.metrics-total-amount {
	width: var(--total-amount-width);
}

.metrics-total-bar {
	position: absolute;
	left: var(--total-percentage-width);
	right: var(--total-amount-width);
	height: 100%;
	flex-shrink: 1;
	flex-grow: 1;
	background-color: color-mix(in srgb, var(--bs-white) 5%, transparent);
}

.metrics-total-bar-fill {
	position: absolute;
	top: 0;
	left: 0;
	width: var(--percentage);
	height: 100%;
	background-color: currentColor;
}

.tippy-box[data-theme='metrics-table'] {
	@include media-breakpoint-down(sm) {
		max-width: 300px;
	}

	thead {
		th {
			padding-top: 0;
			text-align: center;
		}
	}

	tfoot {
		tr:last-child {
			td {
				font-weight: var(--font-weight-medium);
				padding-bottom: 0;
			}
		}
	}
}
