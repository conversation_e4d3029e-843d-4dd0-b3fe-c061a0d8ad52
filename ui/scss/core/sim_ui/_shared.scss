:root {
	--sim-header-height: 4.5rem;

	@include media-breakpoint-down(lg) {
		--sim-header-height: 65px;
	}
}

td,
th {
	padding: auto;
}

.sim-ui {
	// Allow for styled scrolling
	max-height: 100vh;
	overflow-y: auto;
	scrollbar-color: var(--bs-primary) var(--bs-body-bg);
	scrollbar-width: thin;

	&::-webkit-scrollbar {
		width: 0.2rem;
	}

	&::-webkit-scrollbar-track {
		background-color: var(--bs-body-bg);
	}

	&::-webkit-scrollbar-thumb {
		background-color: var(--bs-primary);
	}

	.sim-root {
		height: 100%;
		min-height: 100vh;
		display: flex;
		flex-direction: column;

		.sim-bg {
			@include media-breakpoint-down(lg) {
				left: 0;
			}
		}

		.sim-container {
			display: flex;
			flex: 1;

			@include media-breakpoint-down(lg) {
				flex-direction: column;
			}
		}

		.sim-sidebar,
		.sim-content {
			@include media-breakpoint-down(lg) {
				width: 100%;
				min-height: unset;
			}
		}

		.sim-content {
			padding: 0 var(--container-padding) var(--container-padding) var(--container-padding);
			flex: 4;
			z-index: 1;
		}
	}
}

.sim-ui--is-unlaunched {
	.import-export {
		display: none !important;
	}

	.sim-sidebar {
		.sim-sidebar-actions > *:not(.sim-ui-unlaunched-container),
		.sim-sidebar-results {
			display: none !important;
		}
	}
}

.sim-ui-unlaunched-container {
	max-width: 400px;

	i {
		color: var(--bs-danger);
	}
}

.sim-toast-container {
	position: fixed;
	bottom: 0;
	right: 0;
	z-index: var(--toast-z-index);
	display: grid;
	grid-gap: var(--spacer-2);
}

.sim-bg {
	position: fixed;
	// These allow the background to fill the page and not be adjusted by browser scrollbars
	top: 0;
	left: 0;
	width: 100vw;
	height: 100vh;
	background-image:
		linear-gradient(
			rgba(var(--theme-background-color), var(--theme-background-opacity)),
			rgba(var(--theme-background-color), var(--theme-background-opacity))
		),
		var(--theme-background-image);
	background-repeat: no-repeat;
	background-size: cover;
	z-index: -1;
}

// TODO: Move these to an organized partial
.hide-damage-metrics {
	.damage-metrics-tab,
	.damage-metrics {
		display: none !important;
	}
}

.hide-threat-metrics {
	.threat-metrics-tab,
	.threat-metrics {
		display: none !important;
	}
}

.hide-healing-metrics {
	.healing-metrics-tab,
	.healing-metrics {
		display: none !important;
	}
}

.hide-experimental {
	.experimental {
		display: none !important;
	}
}

.hide-in-front-of-target {
	.in-front-of-target {
		display: none !important;
	}
}

.hide-ep-ratios {
	.ep-ratios {
		display: none !important;
	}
}
// END TODO
