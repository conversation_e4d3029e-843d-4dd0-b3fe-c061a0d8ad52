@import '../components/sim_title_dropdown';

.sim-sidebar {
	position: relative;
	flex: 1;
	display: flex;
	flex-direction: column;
	align-items: stretch;
	background: var(--bs-body-bg);
	// This must be larger than the z-index on .sim-content otherwise the dropdown is overlapped
	z-index: var(--sidebar-z-index);

	.sim-sidebar-content {
		min-height: calc(100vh - var(--sim-header-height) - 1px);
		padding: var(--gap-width);
		display: flex;
		flex-direction: column;

		@include media-breakpoint-down(xxl) {
			padding-left: calc(var(--gap-width-sm) * 2);
			padding-right: calc(var(--gap-width-sm) * 2);
		}

		@include media-breakpoint-down(lg) {
			padding: calc(var(--gap-width-sm) * 2) var(--gap-width-sm);
			min-height: 0;
		}

		& > *:not(:last-child) {
			margin-bottom: var(--spacer-4);
		}

		.sim-sidebar-actions {
			padding-left: var(--container-padding);
			padding-right: var(--container-padding);
			margin-left: calc(var(--gap-width) * -1);
			margin-right: calc(var(--gap-width) * -1);
			display: flex;
			flex-direction: column;
			align-items: center;
			gap: var(--block-spacer);

			@include media-breakpoint-down(xxl) {
				padding: 0;
				margin: 0;
			}

			& > * {
				margin-bottom: 0;
			}

			.iterations-picker {
				width: 100%;
				flex-flow: column;

				.number-picker-input {
					width: 100%;
					margin: 0;
				}
			}
		}

		.sim-sidebar-results {
			min-height: 25vh;
			display: flex;
			justify-content: center;
			align-items: center;
		}

		.sim-sidebar-stats {
			margin-top: auto;

			@include media-breakpoint-down(lg) {
				margin-top: 0;
			}
		}

		.sim-sidebar-socials {
			display: flex;
			justify-content: center;

			& > *:not(:last-child) {
				margin-right: var(--spacer-3);
			}
		}
	}

	.warning-zone {
		[data-tippy-root] {
			width: 100%;
		}
	}
}

.sim-sidebar-action-button {
	position: relative;

	&.loading {
		transition: none;
		color: var(--bs-btn-disabled-bg);

		.sim-sidebar-action-button-loading-icon {
			display: block;
		}
	}
}

.sim-sidebar-action-button-loading-icon {
	display: none;
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	color: var(--bs-btn-disabled-color);
}

.toast-notice-local-download {
	order: 100;
	width: 100%;
}
