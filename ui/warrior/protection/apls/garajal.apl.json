{"type": "TypeAPL", "simple": {"cooldowns": {"hpPercentForDefensives": 0.3}}, "prepullActions": [{"action": {"castSpell": {"spellId": {"spellId": 71}}}, "doAtValue": {"const": {"val": "-10s"}}}, {"action": {"castSpell": {"spellId": {"spellId": 114206}}}, "doAtValue": {"const": {"val": "-1.5s"}}}, {"action": {"castSpell": {"spellId": {"otherId": "OtherActionPotion"}}}, "doAtValue": {"const": {"val": "-1.5s"}}}, {"action": {"castSpell": {"spellId": {"spellId": 1249459}}}, "doAtValue": {"const": {"val": "-1.5s"}}}, {"action": {"castSpell": {"spellId": {"spellId": 100}}}, "doAtValue": {"const": {"val": "-0.5s"}}}], "priorityList": [{"action": {"schedule": {"schedule": "259s", "innerAction": {"castSpell": {"spellId": {"spellId": 871}}}}}}, {"action": {"schedule": {"schedule": "38s,270s", "innerAction": {"castSpell": {"spellId": {"spellId": 114203}}}}}}, {"action": {"schedule": {"schedule": "53s,285s", "innerAction": {"castSpell": {"spellId": {"spellId": 31821, "tag": -1}}}}}}, {"action": {"condition": {"auraIsActive": {"sourceUnit": {"type": "<PERSON><PERSON><PERSON><PERSON>"}, "auraId": {"spellId": 117752}}}, "castSpell": {"spellId": {"otherId": "OtherActionPotion"}}}}, {"action": {"condition": {"and": {"vals": [{"auraIsActive": {"auraId": {"spellId": 12880}}}, {"cmp": {"op": "OpLe", "lhs": {"currentHealthPercent": {}}, "rhs": {"const": {"val": "50"}}}}]}}, "castSpell": {"spellId": {"spellId": 55694}}}}, {"action": {"condition": {"and": {"vals": [{"not": {"val": {"auraIsActive": {"auraId": {"spellId": 112048}}}}}, {"cmp": {"op": "OpLe", "lhs": {"currentHealthPercent": {}}, "rhs": {"const": {"val": "50%"}}}}, {"cmp": {"op": "OpGe", "lhs": {"currentRage": {}}, "rhs": {"math": {"op": "OpSub", "lhs": {"maxRage": {}}, "rhs": {"const": {"val": "20"}}}}}}]}}, "castSpell": {"spellId": {"spellId": 112048}}}}, {"action": {"condition": {"and": {"vals": [{"or": {"vals": [{"auraIsActive": {"auraId": {"spellId": 122510}}}, {"auraIsActive": {"auraId": {"spellId": 122016}}}, {"cmp": {"op": "OpGe", "lhs": {"currentRage": {}}, "rhs": {"math": {"op": "OpSub", "lhs": {"maxRage": {}}, "rhs": {"const": {"val": "10"}}}}}}]}}, {"cmp": {"op": "OpGt", "lhs": {"currentHealthPercent": {"sourceUnit": {"type": "<PERSON><PERSON><PERSON><PERSON>"}}}, "rhs": {"const": {"val": "20%"}}}}]}}, "castSpell": {"spellId": {"spellId": 78}}}}, {"action": {"condition": {"or": {"vals": [{"cmp": {"op": "OpLt", "lhs": {"auraNumStacks": {"sourceUnit": {"type": "<PERSON><PERSON><PERSON><PERSON>"}, "auraId": {"spellId": 113746}}}, "rhs": {"const": {"val": "3"}}}}, {"auraShouldRefresh": {"auraId": {"spellId": 113746}, "maxOverlap": {"const": {"val": "2s"}}}}]}}, "castSpell": {"spellId": {"spellId": 20243}}}}, {"action": {"autocastOtherCooldowns": {}}}, {"action": {"castSpell": {"spellId": {"spellId": 12292}}}}, {"action": {"castSpell": {"spellId": {"spellId": 6544}}}}, {"action": {"castSpell": {"spellId": {"spellId": 118000}}}}, {"action": {"condition": {"and": {"vals": [{"spellIsKnown": {"spellId": {"spellId": 46924}}}, {"spellIsReady": {"spellId": {"spellId": 46924}}}, {"sequenceIsReady": {"sequenceName": "Bladestorm"}}]}}, "itemSwap": {"swapSet": "Swap1"}}}, {"action": {"condition": {"and": {"vals": [{"spellIsKnown": {"spellId": {"spellId": 46924}}}, {"sequenceIsReady": {"sequenceName": "Bladestorm"}}, {"not": {"val": {"spellIsChanneling": {"spellId": {"spellId": 46924}}}}}]}}, "sequence": {"name": "Bladestorm", "actions": [{"channelSpell": {"spellId": {"spellId": 46924}, "interruptIf": {}}}]}}}, {"action": {"condition": {"spellIsChanneling": {"spellId": {"spellId": 46924}}}, "castSpell": {"spellId": {"spellId": 6673}}}}, {"action": {"condition": {"and": {"vals": [{"spellIsKnown": {"spellId": {"spellId": 46924}}}, {"cmp": {"op": "OpLe", "lhs": {"auraRemainingTime": {"auraId": {"spellId": 46924}}}, "rhs": {"const": {"val": "1.5s"}}}}, {"sequenceIsComplete": {"sequenceName": "Bladestorm"}}]}}, "strictSequence": {"actions": [{"itemSwap": {"swapSet": "Main"}}, {"resetSequence": {"sequenceName": "Bladestorm"}}]}}}, {"action": {"condition": {"and": {"vals": [{"not": {"val": {"auraIsActive": {"auraId": {"spellId": 112048}}}}}, {"cmp": {"op": "OpLe", "lhs": {"currentHealthPercent": {}}, "rhs": {"const": {"val": "80%"}}}}]}}, "castSpell": {"spellId": {"spellId": 112048}}}}, {"action": {"castSpell": {"spellId": {"spellId": 23922}}}}, {"action": {"castSpell": {"spellId": {"spellId": 6572}}}}, {"action": {"condition": {"or": {"vals": [{"auraShouldRefresh": {"auraId": {"spellId": 115798}, "maxOverlap": {"const": {"val": "2s"}}}}, {"cmp": {"op": "OpGt", "lhs": {"numberTargets": {}}, "rhs": {"const": {"val": "2"}}}}]}}, "castSpell": {"spellId": {"spellId": 6343}}}}, {"action": {"condition": {"not": {"val": {"dotIsActive": {"spellId": {"spellId": 115768}}}}}, "castSpell": {"spellId": {"spellId": 20243}}}}, {"action": {"condition": {"cmp": {"op": "OpLt", "lhs": {"currentHealthPercent": {}}, "rhs": {"const": {"val": "60%"}}}}, "castSpell": {"spellId": {"spellId": 103840}}}}, {"action": {"condition": {"cmp": {"op": "OpLt", "lhs": {"currentHealthPercent": {}}, "rhs": {"const": {"val": "60%"}}}}, "castSpell": {"spellId": {"spellId": 34428}}}}, {"action": {"castSpell": {"spellId": {"spellId": 20243}}}}]}