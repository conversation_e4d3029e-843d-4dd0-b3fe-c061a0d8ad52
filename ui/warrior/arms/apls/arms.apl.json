{"type": "TypeAPL", "prepullActions": [{"action": {"castSpell": {"spellId": {"spellId": 2457}}}, "doAtValue": {"const": {"val": "-5s"}}}, {"action": {"castSpell": {"spellId": {"spellId": 6673}}}, "doAtValue": {"const": {"val": "-3.2s"}}}, {"action": {"castSpell": {"spellId": {"otherId": "OtherActionPotion"}}}, "doAtValue": {"const": {"val": "-1.7s"}}}, {"action": {"castSpell": {"spellId": {"spellId": 1249459}}}, "doAtValue": {"const": {"val": "-1.7s"}}}, {"action": {"castSpell": {"spellId": {"spellId": 1250619}}}, "doAtValue": {"const": {"val": "-.5s"}}}], "priorityList": [{"action": {"autocastOtherCooldowns": {}}}, {"action": {"condition": {"and": {"vals": [{"cmp": {"op": "OpLe", "lhs": {"currentRage": {}}, "rhs": {"math": {"op": "OpSub", "lhs": {"maxRage": {}}, "rhs": {"const": {"val": "35"}}}}}}, {"cmp": {"op": "OpGe", "lhs": {"autoTimeToNext": {}}, "rhs": {"const": {"val": "1.5s"}}}}, {"spellIsReady": {"spellId": {"spellId": 1250619}}}]}}, "move": {"rangeFromTarget": {"const": {"val": "6"}}}}}, {"action": {"condition": {"cmp": {"op": "OpGe", "lhs": {"unitDistance": {"sourceUnit": {"type": "<PERSON><PERSON><PERSON><PERSON>"}}}, "rhs": {"const": {"val": "5"}}}}, "castSpell": {"spellId": {"spellId": 1250619}}}}, {"action": {"condition": {"or": {"vals": [{"and": {"vals": [{"isExecutePhase": {"threshold": "E20"}}, {"or": {"vals": [{"and": {"vals": [{"anyTrinketStatProcsActive": {"statType2": 6, "statType3": -1, "minIcdSeconds": 54}}, {"cmp": {"op": "OpGe", "lhs": {"trinketProcsMinRemainingTime": {"statType2": 6, "statType3": -1, "minIcdSeconds": 54}}, "rhs": {"const": {"val": "10s"}}}}]}}, {"cmp": {"op": "OpEq", "lhs": {"numEquippedStatProcTrinkets": {"statType2": 6, "statType3": -1, "minIcdSeconds": 54}}, "rhs": {"const": {"val": "0"}}}}]}}, {"or": {"vals": [{"cmp": {"op": "OpLe", "lhs": {"numStatBuffCooldowns": {"statType2": -1, "statType3": -1}}, "rhs": {"const": {"val": "1"}}}}, {"and": {"vals": [{"cmp": {"op": "OpGe", "lhs": {"trinketProcsMaxRemainingIcd": {"statType2": 6, "statType3": -1, "minIcdSeconds": 54}}, "rhs": {"math": {"op": "OpSub", "lhs": {"remainingTime": {}}, "rhs": {"const": {"val": "25s"}}}}}}, {"spellIsReady": {"spellId": {"itemId": 81268}}}]}}]}}, {"spellIsReady": {"spellId": {"spellId": 1719}}}, {"spellIsReady": {"spellId": {"spellId": 12292}}}]}}, {"cmp": {"op": "OpLe", "lhs": {"remainingTime": {}}, "rhs": {"math": {"op": "OpAdd", "lhs": {"gcdTimeToReady": {}}, "rhs": {"const": {"val": "25s"}}}}}}]}}, "castSpell": {"spellId": {"otherId": "OtherActionPotion"}}}}, {"action": {"condition": {"or": {"vals": [{"cmp": {"op": "OpGt", "lhs": {"remainingTime": {}}, "rhs": {"const": {"val": "125s"}}}}, {"and": {"vals": [{"isExecutePhase": {"threshold": "E20"}}, {"auraIsActive": {"auraId": {"itemId": 76095}}}]}}, {"cmp": {"op": "OpLe", "lhs": {"remainingTime": {}}, "rhs": {"math": {"op": "OpAdd", "lhs": {"gcdTimeToReady": {}}, "rhs": {"const": {"val": "15s"}}}}}}]}}, "castSpell": {"spellId": {"spellId": 33697}}}}, {"action": {"condition": {"cmp": {"op": "OpLe", "lhs": {"currentTime": {}}, "rhs": {"const": {"val": "1s"}}}}, "castSpell": {"spellId": {"spellId": 1719}}}}, {"action": {"condition": {"and": {"vals": [{"cmp": {"op": "OpGt", "lhs": {"numberTargets": {}}, "rhs": {"const": {"val": "1"}}}}, {"dotIsActive": {"spellId": {"spellId": 115768}}}]}}, "castSpell": {"spellId": {"spellId": 6343}}}}, {"action": {"condition": {"cmp": {"op": "OpGt", "lhs": {"numberTargets": {}}, "rhs": {"const": {"val": "1"}}}}, "castSpell": {"spellId": {"spellId": 1250616}}}}, {"action": {"condition": {"cmp": {"op": "OpGt", "lhs": {"numberTargets": {}}, "rhs": {"const": {"val": "1"}}}}, "strictSequence": {"actions": [{"castSpell": {"spellId": {"spellId": 126734}}}, {"castSpell": {"spellId": {"spellId": 12292}}}, {"castSpell": {"spellId": {"spellId": 46924}}}]}}}, {"action": {"condition": {"const": {"val": "false"}}, "castSpell": {"spellId": {"spellId": 1249459}}}}, {"action": {"condition": {"or": {"vals": [{"and": {"vals": [{"not": {"val": {"auraIsActive": {"auraId": {"spellId": 12880}}}}}, {"cmp": {"op": "OpGe", "lhs": {"currentTime": {}}, "rhs": {"const": {"val": "1s"}}}}, {"or": {"vals": [{"auraIsActive": {"sourceUnit": {"type": "<PERSON><PERSON><PERSON><PERSON>"}, "auraId": {"spellId": 86346}}}, {"spellIsReady": {"spellId": {"spellId": 118000}}}]}}]}}, {"cmp": {"op": "OpLe", "lhs": {"remainingTime": {}}, "rhs": {"math": {"op": "OpAdd", "lhs": {"gcdTimeToReady": {}}, "rhs": {"const": {"val": "6s"}}}}}}]}}, "castSpell": {"spellId": {"spellId": 18499}}}}, {"action": {"condition": {"or": {"vals": [{"and": {"vals": [{"or": {"vals": [{"cmp": {"op": "OpGe", "lhs": {"remainingTime": {}}, "rhs": {"const": {"val": "185s"}}}}, {"auraIsActive": {"auraId": {"spellId": 2825, "tag": -1}}}]}}, {"not": {"val": {"auraIsActive": {"auraId": {"spellId": 114206, "tag": -1}}}}}]}}, {"and": {"vals": [{"isExecutePhase": {"threshold": "E20"}}, {"auraIsActive": {"auraId": {"itemId": 76095}}}, {"not": {"val": {"auraIsActive": {"auraId": {"spellId": 114206, "tag": -1}}}}}]}}, {"cmp": {"op": "OpLe", "lhs": {"remainingTime": {}}, "rhs": {"math": {"op": "OpAdd", "lhs": {"gcdTimeToReady": {}}, "rhs": {"const": {"val": "10s"}}}}}}]}}, "castSpell": {"spellId": {"spellId": 114206}}}}, {"action": {"condition": {"or": {"vals": [{"cmp": {"op": "OpGt", "lhs": {"remainingTime": {}}, "rhs": {"const": {"val": "125s"}}}}, {"and": {"vals": [{"isExecutePhase": {"threshold": "E20"}}, {"auraIsActive": {"auraId": {"itemId": 76095}}}]}}, {"cmp": {"op": "OpLe", "lhs": {"remainingTime": {}}, "rhs": {"math": {"op": "OpAdd", "lhs": {"gcdTimeToReady": {}}, "rhs": {"const": {"val": "20s"}}}}}}]}}, "castAllStatBuffCooldowns": {"statType2": -1, "statType3": -1}}}, {"action": {"condition": {"and": {"vals": [{"cmp": {"op": "OpGe", "lhs": {"numStatBuffCooldowns": {"statType2": -1, "statType3": -1}}, "rhs": {"const": {"val": "2"}}}}]}}, "castSpell": {"spellId": {"spellId": 126734}}}}, {"action": {"condition": {"and": {"vals": [{"or": {"vals": [{"cmp": {"op": "OpGe", "lhs": {"remainingTime": {}}, "rhs": {"const": {"val": "72s"}}}}, {"and": {"vals": [{"isExecutePhase": {"threshold": "E20"}}, {"or": {"vals": [{"auraIsActive": {"auraId": {"itemId": 76095}}}, {"cmp": {"op": "OpLe", "lhs": {"remainingTime": {}}, "rhs": {"const": {"val": "15s"}}}}]}}]}}]}}, {"cmp": {"op": "OpLe", "lhs": {"numStatBuffCooldowns": {"statType2": -1, "statType3": -1}}, "rhs": {"const": {"val": "1"}}}}]}}, "strictSequence": {"actions": [{"castSpell": {"spellId": {"spellId": 126734}}}, {"castSpell": {"spellId": {"spellId": 12292}}}]}}}, {"action": {"condition": {"and": {"vals": [{"or": {"vals": [{"cmp": {"op": "OpGe", "lhs": {"remainingTime": {}}, "rhs": {"const": {"val": "72s"}}}}, {"and": {"vals": [{"isExecutePhase": {"threshold": "E20"}}, {"or": {"vals": [{"auraIsActive": {"auraId": {"itemId": 76095}}}, {"cmp": {"op": "OpLe", "lhs": {"remainingTime": {}}, "rhs": {"const": {"val": "15s"}}}}]}}]}}]}}, {"cmp": {"op": "OpGe", "lhs": {"numStatBuffCooldowns": {"statType2": -1, "statType3": -1}}, "rhs": {"const": {"val": "2"}}}}]}}, "castSpell": {"spellId": {"spellId": 12292}}}}, {"action": {"condition": {"and": {"vals": [{"or": {"vals": [{"and": {"vals": [{"cmp": {"op": "OpGe", "lhs": {"remainingTime": {}}, "rhs": {"const": {"val": "60s"}}}}, {"cmp": {"op": "OpGe", "lhs": {"currentTime": {}}, "rhs": {"const": {"val": "1.5s"}}}}]}}, {"and": {"vals": [{"isExecutePhase": {"threshold": "E20"}}, {"or": {"vals": [{"auraIsActive": {"auraId": {"itemId": 76095}}}, {"cmp": {"op": "OpLe", "lhs": {"remainingTime": {}}, "rhs": {"const": {"val": "15s"}}}}]}}]}}]}}, {"or": {"vals": [{"auraIsInactiveWithReactionTime": {"sourceUnit": {"type": "<PERSON><PERSON><PERSON><PERSON>"}, "auraId": {"spellId": 86346}}}, {"and": {"vals": [{"auraIsActive": {"auraId": {"spellId": 12292}}}, {"cmp": {"op": "OpLe", "lhs": {"auraRemainingTime": {"auraId": {"spellId": 12292}}}, "rhs": {"const": {"val": "1.5s"}}}}, {"not": {"val": {"isExecutePhase": {"threshold": "E20"}}}}]}}]}}]}}, "castSpell": {"spellId": {"spellId": 118000}}}}, {"action": {"condition": {"or": {"vals": [{"or": {"vals": [{"and": {"vals": [{"cmp": {"op": "OpGe", "lhs": {"remainingTime": {}}, "rhs": {"const": {"val": "95s"}}}}, {"auraIsKnown": {"auraId": {"spellId": 123144}}}]}}, {"cmp": {"op": "OpGe", "lhs": {"remainingTime": {}}, "rhs": {"const": {"val": "185s"}}}}]}}, {"and": {"vals": [{"isExecutePhase": {"threshold": "E20"}}, {"auraIsActive": {"auraId": {"itemId": 76095}}}]}}, {"cmp": {"op": "OpLe", "lhs": {"remainingTime": {}}, "rhs": {"math": {"op": "OpAdd", "lhs": {"gcdTimeToReady": {}}, "rhs": {"const": {"val": "12s"}}}}}}]}}, "castSpell": {"spellId": {"spellId": 1719}}}}, {"action": {"condition": {"and": {"vals": [{"not": {"val": {"auraIsActive": {"sourceUnit": {"type": "<PERSON><PERSON><PERSON><PERSON>"}, "auraId": {"spellId": 86346}}}}}, {"isExecutePhase": {"threshold": "E20"}}, {"cmp": {"op": "OpLe", "lhs": {"remainingTime": {}}, "rhs": {"const": {"val": "26.5s"}}}}]}}, "castSpell": {"spellId": {"spellId": 86346}}}}, {"action": {"condition": {"or": {"vals": [{"and": {"vals": [{"auraIsActive": {"sourceUnit": {"type": "<PERSON><PERSON><PERSON><PERSON>"}, "auraId": {"spellId": 86346}}}, {"not": {"val": {"auraIsKnown": {"auraId": {"spellId": 123142}}}}}]}}, {"cmp": {"op": "OpLe", "lhs": {"remainingTime": {}}, "rhs": {"const": {"val": "3s"}}}}]}}, "castSpell": {"spellId": {"spellId": 5308}}}}, {"action": {"castSpell": {"spellId": {"spellId": 12294}}}}, {"action": {"condition": {"auraIsActive": {"sourceUnit": {"type": "<PERSON><PERSON><PERSON><PERSON>"}, "auraId": {"spellId": 86346}}}, "castSpell": {"spellId": {"spellId": 6544}}}}, {"action": {"condition": {"and": {"vals": [{"auraIsActive": {"sourceUnit": {"type": "<PERSON><PERSON><PERSON><PERSON>"}, "auraId": {"spellId": 86346}}}, {"not": {"val": {"isExecutePhase": {"threshold": "E20"}}}}]}}, "castSpell": {"spellId": {"spellId": 1464}}}}, {"action": {"condition": {"auraIsActive": {"sourceUnit": {"type": "<PERSON><PERSON><PERSON><PERSON>"}, "auraId": {"spellId": 86346}}}, "castSpell": {"spellId": {"spellId": 107570, "tag": 1}}}}, {"action": {"condition": {"auraIsActive": {"sourceUnit": {"type": "<PERSON><PERSON><PERSON><PERSON>"}, "auraId": {"spellId": 86346}}}, "castSpell": {"spellId": {"spellId": 5308}}}}, {"action": {"condition": {"not": {"val": {"auraIsActive": {"sourceUnit": {"type": "<PERSON><PERSON><PERSON><PERSON>"}, "auraId": {"spellId": 86346}}}}}, "castSpell": {"spellId": {"spellId": 86346}}}}, {"action": {"condition": {"or": {"vals": [{"and": {"vals": [{"not": {"val": {"auraIsActive": {"sourceUnit": {"type": "<PERSON><PERSON><PERSON><PERSON>"}, "auraId": {"spellId": 86346}}}}}, {"or": {"vals": [{"cmp": {"op": "OpGe", "lhs": {"currentRage": {}}, "rhs": {"const": {"val": "65"}}}}, {"cmp": {"op": "OpLe", "lhs": {"remainingTime": {}}, "rhs": {"const": {"val": "6s"}}}}]}}]}}, {"not": {"val": {"auraIsActive": {"auraId": {"spellId": 139958}}}}}]}}, "castSpell": {"spellId": {"spellId": 5308}}}}, {"action": {"condition": {"or": {"vals": [{"and": {"vals": [{"cmp": {"op": "OpGe", "lhs": {"currentRage": {}}, "rhs": {"const": {"val": "110"}}}}, {"auraIsInactiveWithReactionTime": {"sourceUnit": {"type": "<PERSON><PERSON><PERSON><PERSON>"}, "auraId": {"spellId": 86346}}}, {"not": {"val": {"isExecutePhase": {"threshold": "E20"}}}}]}}]}}, "castSpell": {"spellId": {"spellId": 1464}}}}, {"action": {"condition": {"and": {"vals": [{"cmp": {"op": "OpEq", "lhs": {"numberTargets": {}}, "rhs": {"const": {"val": "1"}}}}, {"not": {"val": {"and": {"vals": [{"auraIsActive": {"sourceUnit": {"type": "<PERSON><PERSON><PERSON><PERSON>"}, "auraId": {"spellId": 86346}}}, {"isExecutePhase": {"threshold": "E20"}}, {"cmp": {"op": "OpGe", "lhs": {"currentRage": {}}, "rhs": {"const": {"val": "30"}}}}]}}}}, {"not": {"val": {"spellIsReady": {"spellId": {"spellId": 1250619}}}}}]}}, "channelSpell": {"spellId": {"spellId": 46924}, "interruptIf": {"spellIsReady": {"spellId": {"spellId": 12294}}}}}}, {"action": {"condition": {"auraIsActive": {"auraId": {"spellId": 60503}}}, "castSpell": {"spellId": {"spellId": 7384}}}}, {"action": {"condition": {"and": {"vals": [{"not": {"val": {"spellCanCast": {"spellId": {"spellId": 1250619}}}}}, {"not": {"val": {"unitIsMoving": {}}}}]}}, "channelSpell": {"spellId": {"spellId": 46924}, "interruptIf": {"cmp": {"op": "OpEq", "lhs": {"spellChanneledTicks": {"spellId": {"spellId": 46924}}}, "rhs": {"const": {"val": "4"}}}}}}}, {"action": {"condition": {"and": {"vals": [{"auraIsActive": {"auraId": {"spellId": 46924}}}, {"spellIsKnown": {"spellId": {"spellId": 46924}}}]}}, "castSpell": {"spellId": {"spellId": 6673}}}}, {"action": {"condition": {"cmp": {"op": "OpLt", "lhs": {"currentRage": {}}, "rhs": {"const": {"val": "10"}}}}, "castSpell": {"spellId": {"spellId": 6673}}}}, {"action": {"condition": {"or": {"vals": [{"cmp": {"op": "OpGe", "lhs": {"currentRage": {}}, "rhs": {"const": {"val": "100"}}}}, {"and": {"vals": [{"cmp": {"op": "OpLt", "lhs": {"spellTimeToReady": {"spellId": {"spellId": 1250619}}}, "rhs": {"const": {"val": "1.5s"}}}}, {"cmp": {"op": "OpGt", "lhs": {"currentRage": {}}, "rhs": {"math": {"op": "OpSub", "lhs": {"maxRage": {}}, "rhs": {"const": {"val": "35"}}}}}}]}}]}}, "castSpell": {"spellId": {"spellId": 78}}}}]}