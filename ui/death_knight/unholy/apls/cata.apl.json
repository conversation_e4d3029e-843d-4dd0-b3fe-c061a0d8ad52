{"type": "TypeAPL", "prepullActions": [{"action": {"castSpell": {"spellId": {"spellId": 48265}}}, "doAtValue": {"const": {"val": "-20s"}}}, {"action": {"castSpell": {"spellId": {"spellId": 57330}}}, "doAtValue": {"const": {"val": "-12s"}}}, {"action": {"castSpell": {"spellId": {"spellId": 47541, "tag": 2}}}, "doAtValue": {"const": {"val": "-11s"}}}, {"action": {"castSpell": {"spellId": {"spellId": 47541, "tag": 2}}}, "doAtValue": {"const": {"val": "-10s"}}}, {"action": {"castSpell": {"spellId": {"spellId": 47541, "tag": 2}}}, "doAtValue": {"const": {"val": "-9s"}}}, {"action": {"castSpell": {"spellId": {"spellId": 47541, "tag": 2}}}, "doAtValue": {"const": {"val": "-8s"}}}, {"action": {"castSpell": {"spellId": {"spellId": 42650}}}, "doAtValue": {"const": {"val": "-7s"}}}, {"action": {"castSpell": {"spellId": {"spellId": 47541, "tag": 2}}}, "doAtValue": {"const": {"val": "-1s"}}}, {"action": {"castSpell": {"spellId": {"otherId": "OtherActionPotion"}}}, "doAtValue": {"const": {"val": "-1s"}}}], "priorityList": [{"action": {"condition": {"or": {"vals": [{"and": {"vals": [{"auraIsActiveWithReactionTime": {"auraId": {"spellId": 53365}}}, {"allTrinketStatProcsActive": {"statType2": 6, "statType3": -1}}]}}, {"and": {"vals": [{"cmp": {"op": "OpEq", "lhs": {"auraNumStacks": {"sourceUnit": {"type": "Pet", "index": 1, "owner": {"type": "Self"}}, "auraId": {"spellId": 91342}}}, "rhs": {"const": {"val": "5"}}}}, {"cmp": {"op": "OpLt", "lhs": {"currentTime": {}}, "rhs": {"const": {"val": "60s"}}}}, {"cmp": {"op": "OpGt", "lhs": {"currentTime": {}}, "rhs": {"const": {"val": "5s"}}}}, {"or": {"vals": [{"cmp": {"op": "OpGe", "lhs": {"currentRuneCount": {"runeType": "RuneUnholy"}}, "rhs": {"const": {"val": "1"}}}}, {"cmp": {"op": "OpGe", "lhs": {"currentRuneCount": {"runeType": "RuneDeath"}}, "rhs": {"const": {"val": "1"}}}}]}}]}}, {"and": {"vals": [{"cmp": {"op": "OpGt", "lhs": {"currentTime": {}}, "rhs": {"const": {"val": "60s"}}}}, {"anyTrinketStatProcsActive": {"statType2": 6, "statType3": -1, "minIcdSeconds": 60}}, {"cmp": {"op": "OpGt", "lhs": {"trinketProcsMinRemainingTime": {"statType2": 6, "statType3": -1, "minIcdSeconds": 60}}, "rhs": {"const": {"val": "10s"}}}}]}}, {"cmp": {"op": "OpLe", "lhs": {"remainingTime": {}}, "rhs": {"const": {"val": "35s"}}}}]}}, "castSpell": {"spellId": {"spellId": 49016}}}}, {"action": {"condition": {"and": {"vals": [{"cmp": {"op": "OpGe", "lhs": {"remainingTime": {}}, "rhs": {"const": {"val": "15s"}}}}, {"cmp": {"op": "OpGt", "lhs": {"currentTime": {}}, "rhs": {"const": {"val": "5s"}}}}]}}, "castSpell": {"spellId": {"spellId": 63560}}}}, {"action": {"condition": {"cmp": {"op": "OpGe", "lhs": {"currentTime": {}}, "rhs": {"const": {"val": "5s"}}}}, "castSpell": {"spellId": {"spellId": 2825, "tag": -1}}}}, {"action": {"condition": {"or": {"vals": [{"and": {"vals": [{"auraIsActiveWithReactionTime": {"auraId": {"spellId": 53365}}}, {"allTrinketStatProcsActive": {"statType2": 6, "statType3": -1}}, {"cmp": {"op": "OpGe", "lhs": {"currentTime": {}}, "rhs": {"const": {"val": "5s"}}}}]}}, {"cmp": {"op": "OpGe", "lhs": {"spellTimeToReady": {"spellId": {"spellId": 49206}}}, "rhs": {"const": {"val": "30s"}}}}, {"cmp": {"op": "OpLe", "lhs": {"remainingTime": {}}, "rhs": {"const": {"val": "35s"}}}}, {"and": {"vals": [{"auraIsActive": {"auraId": {"itemId": 58146}}}, {"cmp": {"op": "OpLt", "lhs": {"auraRemainingTime": {"auraId": {"itemId": 58146}}}, "rhs": {"const": {"val": "15s"}}}}]}}, {"and": {"vals": [{"spellIsReady": {"spellId": {"spellId": 49206}}}, {"cmp": {"op": "OpLe", "lhs": {"trinketProcsMinRemainingTime": {"statType2": 6, "statType3": -1, "minIcdSeconds": 60}}, "rhs": {"const": {"val": "15s"}}}}]}}]}}, "castSpell": {"spellId": {"itemId": 69002}}}}, {"action": {"condition": {"or": {"vals": [{"and": {"vals": [{"allTrinketStatProcsActive": {"statType2": 6, "statType3": -1}}, {"or": {"vals": [{"auraIsActiveWithReactionTime": {"auraId": {"spellId": 53365}}}, {"cmp": {"op": "OpLe", "lhs": {"trinketProcsMinRemainingTime": {"statType2": 6, "statType3": -1, "minIcdSeconds": 60}}, "rhs": {"const": {"val": "20s"}}}}]}}, {"spellIsReady": {"spellId": {"spellId": 77575}}}, {"cmp": {"op": "OpGe", "lhs": {"currentTime": {}}, "rhs": {"const": {"val": "5s"}}}}]}}, {"and": {"vals": [{"cmp": {"op": "OpLe", "lhs": {"trinketProcsMinRemainingTime": {"statType2": 6, "statType3": -1, "minIcdSeconds": 60}}, "rhs": {"const": {"val": "20s"}}}}, {"spellIsReady": {"spellId": {"spellId": 49206}}}, {"cmp": {"op": "OpGe", "lhs": {"currentTime": {}}, "rhs": {"const": {"val": "5s"}}}}]}}, {"cmp": {"op": "OpLe", "lhs": {"remainingTime": {}}, "rhs": {"const": {"val": "35s"}}}}, {"and": {"vals": [{"auraIsActive": {"auraId": {"itemId": 58146}}}, {"cmp": {"op": "OpLe", "lhs": {"auraRemainingTime": {"auraId": {"itemId": 58146}}}, "rhs": {"const": {"val": "20s"}}}}]}}, {"and": {"vals": [{"cmp": {"op": "OpGe", "lhs": {"currentTime": {}}, "rhs": {"const": {"val": "5s"}}}}, {"cmp": {"op": "OpEq", "lhs": {"numEquippedStatProcTrinkets": {"statType2": 6, "statType3": -1, "minIcdSeconds": 110}}, "rhs": {"const": {"val": "0"}}}}]}}]}}, "castSpell": {"spellId": {"itemId": 62464}}}}, {"hide": true, "action": {"condition": {"or": {"vals": [{"and": {"vals": [{"allTrinketStatProcsActive": {"statType2": 6, "statType3": -1}}, {"spellIsReady": {"spellId": {"spellId": 77575}}}, {"cmp": {"op": "OpGe", "lhs": {"currentTime": {}}, "rhs": {"const": {"val": "5s"}}}}]}}, {"cmp": {"op": "OpLe", "lhs": {"remainingTime": {}}, "rhs": {"const": {"val": "35s"}}}}, {"and": {"vals": [{"auraIsActive": {"auraId": {"itemId": 58146}}}, {"cmp": {"op": "OpLe", "lhs": {"auraRemainingTime": {"auraId": {"itemId": 58146}}}, "rhs": {"const": {"val": "20s"}}}}]}}, {"and": {"vals": [{"cmp": {"op": "OpGe", "lhs": {"currentTime": {}}, "rhs": {"const": {"val": "5s"}}}}, {"cmp": {"op": "OpEq", "lhs": {"numEquippedStatProcTrinkets": {"statType2": 6, "statType3": -1, "minIcdSeconds": 110}}, "rhs": {"const": {"val": "0"}}}}]}}]}}, "castSpell": {"spellId": {"itemId": 62464}}}}, {"action": {"condition": {"or": {"vals": [{"and": {"vals": [{"allTrinketStatProcsActive": {"statType2": 6, "statType3": -1}}, {"or": {"vals": [{"auraIsActiveWithReactionTime": {"auraId": {"spellId": 53365}}}, {"cmp": {"op": "OpLe", "lhs": {"trinketProcsMinRemainingTime": {"statType2": 6, "statType3": -1, "minIcdSeconds": 60}}, "rhs": {"const": {"val": "15s"}}}}]}}, {"cmp": {"op": "OpGe", "lhs": {"currentTime": {}}, "rhs": {"const": {"val": "5s"}}}}]}}, {"and": {"vals": [{"cmp": {"op": "OpLe", "lhs": {"trinketProcsMinRemainingTime": {"statType2": 6, "statType3": -1, "minIcdSeconds": 60}}, "rhs": {"const": {"val": "15s"}}}}, {"spellIsReady": {"spellId": {"spellId": 49206}}}]}}, {"cmp": {"op": "OpLe", "lhs": {"remainingTime": {}}, "rhs": {"const": {"val": "35s"}}}}, {"and": {"vals": [{"auraIsActive": {"auraId": {"itemId": 58146}}}, {"cmp": {"op": "OpLe", "lhs": {"auraRemainingTime": {"auraId": {"itemId": 58146}}}, "rhs": {"const": {"val": "15s"}}}}]}}]}}, "castSpell": {"spellId": {"itemId": 77116}}}}, {"action": {"condition": {"and": {"vals": [{"and": {"vals": [{"auraIsActiveWithReactionTime": {"auraId": {"spellId": 53365}}}, {"anyTrinketStatProcsActive": {"statType2": 6, "statType3": -1, "minIcdSeconds": 60}}, {"cmp": {"op": "OpGt", "lhs": {"trinketProcsMaxRemainingIcd": {"statType2": 6, "statType3": -1, "minIcdSeconds": 60}}, "rhs": {"math": {"op": "OpSub", "lhs": {"trinketProcsMinRemainingTime": {"statType2": 6, "statType3": -1, "minIcdSeconds": 60}}, "rhs": {"const": {"val": "5s"}}}}}}]}}, {"spellIsReady": {"spellId": {"spellId": 49206}}}, {"cmp": {"op": "OpGt", "lhs": {"currentTime": {}}, "rhs": {"const": {"val": "60s"}}}}, {"cmp": {"op": "OpEq", "lhs": {"numEquippedStatProcTrinkets": {"statType2": 6, "statType3": -1, "minIcdSeconds": 60}}, "rhs": {"const": {"val": "2"}}}}]}}, "castSpell": {"spellId": {"spellId": 82174}}}}, {"action": {"condition": {"and": {"vals": [{"or": {"vals": [{"and": {"vals": [{"auraIsActiveWithReactionTime": {"auraId": {"spellId": 53365}}}, {"allTrinketStatProcsActive": {"statType2": 6, "statType3": -1}}, {"cmp": {"op": "OpGe", "lhs": {"currentTime": {}}, "rhs": {"const": {"val": "5s"}}}}]}}, {"and": {"vals": [{"cmp": {"op": "OpGe", "lhs": {"spellTimeToReady": {"spellId": {"spellId": 49206}}}, "rhs": {"const": {"val": "30s"}}}}]}}, {"cmp": {"op": "OpLe", "lhs": {"remainingTime": {}}, "rhs": {"const": {"val": "35s"}}}}, {"and": {"vals": [{"auraIsActive": {"auraId": {"itemId": 58146}}}, {"cmp": {"op": "OpLt", "lhs": {"auraRemainingTime": {"auraId": {"itemId": 58146}}}, "rhs": {"const": {"val": "10.5s"}}}}]}}, {"and": {"vals": [{"spellIsReady": {"spellId": {"spellId": 49206}}}, {"cmp": {"op": "OpLt", "lhs": {"trinketProcsMinRemainingTime": {"statType2": 6, "statType3": -1, "minIcdSeconds": 60}}, "rhs": {"const": {"val": "10.5s"}}}}]}}]}}, {"not": {"val": {"or": {"vals": [{"auraIsKnown": {"auraId": {"itemId": 69002}}}, {"auraIsKnown": {"auraId": {"itemId": 62464}}}, {"auraIsKnown": {"auraId": {"itemId": 77116}}}]}}}}]}}, "castSpell": {"spellId": {"spellId": 82174}}}}, {"action": {"condition": {"or": {"vals": [{"and": {"vals": [{"or": {"vals": [{"auraIsKnown": {"auraId": {"itemId": 69002}}}, {"auraIsKnown": {"auraId": {"itemId": 62464}}}, {"auraIsKnown": {"auraId": {"itemId": 77116}}}]}}, {"or": {"vals": [{"cmp": {"op": "OpGt", "lhs": {"spellTimeToReady": {"spellId": {"itemId": 69002}}}, "rhs": {"const": {"val": "10s"}}}}, {"cmp": {"op": "OpGt", "lhs": {"spellTimeToReady": {"spellId": {"itemId": 62464}}}, "rhs": {"const": {"val": "10s"}}}}, {"cmp": {"op": "OpGt", "lhs": {"spellTimeToReady": {"spellId": {"itemId": 77116}}}, "rhs": {"const": {"val": "10s"}}}}, {"and": {"vals": [{"cmp": {"op": "OpGt", "lhs": {"trinketProcsMaxRemainingIcd": {"statType2": 6, "statType3": -1, "minIcdSeconds": 60}}, "rhs": {"const": {"val": "10s"}}}}, {"cmp": {"op": "OpGt", "lhs": {"remainingTime": {}}, "rhs": {"const": {"val": "45s"}}}}]}}]}}]}}, {"cmp": {"op": "OpLe", "lhs": {"remainingTime": {}}, "rhs": {"const": {"val": "35s"}}}}]}}, "castSpell": {"spellId": {"spellId": 82174}}}}, {"action": {"condition": {"or": {"vals": [{"and": {"vals": [{"auraIsActiveWithReactionTime": {"auraId": {"spellId": 53365}}}, {"allTrinketStatProcsActive": {"statType2": 6, "statType3": -1, "minIcdSeconds": 60}}]}}, {"and": {"vals": [{"auraIsActive": {"auraId": {"itemId": 58146}}}, {"or": {"vals": [{"cmp": {"op": "OpGe", "lhs": {"auraRemainingTime": {"sourceUnit": {"type": "Pet", "index": 1, "owner": {"type": "Self"}}, "auraId": {"spellId": 63560}}}, "rhs": {"const": {"val": "7.5s"}}}}, {"cmp": {"op": "OpLt", "lhs": {"auraRemainingTime": {"auraId": {"itemId": 58146}}}, "rhs": {"const": {"val": "10.5s"}}}}]}}]}}, {"and": {"vals": [{"cmp": {"op": "OpLt", "lhs": {"remainingTime": {}}, "rhs": {"const": {"val": "10.5s"}}}}]}}]}}, "castSpell": {"spellId": {"spellId": 26297}}}}, {"action": {"condition": {"and": {"vals": [{"and": {"vals": [{"auraIsActiveWithReactionTime": {"auraId": {"spellId": 53365}}}, {"anyTrinketStatProcsActive": {"statType2": 6, "statType3": -1, "minIcdSeconds": 60}}, {"cmp": {"op": "OpGt", "lhs": {"trinketProcsMaxRemainingIcd": {"statType2": 6, "statType3": -1, "minIcdSeconds": 60}}, "rhs": {"math": {"op": "OpSub", "lhs": {"trinketProcsMinRemainingTime": {"statType2": 6, "statType3": -1, "minIcdSeconds": 60}}, "rhs": {"const": {"val": "5s"}}}}}}]}}, {"spellIsReady": {"spellId": {"spellId": 49206}}}, {"cmp": {"op": "OpGt", "lhs": {"currentTime": {}}, "rhs": {"const": {"val": "60s"}}}}, {"cmp": {"op": "OpEq", "lhs": {"numEquippedStatProcTrinkets": {"statType2": 6, "statType3": -1, "minIcdSeconds": 60}}, "rhs": {"const": {"val": "2"}}}}]}}, "castSpell": {"spellId": {"spellId": 33697}}}}, {"action": {"condition": {"or": {"vals": [{"and": {"vals": [{"allTrinketStatProcsActive": {"statType2": 6, "statType3": -1}}, {"or": {"vals": [{"auraIsActiveWithReactionTime": {"auraId": {"spellId": 53365}}}, {"cmp": {"op": "OpLe", "lhs": {"trinketProcsMinRemainingTime": {"statType2": 6, "statType3": -1, "minIcdSeconds": 60}}, "rhs": {"const": {"val": "15s"}}}}]}}, {"spellIsReady": {"spellId": {"spellId": 77575}}}, {"cmp": {"op": "OpGe", "lhs": {"currentTime": {}}, "rhs": {"const": {"val": "5s"}}}}]}}, {"and": {"vals": [{"cmp": {"op": "OpLe", "lhs": {"trinketProcsMinRemainingTime": {"statType2": 6, "statType3": -1, "minIcdSeconds": 60}}, "rhs": {"const": {"val": "15s"}}}}, {"spellIsReady": {"spellId": {"spellId": 49206}}}]}}, {"cmp": {"op": "OpLe", "lhs": {"remainingTime": {}}, "rhs": {"const": {"val": "35s"}}}}, {"and": {"vals": [{"auraIsActive": {"auraId": {"itemId": 58146}}}, {"cmp": {"op": "OpLe", "lhs": {"auraRemainingTime": {"auraId": {"itemId": 58146}}}, "rhs": {"const": {"val": "15s"}}}}]}}]}}, "castSpell": {"spellId": {"spellId": 33697}}}}, {"action": {"condition": {"or": {"vals": [{"and": {"vals": [{"cmp": {"op": "OpLe", "lhs": {"remainingTime": {}}, "rhs": {"const": {"val": "35s"}}}}, {"or": {"vals": [{"cmp": {"op": "OpGt", "lhs": {"spellTimeToReady": {"spellId": {"spellId": 49016}}}, "rhs": {"math": {"op": "OpSub", "lhs": {"remainingTime": {}}, "rhs": {"const": {"val": "23s"}}}}}}, {"spellIsReady": {"spellId": {"spellId": 49206}}}]}}]}}, {"and": {"vals": [{"auraIsActive": {"auraId": {"spellId": 49016}}}, {"or": {"vals": [{"spellIsReady": {"spellId": {"spellId": 49206}}}, {"cmp": {"op": "OpLt", "lhs": {"auraRemainingTime": {"auraId": {"spellId": 49016}}}, "rhs": {"const": {"val": "26s"}}}}, {"cmp": {"op": "OpGe", "lhs": {"auraRemainingTime": {"sourceUnit": {"type": "Pet", "index": 1, "owner": {"type": "Self"}}, "auraId": {"spellId": 63560}}}, "rhs": {"const": {"val": "24s"}}}}]}}]}}, {"and": {"vals": [{"cmp": {"op": "OpGt", "lhs": {"spellTimeToReady": {"spellId": {"spellId": 49206}}}, "rhs": {"math": {"op": "OpSub", "lhs": {"remainingTime": {}}, "rhs": {"const": {"val": "30s"}}}}}}, {"anyTrinketStatProcsActive": {"statType2": 6, "statType3": -1, "minIcdSeconds": 60}}]}}]}}, "castSpell": {"spellId": {"itemId": 58146}}}}, {"action": {"condition": {"and": {"vals": [{"and": {"vals": [{"anyTrinketStatProcsActive": {"statType2": 6, "statType3": -1, "minIcdSeconds": 60}}, {"auraIsActiveWithReactionTime": {"auraId": {"spellId": 53365}}}, {"cmp": {"op": "OpGt", "lhs": {"trinketProcsMaxRemainingIcd": {"statType2": 6, "statType3": -1, "minIcdSeconds": 60}}, "rhs": {"math": {"op": "OpSub", "lhs": {"trinketProcsMinRemainingTime": {"statType2": 6, "statType3": -1, "minIcdSeconds": 60}}, "rhs": {"const": {"val": "5s"}}}}}}]}}, {"cmp": {"op": "OpGt", "lhs": {"currentTime": {}}, "rhs": {"const": {"val": "60s"}}}}, {"cmp": {"op": "OpEq", "lhs": {"numEquippedStatProcTrinkets": {"statType2": 6, "statType3": -1, "minIcdSeconds": 60}}, "rhs": {"const": {"val": "2"}}}}]}}, "castSpell": {"spellId": {"spellId": 49206}}}}, {"action": {"condition": {"or": {"vals": [{"and": {"vals": [{"allTrinketStatProcsActive": {"statType2": 6, "statType3": -1}}, {"auraIsActiveWithReactionTime": {"auraId": {"spellId": 53365}}}, {"or": {"vals": [{"cmp": {"op": "OpGt", "lhs": {"currentTime": {}}, "rhs": {"const": {"val": "60s"}}}}, {"auraIsActive": {"sourceUnit": {"type": "Pet", "index": 1, "owner": {"type": "Self"}}, "auraId": {"spellId": 63560}}}]}}, {"cmp": {"op": "OpGt", "lhs": {"remainingTime": {}}, "rhs": {"const": {"val": "6s"}}}}, {"or": {"vals": [{"not": {"val": {"auraIsKnown": {"auraId": {"itemId": 62464}}}}}, {"auraIsActive": {"auraId": {"itemId": 62464}}}, {"cmp": {"op": "OpLt", "lhs": {"trinketProcsMinRemainingTime": {"statType2": 6, "statType3": -1, "minIcdSeconds": 60}}, "rhs": {"math": {"op": "OpSub", "lhs": {"spellTimeToReady": {"spellId": {"itemId": 62464}}}, "rhs": {"const": {"val": "4"}}}}}}]}}]}}, {"and": {"vals": [{"cmp": {"op": "OpLt", "lhs": {"trinketProcsMinRemainingTime": {"statType2": 6, "statType3": -1, "minIcdSeconds": 60}}, "rhs": {"const": {"val": "4s"}}}}, {"auraIsActiveWithReactionTime": {"auraId": {"spellId": 53365}}}, {"cmp": {"op": "OpGt", "lhs": {"remainingTime": {}}, "rhs": {"const": {"val": "7s"}}}}]}}, {"and": {"vals": [{"allTrinketStatProcsActive": {"statType2": 6, "statType3": -1, "minIcdSeconds": 60}}, {"cmp": {"op": "OpLt", "lhs": {"trinketProcsMinRemainingTime": {"statType2": 6, "statType3": -1, "minIcdSeconds": 60}}, "rhs": {"const": {"val": "4s"}}}}, {"cmp": {"op": "OpGt", "lhs": {"remainingTime": {}}, "rhs": {"const": {"val": "7s"}}}}]}}, {"and": {"vals": [{"cmp": {"op": "OpLt", "lhs": {"remainingTime": {}}, "rhs": {"const": {"val": "35s"}}}}, {"cmp": {"op": "OpGt", "lhs": {"remainingTime": {}}, "rhs": {"const": {"val": "7s"}}}}]}}, {"and": {"vals": [{"cmp": {"op": "OpLe", "lhs": {"auraRemainingTime": {"auraId": {"itemId": 58146}}}, "rhs": {"const": {"val": "4s"}}}}, {"auraIsActive": {"auraId": {"itemId": 58146}}}, {"cmp": {"op": "OpGt", "lhs": {"remainingTime": {}}, "rhs": {"const": {"val": "7s"}}}}]}}]}}, "castSpell": {"spellId": {"spellId": 49206}}}}, {"action": {"condition": {"and": {"vals": [{"cmp": {"op": "OpLt", "lhs": {"remainingTime": {}}, "rhs": {"const": {"val": "45s"}}}}, {"or": {"vals": [{"not": {"val": {"dotIsActive": {"spellId": {"spellId": 55095}}}}}, {"not": {"val": {"dotIsActive": {"spellId": {"spellId": 55078}}}}}]}}]}}, "castSpell": {"spellId": {"spellId": 77575}}}}, {"action": {"condition": {"and": {"vals": [{"cmp": {"op": "OpGt", "lhs": {"spellTimeToReady": {"spellId": {"spellId": 49206}}}, "rhs": {"const": {"val": "177s"}}}}, {"cmp": {"op": "OpGe", "lhs": {"numEquippedStatProcTrinkets": {"statType2": 6, "statType3": -1, "minIcdSeconds": 110}}, "rhs": {"const": {"val": "1"}}}}]}}, "castSpell": {"spellId": {"spellId": 77575}}}}, {"action": {"condition": {"and": {"vals": [{"auraIsActiveWithReactionTime": {"auraId": {"spellId": 53365}}}, {"allTrinketStatProcsActive": {"statType2": -1, "statType3": -1}}, {"cmp": {"op": "OpGt", "lhs": {"currentTime": {}}, "rhs": {"const": {"val": "8s"}}}}, {"or": {"vals": [{"cmp": {"op": "OpLt", "lhs": {"trinketProcsMinRemainingTime": {"statType2": 6, "statType3": -1, "minIcdSeconds": 20}}, "rhs": {"math": {"op": "OpSub", "lhs": {"spellTimeToReady": {"spellId": {"spellId": 82174}}}, "rhs": {"const": {"val": "2s"}}}}}}, {"auraIsActive": {"auraId": {"spellId": 96229}}}]}}, {"or": {"vals": [{"cmp": {"op": "OpLt", "lhs": {"trinketProcsMinRemainingTime": {"statType2": 6, "statType3": -1, "minIcdSeconds": 20}}, "rhs": {"math": {"op": "OpSub", "lhs": {"trinketProcsMaxRemainingIcd": {"statType2": 6, "statType3": -1, "minIcdSeconds": 110}}, "rhs": {"const": {"val": "2s"}}}}}}, {"allTrinketStatProcsActive": {"statType2": 6, "statType3": -1, "minIcdSeconds": 110}}]}}, {"not": {"val": {"or": {"vals": [{"auraIsKnown": {"auraId": {"itemId": 69002}}}, {"auraIsKnown": {"auraId": {"itemId": 62464}}}, {"auraIsKnown": {"auraId": {"itemId": 77116}}}]}}}}]}}, "castSpell": {"spellId": {"spellId": 77575}}}}, {"action": {"condition": {"and": {"vals": [{"auraIsActiveWithReactionTime": {"auraId": {"spellId": 53365}}}, {"allTrinketStatProcsActive": {"statType2": -1, "statType3": -1}}, {"cmp": {"op": "OpGt", "lhs": {"currentTime": {}}, "rhs": {"const": {"val": "8s"}}}}, {"or": {"vals": [{"cmp": {"op": "OpLt", "lhs": {"trinketProcsMinRemainingTime": {"statType2": 6, "statType3": -1, "minIcdSeconds": 20}}, "rhs": {"math": {"op": "OpSub", "lhs": {"spellTimeToReady": {"spellId": {"itemId": 69002}}}, "rhs": {"const": {"val": "2s"}}}}}}, {"auraIsActive": {"auraId": {"itemId": 69002}}}]}}, {"auraIsKnown": {"auraId": {"itemId": 69002}}}]}}, "castSpell": {"spellId": {"spellId": 77575}}}}, {"action": {"condition": {"and": {"vals": [{"auraIsActiveWithReactionTime": {"auraId": {"spellId": 53365}}}, {"allTrinketStatProcsActive": {"statType2": -1, "statType3": -1}}, {"cmp": {"op": "OpGt", "lhs": {"currentTime": {}}, "rhs": {"const": {"val": "8s"}}}}, {"or": {"vals": [{"cmp": {"op": "OpLt", "lhs": {"trinketProcsMinRemainingTime": {"statType2": 6, "statType3": -1, "minIcdSeconds": 20}}, "rhs": {"math": {"op": "OpSub", "lhs": {"spellTimeToReady": {"spellId": {"itemId": 62464}}}, "rhs": {"const": {"val": "2s"}}}}}}, {"auraIsActive": {"auraId": {"itemId": 62464}}}]}}, {"auraIsKnown": {"auraId": {"itemId": 62464}}}]}}, "castSpell": {"spellId": {"spellId": 77575}}}}, {"action": {"condition": {"and": {"vals": [{"auraIsActiveWithReactionTime": {"auraId": {"spellId": 53365}}}, {"allTrinketStatProcsActive": {"statType2": -1, "statType3": -1}}, {"cmp": {"op": "OpGt", "lhs": {"currentTime": {}}, "rhs": {"const": {"val": "8s"}}}}, {"or": {"vals": [{"cmp": {"op": "OpLt", "lhs": {"trinketProcsMinRemainingTime": {"statType2": 6, "statType3": -1, "minIcdSeconds": 20}}, "rhs": {"math": {"op": "OpSub", "lhs": {"spellTimeToReady": {"spellId": {"itemId": 77116}}}, "rhs": {"const": {"val": "2s"}}}}}}, {"auraIsActive": {"auraId": {"itemId": 77116}}}]}}, {"auraIsKnown": {"auraId": {"itemId": 77116}}}]}}, "castSpell": {"spellId": {"spellId": 77575}}}}, {"hide": true, "action": {"condition": {"and": {"vals": [{"auraIsActiveWithReactionTime": {"auraId": {"spellId": 53365}}}, {"allTrinketStatProcsActive": {"statType2": -1, "statType3": -1}}, {"cmp": {"op": "OpGt", "lhs": {"currentTime": {}}, "rhs": {"const": {"val": "8s"}}}}, {"or": {"vals": [{"cmp": {"op": "OpLt", "lhs": {"trinketProcsMinRemainingTime": {"statType2": 6, "statType3": -1, "minIcdSeconds": 20}}, "rhs": {"math": {"op": "OpSub", "lhs": {"spellTimeToReady": {"spellId": {"spellId": 82174}}}, "rhs": {"const": {"val": "2s"}}}}}}, {"auraIsActive": {"auraId": {"spellId": 96229}}}, {"auraIsActive": {"auraId": {"itemId": 69002}}}]}}, {"or": {"vals": [{"cmp": {"op": "OpLt", "lhs": {"trinketProcsMinRemainingTime": {"statType2": 6, "statType3": -1, "minIcdSeconds": 20}}, "rhs": {"math": {"op": "OpSub", "lhs": {"spellTimeToReady": {"spellId": {"itemId": 69002}}}, "rhs": {"const": {"val": "2s"}}}}}}, {"auraIsActive": {"auraId": {"itemId": 69002}}}]}}, {"auraIsKnown": {"auraId": {"itemId": 69002}}}]}}, "castSpell": {"spellId": {"spellId": 77575}}}}, {"hide": true, "action": {"condition": {"and": {"vals": [{"auraIsActiveWithReactionTime": {"auraId": {"spellId": 53365}}}, {"allTrinketStatProcsActive": {"statType2": -1, "statType3": -1}}, {"cmp": {"op": "OpGt", "lhs": {"currentTime": {}}, "rhs": {"const": {"val": "8s"}}}}, {"or": {"vals": [{"cmp": {"op": "OpLt", "lhs": {"trinketProcsMinRemainingTime": {"statType2": 6, "statType3": -1, "minIcdSeconds": 20}}, "rhs": {"math": {"op": "OpSub", "lhs": {"spellTimeToReady": {"spellId": {"spellId": 82174}}}, "rhs": {"const": {"val": "2s"}}}}}}, {"auraIsActive": {"auraId": {"spellId": 96229}}}, {"auraIsActive": {"auraId": {"itemId": 62464}}}]}}, {"or": {"vals": [{"cmp": {"op": "OpLt", "lhs": {"trinketProcsMinRemainingTime": {"statType2": 6, "statType3": -1, "minIcdSeconds": 20}}, "rhs": {"math": {"op": "OpSub", "lhs": {"spellTimeToReady": {"spellId": {"itemId": 62464}}}, "rhs": {"const": {"val": "2s"}}}}}}, {"auraIsActive": {"auraId": {"itemId": 62464}}}]}}, {"auraIsKnown": {"auraId": {"itemId": 62464}}}]}}, "castSpell": {"spellId": {"spellId": 77575}}}}, {"hide": true, "action": {"condition": {"and": {"vals": [{"auraIsActiveWithReactionTime": {"auraId": {"spellId": 53365}}}, {"allTrinketStatProcsActive": {"statType2": -1, "statType3": -1}}, {"cmp": {"op": "OpGt", "lhs": {"currentTime": {}}, "rhs": {"const": {"val": "8s"}}}}, {"or": {"vals": [{"cmp": {"op": "OpLt", "lhs": {"trinketProcsMinRemainingTime": {"statType2": 6, "statType3": -1, "minIcdSeconds": 20}}, "rhs": {"math": {"op": "OpSub", "lhs": {"spellTimeToReady": {"spellId": {"spellId": 82174}}}, "rhs": {"const": {"val": "2s"}}}}}}, {"auraIsActive": {"auraId": {"spellId": 96229}}}, {"auraIsActive": {"auraId": {"itemId": 77116}}}]}}, {"or": {"vals": [{"cmp": {"op": "OpLt", "lhs": {"trinketProcsMinRemainingTime": {"statType2": 6, "statType3": -1, "minIcdSeconds": 20}}, "rhs": {"math": {"op": "OpSub", "lhs": {"spellTimeToReady": {"spellId": {"itemId": 77116}}}, "rhs": {"const": {"val": "2s"}}}}}}, {"auraIsActive": {"auraId": {"itemId": 77116}}}]}}, {"auraIsKnown": {"auraId": {"itemId": 77116}}}]}}, "castSpell": {"spellId": {"spellId": 77575}}}}, {"action": {"condition": {"and": {"vals": [{"cmp": {"op": "OpGe", "lhs": {"numEquippedStatProcTrinkets": {"statType2": 6, "statType3": -1, "minIcdSeconds": 110}}, "rhs": {"const": {"val": "1"}}}}, {"cmp": {"op": "OpEq", "lhs": {"numEquippedStatProcTrinkets": {"statType2": 6, "statType3": -1, "minIcdSeconds": 60}}, "rhs": {"numEquippedStatProcTrinkets": {"statType2": 6, "statType3": -1, "minIcdSeconds": 110}}}}, {"auraIsActiveWithReactionTime": {"auraId": {"spellId": 53365}}}, {"or": {"vals": [{"cmp": {"op": "OpLt", "lhs": {"trinketProcsMinRemainingTime": {"statType2": 6, "statType3": -1, "minIcdSeconds": 110}}, "rhs": {"math": {"op": "OpSub", "lhs": {"spellTimeToReady": {"spellId": {"spellId": 82174}}}, "rhs": {"const": {"val": "2s"}}}}}}, {"auraIsActive": {"auraId": {"spellId": 96229}}}]}}, {"or": {"vals": [{"cmp": {"op": "OpLt", "lhs": {"trinketProcsMinRemainingTime": {"statType2": 6, "statType3": -1, "minIcdSeconds": 110}}, "rhs": {"math": {"op": "OpSub", "lhs": {"spellTimeToReady": {"spellId": {"spellId": 33697}}}, "rhs": {"const": {"val": "2s"}}}}}}, {"auraIsActive": {"auraId": {"spellId": 33697}}}]}}, {"allTrinketStatProcsActive": {"statType2": 6, "statType3": -1}}]}}, "castSpell": {"spellId": {"spellId": 77575}}}}, {"action": {"condition": {"and": {"vals": [{"and": {"vals": [{"cmp": {"op": "OpLe", "lhs": {"auraRemainingTime": {"auraId": {"itemId": 58146}}}, "rhs": {"const": {"val": "3s"}}}}, {"auraIsActive": {"auraId": {"itemId": 58146}}}]}}]}}, "castSpell": {"spellId": {"spellId": 77575}}}}, {"action": {"condition": {"and": {"vals": [{"anyTrinketStatProcsActive": {"statType2": -1, "statType3": -1}}, {"cmp": {"op": "OpGe", "lhs": {"numEquippedStatProcTrinkets": {"statType2": 6, "statType3": -1, "minIcdSeconds": 60}}, "rhs": {"const": {"val": "1"}}}}, {"cmp": {"op": "OpLt", "lhs": {"trinketProcsMinRemainingTime": {"statType2": 6, "statType3": -1, "minIcdSeconds": 60}}, "rhs": {"const": {"val": "2s"}}}}, {"or": {"vals": [{"auraIsActiveWithReactionTime": {"auraId": {"spellId": 53365}}}, {"allTrinketStatProcsActive": {"statType2": 6, "statType3": -1, "minIcdSeconds": 60}}]}}]}}, "castSpell": {"spellId": {"spellId": 77575}}}}, {"action": {"condition": {"not": {"val": {"dotIsActive": {"spellId": {"spellId": 55095}}}}}, "castSpell": {"spellId": {"spellId": 45477}}}}, {"action": {"condition": {"not": {"val": {"dotIsActive": {"spellId": {"spellId": 55078}}}}}, "castSpell": {"spellId": {"spellId": 45462, "tag": 1}}}}, {"action": {"condition": {"and": {"vals": [{"or": {"vals": [{"cmp": {"op": "OpGt", "lhs": {"runeSlotCooldown": {"runeSlot": "SlotLeftBlood"}}, "rhs": {"const": {"val": "5.5s"}}}}, {"and": {"vals": [{"cmp": {"op": "OpGe", "lhs": {"currentNonDeathRuneCount": {"runeType": "RuneBlood"}}, "rhs": {"const": {"val": "1"}}}}, {"cmp": {"op": "OpEq", "lhs": {"currentNonDeathRuneCount": {"runeType": "RuneFrost"}}, "rhs": {"const": {"val": "0"}}}}, {"cmp": {"op": "OpGe", "lhs": {"runeCooldown": {"runeType": "RuneFrost"}}, "rhs": {"const": {"val": "3.5s"}}}}]}}]}}, {"or": {"vals": [{"not": {"val": {"currentRuneDeath": {"runeSlot": "SlotLeftBlood"}}}}, {"not": {"val": {"currentRuneDeath": {"runeSlot": "SlotRightBlood"}}}}]}}]}}, "castSpell": {"spellId": {"spellId": 45529}}}}, {"action": {"condition": {"and": {"vals": [{"cmp": {"op": "OpLt", "lhs": {"currentNonDeathRuneCount": {"runeType": "RuneBlood"}}, "rhs": {"currentNonDeathRuneCount": {"runeType": "RuneFrost"}}}}, {"or": {"vals": [{"and": {"vals": [{"not": {"val": {"currentRuneDeath": {"runeSlot": "SlotLeftFrost"}}}}, {"not": {"val": {"currentRuneDeath": {"runeSlot": "SlotRightFrost"}}}}]}}, {"and": {"vals": [{"currentRuneDeath": {"runeSlot": "SlotLeftBlood"}}, {"currentRuneDeath": {"runeSlot": "SlotRightBlood"}}, {"cmp": {"op": "OpLt", "lhs": {"nextRuneCooldown": {"runeType": "RuneFrost"}}, "rhs": {"nextRuneCooldown": {"runeType": "RuneBlood"}}}}]}}]}}]}}, "cancelAura": {"auraId": {"spellId": 45529}}}}, {"action": {"condition": {"and": {"vals": [{"cmp": {"op": "OpGe", "lhs": {"currentRunicPower": {}}, "rhs": {"math": {"op": "OpSub", "lhs": {"maxRunicPower": {}}, "rhs": {"const": {"val": "10"}}}}}}, {"not": {"val": {"or": {"vals": [{"cmp": {"op": "OpGe", "lhs": {"currentRuneCount": {"runeType": "RuneDeath"}}, "rhs": {"const": {"val": "3"}}}}, {"cmp": {"op": "OpLe", "lhs": {"nextRuneCooldown": {"runeType": "RuneUnholy"}}, "rhs": {"const": {"val": "1s"}}}}, {"cmp": {"op": "OpLe", "lhs": {"nextRuneCooldown": {"runeType": "RuneBlood"}}, "rhs": {"const": {"val": "1s"}}}}, {"cmp": {"op": "OpLe", "lhs": {"nextRuneCooldown": {"runeType": "RuneFrost"}}, "rhs": {"const": {"val": "1s"}}}}, {"and": {"vals": [{"auraIsActive": {"sourceUnit": {"type": "Pet", "index": 1, "owner": {"type": "Self"}}, "auraId": {"spellId": 63560}}}, {"cmp": {"op": "OpLe", "lhs": {"auraRemainingTime": {"sourceUnit": {"type": "Pet", "index": 1, "owner": {"type": "Self"}}, "auraId": {"spellId": 63560}}}, "rhs": {"const": {"val": "1s"}}}}]}}]}}}}]}}, "castSpell": {"spellId": {"spellId": 47541}}}}, {"action": {"condition": {"and": {"vals": [{"cmp": {"op": "OpEq", "lhs": {"currentNonDeathRuneCount": {"runeType": "RuneBlood"}}, "rhs": {"const": {"val": "2"}}}}, {"cmp": {"op": "OpLt", "lhs": {"currentTime": {}}, "rhs": {"const": {"val": "6s"}}}}]}}, "castSpell": {"spellId": {"spellId": 85948, "tag": 1}}}}, {"action": {"condition": {"cmp": {"op": "OpGe", "lhs": {"remainingTime": {}}, "rhs": {"const": {"val": "6s"}}}}, "castSpell": {"spellId": {"spellId": 43265}}}}, {"action": {"condition": {"and": {"vals": [{"not": {"val": {"auraIsActive": {"sourceUnit": {"type": "Pet", "index": 1, "owner": {"type": "Self"}}, "auraId": {"spellId": 63560}}}}}, {"cmp": {"op": "OpLt", "lhs": {"auraNumStacks": {"sourceUnit": {"type": "Pet", "index": 1, "owner": {"type": "Self"}}, "auraId": {"spellId": 91342}}}, "rhs": {"const": {"val": "5"}}}}, {"cmp": {"op": "OpGe", "lhs": {"currentRunicPower": {}}, "rhs": {"math": {"op": "OpSub", "lhs": {"maxRunicPower": {}}, "rhs": {"const": {"val": "4"}}}}}}]}}, "castSpell": {"spellId": {"spellId": 47541}}}}, {"action": {"condition": {"or": {"vals": [{"cmp": {"op": "OpEq", "lhs": {"currentRuneCount": {"runeType": "RuneUnholy"}}, "rhs": {"const": {"val": "2"}}}}, {"and": {"vals": [{"cmp": {"op": "OpEq", "lhs": {"currentRuneCount": {"runeType": "RuneUnholy"}}, "rhs": {"const": {"val": "1"}}}}, {"cmp": {"op": "OpLt", "lhs": {"nextRuneCooldown": {"runeType": "RuneUnholy"}}, "rhs": {"const": {"val": "1s"}}}}]}}]}}, "castSpell": {"spellId": {"spellId": 55090, "tag": 1}}}}, {"action": {"condition": {"or": {"vals": [{"or": {"vals": [{"cmp": {"op": "OpEq", "lhs": {"currentNonDeathRuneCount": {"runeType": "RuneBlood"}}, "rhs": {"const": {"val": "2"}}}}, {"and": {"vals": [{"cmp": {"op": "OpEq", "lhs": {"currentRuneCount": {"runeType": "RuneBlood"}}, "rhs": {"const": {"val": "1"}}}}, {"cmp": {"op": "OpLt", "lhs": {"nextRuneCooldown": {"runeType": "RuneBlood"}}, "rhs": {"const": {"val": "1s"}}}}]}}]}}, {"or": {"vals": [{"cmp": {"op": "OpEq", "lhs": {"currentNonDeathRuneCount": {"runeType": "RuneFrost"}}, "rhs": {"const": {"val": "2"}}}}, {"and": {"vals": [{"cmp": {"op": "OpEq", "lhs": {"currentRuneCount": {"runeType": "RuneFrost"}}, "rhs": {"const": {"val": "1"}}}}, {"cmp": {"op": "OpLt", "lhs": {"nextRuneCooldown": {"runeType": "RuneFrost"}}, "rhs": {"const": {"val": "1s"}}}}]}}]}}]}}, "castSpell": {"spellId": {"spellId": 85948, "tag": 1}}}}, {"action": {"condition": {"or": {"vals": [{"and": {"vals": [{"currentRuneDeath": {"runeSlot": "SlotLeftBlood"}}, {"currentRuneDeath": {"runeSlot": "SlotRightBlood"}}, {"cmp": {"op": "OpLe", "lhs": {"runeSlotCooldown": {"runeSlot": "SlotLeftBlood"}}, "rhs": {"const": {"val": "1s"}}}}, {"cmp": {"op": "OpLe", "lhs": {"runeSlotCooldown": {"runeSlot": "SlotRightBlood"}}, "rhs": {"const": {"val": "1s"}}}}]}}, {"and": {"vals": [{"currentRuneDeath": {"runeSlot": "SlotLeftFrost"}}, {"currentRuneDeath": {"runeSlot": "SlotRightFrost"}}, {"cmp": {"op": "OpLe", "lhs": {"runeSlotCooldown": {"runeSlot": "SlotLeftFrost"}}, "rhs": {"const": {"val": "1s"}}}}, {"cmp": {"op": "OpLe", "lhs": {"runeSlotCooldown": {"runeSlot": "SlotRightFrost"}}, "rhs": {"const": {"val": "1s"}}}}]}}]}}, "castSpell": {"spellId": {"spellId": 55090, "tag": 1}}}}, {"action": {"condition": {"and": {"vals": [{"not": {"val": {"auraIsActive": {"sourceUnit": {"type": "Pet", "index": 1, "owner": {"type": "Self"}}, "auraId": {"spellId": 63560}}}}}, {"cmp": {"op": "OpEq", "lhs": {"auraNumStacks": {"sourceUnit": {"type": "Pet", "index": 1, "owner": {"type": "Self"}}, "auraId": {"spellId": 91342}}}, "rhs": {"const": {"val": "4"}}}}, {"or": {"vals": [{"cmp": {"op": "OpGe", "lhs": {"currentRunicPower": {}}, "rhs": {"const": {"val": "34"}}}}, {"auraIsActiveWithReactionTime": {"auraId": {"spellId": 81340}}}]}}]}}, "castSpell": {"spellId": {"spellId": 47541}}}}, {"action": {"condition": {"and": {"vals": [{"auraIsActiveWithReactionTime": {"auraId": {"spellId": 81340}}}, {"cmp": {"op": "OpGt", "lhs": {"nextRuneCooldown": {"runeType": "RuneBlood"}}, "rhs": {"const": {"val": "3s"}}}}, {"cmp": {"op": "OpGt", "lhs": {"nextRuneCooldown": {"runeType": "RuneFrost"}}, "rhs": {"const": {"val": "3s"}}}}, {"cmp": {"op": "OpGt", "lhs": {"nextRuneCooldown": {"runeType": "RuneUnholy"}}, "rhs": {"const": {"val": "3s"}}}}, {"not": {"val": {"and": {"vals": [{"auraIsActive": {"sourceUnit": {"type": "Pet", "index": 1, "owner": {"type": "Self"}}, "auraId": {"spellId": 63560}}}, {"cmp": {"op": "OpLt", "lhs": {"auraRemainingTime": {"sourceUnit": {"type": "Pet", "index": 1, "owner": {"type": "Self"}}, "auraId": {"spellId": 63560}}}, "rhs": {"const": {"val": "2s"}}}}]}}}}]}}, "castSpell": {"spellId": {"spellId": 47541}}}}, {"action": {"castSpell": {"spellId": {"spellId": 55090, "tag": 1}}}}, {"action": {"condition": {"and": {"vals": [{"not": {"val": {"auraIsActive": {"sourceUnit": {"type": "Pet", "index": 1, "owner": {"type": "Self"}}, "auraId": {"spellId": 63560}}}}}, {"cmp": {"op": "OpLt", "lhs": {"auraNumStacks": {"sourceUnit": {"type": "Pet", "index": 1, "owner": {"type": "Self"}}, "auraId": {"spellId": 91342}}}, "rhs": {"const": {"val": "5"}}}}, {"cmp": {"op": "OpGe", "lhs": {"currentRunicPower": {}}, "rhs": {"const": {"val": "44"}}}}]}}, "castSpell": {"spellId": {"spellId": 47541}}}}, {"action": {"castSpell": {"spellId": {"spellId": 85948, "tag": 1}}}}, {"action": {"condition": {"or": {"vals": [{"and": {"vals": [{"not": {"val": {"spellIsReady": {"spellId": {"spellId": 49206}}}}}, {"not": {"val": {"and": {"vals": [{"auraIsActive": {"sourceUnit": {"type": "Pet", "index": 1, "owner": {"type": "Self"}}, "auraId": {"spellId": 63560}}}, {"cmp": {"op": "OpLt", "lhs": {"auraRemainingTime": {"sourceUnit": {"type": "Pet", "index": 1, "owner": {"type": "Self"}}, "auraId": {"spellId": 63560}}}, "rhs": {"const": {"val": "4s"}}}}]}}}}]}}, {"and": {"vals": [{"auraIsActiveWithReactionTime": {"auraId": {"spellId": 81340}}}, {"not": {"val": {"and": {"vals": [{"auraIsActive": {"sourceUnit": {"type": "Pet", "index": 1, "owner": {"type": "Self"}}, "auraId": {"spellId": 63560}}}, {"cmp": {"op": "OpLt", "lhs": {"auraRemainingTime": {"sourceUnit": {"type": "Pet", "index": 1, "owner": {"type": "Self"}}, "auraId": {"spellId": 63560}}}, "rhs": {"const": {"val": "2s"}}}}]}}}}]}}, {"and": {"vals": [{"cmp": {"op": "OpGe", "lhs": {"currentRunicPower": {}}, "rhs": {"const": {"val": "80"}}}}, {"not": {"val": {"and": {"vals": [{"auraIsActive": {"sourceUnit": {"type": "Pet", "index": 1, "owner": {"type": "Self"}}, "auraId": {"spellId": 63560}}}, {"cmp": {"op": "OpLt", "lhs": {"auraRemainingTime": {"sourceUnit": {"type": "Pet", "index": 1, "owner": {"type": "Self"}}, "auraId": {"spellId": 63560}}}, "rhs": {"const": {"val": "3s"}}}}]}}}}]}}, {"and": {"vals": [{"spellIsReady": {"spellId": {"spellId": 49206}}}, {"not": {"val": {"and": {"vals": [{"auraIsActive": {"sourceUnit": {"type": "Pet", "index": 1, "owner": {"type": "Self"}}, "auraId": {"spellId": 63560}}}, {"cmp": {"op": "OpLt", "lhs": {"auraRemainingTime": {"sourceUnit": {"type": "Pet", "index": 1, "owner": {"type": "Self"}}, "auraId": {"spellId": 63560}}}, "rhs": {"const": {"val": "4s"}}}}]}}}}, {"cmp": {"op": "OpGt", "lhs": {"currentTime": {}}, "rhs": {"const": {"val": "175s"}}}}, {"cmp": {"op": "OpGt", "lhs": {"auraRemainingTime": {"auraId": {"spellId": 49016}}}, "rhs": {"const": {"val": "25s"}}}}, {"cmp": {"op": "OpGt", "lhs": {"remainingTime": {}}, "rhs": {"const": {"val": "40s"}}}}]}}, {"and": {"vals": [{"cmp": {"op": "OpLt", "lhs": {"remainingTime": {}}, "rhs": {"const": {"val": "15s"}}}}, {"cmp": {"op": "OpGt", "lhs": {"spellTimeToReady": {"spellId": {"spellId": 49206}}}, "rhs": {"math": {"op": "OpSub", "lhs": {"remainingTime": {}}, "rhs": {"const": {"val": "8"}}}}}}]}}]}}, "castSpell": {"spellId": {"spellId": 47541}}}}, {"action": {"castSpell": {"spellId": {"spellId": 57330}}}}, {"action": {"condition": {"and": {"vals": [{"not": {"val": {"auraIsActive": {"auraId": {"spellId": 2825, "tag": -1}}}}}, {"not": {"val": {"auraIsActive": {"auraId": {"spellId": 51460}}}}}, {"cmp": {"op": "OpLe", "lhs": {"currentRunicPower": {}}, "rhs": {"const": {"val": "38"}}}}, {"and": {"vals": [{"cmp": {"op": "OpEq", "lhs": {"currentRuneCount": {"runeType": "RuneBlood"}}, "rhs": {"const": {"val": "0"}}}}, {"cmp": {"op": "OpEq", "lhs": {"currentRuneCount": {"runeType": "RuneFrost"}}, "rhs": {"const": {"val": "0"}}}}, {"cmp": {"op": "OpEq", "lhs": {"currentRuneCount": {"runeType": "RuneUnholy"}}, "rhs": {"const": {"val": "0"}}}}, {"cmp": {"op": "OpEq", "lhs": {"currentRuneCount": {"runeType": "RuneDeath"}}, "rhs": {"const": {"val": "0"}}}}, {"cmp": {"op": "OpGt", "lhs": {"runeCooldown": {"runeType": "RuneBlood"}}, "rhs": {"const": {"val": "1s"}}}}, {"cmp": {"op": "OpGt", "lhs": {"runeCooldown": {"runeType": "RuneFrost"}}, "rhs": {"const": {"val": "1s"}}}}, {"cmp": {"op": "OpGt", "lhs": {"runeCooldown": {"runeType": "RuneUnholy"}}, "rhs": {"const": {"val": "1s"}}}}]}}]}}, "castSpell": {"spellId": {"spellId": 47568}}}}]}