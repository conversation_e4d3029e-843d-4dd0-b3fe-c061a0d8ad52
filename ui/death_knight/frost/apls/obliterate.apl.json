{"type": "TypeAPL", "prepullActions": [{"action": {"castSpell": {"spellId": {"spellId": 48266}}}, "doAtValue": {"const": {"val": "-9s"}}}, {"action": {"castSpell": {"spellId": {"spellId": 57330}}}, "doAtValue": {"const": {"val": "-8s"}}}, {"action": {"castSpell": {"spellId": {"spellId": 46584}}}, "doAtValue": {"const": {"val": "-7s"}}}, {"action": {"castSpell": {"spellId": {"spellId": 42650}}}, "doAtValue": {"const": {"val": "-6s"}}}, {"action": {"castSpell": {"spellId": {"otherId": "OtherActionPotion"}}}, "doAtValue": {"const": {"val": "-1s"}}}, {"action": {"castSpell": {"spellId": {"spellId": 51271}}}, "doAtValue": {"const": {"val": "-1s"}}}], "priorityList": [{"action": {"castSpell": {"spellId": {"spellId": 2825, "tag": -1}}}}, {"action": {"condition": {"or": {"vals": [{"and": {"vals": [{"cmp": {"op": "OpEq", "lhs": {"currentRuneCount": {"runeType": "RuneFrost"}}, "rhs": {"const": {"val": "0"}}}}, {"or": {"vals": [{"cmp": {"op": "OpLe", "lhs": {"runeCooldown": {"runeType": "RuneFrost"}}, "rhs": {"const": {"val": "0.1"}}}}, {"cmp": {"op": "OpLe", "lhs": {"runeCooldown": {"runeType": "RuneBlood"}}, "rhs": {"const": {"val": "0.1"}}}}]}}, {"or": {"vals": [{"gcdIsReady": {}}, {"cmp": {"op": "OpLt", "lhs": {"gcdTimeToReady": {}}, "rhs": {"min": {"vals": [{"runeCooldown": {"runeType": "RuneFrost"}}, {"runeCooldown": {"runeType": "RuneBlood"}}]}}}}]}}]}}, {"and": {"vals": [{"cmp": {"op": "OpEq", "lhs": {"currentRuneCount": {"runeType": "RuneFrost"}}, "rhs": {"const": {"val": "1"}}}}, {"or": {"vals": [{"cmp": {"op": "OpLe", "lhs": {"runeCooldown": {"runeType": "RuneFrost"}}, "rhs": {"const": {"val": "0.1"}}}}, {"cmp": {"op": "OpLe", "lhs": {"runeCooldown": {"runeType": "RuneBlood"}}, "rhs": {"const": {"val": "0.1"}}}}]}}, {"or": {"vals": [{"gcdIsReady": {}}, {"cmp": {"op": "OpLt", "lhs": {"gcdTimeToReady": {}}, "rhs": {"min": {"vals": [{"runeCooldown": {"runeType": "RuneFrost"}}, {"runeCooldown": {"runeType": "RuneBlood"}}]}}}}]}}]}}]}}, "wait": {"duration": {"min": {"vals": [{"runeCooldown": {"runeType": "RuneFrost"}}, {"runeCooldown": {"runeType": "RuneBlood"}}]}}}}}, {"action": {"autocastOtherCooldowns": {}}}, {"action": {"condition": {"cmp": {"op": "OpLe", "lhs": {"spellTimeToReady": {"spellId": {"spellId": 51271}}}, "rhs": {"const": {"val": "5"}}}}, "castSpell": {"spellId": {"spellId": 46584}}}}, {"action": {"condition": {"or": {"vals": [{"cmp": {"op": "OpLe", "lhs": {"remainingTime": {}}, "rhs": {"const": {"val": "30s"}}}}, {"and": {"vals": [{"cmp": {"op": "OpLe", "lhs": {"remainingTime": {}}, "rhs": {"const": {"val": "60s"}}}}, {"auraIsActive": {"auraId": {"spellId": 51271}}}]}}]}}, "castSpell": {"spellId": {"otherId": "OtherActionPotion"}}}}, {"action": {"condition": {"or": {"vals": [{"isExecutePhase": {"threshold": "E35"}}, {"and": {"vals": [{"auraIsKnown": {"auraId": {"spellId": 138347}}}, {"isExecutePhase": {"threshold": "E45"}}]}}]}}, "castSpell": {"spellId": {"spellId": 114867, "tag": 2}}}}, {"action": {"condition": {"and": {"vals": [{"spellIsReady": {"spellId": {"spellId": 114867, "tag": 2}}}, {"or": {"vals": [{"isExecutePhase": {"threshold": "E35"}}, {"and": {"vals": [{"auraIsKnown": {"auraId": {"spellId": 138347}}}, {"isExecutePhase": {"threshold": "E45"}}]}}]}}]}}, "castSpell": {"spellId": {"spellId": 45529}}}}, {"action": {"condition": {"or": {"vals": [{"cmp": {"op": "OpLt", "lhs": {"dotRemainingTime": {"spellId": {"spellId": 55078}}}, "rhs": {"const": {"val": "1s"}}}}, {"cmp": {"op": "OpLt", "lhs": {"dotRemainingTime": {"spellId": {"spellId": 55095}}}, "rhs": {"const": {"val": "1s"}}}}]}}, "castSpell": {"spellId": {"spellId": 123693}}}}, {"action": {"condition": {"cmp": {"op": "OpGt", "lhs": {"auraNumStacks": {"auraId": {"spellId": 114851}}}, "rhs": {"const": {"val": "10"}}}}, "castSpell": {"spellId": {"spellId": 45529}}}}, {"action": {"condition": {"or": {"vals": [{"and": {"vals": [{"cmp": {"op": "OpEq", "lhs": {"currentRuneCount": {"runeType": "RuneBlood"}}, "rhs": {"const": {"val": "0"}}}}, {"cmp": {"op": "OpEq", "lhs": {"currentRuneCount": {"runeType": "RuneFrost"}}, "rhs": {"const": {"val": "0"}}}}]}}, {"and": {"vals": [{"cmp": {"op": "OpEq", "lhs": {"currentRuneCount": {"runeType": "RuneBlood"}}, "rhs": {"const": {"val": "0"}}}}, {"cmp": {"op": "OpEq", "lhs": {"currentRuneCount": {"runeType": "RuneUnholy"}}, "rhs": {"const": {"val": "0"}}}}]}}, {"and": {"vals": [{"cmp": {"op": "OpEq", "lhs": {"currentRuneCount": {"runeType": "RuneFrost"}}, "rhs": {"const": {"val": "0"}}}}, {"cmp": {"op": "OpEq", "lhs": {"currentRuneCount": {"runeType": "RuneUnholy"}}, "rhs": {"const": {"val": "0"}}}}]}}]}}, "castSpell": {"spellId": {"spellId": 123693}}}}, {"action": {"condition": {"or": {"vals": [{"not": {"val": {"dotIsActive": {"spellId": {"spellId": 55078}}}}}, {"not": {"val": {"dotIsActive": {"spellId": {"spellId": 55095}}}}}]}}, "castSpell": {"spellId": {"spellId": 77575}}}}, {"action": {"condition": {"or": {"vals": [{"not": {"val": {"dotIsActive": {"spellId": {"spellId": 55078}}}}}, {"not": {"val": {"dotIsActive": {"spellId": {"spellId": 55095}}}}}]}}, "castSpell": {"spellId": {"spellId": 115989}}}}, {"action": {"condition": {"not": {"val": {"dotIsActive": {"spellId": {"spellId": 55095}}}}}, "castSpell": {"spellId": {"spellId": 49184}}}}, {"action": {"condition": {"not": {"val": {"dotIsActive": {"spellId": {"spellId": 55078}}}}}, "castSpell": {"spellId": {"spellId": 45462, "tag": 1}}}}, {"action": {"condition": {"and": {"vals": [{"auraIsActiveWithReactionTime": {"auraId": {"spellId": 51124}}}, {"or": {"vals": [{"and": {"vals": [{"cmp": {"op": "OpEq", "lhs": {"currentRuneCount": {"runeType": "RuneBlood"}}, "rhs": {"const": {"val": "0"}}}}, {"cmp": {"op": "OpEq", "lhs": {"currentRuneCount": {"runeType": "RuneFrost"}}, "rhs": {"const": {"val": "0"}}}}, {"cmp": {"op": "OpEq", "lhs": {"currentRuneCount": {"runeType": "RuneUnholy"}}, "rhs": {"const": {"val": "1"}}}}]}}, {"and": {"vals": [{"cmp": {"op": "OpEq", "lhs": {"currentRuneCount": {"runeType": "RuneBlood"}}, "rhs": {"const": {"val": "1"}}}}, {"cmp": {"op": "OpEq", "lhs": {"currentRuneCount": {"runeType": "RuneFrost"}}, "rhs": {"const": {"val": "0"}}}}, {"cmp": {"op": "OpEq", "lhs": {"currentRuneCount": {"runeType": "RuneUnholy"}}, "rhs": {"const": {"val": "0"}}}}]}}, {"and": {"vals": [{"cmp": {"op": "OpEq", "lhs": {"currentRuneCount": {"runeType": "RuneBlood"}}, "rhs": {"const": {"val": "0"}}}}, {"cmp": {"op": "OpEq", "lhs": {"currentRuneCount": {"runeType": "RuneFrost"}}, "rhs": {"const": {"val": "1"}}}}, {"cmp": {"op": "OpEq", "lhs": {"currentRuneCount": {"runeType": "RuneUnholy"}}, "rhs": {"const": {"val": "0"}}}}]}}]}}]}}, "castSpell": {"spellId": {"spellId": 45529}}}}, {"action": {"condition": {"auraIsActiveWithReactionTime": {"auraId": {"spellId": 51124}}}, "castSpell": {"spellId": {"spellId": 49020, "tag": 1}}}}, {"action": {"condition": {"and": {"vals": [{"cmp": {"op": "OpGt", "lhs": {"currentRunicPower": {}}, "rhs": {"const": {"val": "76"}}}}]}}, "castSpell": {"spellId": {"spellId": 49143, "tag": 1}}}}, {"action": {"condition": {"or": {"vals": [{"cmp": {"op": "OpEq", "lhs": {"currentRuneCount": {"runeType": "RuneBlood"}}, "rhs": {"const": {"val": "2"}}}}, {"cmp": {"op": "OpEq", "lhs": {"currentRuneCount": {"runeType": "RuneFrost"}}, "rhs": {"const": {"val": "2"}}}}, {"cmp": {"op": "OpEq", "lhs": {"currentRuneCount": {"runeType": "RuneUnholy"}}, "rhs": {"const": {"val": "2"}}}}]}}, "castSpell": {"spellId": {"spellId": 49020, "tag": 1}}}}, {"action": {"condition": {"auraIsActiveWithReactionTime": {"auraId": {"spellId": 59052}}}, "castSpell": {"spellId": {"spellId": 49184}}}}, {"action": {"condition": {"or": {"vals": [{"cmp": {"op": "OpLt", "lhs": {"dotRemainingTime": {"spellId": {"spellId": 55078}}}, "rhs": {"const": {"val": "3s"}}}}, {"cmp": {"op": "OpLt", "lhs": {"dotRemainingTime": {"spellId": {"spellId": 55095}}}, "rhs": {"const": {"val": "3s"}}}}]}}, "castSpell": {"spellId": {"spellId": 77575}}}}, {"action": {"condition": {"cmp": {"op": "OpLt", "lhs": {"dotRemainingTime": {"spellId": {"spellId": 55078}}}, "rhs": {"const": {"val": "3s"}}}}, "castSpell": {"spellId": {"spellId": 45462, "tag": 1}}}}, {"action": {"condition": {"cmp": {"op": "OpLt", "lhs": {"dotRemainingTime": {"spellId": {"spellId": 55095}}}, "rhs": {"const": {"val": "3s"}}}}, "castSpell": {"spellId": {"spellId": 49184}}}}, {"action": {"condition": {"or": {"vals": [{"cmp": {"op": "OpLt", "lhs": {"dotRemainingTime": {"spellId": {"spellId": 55078}}}, "rhs": {"const": {"val": "3s"}}}}, {"cmp": {"op": "OpLt", "lhs": {"dotRemainingTime": {"spellId": {"spellId": 55095}}}, "rhs": {"const": {"val": "3s"}}}}]}}, "castSpell": {"spellId": {"spellId": 115989}}}}, {"action": {"condition": {"or": {"vals": [{"and": {"vals": [{"auraIsKnown": {"auraId": {"spellId": 81229}}}, {"or": {"vals": [{"cmp": {"op": "OpEq", "lhs": {"currentNonDeathRuneCount": {"runeType": "RuneBlood"}}, "rhs": {"const": {"val": "0"}}}}, {"cmp": {"op": "OpEq", "lhs": {"currentNonDeathRuneCount": {"runeType": "RuneFrost"}}, "rhs": {"const": {"val": "0"}}}}, {"cmp": {"op": "OpEq", "lhs": {"currentNonDeathRuneCount": {"runeType": "RuneUnholy"}}, "rhs": {"const": {"val": "0"}}}}]}}]}}, {"and": {"vals": [{"spellIsKnown": {"spellId": {"spellId": 45529}}}, {"cmp": {"op": "OpLe", "lhs": {"auraNumStacks": {"auraId": {"spellId": 114851}}}, "rhs": {"const": {"val": "10"}}}}, {"auraIsInactiveWithReactionTime": {"auraId": {"spellId": 51124}}}]}}]}}, "castSpell": {"spellId": {"spellId": 49143, "tag": 1}}}}, {"action": {"castSpell": {"spellId": {"spellId": 57330}}}}, {"action": {"castSpell": {"spellId": {"spellId": 49020, "tag": 1}}}}, {"action": {"castSpell": {"spellId": {"spellId": 114867, "tag": 2}}}}, {"action": {"condition": {"and": {"vals": [{"cmp": {"op": "OpGe", "lhs": {"currentRunicPower": {}}, "rhs": {"const": {"val": "20"}}}}, {"cmp": {"op": "OpGt", "lhs": {"auraNumStacks": {"auraId": {"spellId": 114851}}}, "rhs": {"const": {"val": "10"}}}}]}}, "castSpell": {"spellId": {"spellId": 45529}}}}, {"action": {"castSpell": {"spellId": {"spellId": 49143, "tag": 1}}}}, {"action": {"condition": {"and": {"vals": [{"cmp": {"op": "OpEq", "lhs": {"currentRuneCount": {"runeType": "RuneBlood"}}, "rhs": {"const": {"val": "0"}}}}, {"cmp": {"op": "OpEq", "lhs": {"currentRuneCount": {"runeType": "RuneFrost"}}, "rhs": {"const": {"val": "0"}}}}, {"cmp": {"op": "OpEq", "lhs": {"currentRuneCount": {"runeType": "RuneUnholy"}}, "rhs": {"const": {"val": "0"}}}}, {"cmp": {"op": "OpLt", "lhs": {"currentRunicPower": {}}, "rhs": {"const": {"val": "20"}}}}]}}, "castSpell": {"spellId": {"spellId": 47568}}}}, {"action": {"castSpell": {"spellId": {"spellId": 123693}}}}]}