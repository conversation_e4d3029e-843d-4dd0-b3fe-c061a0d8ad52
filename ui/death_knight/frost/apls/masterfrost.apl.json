{"type": "TypeAPL", "prepullActions": [{"action": {"castSpell": {"spellId": {"spellId": 48266}}}, "doAtValue": {"const": {"val": "-9s"}}}, {"action": {"castSpell": {"spellId": {"spellId": 57330}}}, "doAtValue": {"const": {"val": "-8s"}}}, {"action": {"castSpell": {"spellId": {"spellId": 46584}}}, "doAtValue": {"const": {"val": "-7s"}}}, {"action": {"castSpell": {"spellId": {"spellId": 42650}}}, "doAtValue": {"const": {"val": "-6s"}}}, {"action": {"castSpell": {"spellId": {"otherId": "OtherActionPotion"}}}, "doAtValue": {"const": {"val": "-1s"}}}, {"action": {"castSpell": {"spellId": {"spellId": 51271}}}, "doAtValue": {"const": {"val": "-1s"}}}], "priorityList": [{"action": {"castSpell": {"spellId": {"spellId": 2825, "tag": -1}}}}, {"action": {"condition": {"or": {"vals": [{"and": {"vals": [{"cmp": {"op": "OpEq", "lhs": {"currentRuneCount": {"runeType": "RuneFrost"}}, "rhs": {"const": {"val": "0"}}}}, {"or": {"vals": [{"cmp": {"op": "OpLe", "lhs": {"runeCooldown": {"runeType": "RuneFrost"}}, "rhs": {"const": {"val": "0.1"}}}}, {"cmp": {"op": "OpLe", "lhs": {"runeCooldown": {"runeType": "RuneBlood"}}, "rhs": {"const": {"val": "0.1"}}}}]}}, {"or": {"vals": [{"gcdIsReady": {}}, {"cmp": {"op": "OpLt", "lhs": {"gcdTimeToReady": {}}, "rhs": {"min": {"vals": [{"runeCooldown": {"runeType": "RuneBlood"}}, {"runeCooldown": {"runeType": "RuneFrost"}}]}}}}]}}]}}, {"and": {"vals": [{"cmp": {"op": "OpEq", "lhs": {"currentRuneCount": {"runeType": "RuneFrost"}}, "rhs": {"const": {"val": "1"}}}}, {"or": {"vals": [{"cmp": {"op": "OpLe", "lhs": {"runeCooldown": {"runeType": "RuneFrost"}}, "rhs": {"const": {"val": "0.1"}}}}, {"cmp": {"op": "OpLe", "lhs": {"runeCooldown": {"runeType": "RuneBlood"}}, "rhs": {"const": {"val": "0.1"}}}}]}}, {"or": {"vals": [{"gcdIsReady": {}}, {"cmp": {"op": "OpLt", "lhs": {"gcdTimeToReady": {}}, "rhs": {"min": {"vals": [{"runeCooldown": {"runeType": "RuneBlood"}}, {"runeCooldown": {"runeType": "RuneFrost"}}]}}}}]}}]}}]}}, "wait": {"duration": {"min": {"vals": [{"runeCooldown": {"runeType": "RuneBlood"}}, {"runeCooldown": {"runeType": "RuneFrost"}}]}}}}}, {"action": {"castSpell": {"spellId": {"spellId": 51271}}}}, {"action": {"autocastOtherCooldowns": {}}}, {"action": {"condition": {"or": {"vals": [{"cmp": {"op": "OpLe", "lhs": {"spellTimeToReady": {"spellId": {"spellId": 51271}}}, "rhs": {"const": {"val": "5"}}}}, {"auraIsActive": {"auraId": {"spellId": 51271}}}]}}, "castSpell": {"spellId": {"spellId": 46584}}}}, {"action": {"condition": {"or": {"vals": [{"cmp": {"op": "OpLe", "lhs": {"remainingTime": {}}, "rhs": {"const": {"val": "30s"}}}}, {"and": {"vals": [{"cmp": {"op": "OpLe", "lhs": {"remainingTime": {}}, "rhs": {"const": {"val": "60s"}}}}, {"auraIsActive": {"auraId": {"spellId": 51271}}}]}}]}}, "castSpell": {"spellId": {"otherId": "OtherActionPotion"}}}}, {"action": {"condition": {"cmp": {"op": "OpGt", "lhs": {"auraNumStacks": {"auraId": {"spellId": 114851}}}, "rhs": {"const": {"val": "10"}}}}, "castSpell": {"spellId": {"spellId": 45529}}}}, {"action": {"condition": {"or": {"vals": [{"isExecutePhase": {"threshold": "E35"}}, {"and": {"vals": [{"auraIsKnown": {"auraId": {"spellId": 138347}}}, {"isExecutePhase": {"threshold": "E45"}}]}}]}}, "castSpell": {"spellId": {"spellId": 114867, "tag": 2}}}}, {"action": {"condition": {"and": {"vals": [{"spellIsReady": {"spellId": {"spellId": 114867, "tag": 2}}}, {"or": {"vals": [{"isExecutePhase": {"threshold": "E35"}}, {"and": {"vals": [{"auraIsKnown": {"auraId": {"spellId": 138347}}}, {"isExecutePhase": {"threshold": "E45"}}]}}]}}]}}, "castSpell": {"spellId": {"spellId": 45529}}}}, {"action": {"condition": {"and": {"vals": [{"auraIsActiveWithReactionTime": {"auraId": {"spellId": 51124}}}, {"cmp": {"op": "OpLt", "lhs": {"currentRunicPower": {}}, "rhs": {"const": {"val": "76"}}}}, {"cmp": {"op": "OpGt", "lhs": {"currentNonDeathRuneCount": {"runeType": "RuneUnholy"}}, "rhs": {"const": {"val": "0"}}}}, {"dotIsActive": {"spellId": {"spellId": 55078}}}, {"dotIsActive": {"spellId": {"spellId": 55095}}}]}}, "castSpell": {"spellId": {"spellId": 49020, "tag": 1}}}}, {"action": {"condition": {"or": {"vals": [{"auraIsActiveWithReactionTime": {"auraId": {"spellId": 51124}}}, {"cmp": {"op": "OpGt", "lhs": {"currentRunicPower": {}}, "rhs": {"const": {"val": "88"}}}}]}}, "castSpell": {"spellId": {"spellId": 49143, "tag": 1}}}}, {"action": {"condition": {"or": {"vals": [{"cmp": {"op": "OpLt", "lhs": {"dotRemainingTime": {"spellId": {"spellId": 55078}}}, "rhs": {"const": {"val": "1"}}}}, {"cmp": {"op": "OpLt", "lhs": {"dotRemainingTime": {"spellId": {"spellId": 55095}}}, "rhs": {"const": {"val": "1"}}}}]}}, "castSpell": {"spellId": {"spellId": 123693}}}}, {"action": {"condition": {"cmp": {"op": "OpGt", "lhs": {"currentRuneCount": {"runeType": "RuneFrost"}}, "rhs": {"const": {"val": "1"}}}}, "castSpell": {"spellId": {"spellId": 49184}}}}, {"action": {"condition": {"and": {"vals": [{"auraIsActive": {"auraId": {"spellId": 51271}}}, {"cmp": {"op": "OpEq", "lhs": {"currentRuneCount": {"runeType": "RuneDeath"}}, "rhs": {"const": {"val": "0"}}}}]}}, "castSpell": {"spellId": {"spellId": 45529}}}}, {"action": {"condition": {"or": {"vals": [{"cmp": {"op": "OpLt", "lhs": {"dotRemainingTime": {"spellId": {"spellId": 55078}}}, "rhs": {"const": {"val": "3s"}}}}, {"cmp": {"op": "OpLt", "lhs": {"dotRemainingTime": {"spellId": {"spellId": 55095}}}, "rhs": {"const": {"val": "3s"}}}}]}}, "castSpell": {"spellId": {"spellId": 115989}}}}, {"action": {"condition": {"and": {"vals": [{"not": {"val": {"dotIsActive": {"spellId": {"spellId": 55078}}}}}, {"cmp": {"op": "OpEq", "lhs": {"currentNonDeathRuneCount": {"runeType": "RuneUnholy"}}, "rhs": {"const": {"val": "0"}}}}]}}, "castSpell": {"spellId": {"spellId": 77575}}}}, {"action": {"condition": {"not": {"val": {"dotIsActive": {"spellId": {"spellId": 55095}}}}}, "castSpell": {"spellId": {"spellId": 49184}}}}, {"action": {"condition": {"and": {"vals": [{"not": {"val": {"dotIsActive": {"spellId": {"spellId": 55078}}}}}, {"cmp": {"op": "OpGt", "lhs": {"currentNonDeathRuneCount": {"runeType": "RuneUnholy"}}, "rhs": {"const": {"val": "0"}}}}]}}, "castSpell": {"spellId": {"spellId": 45462, "tag": 1}}}}, {"action": {"condition": {"auraIsActiveWithReactionTime": {"auraId": {"spellId": 59052}}}, "castSpell": {"spellId": {"spellId": 49184}}}}, {"action": {"condition": {"cmp": {"op": "OpGt", "lhs": {"currentRunicPower": {}}, "rhs": {"const": {"val": "76"}}}}, "castSpell": {"spellId": {"spellId": 49143, "tag": 1}}}}, {"action": {"condition": {"and": {"vals": [{"cmp": {"op": "OpGt", "lhs": {"currentNonDeathRuneCount": {"runeType": "RuneUnholy"}}, "rhs": {"const": {"val": "0"}}}}, {"dotIsActive": {"spellId": {"spellId": 55078}}}, {"dotIsActive": {"spellId": {"spellId": 55095}}}]}}, "castSpell": {"spellId": {"spellId": 49020, "tag": 1}}}}, {"action": {"castSpell": {"spellId": {"spellId": 49184}}}}, {"action": {"condition": {"and": {"vals": [{"auraIsKnown": {"auraId": {"spellId": 81229}}}, {"cmp": {"op": "OpEq", "lhs": {"currentNonDeathRuneCount": {"runeType": "RuneUnholy"}}, "rhs": {"const": {"val": "1"}}}}]}}, "castSpell": {"spellId": {"spellId": 49143, "tag": 1}}}}, {"hide": true, "action": {"condition": {"cmp": {"op": "OpGe", "lhs": {"currentRunicPower": {}}, "rhs": {"const": {"val": "40"}}}}, "castSpell": {"spellId": {"spellId": 49143, "tag": 1}}}}, {"action": {"castSpell": {"spellId": {"spellId": 57330}}}}, {"action": {"condition": {"and": {"vals": [{"cmp": {"op": "OpEq", "lhs": {"currentRuneCount": {"runeType": "RuneBlood"}}, "rhs": {"const": {"val": "0"}}}}, {"cmp": {"op": "OpEq", "lhs": {"currentRuneCount": {"runeType": "RuneFrost"}}, "rhs": {"const": {"val": "0"}}}}, {"cmp": {"op": "OpEq", "lhs": {"currentRuneCount": {"runeType": "RuneUnholy"}}, "rhs": {"const": {"val": "0"}}}}, {"cmp": {"op": "OpLt", "lhs": {"currentRunicPower": {}}, "rhs": {"const": {"val": "20"}}}}]}}, "castSpell": {"spellId": {"spellId": 47568}}}}, {"action": {"condition": {"or": {"vals": [{"and": {"vals": [{"cmp": {"op": "OpEq", "lhs": {"currentRuneCount": {"runeType": "RuneBlood"}}, "rhs": {"const": {"val": "0"}}}}, {"cmp": {"op": "OpEq", "lhs": {"currentRuneCount": {"runeType": "RuneFrost"}}, "rhs": {"const": {"val": "0"}}}}]}}, {"and": {"vals": [{"cmp": {"op": "OpEq", "lhs": {"currentRuneCount": {"runeType": "RuneBlood"}}, "rhs": {"const": {"val": "0"}}}}, {"cmp": {"op": "OpEq", "lhs": {"currentRuneCount": {"runeType": "RuneUnholy"}}, "rhs": {"const": {"val": "0"}}}}]}}, {"and": {"vals": [{"cmp": {"op": "OpEq", "lhs": {"currentRuneCount": {"runeType": "RuneFrost"}}, "rhs": {"const": {"val": "0"}}}}, {"cmp": {"op": "OpEq", "lhs": {"currentRuneCount": {"runeType": "RuneUnholy"}}, "rhs": {"const": {"val": "0"}}}}]}}]}}, "castSpell": {"spellId": {"spellId": 123693}}}}, {"action": {"condition": {"cmp": {"op": "OpGe", "lhs": {"currentRunicPower": {}}, "rhs": {"const": {"val": "20"}}}}, "castSpell": {"spellId": {"spellId": 49143, "tag": 1}}}}]}