<!doctype html>
<html lang="" data-i18n-lang>
	<head>
		<title data-class="@@CLASS@@" data-spec="@@SPEC@@"></title>
		<meta charset="utf-8" />

		<link rel="icon" type="image/x-icon" href="/mop/assets/favicon_io/favicon.ico" />

		<meta name="description" content="" data-class="@@CLASS@@" data-spec="@@SPEC@@" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />

		<link rel="preload" href="/mop/assets/database/db.json" as="fetch" crossorigin="anonymous" />
		<link rel="stylesheet" href="../../scss/core/individual_sim_ui/index.scss" />
		<link rel="stylesheet" href="../../scss/sims/@@CLASS@@/@@SPEC@@/index.scss" />
		<link
			rel="stylesheet"
			href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
			integrity="sha512-9usAa10IRO0HhonpyAIVpjrylPvoDwiPUiKdWk5t3PyolY1cOd4DSE0Ga+ri4AuTroPR5aQvXU9xC6qOPnzFeg=="
			crossorigin="anonymous"
			referrerpolicy="no-referrer" />

		<!-- Load the top-level ui/index.ts -->
		<script src="../../index.ts" type="module"></script>
		<script src="./index.ts" type="module"></script>

		<!-- Global site tag (gtag.js) - Google Analytics -->
		<script async src="https://www.googletagmanager.com/gtag/js?id=G-T3HBB0LXNX"></script>
		<script>
			window.dataLayer = window.dataLayer || [];
			function gtag() {
				dataLayer.push(arguments);
			}
			gtag('js', new Date());
			gtag('config', 'G-YEJ8MEHGV2');
		</script>
	</head>

	<body id="bootstrap-overrides">
		<!-- Load wowhead scripts after done loading everything else -->
		<script>
			const whTooltips = { colorLinks: true };
		</script>
		<script src="https://wow.zamimg.com/js/tooltips.js"></script>
		<script type="module">
			import { initLocalization } from '../../i18n/localization.tsx';
			initLocalization();
		</script>
	</body>
</html>
