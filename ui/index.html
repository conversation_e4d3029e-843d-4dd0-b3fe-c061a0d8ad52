<!DOCTYPE html>
<html lang="" data-i18n-lang>
	<head>
		<title id="title" data-i18n="landing.home.title">Welcome to WoWSims MOP</title>
		<meta charset="utf-8">

		<link rel="icon" type="image/x-icon" href="/mop/assets/favicon_io/favicon.ico">

		<meta name="description" content="" data-i18n="landing.home.description">
		<meta name="viewport" content="width=device-width, initial-scale=1.0">

		<link rel="stylesheet" href="/scss/homepage/index.scss">
		<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" integrity="sha512-9usAa10IRO0HhonpyAIVpjrylPvoDwiPUiKdWk5t3PyolY1cOd4DSE0Ga+ri4AuTroPR5aQvXU9xC6qOPnzFeg==" crossorigin="anonymous" referrerpolicy="no-referrer" />
	</head>
	<body class="bg-black">
		<div class="homepage-image"></div>
		<div id="homepage">
			<header class="homepage-header">
				<div class="container homepage-header-container">
					<nav class="navbar navbar-dark navbar-expand-md flex-wrap align-items-end w-100">
						<div class="navbar-brand-container order-0">
							<a href="#" class="navbar-brand d-flex align-items-center p-0 m-0">
								<img class="wowsims-logo" src="/mop/assets/img/WoW-Simulator-Icon.png">
								<div class="d-flex flex-column">
									<h2 class="wowsims-title" data-i18n="landing.header.wowsims"></h2>
									<h3 class="expansion-title w-100" data-i18n="landing.header.expansion"></h3>
								</div>
							</a>
							<button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#homepageHeaderCollapse" aria-controls="homepageHeaderCollapse" aria-expanded="false" aria-label="" data-i18n="landing.navigation.toggle">
								<i class="fas fa-bars fa-2x"></i>
							</button>
						</div>
						<div id="homepageHeaderCollapse" class="collapse navbar-collapse homepage-header-collapse order-2 order-md-1">
							<div class="navbar-nav">
								<button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#homepageHeaderCollapse" aria-controls="homepageHeaderCollapse" aria-expanded="false" aria-label="" data-i18n="landing.navigation.toggle">
									<i class="fas fa-times fa-2x"></i>
								</button>
								<a href="https://discord.gg/p3DgvmnDCS" target="_blank" class="nav-link link-alt">
									<p class="m-0"><i class="discord-link fab fa-discord fa-2x"></i>&nbsp;</p>
								</a>
								<a href="https://github.com/wowsims/mop" target="_blank" class="nav-link link-alt">
									<p class="m-0"><i class="github-link fab fa-github fa-2x"></i>&nbsp;</p>
								</a>
								<a href="https://patreon.com/wowsims" target="_blank" class="nav-link link-alt">
									<i class="patreon-link fab fa-patreon fa-2x me-2"></i>
									<span class="fs-4 d-md-none d-lg-block" data-i18n="landing.header.supportDevs"></span>
								</a>
								<div class="nav-item dropdown">
									<button class="nav-link dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false" id="languageDropdown">
										<i class="fas fa-globe fa-2x"></i>
									</button>
									<ul class="dropdown-menu dropdown-menu-end" aria-labelledby="languageDropdown">
									</ul>
								</div>
							</div>
						</div>
					</nav>
				</div>
			</header>
			<main>
				<div class="container homepage-content-container">
					<div class="info-container">
						<p id="description" class="wowsims-info" data-i18n="landing.home.welcomeDescription"></p>
					</div>
					<div class="sim-links-container">
						<!-- <div class="sim-links">
							<a href="/mop/raid/" class="sim-link raid-sim-link text-white">
								<div class="sim-link-content">
									<img src="/mop/assets/img/raid_icon.png" class="sim-link-icon">
									<span class="sim-link-title" data-i18n="landing.simulations.full_raid">Full Raid Sim</span>
								</div>
							</a>
						</div> -->

						<div class="sim-links">
							<div class="dropend sim-link-dropdown">
								<button class="sim-link text-death-knight" data-bs-toggle="dropdown" aria-expanded="false">
									<div class="sim-link-content">
										<img src="https://wow.zamimg.com/images/wow/icons/large/class_deathknight.jpg" class="sim-link-icon border-death-knight">
										<div class="d-flex flex-column">
											<span class="sim-link-title" data-i18n="common.classes.death_knight"></span>
											<span class="launch-status-label text-brand" data-i18n="common.status.alpha"></span>
										</div>
									</div>
								</button>
								<ul class="dropdown-menu w-100">
									<li>
										<a href="/mop/death_knight/blood/" class="sim-link text-death-knight">
											<div class="sim-link-content">
												<img src="https://wow.zamimg.com/images/wow/icons/large/spell_deathknight_bloodpresence.jpg" class="sim-link-icon">
												<div class="d-flex flex-column">
													<span class="sim-link-label" data-i18n="common.classes.death_knight"></span>
													<span class="sim-link-title" data-i18n="common.specs.death_knight.blood"></span>
													<span class="launch-status-label text-brand" data-i18n="common.status.alpha"></span>
												</div>
											</div>
										</a>
									</li>
									<li>
										<a href="/mop/death_knight/frost/" class="sim-link text-death-knight">
											<div class="sim-link-content">
												<img src="https://wow.zamimg.com/images/wow/icons/large/spell_deathknight_frostpresence.jpg" class="sim-link-icon">
												<div class="d-flex flex-column">
													<span class="sim-link-label" data-i18n="common.classes.death_knight"></span>
													<span class="sim-link-title" data-i18n="common.specs.death_knight.frost"></span>
													<span class="launch-status-label text-brand" data-i18n="common.status.alpha"></span>
												</div>
											</div>
										</a>
									</li>
									<li>
										<a href="/mop/death_knight/unholy/" class="sim-link text-death-knight">
											<div class="sim-link-content">
												<img src="https://wow.zamimg.com/images/wow/icons/large/spell_deathknight_unholypresence.jpg" class="sim-link-icon">
												<div class="d-flex flex-column">
													<span class="sim-link-label" data-i18n="common.classes.death_knight"></span>
													<span class="sim-link-title" data-i18n="common.specs.death_knight.unholy"></span>
													<span class="launch-status-label text-brand" data-i18n="common.status.alpha"></span>
												</div>
											</div>
										</a>
									</li>
								</ul>
							</div>

							<div class="dropend sim-link-dropdown">
								<button class="sim-link text-priest" data-bs-toggle="dropdown" aria-expanded="false">
									<div class="sim-link-content">
										<img src="https://wow.zamimg.com/images/wow/icons/large/class_priest.jpg" class="sim-link-icon border-priest">
										<div class="d-flex flex-column">
											<span class="sim-link-title" data-i18n="common.classes.priest"></span>
											<span class="launch-status-label text-brand" data-i18n="common.status.alpha"></span>
										</div>
									</div>
								</button>
								<ul class="dropdown-menu w-100">
									<li>
										<a href="/mop/priest/discipline/" class="sim-link text-priest">
											<div class="sim-link-content">
												<img src="https://wow.zamimg.com/images/wow/icons/large/spell_holy_powerwordshield.jpg" class="sim-link-icon">
												<div class="d-flex flex-column">
													<span class="sim-link-label" data-i18n="common.classes.priest"></span>
													<span class="sim-link-title" data-i18n="common.specs.priest.discipline"></span>
													<span class="launch-status-label text-brand" data-i18n="common.status.unlaunched"></span>
												</div>
											</div>
										</a>
									</li>
									<li>
										<a href="/mop/priest/holy/" class="sim-link text-priest">
											<div class="sim-link-content">
												<img src="https://wow.zamimg.com/images/wow/icons/large/spell_holy_guardianspirit.jpg" class="sim-link-icon">
												<div class="d-flex flex-column">
													<span class="sim-link-label" data-i18n="common.classes.priest"></span>
													<span class="sim-link-title" data-i18n="common.specs.priest.holy"></span>
													<span class="launch-status-label text-brand" data-i18n="common.status.unlaunched"></span>
												</div>
											</div>
										</a>
									</li>
									<li>
										<a href="/mop/priest/shadow/" class="sim-link text-priest">
											<div class="sim-link-content">
												<img src="https://wow.zamimg.com/images/wow/icons/large/spell_shadow_shadowwordpain.jpg" class="sim-link-icon">
												<div class="d-flex flex-column">
													<span class="sim-link-label" data-i18n="common.classes.priest"></span>
													<span class="sim-link-title" data-i18n="common.specs.priest.shadow"></span>
													<span class="launch-status-label text-brand" data-i18n="common.status.alpha"></span>
												</div>
											</div>
										</a>
									</li>
								</ul>
							</div>

							<div class="dropend sim-link-dropdown">
								<button class="sim-link text-druid" data-bs-toggle="dropdown" aria-expanded="false">
									<div class="sim-link-content">
										<img src="https://wow.zamimg.com/images/wow/icons/large/class_druid.jpg" class="sim-link-icon border-druid">
										<div class="d-flex flex-column">
											<span class="sim-link-title" data-i18n="common.classes.druid"></span>
											<span class="launch-status-label text-brand" data-i18n="common.status.alpha"></span>
										</div>
									</div>
								</button>
								<ul class="dropdown-menu w-100">
									<li>
										<a href="/mop/druid/balance/" class="sim-link text-druid">
											<div class="sim-link-content">
												<img src="https://wow.zamimg.com/images/wow/icons/large/spell_nature_starfall.jpg" class="sim-link-icon">
												<div class="d-flex flex-column">
													<span class="sim-link-label" data-i18n="common.classes.druid"></span>
													<span class="sim-link-title" data-i18n="common.specs.druid.balance"></span>
													<span class="launch-status-label text-brand" data-i18n="common.status.alpha"></span>
												</div>
											</div>
										</a>
									</li>
									<li>
										<a href="/mop/druid/feral/" class="sim-link text-druid">
											<div class="sim-link-content">
												<img src="https://wow.zamimg.com/images/wow/icons/large/ability_druid_catform.jpg" class="sim-link-icon">
												<div class="d-flex flex-column">
													<span class="sim-link-label" data-i18n="common.classes.druid"></span>
													<span class="sim-link-title" data-i18n="common.specs.druid.feral"></span>
													<span class="launch-status-label text-brand" data-i18n="common.status.alpha"></span>
												</div>
											</div>
										</a>
									</li>
									<li>
										<a href="/mop/druid/guardian/" class="sim-link text-druid">
											<div class="sim-link-content">
												<img src="https://wow.zamimg.com/images/wow/icons/large/ability_racial_bearform.jpg" class="sim-link-icon">
												<div class="d-flex flex-column">
													<span class="sim-link-label" data-i18n="common.classes.druid"></span>
													<span class="sim-link-title" data-i18n="common.specs.druid.guardian"></span>
													<span class="launch-status-label text-brand" data-i18n="common.status.alpha"></span>
												</div>
											</div>
										</a>
									</li>
									<li>
										<a href="/mop/druid/restoration/" class="sim-link text-druid">
											<div class="sim-link-content">
												<img src="https://wow.zamimg.com/images/wow/icons/large/spell_nature_healingtouch.jpg" class="sim-link-icon">
												<div class="d-flex flex-column">
													<span class="sim-link-label" data-i18n="common.classes.druid"></span>
													<span class="sim-link-title" data-i18n="common.specs.druid.restoration"></span>
													<span class="launch-status-label text-brand" data-i18n="common.status.unlaunched"></span>
												</div>
											</div>
										</a>
									</li>
								</ul>
							</div>

							<div class="dropend sim-link-dropdown">
								<button class="sim-link text-rogue" data-bs-toggle="dropdown" aria-expanded="false">
									<div class="sim-link-content">
										<img src="https://wow.zamimg.com/images/wow/icons/large/class_rogue.jpg" class="sim-link-icon border-rogue">
										<div class="d-flex flex-column">
											<span class="sim-link-title" data-i18n="common.classes.rogue"></span>
											<span class="launch-status-label text-brand" data-i18n="common.status.alpha"></span>
										</div>
									</div>
								</button>
								<ul class="dropdown-menu w-100">
									<li>
										<a href="/mop/rogue/assassination/" class="sim-link text-rogue">
											<div class="sim-link-content">
												<img src="https://wow.zamimg.com/images/wow/icons/large/ability_rogue_eviscerate.jpg" class="sim-link-icon">
												<div class="d-flex flex-column">
													<span class="sim-link-label" data-i18n="common.classes.rogue"></span>
													<span class="sim-link-title" data-i18n="common.specs.rogue.assassination"></span>
													<span class="launch-status-label text-brand" data-i18n="common.status.alpha"></span>
												</div>
											</div>
										</a>
									</li>
									<li>
										<a href="/mop/rogue/combat/" class="sim-link text-rogue">
											<div class="sim-link-content">
												<img src="https://wow.zamimg.com/images/wow/icons/large/ability_backstab.jpg" class="sim-link-icon">
												<div class="d-flex flex-column">
													<span class="sim-link-label" data-i18n="common.classes.rogue"></span>
													<span class="sim-link-title" data-i18n="common.specs.rogue.combat"></span>
													<span class="launch-status-label text-brand" data-i18n="common.status.alpha"></span>
												</div>
											</div>
										</a>
									</li>
									<li>
										<a href="/mop/rogue/subtlety/" class="sim-link text-rogue">
											<div class="sim-link-content">
												<img src="https://wow.zamimg.com/images/wow/icons/large/ability_stealth.jpg" class="sim-link-icon">
												<div class="d-flex flex-column">
													<span class="sim-link-label" data-i18n="common.classes.rogue"></span>
													<span class="sim-link-title" data-i18n="common.specs.rogue.subtlety"></span>
													<span class="launch-status-label text-brand" data-i18n="common.status.alpha"></span>
												</div>
											</div>
										</a>
									</li>
								</ul>
							</div>

							<div class="dropend sim-link-dropdown">
								<button class="sim-link text-hunter" data-bs-toggle="dropdown" aria-expanded="false">
									<div class="sim-link-content">
										<img src="https://wow.zamimg.com/images/wow/icons/large/class_hunter.jpg" class="sim-link-icon border-hunter">
										<div class="d-flex flex-column">
											<span class="sim-link-title" data-i18n="common.classes.hunter"></span>
											<span class="launch-status-label text-brand" data-i18n="common.status.alpha"></span>
										</div>
									</div>
								</button>
								<ul class="dropdown-menu w-100">
									<li>
										<a href="/mop/hunter/beast_mastery/" class="sim-link text-hunter">
											<div class="sim-link-content">
												<img src="https://wow.zamimg.com/images/wow/icons/large/ability_hunter_bestialdiscipline.jpg" class="sim-link-icon">
												<div class="d-flex flex-column">
													<span class="sim-link-label" data-i18n="common.classes.hunter"></span>
													<span class="sim-link-title" data-i18n="common.specs.hunter.beast_mastery"></span>
													<span class="launch-status-label text-brand" data-i18n="common.status.alpha"></span>
												</div>
											</div>
										</a>
									</li>
									<li>
										<a href="/mop/hunter/marksmanship/" class="sim-link text-hunter">
											<div class="sim-link-content">
												<img src="https://wow.zamimg.com/images/wow/icons/large/ability_hunter_focusedaim.jpg" class="sim-link-icon">
												<div class="d-flex flex-column">
													<span class="sim-link-label" data-i18n="common.classes.hunter"></span>
													<span class="sim-link-title" data-i18n="common.specs.hunter.marksmanship"></span>
													<span class="launch-status-label text-brand" data-i18n="common.status.alpha"></span>
												</div>
											</div>
										</a>
									</li>
									<li>
										<a href="/mop/hunter/survival/" class="sim-link text-hunter">
											<div class="sim-link-content">
												<img src="https://wow.zamimg.com/images/wow/icons/large/ability_hunter_camouflage.jpg" class="sim-link-icon">
												<div class="d-flex flex-column">
													<span class="sim-link-label" data-i18n="common.classes.hunter"></span>
													<span class="sim-link-title" data-i18n="common.specs.hunter.survival"></span>
													<span class="launch-status-label text-brand" data-i18n="common.status.alpha"></span>
												</div>
											</div>
										</a>
									</li>
								</ul>
							</div>

							<div class="dropend sim-link-dropdown">
								<button class="sim-link text-shaman" data-bs-toggle="dropdown" aria-expanded="false">
									<div class="sim-link-content">
										<img src="https://wow.zamimg.com/images/wow/icons/large/class_shaman.jpg" class="sim-link-icon border-shaman">
										<div class="d-flex flex-column">
											<span class="sim-link-title" data-i18n="common.classes.shaman"></span>
											<span class="launch-status-label text-brand" data-i18n="common.status.alpha"></span>
										</div>
									</div>
								</button>
								<ul class="dropdown-menu w-100">
									<li>
										<a href="/mop/shaman/elemental/" class="sim-link text-shaman">
											<div class="sim-link-content">
												<img src="https://wow.zamimg.com/images/wow/icons/large/spell_nature_lightning.jpg" class="sim-link-icon">
												<div class="d-flex flex-column">
													<span class="sim-link-label" data-i18n="common.classes.shaman"></span>
													<span class="sim-link-title" data-i18n="common.specs.shaman.elemental"></span>
													<span class="launch-status-label text-brand" data-i18n="common.status.alpha"></span>
												</div>
											</div>
										</a>
									</li>
									<li>
										<a href="/mop/shaman/enhancement/" class="sim-link text-shaman">
											<div class="sim-link-content">
												<img src="https://wow.zamimg.com/images/wow/icons/large/spell_nature_lightningshield.jpg" class="sim-link-icon">
												<div class="d-flex flex-column">
													<span class="sim-link-label" data-i18n="common.classes.shaman"></span>
													<span class="sim-link-title" data-i18n="common.specs.shaman.enhancement"></span>
													<span class="launch-status-label text-brand" data-i18n="common.status.alpha"></span>
												</div>
											</div>
										</a>
									</li>
									<li>
										<a href="/mop/shaman/restoration/" class="sim-link text-shaman">
											<div class="sim-link-content">
												<img src="https://wow.zamimg.com/images/wow/icons/large/spell_nature_magicimmunity.jpg" class="sim-link-icon">
												<div class="d-flex flex-column">
													<span class="sim-link-label" data-i18n="common.classes.shaman"></span>
													<span class="sim-link-title" data-i18n="common.specs.shaman.restoration"></span>
													<span class="launch-status-label text-brand" data-i18n="common.status.unlaunched"></span>
												</div>
											</div>
										</a>
									</li>
								</ul>
							</div>

							<div class="dropend sim-link-dropdown">
								<button class="sim-link text-mage" data-bs-toggle="dropdown" aria-expanded="false">
									<div class="sim-link-content">
										<img src="https://wow.zamimg.com/images/wow/icons/large/class_mage.jpg" class="sim-link-icon border-mage">
										<div class="d-flex flex-column">
											<span class="sim-link-title" data-i18n="common.classes.mage"></span>
											<span class="launch-status-label text-brand" data-i18n="common.status.alpha"></span>
										</div>
									</div>
								</button>
								<ul class="dropdown-menu w-100">
									<li>
										<a href="/mop/mage/arcane/" class="sim-link text-mage">
											<div class="sim-link-content">
												<img src="https://wow.zamimg.com/images/wow/icons/large/spell_holy_magicalsentry.jpg" class="sim-link-icon">
												<div class="d-flex flex-column">
													<span class="sim-link-label" data-i18n="common.classes.mage"></span>
													<span class="sim-link-title" data-i18n="common.specs.mage.arcane"></span>
													<span class="launch-status-label text-brand" data-i18n="common.status.alpha"></span>
												</div>
											</div>
										</a>
									</li>
									<li>
										<a href="/mop/mage/fire/" class="sim-link text-mage">
											<div class="sim-link-content">
												<img src="https://wow.zamimg.com/images/wow/icons/large/spell_fire_firebolt02.jpg" class="sim-link-icon">
												<div class="d-flex flex-column">
													<span class="sim-link-label" data-i18n="common.classes.mage"></span>
													<span class="sim-link-title" data-i18n="common.specs.mage.fire"></span>
													<span class="launch-status-label text-brand" data-i18n="common.status.alpha"></span>
												</div>
											</div>
										</a>
									</li>
									<li>
										<a href="/mop/mage/frost/" class="sim-link text-mage">
											<div class="sim-link-content">
												<img src="https://wow.zamimg.com/images/wow/icons/large/spell_frost_frostbolt02.jpg" class="sim-link-icon">
												<div class="d-flex flex-column">
													<span class="sim-link-label" data-i18n="common.classes.mage"></span>
													<span class="sim-link-title" data-i18n="common.specs.mage.frost"></span>
													<span class="launch-status-label text-brand" data-i18n="common.status.alpha"></span>
												</div>
											</div>
										</a>
									</li>
								</ul>
							</div>

							<div class="dropend sim-link-dropdown">
								<button class="sim-link text-monk" data-bs-toggle="dropdown" aria-expanded="false">
									<div class="sim-link-content">
										<img src="https://wow.zamimg.com/images/wow/icons/large/class_monk.jpg" class="sim-link-icon border-monk">
										<div class="d-flex flex-column">
											<span class="sim-link-title" data-i18n="common.classes.monk"></span>
											<span class="launch-status-label text-brand" data-i18n="common.status.alpha"></span>
										</div>
									</div>
								</button>
								<ul class="dropdown-menu w-100">
									<li>
										<a href="/mop/monk/brewmaster/" class="sim-link text-monk">
											<div class="sim-link-content">
												<img src="https://wow.zamimg.com/images/wow/icons/large/spell_monk_brewmaster_spec.jpg" class="sim-link-icon">
												<div class="d-flex flex-column">
													<span class="sim-link-label" data-i18n="common.classes.monk"></span>
													<span class="sim-link-title" data-i18n="common.specs.monk.brewmaster"></span>
													<span class="launch-status-label text-brand" data-i18n="common.status.alpha"></span>
												</div>
											</div>
										</a>
									</li>
									<li>
										<a href="/mop/monk/mistweaver/" class="sim-link text-monk">
											<div class="sim-link-content">
												<img src="https://wow.zamimg.com/images/wow/icons/large/spell_monk_mistweaver_spec.jpg" class="sim-link-icon">
												<div class="d-flex flex-column">
													<span class="sim-link-label" data-i18n="common.classes.monk"></span>
													<span class="sim-link-title" data-i18n="common.specs.monk.mistweaver"></span>
													<span class="launch-status-label text-brand" data-i18n="common.status.unlaunched"></span>
												</div>
											</div>
										</a>
									</li>
									<li>
										<a href="/mop/monk/windwalker/" class="sim-link text-monk">
											<div class="sim-link-content">
												<img src="https://wow.zamimg.com/images/wow/icons/large/spell_monk_windwalker_spec.jpg" class="sim-link-icon">
												<div class="d-flex flex-column">
													<span class="sim-link-label" data-i18n="common.classes.monk"></span>
													<span class="sim-link-title" data-i18n="common.specs.monk.windwalker"></span>
													<span class="launch-status-label text-brand" data-i18n="common.status.alpha"></span>
												</div>
											</div>
										</a>
									</li>
								</ul>
							</div>

							<div class="dropend sim-link-dropdown">
								<button class="sim-link text-warlock" data-bs-toggle="dropdown" aria-expanded="false">
									<div class="sim-link-content">
										<img src="https://wow.zamimg.com/images/wow/icons/large/class_warlock.jpg" class="sim-link-icon border-warlock">
										<div class="d-flex flex-column">
											<span class="sim-link-title" data-i18n="common.classes.warlock"></span>
											<span class="launch-status-label text-brand" data-i18n="common.status.alpha"></span>
										</div>
									</div>
								</button>
								<ul class="dropdown-menu w-100">
									<li>
										<a href="/mop/warlock/affliction/" class="sim-link text-warlock">
											<div class="sim-link-content">
												<img src="https://wow.zamimg.com/images/wow/icons/large/spell_shadow_deathcoil.jpg" class="sim-link-icon">
												<div class="d-flex flex-column">
													<span class="sim-link-label" data-i18n="common.classes.warlock"></span>
													<span class="sim-link-title" data-i18n="common.specs.warlock.affliction"></span>
													<span class="launch-status-label text-brand" data-i18n="common.status.alpha"></span>
												</div>
											</div>
										</a>
									</li>
									<li>
										<a href="/mop/warlock/demonology/" class="sim-link text-warlock">
											<div class="sim-link-content">
												<img src="https://wow.zamimg.com/images/wow/icons/large/spell_shadow_metamorphosis.jpg" class="sim-link-icon">
												<div class="d-flex flex-column">
													<span class="sim-link-label" data-i18n="common.classes.warlock"></span>
													<span class="sim-link-title" data-i18n="common.specs.warlock.demonology"></span>
													<span class="launch-status-label text-brand" data-i18n="common.status.alpha"></span>
												</div>
											</div>
										</a>
									</li>
									<li>
										<a href="/mop/warlock/destruction/" class="sim-link text-warlock">
											<div class="sim-link-content">
												<img src="https://wow.zamimg.com/images/wow/icons/large/spell_shadow_rainoffire.jpg" class="sim-link-icon">
												<div class="d-flex flex-column">
													<span class="sim-link-label" data-i18n="common.classes.warlock"></span>
													<span class="sim-link-title" data-i18n="common.specs.warlock.destruction"></span>
													<span class="launch-status-label text-brand" data-i18n="common.status.alpha"></span>
												</div>
											</div>
										</a>
									</li>
								</ul>
							</div>

							<div class="dropend sim-link-dropdown">
								<button class="sim-link text-paladin" data-bs-toggle="dropdown" aria-expanded="false">
									<div class="sim-link-content">
										<img src="https://wow.zamimg.com/images/wow/icons/large/class_paladin.jpg" class="sim-link-icon border-paladin">
										<div class="d-flex flex-column">
											<span class="sim-link-title" data-i18n="common.classes.paladin"></span>
											<span class="launch-status-label text-brand" data-i18n="common.status.alpha"></span>
										</div>
									</div>
								</button>
								<ul class="dropdown-menu w-100">
									<li>
										<a href="/mop/paladin/holy/" class="sim-link text-paladin">
											<div class="sim-link-content">
												<img src="https://wow.zamimg.com/images/wow/icons/large/spell_holy_holybolt.jpg" class="sim-link-icon">
												<div class="d-flex flex-column">
													<span class="sim-link-label" data-i18n="common.classes.paladin"></span>
													<span class="sim-link-title" data-i18n="common.specs.paladin.holy"></span>
													<span class="launch-status-label text-brand" data-i18n="common.status.unlaunched"></span>
												</div>
											</div>
										</a>
									</li>
									<li>
										<a href="/mop/paladin/protection/" class="sim-link text-paladin">
											<div class="sim-link-content">
												<img src="https://wow.zamimg.com/images/wow/icons/large/ability_paladin_shieldofthetemplar.jpg" class="sim-link-icon">
												<div class="d-flex flex-column">
													<span class="sim-link-label" data-i18n="common.classes.paladin"></span>
													<span class="sim-link-title" data-i18n="common.specs.paladin.protection"></span>
													<span class="launch-status-label text-brand" data-i18n="common.status.alpha"></span>
												</div>
											</div>
										</a>
									</li>
									<li>
										<a href="/mop/paladin/retribution/" class="sim-link text-paladin">
											<div class="sim-link-content">
												<img src="https://wow.zamimg.com/images/wow/icons/large/spell_holy_auraoflight.jpg" class="sim-link-icon">
												<div class="d-flex flex-column">
													<span class="sim-link-label" data-i18n="common.classes.paladin"></span>
													<span class="sim-link-title" data-i18n="common.specs.paladin.retribution"></span>
													<span class="launch-status-label text-brand" data-i18n="common.status.alpha"></span>
												</div>
											</div>
										</a>
									</li>
								</ul>
							</div>

							<div class="dropend sim-link-dropdown">
								<button class="sim-link text-warrior" data-bs-toggle="dropdown" aria-expanded="false">
									<div class="sim-link-content">
										<img src="https://wow.zamimg.com/images/wow/icons/large/class_warrior.jpg" class="sim-link-icon border-warrior">
										<div class="d-flex flex-column">
											<span class="sim-link-title" data-i18n="common.classes.warrior"></span>
											<span class="launch-status-label text-brand" data-i18n="common.status.alpha"></span>
										</div>
									</div>
								</button>
								<ul class="dropdown-menu w-100">
									<li>
										<a href="/mop/warrior/arms/" class="sim-link text-warrior">
											<div class="sim-link-content">
												<img src="https://wow.zamimg.com/images/wow/icons/large/ability_warrior_savageblow.jpg" class="sim-link-icon">
												<div class="d-flex flex-column">
													<span class="sim-link-label" data-i18n="common.classes.warrior"></span>
													<span class="sim-link-title" data-i18n="common.specs.warrior.arms"></span>
													<span class="launch-status-label text-brand" data-i18n="common.status.alpha"></span>
												</div>
											</div>
										</a>
									</li>
									<li>
										<a href="/mop/warrior/fury/" class="sim-link text-warrior">
											<div class="sim-link-content">
												<img src="https://wow.zamimg.com/images/wow/icons/large/ability_warrior_innerrage.jpg" class="sim-link-icon">
												<div class="d-flex flex-column">
													<span class="sim-link-label" data-i18n="common.classes.warrior"></span>
													<span class="sim-link-title" data-i18n="common.specs.warrior.fury"></span>
													<span class="launch-status-label text-brand" data-i18n="common.status.alpha"></span>
												</div>
											</div>
										</a>
									</li>
									<li>
										<a href="/mop/warrior/protection/" class="sim-link text-warrior">
											<div class="sim-link-content">
												<img src="https://wow.zamimg.com/images/wow/icons/large/ability_warrior_defensivestance.jpg" class="sim-link-icon">
												<div class="d-flex flex-column">
													<span class="sim-link-label" data-i18n="common.classes.warrior"></span>
													<span class="sim-link-title" data-i18n="common.specs.warrior.protection"></span>
													<span class="launch-status-label text-brand" data-i18n="common.status.alpha"></span>
												</div>
											</div>
										</a>
									</li>
								</ul>
							</div>
						</div>
					</div>
				</div>
			</main>
    	</div>
		<script src="./index.ts" type="module"></script>
		<script async src="https://www.googletagmanager.com/gtag/js?id=G-T3HBB0LXNX"></script>
		<script>
			window.dataLayer = window.dataLayer || [];
			function gtag(){dataLayer.push(arguments);}
			gtag('js', new Date());
			gtag('config', 'G-YEJ8MEHGV2');
		</script>
		<script type="module">
			import { initLocalization } from './i18n/localization.tsx';
			initLocalization();
		</script>
	</body>
</html>
