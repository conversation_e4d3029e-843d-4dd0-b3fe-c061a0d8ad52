{"type": "TypeAPL", "prepullActions": [{"action": {"castSpell": {"spellId": {"spellId": 2894}}}, "doAtValue": {"const": {"val": "-3.2s"}}}, {"action": {"castSpell": {"spellId": {"otherId": "OtherActionPotion"}}}, "doAtValue": {"const": {"val": "-1.7s"}}}, {"action": {"castSpell": {"spellId": {"spellId": 117014}}}, "doAtValue": {"const": {"val": "-1.7s"}}}, {"action": {"castSpell": {"spellId": {"spellId": 51505}}}, "doAtValue": {"const": {"val": "-1.7s"}}}], "priorityList": [{"action": {"condition": {"auraIsKnown": {"auraId": {"spellId": 117012}}}, "castSpell": {"spellId": {"spellId": 73680}}}}, {"action": {"condition": {"auraIsKnown": {"auraId": {"spellId": 120668, "tag": -1}}}, "castSpell": {"spellId": {"spellId": 120668, "tag": -1}}}}, {"action": {"condition": {"and": {"vals": [{"not": {"val": {"auraIsActive": {"auraId": {"spellId": 120668, "tag": -1}}}}}, {"not": {"val": {"auraIsActive": {"auraId": {"spellId": 114049}}}}}]}}, "castSpell": {"spellId": {"spellId": 120668}}}}, {"action": {"autocastOtherCooldowns": {}}}, {"action": {"condition": {"cmp": {"op": "OpLt", "lhs": {"currentTime": {}}, "rhs": {"const": {"val": "5"}}}}, "castSpell": {"spellId": {"spellId": 16166}}}}, {"action": {"condition": {"or": {"vals": [{"cmp": {"op": "OpLe", "lhs": {"remainingTime": {}}, "rhs": {"const": {"val": "26"}}}}, {"and": {"vals": [{"auraIsActive": {"auraId": {"spellId": 16166}}}, {"cmp": {"op": "OpLe", "lhs": {"remainingTime": {}}, "rhs": {"const": {"val": "105"}}}}]}}, {"and": {"vals": [{"auraIsActive": {"auraId": {"spellId": 16166}}}, {"cmp": {"op": "OpLe", "lhs": {"spellTimeToReady": {"spellId": {"spellId": 114049}}}, "rhs": {"const": {"val": "10"}}}}]}}, {"and": {"vals": [{"auraIsActive": {"auraId": {"spellId": 16166}}}, {"auraIsActive": {"auraId": {"spellId": 114049}}}]}}, {"auraIsActive": {"auraId": {"spellId": 114049}}}]}}, "castSpell": {"spellId": {"itemId": 76093}}}}, {"action": {"condition": {"or": {"vals": [{"cmp": {"op": "OpGe", "lhs": {"spellTimeToReady": {"spellId": {"spellId": 114049}}}, "rhs": {"const": {"val": "85s"}}}}, {"cmp": {"op": "OpLe", "lhs": {"spellTimeToReady": {"spellId": {"spellId": 114049}}}, "rhs": {"const": {"val": "4"}}}}]}}, "castSpell": {"spellId": {"spellId": 16166}}}}, {"action": {"condition": {"cmp": {"op": "OpGt", "lhs": {"dotRemainingTime": {"spellId": {"spellId": 8050}}}, "rhs": {"spellCastTime": {"spellId": {"spellId": 51505}}}}}, "castSpell": {"spellId": {"spellId": 51505}}}}, {"action": {"condition": {"cmp": {"op": "OpGt", "lhs": {"dotRemainingTime": {"spellId": {"spellId": 8050}}}, "rhs": {"const": {"val": "15"}}}}, "castSpell": {"spellId": {"spellId": 114049}}}}, {"action": {"condition": {"cmp": {"op": "OpLt", "lhs": {"dotRemainingTime": {"spellId": {"spellId": 8050}}}, "rhs": {"const": {"val": "2"}}}}, "multidot": {"spellId": {"spellId": 8050}, "maxDots": 1, "maxOverlap": {"const": {"val": "2s"}}}}}, {"action": {"condition": {"spellIsKnown": {"spellId": {"spellId": 117014}}}, "castSpell": {"spellId": {"spellId": 117014}}}}, {"action": {"condition": {"and": {"vals": [{"cmp": {"op": "OpGe", "lhs": {"auraNumStacks": {"auraId": {"spellId": 324}}}, "rhs": {"const": {"val": "7"}}}}, {"not": {"val": {"and": {"vals": [{"cmp": {"op": "OpLe", "lhs": {"dotRemainingTime": {"spellId": {"spellId": 8050}}}, "rhs": {"const": {"val": "6s"}}}}, {"cmp": {"op": "OpLe", "lhs": {"spellTimeToReady": {"spellId": {"spellId": 114049}}}, "rhs": {"const": {"val": "6s"}}}}]}}}}]}}, "castSpell": {"spellId": {"spellId": 8042}}}}, {"action": {"condition": {"and": {"vals": [{"not": {"val": {"auraIsActive": {"auraId": {"spellId": 2894}}}}}, {"not": {"val": {"dotIsActive": {"spellId": {"spellId": 3599}}}}}]}}, "castSpell": {"spellId": {"spellId": 3599}}}}, {"action": {"condition": {"or": {"vals": [{"and": {"vals": [{"cmp": {"op": "OpLe", "lhs": {"remainingTime": {}}, "rhs": {"const": {"val": "150"}}}}, {"cmp": {"op": "OpLe", "lhs": {"spellTimeToReady": {"spellId": {"spellId": 114049}}}, "rhs": {"const": {"val": "5"}}}}]}}, {"and": {"vals": [{"cmp": {"op": "OpGt", "lhs": {"remainingTime": {}}, "rhs": {"const": {"val": "180"}}}}]}}, {"cmp": {"op": "OpLt", "lhs": {"remainingTime": {}}, "rhs": {"const": {"val": "35"}}}}]}}, "castSpell": {"spellId": {"spellId": 2894}}}}, {"action": {"condition": {"or": {"vals": [{"and": {"vals": [{"not": {"val": {"auraIsActive": {"auraId": {"spellId": 2894}}}}}, {"cmp": {"op": "OpGe", "lhs": {"remainingTime": {}}, "rhs": {"const": {"val": "5"}}}}, {"cmp": {"op": "OpLe", "lhs": {"spellTimeToReady": {"spellId": {"spellId": 114049}}}, "rhs": {"const": {"val": "20"}}}}, {"cmp": {"op": "OpEq", "lhs": {"shamanFireElementalDuration": {}}, "rhs": {"const": {"val": "60"}}}}]}}, {"and": {"vals": [{"cmp": {"op": "OpLt", "lhs": {"shamanFireElementalDuration": {}}, "rhs": {"const": {"val": "60"}}}}, {"not": {"val": {"auraIsActive": {"auraId": {"spellId": 2894}}}}}, {"cmp": {"op": "OpLt", "lhs": {"spellTimeToReady": {"spellId": {"spellId": 2894}}}, "rhs": {"const": {"val": "65"}}}}]}}, {"cmp": {"op": "OpLe", "lhs": {"remainingTime": {}}, "rhs": {"const": {"val": "62"}}}}]}}, "castSpell": {"spellId": {"spellId": 2062}}}}, {"action": {"castSpell": {"spellId": {"spellId": 403}}}}]}