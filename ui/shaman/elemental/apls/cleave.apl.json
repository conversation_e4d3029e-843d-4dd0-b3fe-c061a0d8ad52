{"type": "TypeAPL", "prepullActions": [{"action": {"castSpell": {"spellId": {"otherId": "OtherActionPotion"}}}, "doAtValue": {"const": {"val": "-2.5s"}}}, {"action": {"castSpell": {"spellId": {"spellId": 2894}}}, "doAtValue": {"const": {"val": "-2.5s"}}}, {"action": {"castSpell": {"spellId": {"spellId": 421}}}, "doAtValue": {"const": {"val": "-1s"}}}], "priorityList": [{"action": {"autocastOtherCooldowns": {}}}, {"action": {"condition": {"cmp": {"op": "OpGt", "lhs": {"dotRemainingTime": {"spellId": {"spellId": 8050}}}, "rhs": {"spellCastTime": {"spellId": {"spellId": 51505}}}}}, "castSpell": {"spellId": {"spellId": 51505}}}}, {"action": {"multidot": {"spellId": {"spellId": 8050}, "maxDots": 2, "maxOverlap": {"const": {"val": "2s"}}}}}, {"action": {"condition": {"cmp": {"op": "OpEq", "lhs": {"auraNumStacks": {"auraId": {"spellId": 324}}}, "rhs": {"const": {"val": "7"}}}}, "castSpell": {"spellId": {"spellId": 8042}}}}, {"action": {"condition": {"and": {"vals": [{"not": {"val": {"auraIsActive": {"auraId": {"spellId": 2894}}}}}, {"not": {"val": {"dotIsActive": {"spellId": {"spellId": 3599}}}}}]}}, "castSpell": {"spellId": {"spellId": 3599}}}}, {"action": {"castSpell": {"spellId": {"spellId": 114074}}}}, {"action": {"castSpell": {"spellId": {"spellId": 421}}}}]}