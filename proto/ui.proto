syntax = "proto3";
package proto;

option go_package = "./proto";

import "api.proto";
import "apl.proto";
import "common.proto";
import "db.proto";
import "spell.proto";
import "paladin.proto";

// Holds all WoW data for the UI.
message UIDatabase {
	repeated UIItem items = 1;
	repeated ItemRandomSuffix random_suffixes = 11;
	repeated UIEnchant enchants = 2;
	repeated UIGem gems = 3;
	repeated PresetEncounter encounters = 6;

	repeated UIZone zones = 8;
	repeated UINPC npcs = 9;

	// Entities for which we just need a name/icon.
	repeated IconData item_icons = 4;
	repeated IconData spell_icons = 5;

	repeated GlyphID glyph_ids = 7;
	repeated ReforgeStat reforge_stats = 12;
	repeated ItemEffectRandPropPoints item_effect_rand_prop_points = 15;

	repeated Consumable consumables = 13;
	repeated SpellEffect spell_effects = 14;
}

message UIZone {
	int32 id = 1;
	string name = 2;
	Expansion expansion = 3;
}
message UINPC {
	int32 id = 1;
	string name = 2;
	int32 zone_id = 3;
}

// Contains all information about an Item needed by the UI.
// Generally this will include everything needed by the sim, plus some
// additional data for displaying / filtering.
// Next tag: 30.
message UIItem {
	int32 id = 1;
	string name = 2;
	string icon = 3;

	ItemType type = 4;
	ArmorType armor_type = 5;
	WeaponType weapon_type = 6;
	HandType hand_type = 7;
	RangedWeaponType ranged_weapon_type = 8;

	repeated double stats = 9;
	repeated GemColor gem_sockets = 10;
	repeated double socketBonus = 11;
	repeated int32 random_suffix_options = 26;
	int32 rand_prop_points = 27;


	// Weapon stats, needed for computing proper EP for melee weapons
	double weapon_damage_min = 12;
	double weapon_damage_max = 13;
	double weapon_speed = 14;

	int32 ilvl = 15;
	int32 phase = 16;
	ItemQuality quality = 17;
	bool unique = 18;
	string name_description = 19;

	// Classes that are allowed to use the item. Empty indicates no special class restrictions.
	repeated Class class_allowlist = 20;
	Profession required_profession = 21;

	// Name of the item set to which this item belongs, if any.
	string set_name = 22;
	// ID of the item set to which this item belongs, if any. (useful for shared pvp set bonuses)
	int32 set_id = 28;

	Expansion expansion = 24;
	repeated UIItemSource sources = 23;

	enum FactionRestriction {
		FACTION_RESTRICTION_UNSPECIFIED = 0;
		FACTION_RESTRICTION_ALLIANCE_ONLY = 1;
		FACTION_RESTRICTION_HORDE_ONLY = 2;
	}

	FactionRestriction faction_restriction = 25;
	map<int32, ScalingItemProperties> scaling_options = 29; // keys are the other ilvl variants that this item could potentially have
	ItemEffect item_effect = 30;
}

enum Expansion {
	ExpansionUnknown = 0;
	ExpansionVanilla = 1;
	ExpansionTbc = 2;
	ExpansionWotlk = 3;
	ExpansionCata = 4;
	ExpansionMop = 5;
}

enum DungeonDifficulty {
	DifficultyUnknown = 0;
	DifficultyNormal = 1;
	DifficultyHeroic = 2;
	DifficultyTitanRuneAlpha = 7;
	DifficultyTitanRuneBeta = 8;
	DifficultyRaid10 = 3;
	DifficultyRaid10H = 4;
	DifficultyRaid25 = 5;
	DifficultyRaid25H = 6;
	DifficultyRaid25RF = 9;
}

enum RepLevel {
	RepLevelUnknown 	= 0;
	RepLevelHated 		= 1;
	RepLevelHostile 	= 2;
	RepLevelUnfriendly 	= 3;
	RepLevelNeutral 	= 4;
	RepLevelFriendly 	= 5;
	RepLevelHonored 	= 6;
	RepLevelRevered 	= 7;
	RepLevelExalted 	= 8;
}

// Use the faction ID for the field index
enum RepFaction {
	RepFactionUnknown = 0;
	RepFactionTheEarthenRing = 1135;
	RepFactionGuardiansOfHyjal = 1158;
	RepFactionTherazane = 1171;
	RepFactionDragonmawClan = 1172;
	RepFactionRamkahen = 1173;
	RepFactionWildhammerClan = 1174;
	RepFactionBaradinsWardens = 1177;
	RepFactionHellscreamsReach = 1178;
	RepFactionAvengersOfHyjal = 1204;

    // MoP Reputations
    RepFactionGoldenLotus              = 1269;
    RepFactionTheTillers               = 1272;
    RepFactionShadoPan                 = 1270;
    RepFactionShadoPanAssault          = 1435;
    RepFactionTheBrewmasters           = 1351;
    RepFactionTheKlaxxi                = 1337;
    RepFactionTheAugustCelestials      = 1341;
    RepFactionTheAnglers               = 1302;
    RepFactionEmperorShaohao           = 1492;
    RepFactionSunreaverOnslaught       = 1388;
    RepFactionKirinTorOffensive        = 1387;
    RepFactionDominanceOffensive       = 1375;
    RepFactionOrderOfTheCloudSerpent   = 1271;
    RepFactionShangXisAcademy          = 1216;
    RepFactionTheLorewalkers           = 1345;
    RepFactionTheBlackPrince           = 1359;
    RepFactionForestHozen              = 1228;
    RepFactionPearlfinJinyu            = 1242;
    RepFactionHozen                    = 1243;
    RepFactionOperationShieldwall      = 1376;

}

message UIItemSource {
	oneof source {
		CraftedSource crafted = 1;
		DropSource drop = 2;
		QuestSource quest = 3;
		SoldBySource sold_by = 4;
		RepSource rep = 5;
	}
}
message CraftedSource {
	Profession profession = 1;
	int32 spell_id = 2;
}
message DropSource {
	DungeonDifficulty difficulty = 1;
	int32 npc_id = 2;
	int32 zone_id = 3;
	string other_name = 4; // For drops in zones from things other than NPCs, e.g. "Trash"
	string category = 5;   // For conditions/categories of drops, e.g. 'Hard Mode' or 'Titan Rune'
}
message QuestSource {
	int32 id = 1;
	string name = 2;
}
message SoldBySource {
	int32 npc_id = 1;
	string npc_name = 2;
	int32 zone_id = 3;
}
message RepSource {
	RepFaction rep_faction_id = 1;
	RepLevel rep_level = 2;
	Faction faction_id = 3;
}

message UIEnchant {
	// All enchants have an effect ID. Some also have an item ID, others have a spell ID,
	// and yet others have both item and spell IDs. No single ID alone is enough to
	// uniquely identify an enchant. Uniqueness requires either effect ID + slot, or
	// effect ID + item/spell ID.
	int32 effect_id = 1; // ID of the effect.
	int32 item_id = 2;   // ID of the enchant "item". Might be 0 if not available.
	int32 spell_id = 3;  // ID of the enchant "spell". Might be 0 if not available.

	string name = 4;
	string icon = 5;

	ItemType type = 6;                  // Which type of item this enchant can be applied to.
	repeated ItemType extra_types = 13; // Extra types for enchants that can go in multiple slots (like armor kits).
	EnchantType enchant_type = 7;

	repeated double stats = 8;

	ItemQuality quality = 9;
	int32 phase = 10;

	// Classes that are allowed to use the enchant. Empty indicates no special class restrictions.
	repeated Class class_allowlist = 11;
	Profession required_profession = 12;
	ItemEffect enchant_effect = 14;
}

message UIGem {
	int32 id = 1;
	string name = 2;
	string icon = 3;
	GemColor color = 4;

	repeated double stats = 5;

	int32 phase = 6;
	ItemQuality quality = 7;
	bool unique = 8;
	Profession required_profession = 9;
	bool disabled_in_challenge_mode = 10;
}

message IconData {
	int32 id = 1;
	string name = 2;
	string icon = 3;
	bool has_buff = 4;
}

message GlyphID {
	int32 item_id = 1;
	int32 spell_id = 2;
}

enum SourceFilterOption {
	SourceUnknown = 0;
	SourceCrafting = 1;
	SourceQuest = 2;
	SourceReputation = 3;
	SourcePvp = 4;
	SourceDungeon = 5;
	SourceDungeonH = 6;
	SourceRaid = 7;
	SourceRaidH = 8;
	SourceRaidRF = 9;
}

enum RaidFilterOption {
	RaidUnknown = 0;
	RaidMogushanVaults = 1;
	RaidHeartOfFear = 2;
	RaidTerraceOfEndlessSpring = 3;
	RaidThroneOfThunder = 4;
	RaidSiegeOfOrgrimmar = 5;
}

// NextIndex: 24
message DatabaseFilters {
	repeated ArmorType armor_types = 1;
	repeated WeaponType weapon_types = 2;
	repeated RangedWeaponType ranged_weapon_types = 16;
	repeated SourceFilterOption sources = 17;
	repeated RaidFilterOption raids = 18;
	UIItem.FactionRestriction faction_restriction = 19;
	int32 min_ilvl = 20;
	int32 max_ilvl = 21;

	double min_mh_weapon_speed = 4;
	double max_mh_weapon_speed = 5;
	double min_oh_weapon_speed = 9;
	double max_oh_weapon_speed = 10;
	double min_ranged_weapon_speed = 14;
	double max_ranged_weapon_speed = 15;

	bool one_handed_weapons = 6;
	bool two_handed_weapons = 7;
	bool matching_gems_only = 8;

	// Item IDs for favorited items.
	repeated int32 favorite_items = 11;
	// Item IDs for favorited gems.
	repeated int32 favorite_gems = 12;
	// Item IDs for favorited random suffixes.
	repeated int32 favorite_random_suffixes = 22;
	// Item IDs for favorited reforges.
	repeated int32 favorite_reforges = 23;
	// Uniquely-identifying strings for favorited enchants.
	repeated string favorite_enchants = 13;
}

message SimSettings {
	int32 iterations = 1;
	int32 phase = 2;
	int64 fixed_rng_seed = 3;
	bool show_damage_metrics = 8;
	bool show_threat_metrics = 4;
	bool show_healing_metrics = 7;
	bool show_experimental = 5;
	bool show_quick_swap = 12;
	bool show_ep_values = 11;
	bool use_custom_ep_values = 13;
	bool use_soft_cap_breakpoints = 14;
	string language = 9;
	Faction faction = 6;
	DatabaseFilters filters = 10;
}

// Contains all information that is imported/exported from an individual sim.
message IndividualSimSettings {
	SimSettings settings = 5;
	RaidBuffs raid_buffs = 1;
	Debuffs debuffs = 8;
	repeated UnitReference tanks = 7;
	PartyBuffs party_buffs = 2;
	Player player = 3;
	Encounter encounter = 4;
	int32 target_dummies = 9;
	UnitStats ep_weights_stats = 10;
	repeated double ep_ratios = 11;
	Stat dps_ref_stat = 12;
	Stat heal_ref_stat = 13;
	Stat tank_ref_stat = 14;
	UnitStats stat_caps = 15;
	UnitStats breakpoint_limits = 16;
}

message StatCapConfig {
	UIStat unit_stat = 1;

	// Breakpoint values in ascending order
	repeated double breakpoints = 2;

	// Should be either TypeSoftCap or TypeThreshold currently
	StatCapType cap_type = 3;

	// postCapEPs[i] is the stat weight value when between breakpoints[i]
	// and breakpoints[i+1]. Used only for TypeSoftCap and ignored for
	// TypeThreshold.
	repeated double post_cap_EPs = 4;
}

// Represents a single attribute that is either a Stat or a PseudoStat.
// Currently used only within the StatCapConfig UI message for configuring Haste
// caps, and is therefore not versioned, since this message is not imported or
// exported from local storage or links.
message UIStat {
	// Uniquely identifies the attribute
	oneof unit_stat {
		Stat stat = 1;
		PseudoStat pseudo_stat = 2;
	}
}

enum StatCapType {
	TypeUnknown = 0;

	// Unused currently, but may be able to combine hard cap + soft cap
	// configuration in the future.
	TypeHardCap = 1;

	// Used for stats that exhibit significant EP changes after particular
	// breakpoint values, but where the post-cap EP remains non-zero.
	// Examples include Spell Haste for Demonology Warlocks and Expertise
	// for tanks.
	TypeSoftCap = 2;

	// Used for stats that exhibit discontinuities in value at discrete
	// thresholds due to in-game rounding effects etc., but where the
	// average value of the stat from one breakpoint to the next does not
	// vary significantly. The most relevant example in early Cata Classic
	// is Mastery Rating for Demonology Warlock, which gets floored in-game
	// to the nearest integer % damage threshold.
	TypeThreshold = 3;
}

// Local storage data for gear settings.
message SavedGearSet {
	EquipmentSpec gear = 1;
	UnitStats bonus_stats_stats = 3;
}

message SavedStatWeightSettings {
	repeated Stat excluded_stats = 1;
    repeated PseudoStat excluded_pseudo_stats = 2;
    int32 api_version = 3; // Needed in case the Stat or PseudoStat enum orderings ever change

}

// Local storage data for other settings.
message SavedSettings {
	RaidBuffs raid_buffs = 1;
	PartyBuffs party_buffs = 2;
	Debuffs debuffs = 7;
	IndividualBuffs player_buffs = 3;
	ConsumesSpec consumables = 4;
	Race race = 5;
	repeated Profession professions = 9;
	bool enable_item_swap = 18;
	ItemSwap item_swap = 17;

	int32 reaction_time_ms = 10;
	int32 channel_clip_delay_ms = 14;
	bool in_front_of_target = 11;
	double distance_from_target = 12;
	HealingModel healing_model = 13;
	double dark_intent_uptime = 19;
	bool challenge_mode = 20;
}

message SavedTalents {
	string talents_string = 1;
	Glyphs glyphs = 2;
}

message SavedRotation {
	APLRotation rotation = 1;
}

message SavedEPWeights {
	UnitStats ep_weights = 1;
}

message BlessingsAssignment {
	// Index corresponds to Spec that the blessing should be applied to.
	repeated Blessings blessings = 1;
}

message BlessingsAssignments {
	// Assignments for each paladin.
	repeated BlessingsAssignment paladins = 1;
}

// Local storage data for a saved encounter.
message SavedEncounter {
	Encounter encounter = 1;
}

// Local storage data for raid sim settings.
message SavedRaid {
	Raid raid = 1;
	BlessingsAssignments blessings = 3;
	Faction faction = 4;
	int32 phase = 5;
}

// Contains all information that is imported/exported from a raid sim.
message RaidSimSettings {
	SimSettings settings = 5;
	Raid raid = 1;
	BlessingsAssignments blessings = 3;
	Encounter encounter = 4;
}

// All the data related to running the sim once.
message SimRun {
	RaidSimRequest request = 1;
	RaidSimResult result = 2;
}

// Contains a sim run and also other context data.
message SimRunData {
	SimRun run = 1;

	// The run stored as a reference, for comparison to the current run.
	SimRun reference_run = 2;
}

// Sent by the sim to the detailed results page.
message DetailedResultsUpdate {
	oneof data {
		// Show results from a run.
		SimRunData run_data = 1;

		// Update sim settings.
		SimSettings settings = 2;
	}
}
