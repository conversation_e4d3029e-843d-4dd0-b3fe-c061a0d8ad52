syntax = "proto3";
package proto;

option go_package = "./proto";

import "common.proto";
import "shaman.proto";
import "druid.proto";

// Rotation options are based heavily on APL. See https://github.com/simulationcraft/simc/wiki/ActionLists.

message APLRotation {
    enum Type {
        TypeUnknown = 0;
        TypeAuto = 1;
        TypeSimple = 2;
        TypeAPL = 3;
    }
    Type type = 3;

    SimpleRotation simple = 4;

	repeated APLPrepullAction prepull_actions = 1;
	repeated APLListItem priority_list = 2;
}

message SimpleRotation {
    string spec_rotation_json = 1;
	Cooldowns cooldowns = 2;
}

message APLPrepullAction {
    APLAction action = 1;
    APLValue do_at_value = 4; // When to perform this prepull action. Should be a negative value.
    bool hide = 3;            // Causes this item to be ignored.
}

message APLListItem {
    bool hide = 1;        // Causes this item to be ignored.
    string notes = 2;     // Comments for the reader.
    APLAction action = 3; // The action to be performed.
}

// NextIndex: 27
message APLAction {
    APLValue condition = 1; // If set, action will only execute if value is true or != 0.

    oneof action {
        // Casting
        APLActionCastSpell cast_spell = 3;
		APLActionCastFriendlySpell cast_friendly_spell = 20;
        APLActionChannelSpell channel_spell = 16;
        APLActionMultidot multidot = 8;
        APLActionStrictMultidot strict_multidot = 26;
        APLActionMultishield multishield = 12;
        APLActionCastAllStatBuffCooldowns cast_all_stat_buff_cooldowns = 23;
        APLActionAutocastOtherCooldowns autocast_other_cooldowns = 7;

        // Timing
        APLActionWait wait = 4;
        APLActionWaitUntil wait_until = 14;
        APLActionSchedule schedule = 15;

        // Sequences
        APLActionSequence sequence = 2;
        APLActionResetSequence reset_sequence = 5;
        APLActionStrictSequence strict_sequence = 6;

        // Misc
        APLActionChangeTarget change_target = 9;
        APLActionActivateAura activate_aura = 13;
        APLActionActivateAuraWithStacks activate_aura_with_stacks = 24;
        APLActionActivateAllStatBuffProcAuras activate_all_stat_buff_proc_auras = 25;
        APLActionCancelAura cancel_aura = 10;
        APLActionTriggerICD trigger_icd = 11;
        APLActionItemSwap item_swap = 17;
        APLActionMove move = 21;
        APLActionMoveDuration move_duration = 22;

        // Class or Spec-specific actions
        APLActionCatOptimalRotationAction cat_optimal_rotation_action = 18;

        // Internal use only, not exposed in UI.
        APLActionCustomRotation custom_rotation = 19;
    }
}

// NextIndex: 106
message APLValue {
	UUID uuid = 85;

    oneof value {
        // Operators
        APLValueConst const = 1;
        APLValueAnd and = 2;
        APLValueOr or = 3;
        APLValueNot not = 4;
        APLValueCompare cmp = 5;
        APLValueMath math = 38;
        APLValueMax max = 47;
        APLValueMin min = 48;

        // Encounter values
        APLValueCurrentTime current_time = 7;
        APLValueCurrentTimePercent current_time_percent = 8;
        APLValueRemainingTime remaining_time = 9;
        APLValueRemainingTimePercent remaining_time_percent = 10;
        APLValueIsExecutePhase is_execute_phase = 41;
        APLValueNumberTargets number_targets = 28;

        // Boss values
        APLValueBossSpellTimeToReady boss_spell_time_to_ready = 64;
        APLValueBossSpellIsCasting boss_spell_is_casting = 65;

        // Resource values
        APLValueCurrentHealth current_health = 26;
        APLValueCurrentHealthPercent current_health_percent = 27;
        APLValueCurrentMana current_mana = 11;
        APLValueCurrentManaPercent current_mana_percent = 12;
        APLValueCurrentRage current_rage = 14;
        APLValueCurrentEnergy current_energy = 15;
        APLValueCurrentFocus current_focus = 66;
        APLValueCurrentComboPoints current_combo_points = 16;
        APLValueCurrentRunicPower current_runic_power = 25;
        APLValueCurrentSolarEnergy current_solar_energy = 68;
        APLValueCurrentLunarEnergy current_lunar_energy = 69;
		APLValueCurrentGenericResource current_generic_resource = 75;
        APLValueMaxHealth max_health = 98;
		APLValueMaxComboPoints max_combo_points = 93;
		APLValueMaxEnergy max_energy = 87;
		APLValueMaxFocus max_focus = 88;
		APLValueMaxRage max_rage = 102;
		APLValueMaxRunicPower max_runic_power = 86;
		APLValueEnergyRegenPerSecond energy_regen_per_second = 89;
		APLValueFocusRegenPerSecond focus_regen_per_second = 90;
		APLValueEnergyTimeToTarget energy_time_to_target = 91;
		APLValueFocusTimeToTarget focus_time_to_target = 92;

		// Unit values
		APLValueUnitIsMoving unit_is_moving = 72;
		APLValueUnitDistance unit_distance = 105;

        // Rune Resource values
        APLValueCurrentRuneCount current_rune_count = 29;
        APLValueCurrentNonDeathRuneCount current_non_death_rune_count = 34;
        APLValueCurrentRuneDeath current_rune_death = 30;
        APLValueCurrentRuneActive current_rune_active = 31;
        APLValueRuneCooldown rune_cooldown = 32;
        APLValueNextRuneCooldown next_rune_cooldown = 33;
        APLValueRuneSlotCooldown rune_slot_cooldown = 53;

        // GCD values
        APLValueGCDIsReady gcd_is_ready = 17;
        APLValueGCDTimeToReady gcd_time_to_ready = 18;

        // Autoattack values
        APLValueAutoTimeToNext auto_time_to_next = 40;

        // Spell values
        APLValueSpellIsKnown spell_is_known = 74;
        APLValueSpellCanCast spell_can_cast = 19;
        APLValueSpellIsReady spell_is_ready = 20;
        APLValueSpellTimeToReady spell_time_to_ready = 21;
        APLValueSpellCastTime spell_cast_time = 35;
        APLValueSpellTravelTime spell_travel_time = 37;
        APLValueSpellCPM spell_cpm = 42;
        APLValueSpellIsChanneling spell_is_channeling = 56;
        APLValueSpellChanneledTicks spell_channeled_ticks = 57;
        APLValueSpellCurrentCost spell_current_cost = 62;
        APLValueSpellNumCharges spell_num_charges = 96;
        APLValueSpellTimeToCharge spell_time_to_charge = 97;

        // Aura values
        APLValueAuraIsKnown aura_is_known = 73;
        APLValueAuraIsActive aura_is_active = 22;
        APLValueAuraIsActiveWithReactionTime aura_is_active_with_reaction_time = 50;
        APLValueAuraIsInactiveWithReactionTime aura_is_inactive_with_reaction_time = 76;
        APLValueAuraRemainingTime aura_remaining_time = 23;
        APLValueAuraNumStacks aura_num_stacks = 24;
        APLValueAuraInternalCooldown aura_internal_cooldown = 39;
        APLValueAuraICDIsReadyWithReactionTime aura_icd_is_ready_with_reaction_time = 51;
        APLValueAuraShouldRefresh aura_should_refresh = 43;

        // Aggregate Aura set values
        APLValueAllTrinketStatProcsActive all_trinket_stat_procs_active = 78; // TODO: Rename in MoP as it includes all item/effect procs
        APLValueAnyTrinketStatProcsActive any_trinket_stat_procs_active = 79; // TODO: Rename in MoP as it includes all item/effect procs
        APLValueTrinketProcsMinRemainingTime trinket_procs_min_remaining_time = 80; // TODO: Rename in MoP as it includes all item/effect procs
        APLValueTrinketProcsMaxRemainingICD trinket_procs_max_remaining_icd = 82; // TODO: Rename in MoP as it includes all item/effect procs
        APLValueNumEquippedStatProcTrinkets num_equipped_stat_proc_trinkets = 81; // TODO: Rename in MoP as it includes all item/effect procs
        APLValueNumStatBuffCooldowns num_stat_buff_cooldowns = 84;

        // Dot values
        APLValueDotIsActive dot_is_active = 6;
        APLValueDotIsActiveOnAllTargets dot_is_active_on_all_targets = 103;
        APLValueDotRemainingTime dot_remaining_time = 13;
        APLValueDotLowestRemainingTime dot_lowest_remaining_time = 104;
        APLValueDotTickFrequency dot_tick_frequency = 67;
		APLValueDotPercentIncrease dot_percent_increase = 101;

        // Sequence values
        APLValueSequenceIsComplete sequence_is_complete = 44;
        APLValueSequenceIsReady sequence_is_ready = 45;
        APLValueSequenceTimeToReady sequence_time_to_ready = 46;

        // Properties
        APLValueChannelClipDelay channel_clip_delay = 58;
        APLValueInputDelay input_delay = 71;
        APLValueFrontOfTarget front_of_target = 63;

        // Class or Spec-specific values
        APLValueTotemRemainingTime totem_remaining_time = 49;
        APLValueCatExcessEnergy cat_excess_energy = 52;
        APLValueCatNewSavageRoarDuration cat_new_savage_roar_duration = 61;
        APLValueWarlockHandOfGuldanInFlight warlock_hand_of_guldan_in_flight = 59;
        APLValueWarlockHauntInFlight warlock_haunt_in_flight = 60;
        APLValueCurrentEclipsePhase druid_current_eclipse_phase = 70;
		APLValueMageCurrentCombustionDotEstimate mage_current_combustion_dot_estimate = 77;
        APLValueShamanFireElementalDuration shaman_fire_elemental_duration = 83;
        APLValueMonkCurrentChi monk_current_chi = 94;
		APLValueMonkMaxChi monk_max_chi = 95;
		APLValueBrewmasterMonkCurrentStaggerPercent brewmaster_monk_current_stagger_percent = 99;
		APLValueProtectionPaladinDamageTakenLastGlobal protection_paladin_damage_taken_last_global = 100;
    }
}

///////////////////////////////////////////////////////////////////////////
//                                 ACTIONS
///////////////////////////////////////////////////////////////////////////

message APLActionCastSpell {
    ActionID spell_id = 1;
    UnitReference target = 2;
}

message APLActionCastFriendlySpell {
    ActionID spell_id = 1;
    UnitReference target = 2;
}

message APLActionChannelSpell {
    ActionID spell_id = 1;
    UnitReference target = 2;

    APLValue interrupt_if = 3;
    bool allow_recast = 5;
}

message APLActionMultidot {
    ActionID spell_id = 1;
    int32 max_dots = 2;
    APLValue max_overlap = 3;
}

message APLActionStrictMultidot {
    ActionID spell_id = 1;
    int32 max_dots = 2;
    APLValue max_overlap = 3;
}

message APLActionMultishield {
    ActionID spell_id = 1;
    int32 max_shields = 2;
    APLValue max_overlap = 3;
}

message APLActionCastAllStatBuffCooldowns {
	int32 stat_type1 = 1;
	int32 stat_type2 = 2;
	int32 stat_type3 = 3;
}

message APLActionAutocastOtherCooldowns {
}

message APLActionWait {
    APLValue duration = 1;
}

message APLActionWaitUntil {
    APLValue condition = 1;
}

message APLActionSchedule {
    // Comma-separated list of times, e.g. '0s, 30s, 60s'
    string schedule = 1;

    APLAction inner_action = 2;
}

message APLActionSequence {
    string name = 1;

    repeated APLAction actions = 2;
}

message APLActionResetSequence {
    string sequence_name = 1;
}

message APLActionStrictSequence {
    repeated APLAction actions = 1;
}

message APLActionChangeTarget {
    UnitReference new_target = 1;
}

message APLActionCancelAura {
    ActionID aura_id = 1;
}

message APLActionActivateAura {
    ActionID aura_id = 1;
}

message APLActionActivateAuraWithStacks {
    ActionID aura_id = 1;
    int32 num_stacks = 2;
}

message APLActionActivateAllStatBuffProcAuras {
	APLActionItemSwap.SwapSet swap_set = 1;

    int32 stat_type1 = 2;
    int32 stat_type2 = 3;
    int32 stat_type3 = 4;
}

message APLActionTriggerICD {
    ActionID aura_id = 1;
}

message APLActionItemSwap {
	enum SwapSet {
		Unknown = 0;
		Main = 1;
		Swap1 = 2;
	}

    // The set to swap to.
    SwapSet swap_set = 1;
}

message APLActionCatOptimalRotationAction {
    FeralDruid.Rotation.AplType rotation_type = 1;
    bool maintain_faerie_fire = 3;
    bool manual_params = 2;
    float min_roar_offset = 4;
    int32 rip_leeway = 5;
    bool use_rake = 6;
    bool use_bite = 7;
    float bite_time = 8;
    float berserk_bite_time = 15;
    bool bite_during_execute = 9;
    bool allow_aoe_berserk = 10;
    bool melee_weave = 11;
    bool bear_weave = 12;
    bool snek_weave = 13;
    bool cancel_primal_madness = 14;
}

message APLActionMove {
    APLValue range_from_target = 1;
}

message APLActionMoveDuration {
    APLValue duration = 1;
}

message APLActionCustomRotation {
}

///////////////////////////////////////////////////////////////////////////
//                                  VALUES
///////////////////////////////////////////////////////////////////////////

enum APLValueType {
    ValueTypeUnknown = 0;
    ValueTypeBool = 1;
    ValueTypeInt = 2;
    ValueTypeFloat = 3;
    ValueTypeDuration = 4;
    ValueTypeString = 5;
}

message APLValueConst {
    string val = 1;
}

message APLValueAnd {
    repeated APLValue vals = 1;
}
message APLValueOr {
    repeated APLValue vals = 1;
}
message APLValueNot {
    APLValue val = 1;
}
message APLValueCompare {
    enum ComparisonOperator {
        OpUnknown = 0;
        OpEq = 1; // Equals
        OpNe = 2; // Not equals
        OpLt = 3; // Less than
        OpLe = 4; // Less than or equal
        OpGt = 5; // Greater than
        OpGe = 6; // Greater than or equal
    }
    ComparisonOperator op = 1;

    APLValue lhs = 2;
    APLValue rhs = 3;
}
message APLValueMath {
    enum MathOperator {
        OpUnknown = 0;
        OpAdd = 1; // Add
        OpSub = 2; // Subtract
        OpMul = 3; // Multiply
        OpDiv = 4; // Divide
    }
    MathOperator op = 1;

    APLValue lhs = 2;
    APLValue rhs = 3;
}
message APLValueMax {
    repeated APLValue vals = 1;
}
message APLValueMin {
    repeated APLValue vals = 1;
}

message APLValueCurrentTime {}
message APLValueCurrentTimePercent {}
message APLValueRemainingTime {}
message APLValueRemainingTimePercent {}
message APLValueNumberTargets {}
message APLValueIsExecutePhase {
    enum ExecutePhaseThreshold {
        Unknown = 0;
        E20 = 1;
        E25 = 2;
        E35 = 3;
        E45 = 4;
		E90 = 5;
    }
    ExecutePhaseThreshold threshold = 1;
}

message APLValueBossSpellTimeToReady {
    UnitReference target_unit = 1;
    ActionID spell_id = 2;
}

message APLValueBossSpellIsCasting {
    UnitReference target_unit = 1;
    ActionID spell_id = 2;
}
message APLValueUnitIsMoving {
    UnitReference source_unit = 1;
}
message APLValueUnitDistance {
    UnitReference source_unit = 1;
}
message APLValueCurrentHealth {
    UnitReference source_unit = 1;
}
message APLValueCurrentHealthPercent {
    UnitReference source_unit = 1;
}
message APLValueCurrentMana {
    UnitReference source_unit = 1;
}
message APLValueCurrentManaPercent {
    UnitReference source_unit = 1;
}
message APLValueCurrentRage {}
message APLValueCurrentEnergy {}
message APLValueCurrentFocus {}
message APLValueCurrentComboPoints {}
message APLValueCurrentRunicPower {}
message APLValueCurrentSolarEnergy {}
message APLValueCurrentLunarEnergy {}
message APLValueCurrentGenericResource {}
message APLValueMaxHealth {}
message APLValueMaxComboPoints {}
message APLValueMaxEnergy {}
message APLValueMaxFocus {}
message APLValueMaxRage {}
message APLValueMaxRunicPower {}
message APLValueEnergyRegenPerSecond {}
message APLValueFocusRegenPerSecond {}
message APLValueEnergyTimeToTarget {
	APLValue target_energy = 1;
}
message APLValueFocusTimeToTarget {
	APLValue target_focus = 1;
}

enum APLValueRuneType {
    RuneUnknown = 0;
    RuneBlood = 1;
    RuneFrost = 2;
    RuneUnholy = 3;
    RuneDeath = 4;
}
enum APLValueRuneSlot {
    SlotUnknown = 0;
    SlotLeftBlood = 1;
    SlotRightBlood = 2;
    SlotLeftFrost = 3;
    SlotRightFrost = 4;
    SlotLeftUnholy = 5;
    SlotRightUnholy = 6;
}

message APLValueCurrentRuneCount{
    APLValueRuneType rune_type = 1;
}
message APLValueCurrentNonDeathRuneCount{
    APLValueRuneType rune_type = 1;
}
message APLValueCurrentRuneDeath{
    APLValueRuneSlot rune_slot = 1;
}
message APLValueCurrentRuneActive{
    APLValueRuneSlot rune_slot = 1;
}
message APLValueRuneCooldown{
    APLValueRuneType rune_type = 1;
}
message APLValueNextRuneCooldown{
    APLValueRuneType rune_type = 1;
}
message APLValueRuneSlotCooldown{
    APLValueRuneSlot rune_slot = 1;
}

enum APLValueEclipsePhase {
    UnknownPhase = 0;
    NeutralPhase = 1;
    SolarPhase = 2;
    LunarPhase = 3;
}

message APLValueCurrentEclipsePhase {
    APLValueEclipsePhase eclipse_phase = 1;
}

message APLValueGCDIsReady {}
message APLValueGCDTimeToReady {}

message APLValueAutoTimeToNext {}

message APLValueSpellIsKnown {
    ActionID spell_id = 1;
}
message APLValueSpellCanCast {
    ActionID spell_id = 1;
}
message APLValueSpellIsReady {
    ActionID spell_id = 1;
}
message APLValueSpellTimeToReady {
    ActionID spell_id = 1;
}
message APLValueSpellCastTime {
    ActionID spell_id = 1;
}
message APLValueChannelClipDelay {
}
message APLValueInputDelay {
}
message APLValueFrontOfTarget {
}

message APLValueSpellTravelTime {
    ActionID spell_id = 1;
}
message APLValueSpellCPM {
    ActionID spell_id = 1;
}
message APLValueSpellIsChanneling {
    ActionID spell_id = 1;
}
message APLValueSpellChanneledTicks {
    ActionID spell_id = 1;
}
message APLValueSpellCurrentCost {
    ActionID spell_id = 1;
}
message APLValueSpellNumCharges{
    ActionID spell_id = 1;
}
message APLValueSpellTimeToCharge{
    ActionID spell_id = 1;
}

message APLValueAuraIsKnown {
    UnitReference source_unit = 2;
    ActionID aura_id = 1;
}
message APLValueAuraIsActive {
    UnitReference source_unit = 2;
    ActionID aura_id = 1;
}
message APLValueAuraIsActiveWithReactionTime {
    UnitReference source_unit = 2;
    ActionID aura_id = 1;
}
message APLValueAuraIsInactiveWithReactionTime {
    UnitReference source_unit = 2;
    ActionID aura_id = 1;
}
message APLValueAuraRemainingTime {
    UnitReference source_unit = 2;
    ActionID aura_id = 1;
}
message APLValueAuraNumStacks {
    UnitReference source_unit = 2;
    ActionID aura_id = 1;
}
message APLValueAuraInternalCooldown {
    UnitReference source_unit = 2;
    ActionID aura_id = 1;
}
message APLValueAuraICDIsReadyWithReactionTime {
    UnitReference source_unit = 2;
    ActionID aura_id = 1;
}
message APLValueAuraShouldRefresh {
    UnitReference source_unit = 2;
    ActionID aura_id = 1;
    APLValue max_overlap = 3;
}

message APLValueAllTrinketStatProcsActive {
    int32 stat_type1 = 1;
    int32 stat_type2 = 2;
    int32 stat_type3 = 3;
    double min_icd_seconds = 4;
}
message APLValueAnyTrinketStatProcsActive {
    int32 stat_type1 = 1;
    int32 stat_type2 = 2;
    int32 stat_type3 = 3;
    double min_icd_seconds = 4;
}
message APLValueTrinketProcsMinRemainingTime {
    int32 stat_type1 = 1;
    int32 stat_type2 = 2;
    int32 stat_type3 = 3;
    double min_icd_seconds = 4;
}
message APLValueTrinketProcsMaxRemainingICD {
    int32 stat_type1 = 1;
    int32 stat_type2 = 2;
    int32 stat_type3 = 3;
    double min_icd_seconds = 4;
}
message APLValueNumEquippedStatProcTrinkets {
    int32 stat_type1 = 1;
    int32 stat_type2 = 2;
    int32 stat_type3 = 3;
    double min_icd_seconds = 4;
}
message APLValueNumStatBuffCooldowns {
    int32 stat_type1 = 1;
    int32 stat_type2 = 2;
    int32 stat_type3 = 3;
}

message APLValueDotIsActive {
    UnitReference target_unit = 2;
    ActionID spell_id = 1;
}
message APLValueDotIsActiveOnAllTargets {
    ActionID spell_id = 1;
}
message APLValueDotRemainingTime {
    UnitReference target_unit = 2;
    ActionID spell_id = 1;
}
message APLValueDotLowestRemainingTime {
    ActionID spell_id = 1;
}
message APLValueDotTickFrequency {
    UnitReference target_unit = 2;
    ActionID spell_id = 1;
}

message APLValueSequenceIsComplete {
    string sequence_name = 1;
}
message APLValueSequenceIsReady {
    string sequence_name = 1;
}
message APLValueSequenceTimeToReady {
    string sequence_name = 1;
}

message APLValueTotemRemainingTime {
    ShamanTotems.TotemType totem_type = 1;
}
message APLValueCatExcessEnergy {}
message APLValueCatNewSavageRoarDuration {}
message APLValueWarlockHandOfGuldanInFlight {}
message APLValueWarlockHauntInFlight {}
message APLValueMageCurrentCombustionDotEstimate {}
message APLValueShamanFireElementalDuration {}

message APLValueMonkCurrentChi {}
message APLValueMonkMaxChi {}
message APLValueBrewmasterMonkCurrentStaggerPercent {}
message APLValueProtectionPaladinDamageTakenLastGlobal {}

message APLValueDotPercentIncrease {
	ActionID spell_id = 1;
	UnitReference target_unit = 2;
}
