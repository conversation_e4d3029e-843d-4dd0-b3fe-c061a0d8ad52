syntax = "proto3";
package proto;

option go_package = "./proto";

import "common.proto";
// DO NOT REMOVE THE COMMENTS
// BEGIN GENERATED
// DruidTalents message.
message DruidTalents {
    bool feline_swiftness = 1;
    bool displacer_beast = 2;
    bool wild_charge = 3;
    bool yseras_gift = 4;
    bool renewal = 5;
    bool cenarion_ward = 6;
    bool faerie_swarm = 7;
    bool mass_entanglement = 8;
    bool typhoon = 9;
    bool soul_of_the_forest = 10;
    bool incarnation = 11;
    bool force_of_nature = 12;
    bool disorienting_roar = 13;
    bool ursols_vortex = 14;
    bool mighty_bash = 15;
    bool heart_of_the_wild = 16;
    bool dream_of_cenarius = 17;
    bool natures_vigil = 18;
}

enum DruidMajorGlyph {
    DruidMajorGlyphNone = 0;
    GlyphOfFrenziedRegeneration = 40896;
    GlyphOfMaul = 40897;
    GlyphOfOmens = 40899;
    GlyphOfShred = 40901;
    GlyphOfProwl = 40902;
    GlyphOfPounce = 40903;
    GlyphOfStampede = 40906;
    GlyphOfInnervate = 40908;
    GlyphOfRebirth = 40909;
    GlyphOfRegrowth = 40912;
    GlyphOfRejuvenation = 40913;
    GlyphOfHealingTouch = 40914;
    GlyphOfEfflorescence = 40915;
    GlyphOfGuidedStars = 40916;
    GlyphOfHurricane = 40920;
    GlyphOfSkullBash = 40921;
    GlyphOfNaturesGrasp = 40922;
    GlyphOfSavagery = 40923;
    GlyphOfEntanglingRoots = 40924;
    GlyphOfBlooming = 43331;
    GlyphOfDash = 43674;
    GlyphOfMasterShapeshifter = 44928;
    GlyphOfSurvivalInstincts = 45601;
    GlyphOfWildGrowth = 45602;
    GlyphOfMightOfUrsoc = 45603;
    GlyphOfStampedingRoar = 45604;
    GlyphOfCyclone = 45622;
    GlyphOfBarkskin = 45623;
    GlyphOfFerociousBite = 48720;
    GlyphOfFaeSilence = 67484;
    GlyphOfFaerieFire = 67485;
    GlyphOfCatForm = 67487;
}

enum DruidMinorGlyph {
    DruidMinorGlyphNone = 0;
    GlyphOfTheStag = 40900;
    GlyphOfTheOrca = 40919;
    GlyphOfAquaticForm = 43316;
    GlyphOfGrace = 43332;
    GlyphOfTheChameleon = 43334;
    GlyphOfCharmWoodlandCreature = 43335;
    GlyphOfStars = 44922;
    GlyphOfThePredator = 67486;
    GlyphOfTheTreant = 68039;
    GlyphOfTheCheetah = 89868;
    GlyphOfFocus = 93203;
    GlyphOfTheSproutingMushroom = 104102;
    GlyphOfOneWithNature = 104103;
}

// END GENERATED

message DruidOptions {
	UnitReference innervate_target = 1;
}

message BalanceDruid {
  message Rotation {}

  message Options {
    DruidOptions class_options = 1;
	float okf_uptime = 2;
  }
  Options options = 3;
}

message FeralDruid {
  message Rotation {
	enum AplType {
		SingleTarget = 0;
		Aoe = 1;
	}
    enum BearweaveType {
      None = 0;
      Mangle = 1;
      Lacerate = 2;
    }
    enum BiteModeType {
      Emperical = 0;
      Analytical = 1;
    }

	bool bear_weave = 1;
	bool maintain_faerie_fire = 2;
	int32 min_combos_for_rip = 3;
	bool use_rake = 4;
	bool use_bite = 5;
	bool mangle_spam = 6;
	float bite_time = 7;
        float berserk_bite_time = 16;
	int32 min_combos_for_bite = 8;
	bool bite_during_execute = 13;
	int32 rip_leeway = 19;
	float min_roar_offset = 11;
	BiteModeType bite_mode_type = 12;
	bool snek_weave = 14;
	bool manual_params = 17;
	AplType rotation_type = 21;
	bool allow_aoe_berserk = 9;
	bool melee_weave = 15;
	bool cancel_primal_madness = 10;
        bool prepull_tranquility = 18;
  }
  Rotation rotation = 1;

  message Options {
    DruidOptions class_options = 1;
    bool cannot_shred_target = 2;
    bool assume_bleed_active = 4;
  }
  Options options = 3;
}

message GuardianDruid {
  message Rotation {
		bool maintain_faerie_fire = 1;
		bool maintain_demoralizing_roar = 2;
		double demo_time = 3;
		double pulverize_time = 4;
		bool prepull_stampede = 5;
  }

  message Options {
    DruidOptions class_options = 1;
  }
  Options options = 3;
}

message RestorationDruid {
  message Rotation {}

  message Options {
    DruidOptions class_options = 1;
  }
  Options options = 3;
}
