syntax = "proto3";
package proto;

option go_package = "./proto";

// DO NOT REMOVE THE COMMENTS
// BEGIN GENERATED
// MonkTalents message.
message MonkTalents {
    bool celerity = 1;
    bool tigers_lust = 2;
    bool momentum = 3;
    bool chi_wave = 4;
    bool zen_sphere = 5;
    bool chi_burst = 6;
    bool power_strikes = 7;
    bool ascension = 8;
    bool chi_brew = 9;
    bool ring_of_peace = 10;
    bool charging_ox_wave = 11;
    bool leg_sweep = 12;
    bool healing_elixirs = 13;
    bool dampen_harm = 14;
    bool diffuse_magic = 15;
    bool rushing_jade_wind = 16;
    bool invoke_xuen_the_white_tiger = 17;
    bool chi_torpedo = 18;
}

enum MonkMajorGlyph {
    MonkMajorGlyphNone = 0;
    GlyphOfRapidRolling = 82345;
    GlyphOfTranscendence = 84652;
    GlyphOfBreathOfFire = 85685;
    GlyphOfClash = 85687;
    GlyphOfEnduringHealingSphere = 85689;
    GlyphOfGuard = 85691;
    GlyphOfManaTea = 85692;
    GlyphOfZenMeditation = 85695;
    GlyphOfRenewingMists = 85696;
    GlyphOfSpinningCraneKick = 85697;
    GlyphOfSurgingMist = 85699;
    GlyphOfTouchOfDeath = 85700;
    GlyphOfNimbleBrew = 87880;
    GlyphOfAfterlife = 87891;
    GlyphOfFistsOfFury = 87892;
    GlyphOfFortifyingBrew = 87893;
    GlyphOfLeerOfTheOx = 87894;
    GlyphOfLifeCocoon = 87895;
    GlyphOfFortuitousSpheres = 87896;
    GlyphOfParalysis = 87897;
    GlyphOfSparring = 87898;
    GlyphOfDetox = 87899;
    GlyphOfTouchOfKarma = 87900;
    GlyphOfTargetedExpulsion = 87901;
}

enum MonkMinorGlyph {
    MonkMinorGlyphNone = 0;
    GlyphOfSpinningFireBlossom = 85698;
    GlyphOfCracklingTigerLightning = 87881;
    GlyphOfFlyingSerpentKick = 87882;
    GlyphOfHonor = 87883;
    GlyphOfJab = 87884;
    GlyphOfRisingTigerKick = 87885;
    GlyphOfSpiritRoll = 87887;
    GlyphOfFightingPose = 87888;
    GlyphOfWaterRoll = 87889;
    GlyphOfZenFlight = 87890;
    GlyphOfBlackoutKick = 90715;
}

// END GENERATED

enum MonkStance {
	None = 0;
	SturdyOx = 1;
	WiseSerpent = 2;
	FierceTiger = 3;
}

message MonkOptions {}

message BrewmasterMonk {
	message Rotation {}

	message Options {
		MonkOptions class_options = 1;
	}
	Options options = 1;
}

message MistweaverMonk {
	message Rotation {}

	message Options {
		MonkOptions class_options = 1;
	}
	Options options = 1;
}

message WindwalkerMonk {
	message Rotation {}

	message Options {
		MonkOptions class_options = 1;
	}
	Options options = 1;
}
