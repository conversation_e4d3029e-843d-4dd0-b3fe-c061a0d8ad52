syntax = "proto3";
package proto;

option go_package = "./proto";
// DO NOT REMOVE THE COMMENTS
// BEGIN GENERATED
// WarriorTalents message.
message WarriorTalents {
    bool juggernaut = 1;
    bool double_time = 2;
    bool warbringer = 3;
    bool enraged_regeneration = 4;
    bool second_wind = 5;
    bool impending_victory = 6;
    bool staggering_shout = 7;
    bool piercing_howl = 8;
    bool disrupting_shout = 9;
    bool bladestorm = 10;
    bool shockwave = 11;
    bool dragon_roar = 12;
    bool mass_spell_reflection = 13;
    bool safeguard = 14;
    bool vigilance = 15;
    bool avatar = 16;
    bool bloodbath = 17;
    bool storm_bolt = 18;
}

enum WarriorMajorGlyph {
    WarriorMajorGlyphNone = 0;
    GlyphOfLongCharge = 43397;
    GlyphOfUnendingRage = 43399;
    GlyphOfEnragedSpeed = 43413;
    GlyphOfHinderingStrikes = 43414;
    GlyphOfHeavyRepercussions = 43415;
    GlyphOfBloodthirst = 43416;
    GlyphOfRudeInterruption = 43417;
    GlyphOfGagOrder = 43418;
    GlyphOfBlitz = 43419;
    GlyphOfMortalStrike = 43421;
    GlyphOfDieByTheSword = 43422;
    GlyphOfHamstring = 43423;
    GlyphOfHoldTheLine = 43424;
    GlyphOfShieldSlam = 43425;
    GlyphOfHoarseVoice = 43427;
    GlyphOfSweepingStrikes = 43428;
    GlyphOfResonatingPower = 43430;
    GlyphOfVictoryRush = 43431;
    GlyphOfRagingWind = 43432;
    GlyphOfWhirlwind = 45790;
    GlyphOfDeathFromAbove = 45792;
    GlyphOfVictoriousThrow = 45793;
    GlyphOfSpellReflection = 45795;
    GlyphOfShieldWall = 45797;
    GlyphOfColossusSmash = 63481;
    GlyphOfBullRush = 67482;
    GlyphOfRecklessness = 67483;
    GlyphOfIncite = 83096;
    GlyphOfImpalingThrows = 104055;
    GlyphOfTheExecutor = 104056;
}

enum WarriorMinorGlyph {
    WarriorMinorGlyphNone = 0;
    GlyphOfMysticShout = 43395;
    GlyphOfBloodcurdlingShout = 43396;
    GlyphOfGushingWound = 43398;
    GlyphOfMightyVictory = 43400;
    GlyphOfBloodyHealing = 43412;
    GlyphOfIntimidatingShout = 45794;
    GlyphOfThunderStrike = 49084;
    GlyphOfCrowFeast = 80587;
    GlyphOfBurningAnger = 80588;
    GlyphOfTheBlazingTrail = 85221;
    GlyphOfTheRagingWhirlwind = 104135;
    GlyphOfTheSubtleDefender = 104136;
    GlyphOfTheWatchfulEye = 104137;
    GlyphOfTheWeaponmaster = 104138;
}

// END GENERATED

message WarriorOptions {
}

message ArmsWarrior {
	message Rotation {}

	message Options {
		WarriorOptions class_options = 1;
		bool stance_snapshot = 2;
	}
	Options options = 1;
}

message FuryWarrior {
	message Rotation {}

	message Options {
		WarriorOptions class_options = 1;
		bool stance_snapshot = 2;
		WarriorSyncType sync_type = 3;
		bool use_item_swap_bonus_stats = 4;
	}
	Options options = 1;
}

enum WarriorSyncType {
	WarriorNoSync = 0;
	WarriorSyncMainhandOffhandSwings = 1;
  }

message ProtectionWarrior {
	message Rotation {}

	message Options {
		WarriorOptions class_options = 1;
	}
	Options options = 1;
}
