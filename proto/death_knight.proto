syntax = "proto3";
package proto;

option go_package = "./proto";

import "common.proto";

// DO NOT REMOVE THE COMMENTS
// BEGIN GENERATED
// DeathKnightTalents message.
message DeathKnightTalents {
    bool roiling_blood = 1;
    bool plague_leech = 2;
    bool unholy_blight = 3;
    bool lichborne = 4;
    bool anti_magic_zone = 5;
    bool purgatory = 6;
    bool deaths_advance = 7;
    bool chilblains = 8;
    bool asphyxiate = 9;
    bool death_pact = 10;
    bool death_siphon = 11;
    bool conversion = 12;
    bool blood_tap = 13;
    bool runic_empowerment = 14;
    bool runic_corruption = 15;
    bool gorefiends_grasp = 16;
    bool remorseless_winter = 17;
    bool desecrated_ground = 18;
}

enum DeathKnightMajorGlyph {
    DeathKnightMajorGlyphNone = 0;
    GlyphOfAntiMagicShell = 43533;
    GlyphOfUnholyFrenzy = 43534;
    GlyphOfIceboundFortitude = 43536;
    GlyphOfChainsOfIce = 43537;
    GlyphOfDeathGrip = 43541;
    GlyphOfDeathAndDecay = 43542;
    GlyphOfShiftingPresences = 43543;
    GlyphOfIcyTouch = 43546;
    GlyphOfEnduringInfection = 43547;
    GlyphOfPestilence = 43548;
    GlyphOfMindFreeze = 43549;
    GlyphOfStrangulate = 43552;
    GlyphOfPillarOfFrost = 43553;
    GlyphOfVampiricBlood = 43554;
    GlyphOfUnholyCommand = 43825;
    GlyphOfOutbreak = 43826;
    GlyphOfDancingRuneWeapon = 45799;
    GlyphOfDarkSimulacrum = 45800;
    GlyphOfDeathCoil = 45804;
    GlyphOfDarkSuccor = 68793;
    GlyphOfSwiftDeath = 104046;
    GlyphOfLoudHorn = 104047;
    GlyphOfRegenerativeMagic = 104048;
    GlyphOfFesteringBlood = 104049;
}

enum DeathKnightMinorGlyph {
    DeathKnightMinorGlyphNone = 0;
    GlyphOfTheGeist = 43535;
    GlyphOfDeathsEmbrace = 43539;
    GlyphOfHornOfWinter = 43544;
    GlyphOfArmyOfTheDead = 43550;
    GlyphOfFoulMenagerie = 43551;
    GlyphOfPathOfFrost = 43671;
    GlyphOfResilientGrip = 43672;
    GlyphOfDeathGate = 43673;
    GlyphOfCorpseExplosion = 43827;
    GlyphOfTranquilGrip = 45806;
    GlyphOfTheSkeleton = 104099;
    GlyphOfTheLongWinter = 104101;
}

// END GENERATED
message DeathKnightOptions {
}

message BloodDeathKnight {
	message Rotation {}

	message Options {
		DeathKnightOptions class_options = 1;
	}
	Options options = 3;
}

message FrostDeathKnight {
	message Rotation {}

	message Options {
		DeathKnightOptions class_options = 1;
	}
	Options options = 3;
}

message UnholyDeathKnight {
	message Rotation {}

	message Options {
		DeathKnightOptions class_options = 1;
		UnitReference unholy_frenzy_target = 2;
	}
	Options options = 3;
}
