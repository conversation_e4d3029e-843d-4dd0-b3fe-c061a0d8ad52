{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "Settings": {"BaseDir": "F:\\World of Warcraft", "BuildConfig": "buildConfig", "CDNConfig": "cdnConfig", "Region": "us", "Product": "wow_classic"}, "TargetDirectory": "dbfilesclient", "DatabaseFile": "wowsims.db", "Tables": ["Spell", "<PERSON><PERSON>", "GemProperties", "ItemSet", "ItemSetSpell", "ItemSubClass", "ItemSubClassMask", "ItemReforge", "ItemBonus", "ItemEffect", "ItemClass", "ItemRandomProperties", "ItemExtendedCost", "ItemRandomSuffix", "RandPropPoints", "ItemDamageAmmo", "ItemDamageOneHand", "ItemDamageOneHandCaster", "ItemDamageRanged", "ItemDamageThrown", "ItemDamageTwoHand", "ItemDamageTwoHandCaster", "ItemDamageWand", "ItemNameDescription", "ItemSparse", "ArmorLocation", "ItemArmorTotal", "ItemArmorShield", "ItemArmorQuality", "SkillLineAbility", "Curve", "CurvePoint", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SpellEquippedItems", "SpellRadius", "SpellMisc", "SpellLevels", "SpellName", "ScalingStatDistribution", "SpellCooldowns", "SpellScaling", "SpellClassOptions", "SpellItemEnchantment", "SpellEffect"]}