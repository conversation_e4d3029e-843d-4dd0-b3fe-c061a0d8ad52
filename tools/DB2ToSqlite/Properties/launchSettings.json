﻿{
	"$schema": "https://json.schemastore.org/launchsettings.json",
	"profiles": {
		"http": {
			"commandName": "Project",
			"dotnetRunMessages": true,
			"launchBrowser": false,
			"applicationUrl": "http://localhost:5221",
			"environmentVariables": {
				"ASPNETCORE_ENVIRONMENT": "Development"
			}
		},
		"https": {
			"commandName": "Project",
			"dotnetRunMessages": true,
			"launchBrowser": false,
			"applicationUrl": "https://localhost:7268;http://localhost:5221",
			"environmentVariables": {
				"ASPNETCORE_ENVIRONMENT": "Development"
			}
		}
	}
}
