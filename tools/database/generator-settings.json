{"Settings": {"BaseDir": "/mnt/f/World of Warcraft", "BuildConfig": "buildConfig", "CDNConfig": "cdnConfig", "Region": "us", "Product": "wow_classic"}, "TargetDirectory": "dbfilesclient", "DatabaseFile": "wowsims.db", "GameTablesOutDirectory": "../../assets/db_inputs/basestats", "GameTables": ["chancetomeleecrit", "chancetomeleecritbase", "chancetospellcrit", "chancetospellcritbase", "combatratings", "octbasempbyclass", "OCTBaseHPByClass", "SpellScaling"], "Tables": ["Spell", "<PERSON><PERSON>", "GemProperties", "ItemSet", "ItemSetSpell", "ItemSubClass", "ItemSubClassMask", "ItemReforge", "ItemBonus", "ItemEffect", "ItemClass", "SpellCategory", "ItemRandomProperties", "ItemExtendedCost", "ItemRandomSuffix", "RandPropPoints", "ItemDamageAmmo", "ItemDamageOneHand", "ItemDamageOneHandCaster", "ItemDamageRanged", "ItemDamageThrown", "ItemDamageTwoHand", "ItemDamageTwoHandCaster", "ItemDamageWand", "ItemNameDescription", "ItemSparse", "ArmorLocation", "ItemArmorTotal", "ItemArmorShield", "ItemArmorQuality", "SkillLineAbility", "SkillLine", "Curve", "CurvePoint", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SpellEquippedItems", "SpellRadius", "SpellMisc", "SpellLevels", "SpellPower", "SpellLabel", "SpellName", "ScalingStatDistribution", "SpellCooldowns", "SpellScaling", "SpellClassOptions", "SpellRange", "SpellReagents", "SpellInterrupts", "SpellItemEnchantment", "SpellCategories", "SpellShapeshift", "SpellEffect", "SpellDuration", "SpellAuraOptions", "GlyphProperties", "SpellMechanic", "Talent", "TalentTab"]}