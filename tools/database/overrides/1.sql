PRAGMA foreign_keys = OFF;
BEGIN TRANSACTION;

CREATE TABLE IF NOT EXISTS item_enchantment_template (
  entry   INTEGER NOT NULL,
  ench    INTEGER NOT NULL,
  chance  INTEGER,
  "TYPE"  INTEGER,
  PRIMARY KEY(entry, ench, "TYPE")
);

INSERT INTO item_enchantment_template(entry, ench, chance, "TYPE") VALUES
  (533, 5, 12, 0),
(533, 40, 12, 0),
(533, 91, 12, 0),
(533, 133, 12, 0),
(533, 134, 12, 0),
(533, 135, 12, 0),
(533, 136, 12, 0),
(533, 137, 12, 0),
(534, 7, 12, 0),
(534, 43, 12, 0),
(534, 118, 12, 0),
(534, 120, 12, 0),
(534, 121, 12, 0),
(534, 122, 12, 0),
(534, 123, 12, 0),
(534, 139, 12, 0),
(539, 6, 12, 0),
(539, 8, 12, 0),
(539, 36, 12, 0),
(539, 37, 12, 0),
(539, 39, 12, 0),
(539, 42, 12, 0),
(539, 131, 12, 0),
(539, 132, 12, 0),
(541, 336, 20, 0),
(541, 337, 20, 0),
(541, 338, 20, 0),
(541, 339, 20, 0),
(541, 340, 20, 0),
(542, 339, 33, 0),
(542, 340, 33, 0),
(542, 343, 33, 0),
(543, 337, 25, 0),
(543, 338, 25, 0),
(543, 339, 25, 0),
(543, 341, 25, 0),
(547, 345, 11, 0),
(547, 346, 11, 0),
(547, 347, 11, 0),
(547, 349, 11, 0),
(547, 350, 11, 0),
(547, 366, 11, 0),
(547, 367, 11, 0),
(547, 368, 11, 0),
(547, 369, 11, 0),
(548, 344, 9, 0),
(548, 345, 9, 0),
(548, 346, 9, 0),
(548, 347, 9, 0),
(548, 348, 9, 0),
(548, 352, 9, 0),
(548, 364, 9, 0),
(548, 365, 9, 0),
(548, 366, 9, 0),
(548, 370, 9, 0),
(548, 371, 9, 0),
(549, 344, 10, 0),
(549, 345, 10, 0),
(549, 346, 10, 0),
(549, 347, 10, 0),
(549, 348, 10, 0),
(549, 353, 10, 0),
(549, 354, 10, 0),
(549, 355, 10, 0),
(549, 356, 10, 0),
(549, 357, 10, 0),
(550, 344, 12, 0),
(550, 347, 12, 0),
(550, 348, 12, 0),
(550, 351, 12, 0),
(550, 360, 12, 0),
(550, 361, 12, 0),
(550, 362, 12, 0),
(550, 363, 12, 0),
(551, 344, 10, 0),
(551, 345, 10, 0),
(551, 346, 10, 0),
(551, 347, 10, 0),
(551, 348, 10, 0),
(551, 353, 10, 0),
(551, 354, 10, 0),
(551, 355, 10, 0),
(551, 356, 10, 0),
(551, 357, 10, 0),
(552, 344, 16, 0),
(552, 345, 16, 0),
(552, 347, 16, 0),
(552, 348, 16, 0),
(552, 359, 16, 0),
(552, 360, 16, 0),
(553, 347, 25, 0),
(553, 348, 25, 0),
(553, 351, 25, 0),
(553, 363, 25, 0),
(554, 344, 20, 0),
(554, 345, 20, 0),
(554, 347, 20, 0),
(554, 348, 20, 0),
(554, 357, 20, 0),
(555, 347, 33, 0),
(555, 351, 33, 0),
(555, 361, 33, 0),
(556, 345, 11, 0),
(556, 346, 11, 0),
(556, 347, 11, 0),
(556, 349, 11, 0),
(556, 350, 11, 0),
(556, 352, 11, 0),
(556, 366, 11, 0),
(556, 367, 11, 0),
(556, 368, 11, 0),
(557, 344, 20, 0),
(557, 347, 20, 0),
(557, 348, 20, 0),
(557, 351, 20, 0),
(557, 360, 20, 0),
(558, 344, 11, 0),
(558, 345, 11, 0),
(558, 346, 11, 0),
(558, 347, 11, 0),
(558, 348, 11, 0),
(558, 353, 11, 0),
(558, 354, 11, 0),
(558, 355, 11, 0),
(558, 357, 11, 0),
(559, 344, 12, 0),
(559, 347, 12, 0),
(559, 348, 12, 0),
(559, 351, 12, 0),
(559, 360, 12, 0),
(559, 361, 12, 0),
(559, 362, 12, 0),
(559, 363, 12, 0),
(560, 345, 20, 0),
(560, 347, 20, 0),
(560, 353, 20, 0),
(560, 354, 20, 0),
(560, 355, 20, 0),
(561, 344, 20, 0),
(561, 347, 20, 0),
(561, 348, 20, 0),
(561, 358, 20, 0),
(561, 360, 20, 0),
(562, 344, 10, 0),
(562, 345, 10, 0),
(562, 346, 10, 0),
(562, 347, 10, 0),
(562, 348, 10, 0),
(562, 352, 10, 0),
(562, 364, 10, 0),
(562, 366, 10, 0),
(562, 370, 10, 0),
(562, 371, 10, 0),
(563, 345, 11, 0),
(563, 346, 11, 0),
(563, 347, 11, 0),
(563, 349, 11, 0),
(563, 350, 11, 0),
(563, 352, 11, 0),
(563, 366, 11, 0),
(563, 368, 11, 0),
(563, 369, 11, 0),
(564, 344, 10, 0),
(564, 345, 10, 0),
(564, 346, 10, 0),
(564, 347, 10, 0),
(564, 348, 10, 0),
(564, 352, 10, 0),
(564, 364, 10, 0),
(564, 365, 10, 0),
(564, 370, 10, 0),
(564, 371, 10, 0),
(565, 366, 25, 0),
(565, 367, 25, 0),
(565, 368, 25, 0),
(565, 369, 25, 0),
(566, 364, 20, 0),
(566, 365, 20, 0),
(566, 366, 20, 0),
(566, 370, 20, 0),
(566, 371, 20, 0),
(567, 360, 25, 0),
(567, 361, 25, 0),
(567, 362, 25, 0),
(567, 363, 25, 0),
(569, 5, 4, 0),
(569, 7, 4, 0),
(569, 14, 4, 0),
(569, 40, 4, 0),
(569, 41, 4, 0),
(569, 43, 4, 0),
(569, 91, 4, 0),
(569, 118, 4, 0),
(569, 120, 4, 0),
(569, 121, 4, 0),
(569, 122, 4, 0),
(569, 123, 4, 0),
(569, 133, 4, 0),
(569, 134, 4, 0),
(569, 135, 4, 0),
(569, 136, 4, 0),
(569, 137, 4, 0),
(569, 139, 4, 0),
(569, 353, 4, 0),
(569, 354, 4, 0),
(569, 355, 4, 0),
(569, 356, 4, 0),
(569, 357, 4, 0),
(570, 358, 25, 0),
(570, 359, 25, 0),
(570, 360, 25, 0),
(570, 361, 25, 0),
(573, 360, 25, 0),
(573, 361, 25, 0),
(573, 362, 25, 0),
(573, 363, 25, 0),
(576, 360, 25, 0),
(576, 361, 25, 0),
(576, 362, 25, 0),
(576, 363, 25, 0),
(577, 353, 20, 0),
(577, 354, 20, 0),
(577, 355, 20, 0),
(577, 356, 20, 0),
(577, 357, 20, 0),
(578, 360, 25, 0),
(578, 361, 25, 0),
(578, 362, 25, 0),
(578, 363, 25, 0),
(583, 352, 16, 0),
(583, 364, 16, 0),
(583, 365, 16, 0),
(583, 366, 16, 0),
(583, 370, 16, 0),
(583, 371, 16, 0),
(584, 352, 20, 0),
(584, 366, 20, 0),
(584, 367, 20, 0),
(584, 368, 20, 0),
(584, 369, 20, 0),
(585, 353, 20, 0),
(585, 354, 20, 0),
(585, 355, 20, 0),
(585, 356, 20, 0),
(585, 357, 20, 0),
(586, 360, 25, 0),
(586, 361, 25, 0),
(586, 362, 25, 0),
(586, 363, 25, 0),
(587, 358, 25, 0),
(587, 359, 25, 0),
(587, 360, 25, 0),
(587, 361, 25, 0),
(629, 377, 9, 0),
(629, 378, 9, 0),
(629, 379, 9, 0),
(629, 380, 9, 0),
(629, 381, 9, 0),
(629, 385, 9, 0),
(629, 397, 9, 0),
(629, 398, 9, 0),
(629, 399, 9, 0),
(629, 403, 9, 0),
(629, 404, 9, 0),
(630, 378, 10, 0),
(630, 379, 10, 0),
(630, 380, 10, 0),
(630, 382, 10, 0),
(630, 383, 10, 0),
(630, 385, 10, 0),
(630, 399, 10, 0),
(630, 400, 10, 0),
(630, 401, 10, 0),
(630, 402, 10, 0),
(631, 377, 10, 0),
(631, 378, 10, 0),
(631, 379, 10, 0),
(631, 380, 10, 0),
(631, 381, 10, 0),
(631, 386, 10, 0),
(631, 387, 10, 0),
(631, 388, 10, 0),
(631, 389, 10, 0),
(631, 390, 10, 0),
(632, 377, 12, 0),
(632, 380, 12, 0),
(632, 381, 12, 0),
(632, 384, 12, 0),
(632, 393, 12, 0),
(632, 394, 12, 0),
(632, 395, 12, 0),
(632, 396, 12, 0),
(633, 377, 12, 0),
(633, 378, 12, 0),
(633, 380, 12, 0),
(633, 381, 12, 0),
(633, 391, 12, 0),
(633, 392, 12, 0),
(633, 393, 12, 0),
(633, 394, 12, 0),
(634, 461, 9, 0),
(634, 462, 9, 0),
(634, 463, 9, 0),
(634, 464, 9, 0),
(634, 465, 9, 0),
(634, 469, 9, 0),
(634, 481, 9, 0),
(634, 482, 9, 0),
(634, 483, 9, 0),
(634, 487, 9, 0),
(634, 488, 9, 0),
(635, 462, 10, 0),
(635, 463, 10, 0),
(635, 464, 10, 0),
(635, 466, 10, 0),
(635, 467, 10, 0),
(635, 469, 10, 0),
(635, 483, 10, 0),
(635, 484, 10, 0),
(635, 485, 10, 0),
(635, 486, 10, 0),
(636, 461, 10, 0),
(636, 462, 10, 0),
(636, 463, 10, 0),
(636, 464, 10, 0),
(636, 465, 10, 0),
(636, 470, 10, 0),
(636, 471, 10, 0),
(636, 472, 10, 0),
(636, 473, 10, 0),
(636, 474, 10, 0),
(637, 461, 12, 0),
(637, 464, 12, 0),
(637, 465, 12, 0),
(637, 468, 12, 0),
(637, 477, 12, 0),
(637, 478, 12, 0),
(637, 479, 12, 0),
(637, 480, 12, 0),
(638, 461, 12, 0),
(638, 462, 12, 0),
(638, 464, 12, 0),
(638, 465, 12, 0),
(638, 475, 12, 0),
(638, 476, 12, 0),
(638, 477, 12, 0),
(638, 478, 12, 0),
(639, 405, 9, 0),
(639, 406, 9, 0),
(639, 407, 9, 0),
(639, 408, 9, 0),
(639, 409, 9, 0),
(639, 413, 9, 0),
(639, 425, 9, 0),
(639, 426, 9, 0),
(639, 427, 9, 0),
(639, 431, 9, 0),
(639, 432, 9, 0),
(640, 406, 10, 0),
(640, 407, 10, 0),
(640, 408, 10, 0),
(640, 410, 10, 0),
(640, 411, 10, 0),
(640, 413, 10, 0),
(640, 427, 10, 0),
(640, 428, 10, 0),
(640, 429, 10, 0),
(640, 430, 10, 0),
(641, 405, 10, 0),
(641, 406, 10, 0),
(641, 407, 10, 0),
(641, 408, 10, 0),
(641, 409, 10, 0),
(641, 414, 10, 0),
(641, 415, 10, 0),
(641, 416, 10, 0),
(641, 417, 10, 0),
(641, 418, 10, 0),
(642, 405, 12, 0),
(642, 408, 12, 0),
(642, 409, 12, 0),
(642, 412, 12, 0),
(642, 421, 12, 0),
(642, 422, 12, 0),
(642, 423, 12, 0),
(642, 424, 12, 0),
(643, 405, 12, 0),
(643, 406, 12, 0),
(643, 408, 12, 0),
(643, 409, 12, 0),
(643, 419, 12, 0),
(643, 420, 12, 0),
(643, 421, 12, 0),
(643, 422, 12, 0),
(644, 433, 10, 0),
(644, 434, 10, 0),
(644, 435, 10, 0),
(644, 436, 10, 0),
(644, 441, 10, 0),
(644, 453, 10, 0),
(644, 454, 10, 0),
(644, 455, 10, 0),
(644, 459, 10, 0),
(644, 460, 10, 0),
(645, 434, 10, 0),
(645, 435, 10, 0),
(645, 436, 10, 0),
(645, 438, 10, 0),
(645, 439, 10, 0),
(645, 441, 10, 0),
(645, 455, 10, 0),
(645, 456, 10, 0),
(645, 457, 10, 0),
(645, 458, 10, 0),
(646, 433, 10, 0),
(646, 434, 10, 0),
(646, 435, 10, 0),
(646, 436, 10, 0),
(646, 437, 10, 0),
(646, 442, 10, 0),
(646, 443, 10, 0),
(646, 444, 10, 0),
(646, 445, 10, 0),
(646, 446, 10, 0),
(647, 433, 12, 0),
(647, 436, 12, 0),
(647, 437, 12, 0),
(647, 440, 12, 0),
(647, 449, 12, 0),
(647, 450, 12, 0),
(647, 451, 12, 0),
(647, 452, 12, 0),
(648, 433, 12, 0),
(648, 434, 12, 0),
(648, 436, 12, 0),
(648, 437, 12, 0),
(648, 447, 12, 0),
(648, 448, 12, 0),
(648, 449, 12, 0),
(648, 450, 12, 0),
(651, 345, 11, 0),
(651, 346, 11, 0),
(651, 347, 11, 0),
(651, 349, 11, 0),
(651, 350, 11, 0),
(651, 366, 11, 0),
(651, 367, 11, 0),
(651, 368, 11, 0),
(651, 369, 11, 0),
(652, 344, 9, 0),
(652, 345, 9, 0),
(652, 346, 9, 0),
(652, 347, 9, 0),
(652, 348, 9, 0),
(652, 352, 9, 0),
(652, 364, 9, 0),
(652, 365, 9, 0),
(652, 366, 9, 0),
(652, 370, 9, 0),
(652, 371, 9, 0),
(653, 344, 10, 0),
(653, 345, 10, 0),
(653, 346, 10, 0),
(653, 347, 10, 0),
(653, 348, 10, 0),
(653, 353, 10, 0),
(653, 354, 10, 0),
(653, 355, 10, 0),
(653, 356, 10, 0),
(653, 357, 10, 0),
(654, 344, 12, 0),
(654, 345, 12, 0),
(654, 347, 12, 0),
(654, 348, 12, 0),
(654, 358, 12, 0),
(654, 359, 12, 0),
(654, 360, 12, 0),
(654, 361, 12, 0),
(655, 344, 12, 0),
(655, 347, 12, 0),
(655, 348, 12, 0),
(655, 351, 12, 0),
(655, 360, 12, 0),
(655, 361, 12, 0),
(655, 362, 12, 0),
(655, 363, 12, 0);

COMMIT;
