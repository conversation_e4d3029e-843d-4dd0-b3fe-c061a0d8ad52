name: Release

on:
    workflow_dispatch:
        # Inputs the workflow accepts.
        inputs:
            ref:
                description: 'Commit Reference'
                default: 'master'
                required: true
            tag:
                description: 'Version Tag'
                default: 'v0.0.0'
                required: true

jobs:
    build-and-release:
        runs-on: ubuntu-latest
        permissions:
            contents: write
        steps:
            - name: Install Go
              uses: actions/setup-go@v2
              with:
                  go-version: 1.23.x

            - name: Install Protoc
              uses: arduino/setup-protoc@v1
              with:
                  version: 3.x
                  repo-token: ${{ secrets.GITHUB_TOKEN }}

            - name: Install Protoc Go plugin
              run: go install google.golang.org/protobuf/cmd/protoc-gen-go@latest

            - name: Install Node
              uses: actions/setup-node@v3
              with:
                  node-version: 20

            - name: Checkout
              uses: actions/checkout@v2.3.1
              with:
                  ref: ${{ github.event.inputs.ref }}

            - name: Build 🔧
              run: |
                  make clean && VERSION=${{ github.event.inputs.tag }} make release

            - name: Test
              run: |
                  make test

            - name: Release
              uses: ncipollo/release-action@v1
              with:
                  commit: ${{ github.event.inputs.ref }}
                  tag: ${{ github.event.inputs.tag }}
                  generateReleaseNotes: true
                  makeLatest: true
                  artifactErrorsFailBuild: true
                  artifacts: |
                      wowsimcli-amd64-linux.zip
                      wowsimcli-windows.exe.zip
                      wowsimcli-arm64-darwin.zip
                      wowsimmop-amd64-darwin.zip
                      wowsimmop-arm64-darwin.zip
                      wowsimmop-amd64-linux.zip
                      wowsimmop-windows.exe.zip
