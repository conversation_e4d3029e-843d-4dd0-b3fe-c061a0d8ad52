{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Launch Sim",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "program": "${workspaceFolder}/sim/web/main.go",
        },
		{
            "name": "Test Current File",
            "type": "go",
            "request": "launch",
            "mode": "test",
            "program": "${fileDirname}",
            "args": ["--tags=with_db"],
            "env": {
                "DEBUG_FIRST_COMPARE": "1"
            },
            "showLog": true
        },
		{
            "name": "Test Balance Druid",
            "type": "go",
            "request": "launch",
            "mode": "test",
            "program": "${workspaceFolder}/sim/druid/balance/balance_test.go",
            "args": ["-test.v", /*"-test.run=TestBalance-AllItems-FoulGiftoftheDemonLord"*/],
            "buildFlags": "-tags=with_db",
            "env": {
                "DEBUG_FIRST_COMPARE": "1"
            },
            "showLog": true
        },
    ]
}
