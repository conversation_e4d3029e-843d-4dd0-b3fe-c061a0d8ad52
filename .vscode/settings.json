{"editor.defaultFormatter": "esbenp.prettier-vscode", "eslint.validate": ["javascript", "typescript"], "eslint.nodePath": "./node_modules", "eslint.workingDirectories": ["."], "javascript.preferences.importModuleSpecifier": "relative", "javascript.preferences.importModuleSpecifierEnding": "minimal", "typescript.preferences.importModuleSpecifier": "relative", "typescript.preferences.importModuleSpecifierEnding": "minimal", "[javascript][javascriptreact][typescript][typescriptreact][scss][html]": {"editor.codeActionsOnSave": {"source.fixAll.eslint": "always"}}, "json.schemas": [{"fileMatch": ["*.apl.json"], "url": "./schemas/apl_rotation.schema.json"}, {"fileMatch": ["*.gear.json"], "url": "./schemas/gear.schema.json"}], "Lua.diagnostics.globals": ["FONT_COLOR_CODE_CLOSE", "SELECTED_CHAT_FRAME", "DEFAULT_CHAT_FRAME", "InterfaceOptions_AddCategory", "NORMAL_FONT_COLOR", "GameFontNormal", "GameFontHighlight", "ACCEPT", "CANCEL", "GameFontHighlightLarge", "GameFontHighlightSmall", "SlashCmdList", "hash_SlashCmdList", "NORMAL_FONT_COLOR_CODE", "CLOSE", "PanelTemplates_TabResize", "PanelTemplates_SetDisabledTabState", "PanelTemplates_SelectTab", "PanelTemplates_DeselectTab", "SetDesaturation", "ColorPickerFrame", "OpacitySliderFrame", "ChatFontNormal", "OKAY", "NOT_BOUND", "NUM_BAG_SLOTS", "NUM_BANKBAGSLOTS", "InterfaceOptionsFrame_OpenToCategory", "BANK_CONTAINER", "CreateFrame", "geter<PERSON><PERSON><PERSON><PERSON>", "IsLoggedIn", "PlaySound", "GetRealmName", "UnitName", "UnitClass", "UnitRace", "UnitFactionGroup", "GetCurrentRegion", "GetLocale", "C_Container", "GetTalentInfo", "UnitFullName", "GetPlayerInfoByGUID", "UnitLevel", "tInvert", "GetItemInfo", "IsEquippableItem", "UnitGUID", "GetSpellInfo", "GetInventorySlotInfo", "GetInventoryItemLink", "max", "min", "ceil", "BackdropTemplateMixin", "floor", "GetCursorInfo", "ClearCursor", "hooksecurefunc", "GetMacroInfo", "IsShiftKeyDown", "IsControlKeyDown", "IsAltKeyDown", "issecurevariable", "GetTime", "C_Timer", "format", "gsub", "strfind", "strsub", "strchar", "strbyte", "tinsert", "UIParent"], "[go]": {"editor.defaultFormatter": "golang.go"}, "makefile.configureOnOpen": false}