character_stats_results: {
 key: "TestFrostMasterfrost-CharacterStats-Default"
 value: {
  final_stats: 19622.295
  final_stats: 223.65
  final_stats: 22366.3
  final_stats: 119.7
  final_stats: 150
  final_stats: 2836
  final_stats: 2468
  final_stats: 4617
  final_stats: 2552
  final_stats: 2e-05
  final_stats: 18062.05731
  final_stats: 11856
  final_stats: 43444.049
  final_stats: 0
  final_stats: 0
  final_stats: 0
  final_stats: 0
  final_stats: 34302
  final_stats: 0
  final_stats: 459531.2
  final_stats: 0
  final_stats: 0
  final_stats: 8.34118
  final_stats: 15.84706
  final_stats: 14.1357
  final_stats: 9.11333
  final_stats: 0
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-AgilePrimalDiamond"
 value: {
  dps: 150767.02457
  tps: 139384.82237
  hps: 1608.0651
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-AssuranceofConsequence-105472"
 value: {
  dps: 150247.08187
  tps: 138843.39975
  hps: 1585.08854
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-AusterePrimalDiamond"
 value: {
  dps: 148836.26857
  tps: 137460.40048
  hps: 1619.78403
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-BattlegearoftheLostCatacomb"
 value: {
  dps: 137400.53325
  tps: 126697.3221
  hps: 1627.87876
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-BattleplateofCyclopeanDread"
 value: {
  dps: 157284.76363
  tps: 145715.6507
  hps: 1801.76083
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-BattleplateoftheAll-ConsumingMaw"
 value: {
  dps: 151255.82593
  tps: 136514.9768
  hps: 1842.88223
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-BurningPrimalDiamond"
 value: {
  dps: 150749.27717
  tps: 139369.84301
  hps: 1608.0651
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-CapacitivePrimalDiamond"
 value: {
  dps: 149456.24957
  tps: 138026.69432
  hps: 1611.57592
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-CourageousPrimalDiamond"
 value: {
  dps: 148836.26857
  tps: 137460.40048
  hps: 1602.38529
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-DelicateVialoftheSanguinaire-96895"
 value: {
  dps: 150124.6242
  tps: 138747.51235
  hps: 1585.08854
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-DestructivePrimalDiamond"
 value: {
  dps: 149603.67729
  tps: 138148.94196
  hps: 1613.87357
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-EffulgentPrimalDiamond"
 value: {
  dps: 148836.26857
  tps: 137460.40048
  hps: 1619.78403
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-EmberPrimalDiamond"
 value: {
  dps: 148836.26857
  tps: 137460.40048
  hps: 1602.38529
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-EnchantWeapon-BloodyDancingSteel-5125"
 value: {
  dps: 145856.70746
  tps: 134766.54138
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-EnchantWeapon-Colossus-4445"
 value: {
  dps: 142097.55155
  tps: 131458.5577
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-EnchantWeapon-DancingSteel-4444"
 value: {
  dps: 146103.62434
  tps: 134972.31591
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-EnchantWeapon-JadeSpirit-4442"
 value: {
  dps: 142097.55155
  tps: 131458.5577
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-EnchantWeapon-River'sSong-4446"
 value: {
  dps: 142097.55155
  tps: 131458.5577
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-EnchantWeapon-SpiritofConquest-5124"
 value: {
  dps: 142097.55155
  tps: 131458.5577
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-EnchantWeapon-Windsong-4441"
 value: {
  dps: 144042.01731
  tps: 133249.91426
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-EnigmaticPrimalDiamond"
 value: {
  dps: 149603.67729
  tps: 138148.94196
  hps: 1613.87357
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-EternalPrimalDiamond"
 value: {
  dps: 148836.26857
  tps: 137460.40048
  hps: 1602.38529
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-EvilEyeofGalakras-105491"
 value: {
  dps: 162855.01106
  tps: 150318.00955
  hps: 1589.54599
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-FabledFeatherofJi-Kun-96842"
 value: {
  dps: 187220.2239
  tps: 171430.76951
  hps: 1641.58331
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-Fen-Yu,FuryofXuen-102248"
 value: {
  dps: 157410.96985
  tps: 146126.2693
  hps: 1743.10165
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-FleetPrimalDiamond"
 value: {
  dps: 149855.61221
  tps: 138479.74412
  hps: 1602.38529
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-ForlornPrimalDiamond"
 value: {
  dps: 148836.26857
  tps: 137460.40048
  hps: 1602.38529
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-GazeoftheTwins-96915"
 value: {
  dps: 161637.78204
  tps: 149106.23136
  hps: 1622.5679
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-Gong-Lu,StrengthofXuen-102249"
 value: {
  dps: 167172.97611
  tps: 154991.77995
  hps: 1716.52305
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-Horridon'sLastGasp-96757"
 value: {
  dps: 150120.5719
  tps: 138743.46005
  hps: 1585.08854
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-ImpassivePrimalDiamond"
 value: {
  dps: 149603.67729
  tps: 138148.94196
  hps: 1613.87357
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-IndomitablePrimalDiamond"
 value: {
  dps: 148836.26857
  tps: 137460.40048
  hps: 1619.78403
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-InscribedBagofHydra-Spawn-96828"
 value: {
  dps: 150124.6242
  tps: 138747.51235
  hps: 1585.08854
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-Ji-Kun'sRisingWinds-96843"
 value: {
  dps: 150124.6242
  tps: 138747.51235
  hps: 1585.08854
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-PhaseFingers-4697"
 value: {
  dps: 150991.79029
  tps: 139589.46535
  hps: 1608.0651
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-PlateofCyclopeanDread"
 value: {
  dps: 142125.16443
  tps: 131405.67899
  hps: 1668.10957
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-PlateoftheAll-ConsumingMaw"
 value: {
  dps: 136005.25257
  tps: 125407.07458
  hps: 1729.94102
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-PlateoftheLostCatacomb"
 value: {
  dps: 124525.16132
  tps: 114853.21189
  hps: 1536.21892
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-PowerfulPrimalDiamond"
 value: {
  dps: 148836.26857
  tps: 137460.40048
  hps: 1619.78403
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-PriceofProgress-81266"
 value: {
  dps: 150124.6242
  tps: 138747.51235
  hps: 1585.08854
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-Primordius'TalismanofRage-96873"
 value: {
  dps: 162328.35915
  tps: 149742.70282
  hps: 1632.30997
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-Qian-Le,CourageofNiuzao-102245"
 value: {
  dps: 150344.00292
  tps: 139202.11305
  hps: 1697.19316
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-Qian-Ying,FortitudeofNiuzao-102250"
 value: {
  dps: 157768.40757
  tps: 145966.98825
  hps: 1671.58106
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-Renataki'sSoulCharm-96741"
 value: {
  dps: 150627.8096
  tps: 139150.43275
  hps: 1585.08854
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-ReverberatingPrimalDiamond"
 value: {
  dps: 151888.03442
  tps: 140401.11314
  hps: 1608.0651
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-RevitalizingPrimalDiamond"
 value: {
  dps: 150749.27717
  tps: 139369.84301
  hps: 1608.0651
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-RuneofCinderglacier-3369"
 value: {
  dps: 143945.86708
  tps: 133306.87323
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-RuneofSpellbreaking-3595"
 value: {
  dps: 142097.55155
  tps: 131458.5577
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-RuneofSpellshattering-3367"
 value: {
  dps: 142097.55155
  tps: 131458.5577
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-RuneofSwordbreaking-3594"
 value: {
  dps: 142097.55155
  tps: 131458.5577
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-RuneofSwordshattering-3365"
 value: {
  dps: 142097.55155
  tps: 131458.5577
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-RuneoftheNerubianCarapace-3883"
 value: {
  dps: 142097.55155
  tps: 131458.5577
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-RuneoftheStoneskinGargoyle-3847"
 value: {
  dps: 142097.55155
  tps: 131458.5577
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-SinisterPrimalDiamond"
 value: {
  dps: 149456.24957
  tps: 138026.69432
  hps: 1611.57592
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-SynapseSprings(MarkII)-4898"
 value: {
  dps: 153077.61473
  tps: 141336.64515
  hps: 1608.0651
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-TalismanofBloodlust-96864"
 value: {
  dps: 153157.12699
  tps: 141329.96491
  hps: 1644.8276
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-TyrannicalPrimalDiamond"
 value: {
  dps: 148836.26857
  tps: 137460.40048
  hps: 1602.38529
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-UnerringVisionofLeiShen-96930"
 value: {
  dps: 153691.3567
  tps: 142073.32093
  hps: 1696.03775
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-Wushoolay'sFinalChoice-96785"
 value: {
  dps: 151469.29078
  tps: 140032.15201
  hps: 1641.58331
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-Xing-Ho,BreathofYu'lon-102246"
 value: {
  dps: 152081.38393
  tps: 140733.80006
  hps: 1728.60423
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-YaungolFireCarrier-86518"
 value: {
  dps: 151888.03442
  tps: 140401.11314
  hps: 1608.0651
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-ZenAlchemistStone-75274"
 value: {
  dps: 159227.12854
  tps: 146874.48078
  hps: 1585.08854
 }
}
dps_results: {
 key: "TestFrostMasterfrost-Average-Default"
 value: {
  dps: 155513.30814
  tps: 143609.8704
  hps: 1518.1863
 }
}
dps_results: {
 key: "TestFrostMasterfrost-Settings-Orc-p1.masterfrost-Basic-masterfrost-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 604504.41765
  tps: 592506.72632
  hps: 1598.92806
 }
}
dps_results: {
 key: "TestFrostMasterfrost-Settings-Orc-p1.masterfrost-Basic-masterfrost-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 154658.94437
  tps: 142851.75313
  hps: 1598.92806
 }
}
dps_results: {
 key: "TestFrostMasterfrost-Settings-Orc-p1.masterfrost-Basic-masterfrost-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 201263.89266
  tps: 155922.24032
  hps: 1807.85632
 }
}
dps_results: {
 key: "TestFrostMasterfrost-Settings-Orc-p1.masterfrost-Basic-masterfrost-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 447285.31678
  tps: 440377.74151
  hps: 1401.17918
 }
}
dps_results: {
 key: "TestFrostMasterfrost-Settings-Orc-p1.masterfrost-Basic-masterfrost-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 116988.66918
  tps: 110081.09391
  hps: 1401.17918
 }
}
dps_results: {
 key: "TestFrostMasterfrost-Settings-Orc-p1.masterfrost-Basic-masterfrost-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 132651.16967
  tps: 109053.57427
  hps: 1489.16241
 }
}
dps_results: {
 key: "TestFrostMasterfrost-Settings-Troll-p1.masterfrost-Basic-masterfrost-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 598521.58255
  tps: 586550.09717
  hps: 1608.0651
 }
}
dps_results: {
 key: "TestFrostMasterfrost-Settings-Troll-p1.masterfrost-Basic-masterfrost-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 153973.85886
  tps: 142148.29294
  hps: 1608.0651
 }
}
dps_results: {
 key: "TestFrostMasterfrost-Settings-Troll-p1.masterfrost-Basic-masterfrost-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 200098.16481
  tps: 154097.14377
  hps: 1831.4616
 }
}
dps_results: {
 key: "TestFrostMasterfrost-Settings-Troll-p1.masterfrost-Basic-masterfrost-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 437054.74261
  tps: 430290.60812
  hps: 1416.22095
 }
}
dps_results: {
 key: "TestFrostMasterfrost-Settings-Troll-p1.masterfrost-Basic-masterfrost-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 116468.5888
  tps: 109704.45431
  hps: 1416.22095
 }
}
dps_results: {
 key: "TestFrostMasterfrost-Settings-Troll-p1.masterfrost-Basic-masterfrost-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 131463.26269
  tps: 107971.78344
  hps: 1521.44392
 }
}
dps_results: {
 key: "TestFrostMasterfrost-Settings-Worgen-p1.masterfrost-Basic-masterfrost-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 601986.11442
  tps: 590429.66838
  hps: 1613.48757
 }
}
dps_results: {
 key: "TestFrostMasterfrost-Settings-Worgen-p1.masterfrost-Basic-masterfrost-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 153971.07433
  tps: 142591.9341
  hps: 1613.48757
 }
}
dps_results: {
 key: "TestFrostMasterfrost-Settings-Worgen-p1.masterfrost-Basic-masterfrost-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 198451.04224
  tps: 154819.2624
  hps: 1844.32847
 }
}
dps_results: {
 key: "TestFrostMasterfrost-Settings-Worgen-p1.masterfrost-Basic-masterfrost-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 444318.13981
  tps: 437685.0044
  hps: 1412.5569
 }
}
dps_results: {
 key: "TestFrostMasterfrost-Settings-Worgen-p1.masterfrost-Basic-masterfrost-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 116361.545
  tps: 109728.40959
  hps: 1412.5569
 }
}
dps_results: {
 key: "TestFrostMasterfrost-Settings-Worgen-p1.masterfrost-Basic-masterfrost-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 130817.70505
  tps: 108244.3002
  hps: 1489.11404
 }
}
dps_results: {
 key: "TestFrostMasterfrost-SwitchInFrontOfTarget-Default"
 value: {
  dps: 144500.13863
  tps: 133463.3063
  hps: 1497.22617
 }
}
