character_stats_results: {
 key: "TestFrostMasterfrost-CharacterStats-Default"
 value: {
  final_stats: 19622.295
  final_stats: 223.65
  final_stats: 22366.3
  final_stats: 119.7
  final_stats: 150
  final_stats: 2836
  final_stats: 2468
  final_stats: 4617
  final_stats: 2552
  final_stats: 2e-05
  final_stats: 18062.05731
  final_stats: 11856
  final_stats: 43444.049
  final_stats: 0
  final_stats: 0
  final_stats: 0
  final_stats: 0
  final_stats: 34302
  final_stats: 0
  final_stats: 459531.2
  final_stats: 0
  final_stats: 0
  final_stats: 8.34118
  final_stats: 15.84706
  final_stats: 14.1357
  final_stats: 9.11333
  final_stats: 0
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-AgilePrimalDiamond"
 value: {
  dps: 152433.55112
  tps: 140961.2256
  hps: 1640.09442
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-AssuranceofConsequence-105472"
 value: {
  dps: 151540.61829
  tps: 140043.39813
  hps: 1610.36276
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-AusterePrimalDiamond"
 value: {
  dps: 150461.12681
  tps: 138994.9886
  hps: 1652.30049
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-BattlegearoftheLostCatacomb"
 value: {
  dps: 138867.2063
  tps: 128030.60363
  hps: 1647.12732
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-BattleplateofCyclopeanDread"
 value: {
  dps: 158410.86564
  tps: 146671.86486
  hps: 1814.92346
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-BattleplateoftheAll-ConsumingMaw"
 value: {
  dps: 152148.98953
  tps: 137130.03185
  hps: 1842.88223
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-BurningPrimalDiamond"
 value: {
  dps: 152418.27011
  tps: 140948.66862
  hps: 1640.09442
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-CapacitivePrimalDiamond"
 value: {
  dps: 151108.96611
  tps: 139585.80805
  hps: 1643.7431
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-CourageousPrimalDiamond"
 value: {
  dps: 150461.12681
  tps: 138994.9886
  hps: 1634.55248
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-DelicateVialoftheSanguinaire-96895"
 value: {
  dps: 151438.86316
  tps: 139972.47797
  hps: 1610.36276
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-DestructivePrimalDiamond"
 value: {
  dps: 151270.96151
  tps: 139724.5416
  hps: 1646.04076
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-EffulgentPrimalDiamond"
 value: {
  dps: 150461.12681
  tps: 138994.9886
  hps: 1652.30049
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-EmberPrimalDiamond"
 value: {
  dps: 150461.12681
  tps: 138994.9886
  hps: 1634.55248
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-EnchantWeapon-BloodyDancingSteel-5125"
 value: {
  dps: 147229.88737
  tps: 136079.56407
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-EnchantWeapon-Colossus-4445"
 value: {
  dps: 143580.63066
  tps: 132880.3825
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-EnchantWeapon-DancingSteel-4444"
 value: {
  dps: 147583.82828
  tps: 136392.27975
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-EnchantWeapon-JadeSpirit-4442"
 value: {
  dps: 143580.63066
  tps: 132880.3825
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-EnchantWeapon-River'sSong-4446"
 value: {
  dps: 143580.63066
  tps: 132880.3825
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-EnchantWeapon-SpiritofConquest-5124"
 value: {
  dps: 143580.63066
  tps: 132880.3825
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-EnchantWeapon-Windsong-4441"
 value: {
  dps: 145063.29037
  tps: 134116.7223
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-EnigmaticPrimalDiamond"
 value: {
  dps: 151270.96151
  tps: 139724.5416
  hps: 1646.04076
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-EternalPrimalDiamond"
 value: {
  dps: 150461.12681
  tps: 138994.9886
  hps: 1634.55248
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-EvilEyeofGalakras-105491"
 value: {
  dps: 162975.73849
  tps: 150341.61433
  hps: 1608.0651
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-FabledFeatherofJi-Kun-96842"
 value: {
  dps: 187920.19434
  tps: 172140.35954
  hps: 1655.10271
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-Fen-Yu,FuryofXuen-102248"
 value: {
  dps: 158478.13619
  tps: 147035.31811
  hps: 1733.4367
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-FleetPrimalDiamond"
 value: {
  dps: 151493.09598
  tps: 140026.95777
  hps: 1634.55248
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-ForlornPrimalDiamond"
 value: {
  dps: 150461.12681
  tps: 138994.9886
  hps: 1634.55248
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-GazeoftheTwins-96915"
 value: {
  dps: 162906.06428
  tps: 150260.01429
  hps: 1661.91297
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-Gong-Lu,StrengthofXuen-102249"
 value: {
  dps: 168169.39638
  tps: 155838.87351
  hps: 1749.49984
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-Horridon'sLastGasp-96757"
 value: {
  dps: 151438.86316
  tps: 139972.47797
  hps: 1610.36276
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-ImpassivePrimalDiamond"
 value: {
  dps: 151270.96151
  tps: 139724.5416
  hps: 1646.04076
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-IndomitablePrimalDiamond"
 value: {
  dps: 150461.12681
  tps: 138994.9886
  hps: 1652.30049
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-InscribedBagofHydra-Spawn-96828"
 value: {
  dps: 151438.86316
  tps: 139972.47797
  hps: 1610.36276
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-Ji-Kun'sRisingWinds-96843"
 value: {
  dps: 151438.86316
  tps: 139972.47797
  hps: 1610.36276
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-PhaseFingers-4697"
 value: {
  dps: 152663.29965
  tps: 141170.62474
  hps: 1640.09442
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-PlateofCyclopeanDread"
 value: {
  dps: 144635.87327
  tps: 133879.72392
  hps: 1716.20377
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-PlateoftheAll-ConsumingMaw"
 value: {
  dps: 137474.3256
  tps: 126803.32705
  hps: 1734.9164
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-PlateoftheLostCatacomb"
 value: {
  dps: 125857.01082
  tps: 116129.98475
  hps: 1570.59136
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-PowerfulPrimalDiamond"
 value: {
  dps: 150461.12681
  tps: 138994.9886
  hps: 1652.30049
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-PriceofProgress-81266"
 value: {
  dps: 151438.86316
  tps: 139972.47797
  hps: 1610.36276
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-Primordius'TalismanofRage-96873"
 value: {
  dps: 164280.14092
  tps: 151547.52757
  hps: 1658.53082
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-Qian-Le,CourageofNiuzao-102245"
 value: {
  dps: 151397.02114
  tps: 140083.53368
  hps: 1728.60423
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-Qian-Ying,FortitudeofNiuzao-102250"
 value: {
  dps: 158848.29453
  tps: 146877.34636
  hps: 1702.99213
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-Renataki'sSoulCharm-96741"
 value: {
  dps: 151882.08736
  tps: 140313.91134
  hps: 1610.36276
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-ReverberatingPrimalDiamond"
 value: {
  dps: 153568.84359
  tps: 141990.89734
  hps: 1640.09442
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-RevitalizingPrimalDiamond"
 value: {
  dps: 152418.27011
  tps: 140948.66862
  hps: 1640.09442
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-RuneofCinderglacier-3369"
 value: {
  dps: 145356.85626
  tps: 134656.6081
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-RuneofSpellbreaking-3595"
 value: {
  dps: 143580.63066
  tps: 132880.3825
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-RuneofSpellshattering-3367"
 value: {
  dps: 143580.63066
  tps: 132880.3825
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-RuneofSwordbreaking-3594"
 value: {
  dps: 143580.63066
  tps: 132880.3825
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-RuneofSwordshattering-3365"
 value: {
  dps: 143580.63066
  tps: 132880.3825
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-RuneoftheNerubianCarapace-3883"
 value: {
  dps: 143580.63066
  tps: 132880.3825
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-RuneoftheStoneskinGargoyle-3847"
 value: {
  dps: 143580.63066
  tps: 132880.3825
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-SinisterPrimalDiamond"
 value: {
  dps: 151108.96611
  tps: 139585.80805
  hps: 1643.7431
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-SynapseSprings(MarkII)-4898"
 value: {
  dps: 154785.33169
  tps: 142942.11902
  hps: 1640.09442
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-TalismanofBloodlust-96864"
 value: {
  dps: 154923.02997
  tps: 143018.51911
  hps: 1674.29274
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-TyrannicalPrimalDiamond"
 value: {
  dps: 150461.12681
  tps: 138994.9886
  hps: 1634.55248
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-UnerringVisionofLeiShen-96930"
 value: {
  dps: 155572.07684
  tps: 143755.85959
  hps: 1696.55243
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-Wushoolay'sFinalChoice-96785"
 value: {
  dps: 152935.06976
  tps: 141467.01675
  hps: 1652.80506
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-Xing-Ho,BreathofYu'lon-102246"
 value: {
  dps: 152907.26181
  tps: 141479.05018
  hps: 1732.44121
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-YaungolFireCarrier-86518"
 value: {
  dps: 153568.84359
  tps: 141990.89734
  hps: 1640.09442
 }
}
dps_results: {
 key: "TestFrostMasterfrost-AllItems-ZenAlchemistStone-75274"
 value: {
  dps: 160659.38401
  tps: 148207.80889
  hps: 1610.36276
 }
}
dps_results: {
 key: "TestFrostMasterfrost-Average-Default"
 value: {
  dps: 156575.39597
  tps: 144547.88019
  hps: 1526.16692
 }
}
dps_results: {
 key: "TestFrostMasterfrost-Settings-Orc-p1.masterfrost-Basic-masterfrost-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 608504.76315
  tps: 596365.40915
  hps: 1609.06566
 }
}
dps_results: {
 key: "TestFrostMasterfrost-Settings-Orc-p1.masterfrost-Basic-masterfrost-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 155463.54042
  tps: 143555.14823
  hps: 1609.06566
 }
}
dps_results: {
 key: "TestFrostMasterfrost-Settings-Orc-p1.masterfrost-Basic-masterfrost-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 202100.40315
  tps: 156436.47441
  hps: 1831.52297
 }
}
dps_results: {
 key: "TestFrostMasterfrost-Settings-Orc-p1.masterfrost-Basic-masterfrost-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 444508.37001
  tps: 437602.16257
  hps: 1399.02379
 }
}
dps_results: {
 key: "TestFrostMasterfrost-Settings-Orc-p1.masterfrost-Basic-masterfrost-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 117308.97626
  tps: 110402.76882
  hps: 1399.02379
 }
}
dps_results: {
 key: "TestFrostMasterfrost-Settings-Orc-p1.masterfrost-Basic-masterfrost-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 136172.21622
  tps: 112510.50327
  hps: 1564.60123
 }
}
dps_results: {
 key: "TestFrostMasterfrost-Settings-Troll-p1.masterfrost-Basic-masterfrost-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 602709.85031
  tps: 590628.5865
  hps: 1640.09442
 }
}
dps_results: {
 key: "TestFrostMasterfrost-Settings-Troll-p1.masterfrost-Basic-masterfrost-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 155690.87563
  tps: 143762.39163
  hps: 1640.09442
 }
}
dps_results: {
 key: "TestFrostMasterfrost-Settings-Troll-p1.masterfrost-Basic-masterfrost-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 204072.03922
  tps: 157457.73647
  hps: 1842.94988
 }
}
dps_results: {
 key: "TestFrostMasterfrost-Settings-Troll-p1.masterfrost-Basic-masterfrost-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 440624.17905
  tps: 433832.00825
  hps: 1429.1529
 }
}
dps_results: {
 key: "TestFrostMasterfrost-Settings-Troll-p1.masterfrost-Basic-masterfrost-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 117353.95519
  tps: 110561.78439
  hps: 1429.1529
 }
}
dps_results: {
 key: "TestFrostMasterfrost-Settings-Troll-p1.masterfrost-Basic-masterfrost-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 135004.80717
  tps: 111345.00562
  hps: 1586.10367
 }
}
dps_results: {
 key: "TestFrostMasterfrost-Settings-Worgen-p1.masterfrost-Basic-masterfrost-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 606314.66517
  tps: 594613.95103
  hps: 1623.62483
 }
}
dps_results: {
 key: "TestFrostMasterfrost-Settings-Worgen-p1.masterfrost-Basic-masterfrost-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 154889.57301
  tps: 143426.05508
  hps: 1623.62483
 }
}
dps_results: {
 key: "TestFrostMasterfrost-Settings-Worgen-p1.masterfrost-Basic-masterfrost-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 199606.77633
  tps: 155748.76967
  hps: 1867.99433
 }
}
dps_results: {
 key: "TestFrostMasterfrost-Settings-Worgen-p1.masterfrost-Basic-masterfrost-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 441937.36893
  tps: 435297.22181
  hps: 1412.68622
 }
}
dps_results: {
 key: "TestFrostMasterfrost-Settings-Worgen-p1.masterfrost-Basic-masterfrost-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 116618.44805
  tps: 109978.30093
  hps: 1412.68622
 }
}
dps_results: {
 key: "TestFrostMasterfrost-Settings-Worgen-p1.masterfrost-Basic-masterfrost-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 134432.4443
  tps: 111750.9815
  hps: 1564.55042
 }
}
dps_results: {
 key: "TestFrostMasterfrost-SwitchInFrontOfTarget-Default"
 value: {
  dps: 145323.97402
  tps: 134210.10222
  hps: 1502.90598
 }
}
