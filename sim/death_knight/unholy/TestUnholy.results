character_stats_results: {
 key: "TestUnholy-CharacterStats-Default"
 value: {
  final_stats: 27463.4955
  final_stats: 218.4
  final_stats: 22694.1
  final_stats: 120.75
  final_stats: 151
  final_stats: 2570
  final_stats: 6524
  final_stats: 5049
  final_stats: 2680
  final_stats: 0
  final_stats: 25355.99596
  final_stats: 6531
  final_stats: 60694.6901
  final_stats: 0
  final_stats: 0
  final_stats: 0
  final_stats: 0
  final_stats: 34302
  final_stats: 0
  final_stats: 464120.4
  final_stats: 0
  final_stats: 0
  final_stats: 7.55882
  final_stats: 15.44118
  final_stats: 20.89517
  final_stats: 15.87333
  final_stats: 0
 }
}
dps_results: {
 key: "TestUnholy-AllItems-AgilePrimalDiamond"
 value: {
  dps: 138034.23889
  tps: 101021.03981
  hps: 1854.53229
 }
}
dps_results: {
 key: "TestUnholy-AllItems-AssuranceofConsequence-105472"
 value: {
  dps: 134181.29034
  tps: 98383.30165
  hps: 1854.53229
 }
}
dps_results: {
 key: "TestUnholy-AllItems-AusterePrimalDiamond"
 value: {
  dps: 137167.69359
  tps: 100169.09757
  hps: 1862.3667
 }
}
dps_results: {
 key: "TestUnholy-AllItems-BattlegearoftheLostCatacomb"
 value: {
  dps: 123606.3787
  tps: 91063.76285
  hps: 1801.09876
 }
}
dps_results: {
 key: "TestUnholy-AllItems-BattleplateofCyclopeanDread"
 value: {
  dps: 137411.55882
  tps: 101387.68768
  hps: 2006.59523
 }
}
dps_results: {
 key: "TestUnholy-AllItems-BattleplateoftheAll-ConsumingMaw"
 value: {
  dps: 138378.1592
  tps: 100560.29383
  hps: 1979.88913
 }
}
dps_results: {
 key: "TestUnholy-AllItems-BurningPrimalDiamond"
 value: {
  dps: 138003.67612
  tps: 100999.76561
  hps: 1854.53229
 }
}
dps_results: {
 key: "TestUnholy-AllItems-CapacitivePrimalDiamond"
 value: {
  dps: 138047.45333
  tps: 100665.33147
  hps: 1849.51979
 }
}
dps_results: {
 key: "TestUnholy-AllItems-CourageousPrimalDiamond"
 value: {
  dps: 137167.69359
  tps: 100169.09757
  hps: 1842.55799
 }
}
dps_results: {
 key: "TestUnholy-AllItems-DelicateVialoftheSanguinaire-96895"
 value: {
  dps: 134075.98284
  tps: 98302.00168
  hps: 1854.53229
 }
}
dps_results: {
 key: "TestUnholy-AllItems-DestructivePrimalDiamond"
 value: {
  dps: 138264.67997
  tps: 100772.67617
  hps: 1855.08924
 }
}
dps_results: {
 key: "TestUnholy-AllItems-EffulgentPrimalDiamond"
 value: {
  dps: 137167.69359
  tps: 100169.09757
  hps: 1862.3667
 }
}
dps_results: {
 key: "TestUnholy-AllItems-ElementiumDeathplateBattlearmor"
 value: {
  dps: 93094.84476
  tps: 70533.58163
  hps: 1292.75946
 }
}
dps_results: {
 key: "TestUnholy-AllItems-ElementiumDeathplateBattlegear"
 value: {
  dps: 98219.55346
  tps: 74297.19296
  hps: 1340.16309
 }
}
dps_results: {
 key: "TestUnholy-AllItems-EmberPrimalDiamond"
 value: {
  dps: 137167.69359
  tps: 100169.09757
  hps: 1842.55799
 }
}
dps_results: {
 key: "TestUnholy-AllItems-EnchantWeapon-BloodyDancingSteel-5125"
 value: {
  dps: 132367.73579
  tps: 96983.64652
 }
}
dps_results: {
 key: "TestUnholy-AllItems-EnchantWeapon-Colossus-4445"
 value: {
  dps: 128424.95147
  tps: 94354.56362
 }
}
dps_results: {
 key: "TestUnholy-AllItems-EnchantWeapon-DancingSteel-4444"
 value: {
  dps: 132599.95206
  tps: 97087.35781
 }
}
dps_results: {
 key: "TestUnholy-AllItems-EnchantWeapon-JadeSpirit-4442"
 value: {
  dps: 128424.95147
  tps: 94354.56362
 }
}
dps_results: {
 key: "TestUnholy-AllItems-EnchantWeapon-River'sSong-4446"
 value: {
  dps: 128424.95147
  tps: 94354.56362
 }
}
dps_results: {
 key: "TestUnholy-AllItems-EnchantWeapon-SpiritofConquest-5124"
 value: {
  dps: 128424.95147
  tps: 94354.56362
 }
}
dps_results: {
 key: "TestUnholy-AllItems-EnchantWeapon-Windsong-4441"
 value: {
  dps: 130184.87347
  tps: 95641.84529
 }
}
dps_results: {
 key: "TestUnholy-AllItems-EnigmaticPrimalDiamond"
 value: {
  dps: 138264.67997
  tps: 100772.67617
  hps: 1855.08924
 }
}
dps_results: {
 key: "TestUnholy-AllItems-EternalPrimalDiamond"
 value: {
  dps: 137167.69359
  tps: 100169.09757
  hps: 1842.55799
 }
}
dps_results: {
 key: "TestUnholy-AllItems-EvilEyeofGalakras-105491"
 value: {
  dps: 151792.01815
  tps: 107645.92715
  hps: 1962.39388
 }
}
dps_results: {
 key: "TestUnholy-AllItems-FabledFeatherofJi-Kun-96842"
 value: {
  dps: 176591.63536
  tps: 126949.97837
  hps: 1861.4941
 }
}
dps_results: {
 key: "TestUnholy-AllItems-FleetPrimalDiamond"
 value: {
  dps: 137830.99071
  tps: 100832.3947
  hps: 1842.55799
 }
}
dps_results: {
 key: "TestUnholy-AllItems-ForlornPrimalDiamond"
 value: {
  dps: 137167.69359
  tps: 100169.09757
  hps: 1842.55799
 }
}
dps_results: {
 key: "TestUnholy-AllItems-GazeoftheTwins-96915"
 value: {
  dps: 147934.15537
  tps: 107650.60792
  hps: 1918.33957
 }
}
dps_results: {
 key: "TestUnholy-AllItems-Horridon'sLastGasp-96757"
 value: {
  dps: 134075.98284
  tps: 98302.00168
  hps: 1854.53229
 }
}
dps_results: {
 key: "TestUnholy-AllItems-ImpassivePrimalDiamond"
 value: {
  dps: 138264.67997
  tps: 100772.67617
  hps: 1855.08924
 }
}
dps_results: {
 key: "TestUnholy-AllItems-IndomitablePrimalDiamond"
 value: {
  dps: 137167.69359
  tps: 100169.09757
  hps: 1862.3667
 }
}
dps_results: {
 key: "TestUnholy-AllItems-InscribedBagofHydra-Spawn-96828"
 value: {
  dps: 134075.98284
  tps: 98302.00168
  hps: 1854.53229
 }
}
dps_results: {
 key: "TestUnholy-AllItems-Ji-Kun'sRisingWinds-96843"
 value: {
  dps: 134075.98284
  tps: 98302.00168
  hps: 1854.53229
 }
}
dps_results: {
 key: "TestUnholy-AllItems-MagmaPlatedBattlearmor"
 value: {
  dps: 90772.85469
  tps: 68437.52506
  hps: 1260.22036
 }
}
dps_results: {
 key: "TestUnholy-AllItems-MagmaPlatedBattlegear"
 value: {
  dps: 95826.03656
  tps: 72352.79944
  hps: 1291.15926
 }
}
dps_results: {
 key: "TestUnholy-AllItems-NecroticBoneplateArmor"
 value: {
  dps: 91156.29362
  tps: 68950.94739
  hps: 1276.7279
 }
}
dps_results: {
 key: "TestUnholy-AllItems-NecroticBoneplateBattlegear"
 value: {
  dps: 94295.54193
  tps: 70784.52156
  hps: 1300.05379
 }
}
dps_results: {
 key: "TestUnholy-AllItems-PhaseFingers-4697"
 value: {
  dps: 138247.83003
  tps: 101167.46445
  hps: 1854.53229
 }
}
dps_results: {
 key: "TestUnholy-AllItems-PlateofCyclopeanDread"
 value: {
  dps: 127663.87234
  tps: 96188.11657
  hps: 1830.8649
 }
}
dps_results: {
 key: "TestUnholy-AllItems-PlateoftheAll-ConsumingMaw"
 value: {
  dps: 122214.544
  tps: 91175.62206
  hps: 1828.74872
 }
}
dps_results: {
 key: "TestUnholy-AllItems-PlateoftheLostCatacomb"
 value: {
  dps: 112468.41458
  tps: 84220.06577
  hps: 1675.19312
 }
}
dps_results: {
 key: "TestUnholy-AllItems-PowerfulPrimalDiamond"
 value: {
  dps: 137167.69359
  tps: 100169.09757
  hps: 1862.3667
 }
}
dps_results: {
 key: "TestUnholy-AllItems-PriceofProgress-81266"
 value: {
  dps: 134075.98284
  tps: 98302.00168
  hps: 1854.53229
 }
}
dps_results: {
 key: "TestUnholy-AllItems-Primordius'TalismanofRage-96873"
 value: {
  dps: 148750.79575
  tps: 108203.04891
  hps: 1918.74799
 }
}
dps_results: {
 key: "TestUnholy-AllItems-Renataki'sSoulCharm-96741"
 value: {
  dps: 134618.0928
  tps: 98659.17767
  hps: 1854.53229
 }
}
dps_results: {
 key: "TestUnholy-AllItems-ReverberatingPrimalDiamond"
 value: {
  dps: 139150.13795
  tps: 101787.22103
  hps: 1854.53229
 }
}
dps_results: {
 key: "TestUnholy-AllItems-RevitalizingPrimalDiamond"
 value: {
  dps: 138003.67612
  tps: 100999.76561
  hps: 1854.53229
 }
}
dps_results: {
 key: "TestUnholy-AllItems-RuneofCinderglacier-3369"
 value: {
  dps: 132127.88785
  tps: 98133.13037
 }
}
dps_results: {
 key: "TestUnholy-AllItems-RuneofRazorice-3370"
 value: {
  dps: 129757.53468
  tps: 95687.14682
 }
}
dps_results: {
 key: "TestUnholy-AllItems-RuneofSpellbreaking-3595"
 value: {
  dps: 128424.95147
  tps: 94354.56362
 }
}
dps_results: {
 key: "TestUnholy-AllItems-RuneofSpellshattering-3367"
 value: {
  dps: 128424.95147
  tps: 94354.56362
 }
}
dps_results: {
 key: "TestUnholy-AllItems-RuneofSwordbreaking-3594"
 value: {
  dps: 128424.95147
  tps: 94354.56362
 }
}
dps_results: {
 key: "TestUnholy-AllItems-RuneofSwordshattering-3365"
 value: {
  dps: 128424.95147
  tps: 94354.56362
 }
}
dps_results: {
 key: "TestUnholy-AllItems-RuneoftheNerubianCarapace-3883"
 value: {
  dps: 128424.95147
  tps: 94354.56362
 }
}
dps_results: {
 key: "TestUnholy-AllItems-RuneoftheStoneskinGargoyle-3847"
 value: {
  dps: 128424.95147
  tps: 94354.56362
 }
}
dps_results: {
 key: "TestUnholy-AllItems-SinisterPrimalDiamond"
 value: {
  dps: 138047.45333
  tps: 100665.33147
  hps: 1849.51979
 }
}
dps_results: {
 key: "TestUnholy-AllItems-SynapseSprings(MarkII)-4898"
 value: {
  dps: 140456.64818
  tps: 102626.28743
  hps: 1859.1735
 }
}
dps_results: {
 key: "TestUnholy-AllItems-TalismanofBloodlust-96864"
 value: {
  dps: 137853.56035
  tps: 100275.86333
  hps: 1909.68836
 }
}
dps_results: {
 key: "TestUnholy-AllItems-TyrannicalPrimalDiamond"
 value: {
  dps: 137167.69359
  tps: 100169.09757
  hps: 1842.55799
 }
}
dps_results: {
 key: "TestUnholy-AllItems-UnerringVisionofLeiShen-96930"
 value: {
  dps: 141766.98575
  tps: 105312.24035
  hps: 1947.17073
 }
}
dps_results: {
 key: "TestUnholy-AllItems-Wushoolay'sFinalChoice-96785"
 value: {
  dps: 134075.98284
  tps: 98302.00168
  hps: 1854.53229
 }
}
dps_results: {
 key: "TestUnholy-AllItems-ZenAlchemistStone-75274"
 value: {
  dps: 144077.03214
  tps: 105753.05164
  hps: 1865.17922
 }
}
dps_results: {
 key: "TestUnholy-Average-Default"
 value: {
  dps: 141829.43575
  tps: 103611.70543
  hps: 1790.52185
 }
}
dps_results: {
 key: "TestUnholy-Settings-Orc-p1-Basic-default-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 352389.18587
  tps: 301087.79266
  hps: 1859.1735
 }
}
dps_results: {
 key: "TestUnholy-Settings-Orc-p1-Basic-default-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 141358.21874
  tps: 103245.61499
  hps: 1859.1735
 }
}
dps_results: {
 key: "TestUnholy-Settings-Orc-p1-Basic-default-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 226268.03795
  tps: 120551.04282
  hps: 2337.91369
 }
}
dps_results: {
 key: "TestUnholy-Settings-Orc-p1-Basic-default-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 262695.29648
  tps: 227646.79049
  hps: 1614.85984
 }
}
dps_results: {
 key: "TestUnholy-Settings-Orc-p1-Basic-default-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 100781.0047
  tps: 75305.0048
  hps: 1614.85984
 }
}
dps_results: {
 key: "TestUnholy-Settings-Orc-p1-Basic-default-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 140515.11999
  tps: 81546.73346
  hps: 1899.8095
 }
}
dps_results: {
 key: "TestUnholy-Settings-Worgen-p1-Basic-default-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 345605.13531
  tps: 296047.41931
  hps: 1871.54982
 }
}
dps_results: {
 key: "TestUnholy-Settings-Worgen-p1-Basic-default-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 140011.23388
  tps: 103085.74003
  hps: 1873.87035
 }
}
dps_results: {
 key: "TestUnholy-Settings-Worgen-p1-Basic-default-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 220996.72027
  tps: 119154.10343
  hps: 2379.51275
 }
}
dps_results: {
 key: "TestUnholy-Settings-Worgen-p1-Basic-default-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 254705.09509
  tps: 220750.12798
  hps: 1610.97793
 }
}
dps_results: {
 key: "TestUnholy-Settings-Worgen-p1-Basic-default-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 99384.04353
  tps: 74694.94702
  hps: 1602.27347
 }
}
dps_results: {
 key: "TestUnholy-Settings-Worgen-p1-Basic-default-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 136778.31501
  tps: 80731.94366
  hps: 1900.40123
 }
}
dps_results: {
 key: "TestUnholy-SwitchInFrontOfTarget-Default"
 value: {
  dps: 135813.30074
  tps: 100107.3612
  hps: 1777.52544
 }
}
