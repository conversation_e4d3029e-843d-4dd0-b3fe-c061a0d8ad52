character_stats_results: {
 key: "TestBlood-CharacterStats-Default"
 value: {
  final_stats: 12541.2
  final_stats: 941.85
  final_stats: 36048.99375
  final_stats: 36.75
  final_stats: 71
  final_stats: 2561
  final_stats: 552
  final_stats: 512
  final_stats: 2555
  final_stats: 2804.00009
  final_stats: 14140.63264
  final_stats: 15919
  final_stats: 27865.64
  final_stats: 0
  final_stats: 0
  final_stats: 0
  final_stats: 0
  final_stats: 54367.428
  final_stats: 0
  final_stats: 651088.9125
  final_stats: 0
  final_stats: 0
  final_stats: 7.53235
  final_stats: 15.04706
  final_stats: 11.01419
  final_stats: 5.92
  final_stats: 0
 }
}
dps_results: {
 key: "TestBlood-AllItems-AgilePrimalDiamond"
 value: {
  dps: 111903.68239
  tps: 677617.10193
  dtps: 97145.18814
  hps: 85413.27779
 }
}
dps_results: {
 key: "TestBlood-AllItems-AssuranceofConsequence-105472"
 value: {
  dps: 110878.85262
  tps: 670130.5825
  dtps: 96129.98259
  hps: 81856.04352
 }
}
dps_results: {
 key: "TestBlood-AllItems-AusterePrimalDiamond"
 value: {
  dps: 111189.38691
  tps: 671312.51123
  dtps: 95998.56582
  hps: 85344.62356
 }
}
dps_results: {
 key: "TestBlood-AllItems-BattlegearoftheLostCatacomb"
 value: {
  dps: 113646.14652
  tps: 686318.38648
  dtps: 97515.19865
  hps: 72886.47902
 }
}
dps_results: {
 key: "TestBlood-AllItems-BattleplateofCyclopeanDread"
 value: {
  dps: 116781.6887
  tps: 702246.46339
  dtps: 92155.8915
  hps: 79830.28261
 }
}
dps_results: {
 key: "TestBlood-AllItems-BattleplateoftheAll-ConsumingMaw"
 value: {
  dps: 119829.14123
  tps: 712268.84761
  dtps: 93229.81707
  hps: 74196.0118
 }
}
dps_results: {
 key: "TestBlood-AllItems-BurningPrimalDiamond"
 value: {
  dps: 111883.58045
  tps: 677491.10477
  dtps: 97145.18814
  hps: 85413.27779
 }
}
dps_results: {
 key: "TestBlood-AllItems-CapacitivePrimalDiamond"
 value: {
  dps: 111761.09231
  tps: 675916.79462
  dtps: 97112.65017
  hps: 85327.97513
 }
}
dps_results: {
 key: "TestBlood-AllItems-CourageousPrimalDiamond"
 value: {
  dps: 111209.19915
  tps: 672718.4553
  dtps: 97095.22443
  hps: 85195.41733
 }
}
dps_results: {
 key: "TestBlood-AllItems-DelicateVialoftheSanguinaire-96895"
 value: {
  dps: 113306.79397
  tps: 685437.08512
  dtps: 94898.87087
  hps: 81647.0736
 }
}
dps_results: {
 key: "TestBlood-AllItems-DestructivePrimalDiamond"
 value: {
  dps: 111921.16845
  tps: 677224.76062
  dtps: 97131.85204
  hps: 85303.27553
 }
}
dps_results: {
 key: "TestBlood-AllItems-EffulgentPrimalDiamond"
 value: {
  dps: 111288.40959
  tps: 672682.94758
  dtps: 96970.79621
  hps: 85650.97739
 }
}
dps_results: {
 key: "TestBlood-AllItems-EmberPrimalDiamond"
 value: {
  dps: 111209.19915
  tps: 672718.4553
  dtps: 97095.22443
  hps: 85195.41733
 }
}
dps_results: {
 key: "TestBlood-AllItems-EnchantWeapon-BloodyDancingSteel-5125"
 value: {
  dps: 110841.89432
  tps: 668768.72423
  dtps: 96697.30393
  hps: 82008.17958
 }
}
dps_results: {
 key: "TestBlood-AllItems-EnchantWeapon-Colossus-4445"
 value: {
  dps: 109057.12746
  tps: 659943.29189
  dtps: 96819.35503
  hps: 81768.91862
 }
}
dps_results: {
 key: "TestBlood-AllItems-EnchantWeapon-DancingSteel-4444"
 value: {
  dps: 110513.95722
  tps: 667988.20972
  dtps: 97196.4336
  hps: 82556.82389
 }
}
dps_results: {
 key: "TestBlood-AllItems-EnchantWeapon-JadeSpirit-4442"
 value: {
  dps: 108690.66764
  tps: 658950.43878
  dtps: 97922.5581
  hps: 82851.54177
 }
}
dps_results: {
 key: "TestBlood-AllItems-EnchantWeapon-River'sSong-4446"
 value: {
  dps: 110246.43686
  tps: 667562.87609
  dtps: 97573.73918
  hps: 81934.19598
 }
}
dps_results: {
 key: "TestBlood-AllItems-EnchantWeapon-SpiritofConquest-5124"
 value: {
  dps: 108690.66764
  tps: 658950.43878
  dtps: 97922.5581
  hps: 82851.54177
 }
}
dps_results: {
 key: "TestBlood-AllItems-EnchantWeapon-Windsong-4441"
 value: {
  dps: 109816.25382
  tps: 664369.06503
  dtps: 97419.42788
  hps: 83778.10871
 }
}
dps_results: {
 key: "TestBlood-AllItems-EnigmaticPrimalDiamond"
 value: {
  dps: 111921.16845
  tps: 677224.76062
  dtps: 97131.85204
  hps: 85303.27553
 }
}
dps_results: {
 key: "TestBlood-AllItems-EternalPrimalDiamond"
 value: {
  dps: 111672.28342
  tps: 675486.73457
  dtps: 97112.65017
  hps: 85310.78415
 }
}
dps_results: {
 key: "TestBlood-AllItems-EvilEyeofGalakras-105491"
 value: {
  dps: 121169.00315
  tps: 692405.90592
  dtps: 84608.93914
  hps: 84470.24333
 }
}
dps_results: {
 key: "TestBlood-AllItems-FabledFeatherofJi-Kun-96842"
 value: {
  dps: 114734.23135
  tps: 691302.6444
  dtps: 92833.70019
  hps: 80553.15209
 }
}
dps_results: {
 key: "TestBlood-AllItems-Fen-Yu,FuryofXuen-102248"
 value: {
  dps: 118165.54722
  tps: 717304.19139
  dtps: 96224.24148
  hps: 88680.61678
 }
}
dps_results: {
 key: "TestBlood-AllItems-FleetPrimalDiamond"
 value: {
  dps: 111377.48579
  tps: 673357.18332
  dtps: 96938.04327
  hps: 86773.17047
 }
}
dps_results: {
 key: "TestBlood-AllItems-ForlornPrimalDiamond"
 value: {
  dps: 111209.19915
  tps: 672718.4553
  dtps: 97095.22443
  hps: 85195.41733
 }
}
dps_results: {
 key: "TestBlood-AllItems-GazeoftheTwins-96915"
 value: {
  dps: 116950.49222
  tps: 707052.44619
  dtps: 93294.5142
  hps: 80539.69999
 }
}
dps_results: {
 key: "TestBlood-AllItems-Gong-Lu,StrengthofXuen-102249"
 value: {
  dps: 120951.28686
  tps: 731995.2753
  dtps: 93379.33967
  hps: 87374.82891
 }
}
dps_results: {
 key: "TestBlood-AllItems-Horridon'sLastGasp-96757"
 value: {
  dps: 110885.20624
  tps: 670481.39367
  dtps: 96130.71166
  hps: 82017.78252
 }
}
dps_results: {
 key: "TestBlood-AllItems-ImpassivePrimalDiamond"
 value: {
  dps: 111921.16845
  tps: 677224.76062
  dtps: 97131.85204
  hps: 85303.27553
 }
}
dps_results: {
 key: "TestBlood-AllItems-IndomitablePrimalDiamond"
 value: {
  dps: 111288.40959
  tps: 672682.94758
  dtps: 96970.79621
  hps: 85650.97739
 }
}
dps_results: {
 key: "TestBlood-AllItems-InscribedBagofHydra-Spawn-96828"
 value: {
  dps: 110863.3562
  tps: 670518.94028
  dtps: 95123.20544
  hps: 81313.42872
 }
}
dps_results: {
 key: "TestBlood-AllItems-Ji-Kun'sRisingWinds-96843"
 value: {
  dps: 112209.28707
  tps: 678803.14964
  dtps: 95817.87731
  hps: 84420.39993
 }
}
dps_results: {
 key: "TestBlood-AllItems-PhaseFingers-4697"
 value: {
  dps: 112183.32638
  tps: 677280.77995
  dtps: 95475.78192
  hps: 83810.36535
 }
}
dps_results: {
 key: "TestBlood-AllItems-PlateofCyclopeanDread"
 value: {
  dps: 116021.43989
  tps: 698916.7408
  dtps: 86866.94543
  hps: 79832.26657
 }
}
dps_results: {
 key: "TestBlood-AllItems-PlateoftheAll-ConsumingMaw"
 value: {
  dps: 124607.90239
  tps: 751672.8586
  dtps: 89650.67557
  hps: 73827.4867
 }
}
dps_results: {
 key: "TestBlood-AllItems-PlateoftheLostCatacomb"
 value: {
  dps: 111810.60675
  tps: 678043.7723
  dtps: 95596.86611
  hps: 77728.29758
 }
}
dps_results: {
 key: "TestBlood-AllItems-PowerfulPrimalDiamond"
 value: {
  dps: 111288.40959
  tps: 672682.94758
  dtps: 96970.79621
  hps: 85650.97739
 }
}
dps_results: {
 key: "TestBlood-AllItems-PriceofProgress-81266"
 value: {
  dps: 110763.04339
  tps: 669382.15595
  dtps: 96129.98259
  hps: 81856.04352
 }
}
dps_results: {
 key: "TestBlood-AllItems-Primordius'TalismanofRage-96873"
 value: {
  dps: 117495.29462
  tps: 707117.03757
  dtps: 93677.16988
  hps: 80923.46686
 }
}
dps_results: {
 key: "TestBlood-AllItems-Qian-Le,CourageofNiuzao-102245"
 value: {
  dps: 112347.21665
  tps: 681303.53068
  dtps: 93725.0799
  hps: 85996.24894
 }
}
dps_results: {
 key: "TestBlood-AllItems-Qian-Ying,FortitudeofNiuzao-102250"
 value: {
  dps: 114409.03097
  tps: 691032.9686
  dtps: 90239.09416
  hps: 83877.08688
 }
}
dps_results: {
 key: "TestBlood-AllItems-Renataki'sSoulCharm-96741"
 value: {
  dps: 112287.0604
  tps: 679279.9608
  dtps: 95939.06688
  hps: 83288.13185
 }
}
dps_results: {
 key: "TestBlood-AllItems-ReverberatingPrimalDiamond"
 value: {
  dps: 112533.19297
  tps: 682188.71878
  dtps: 96762.76202
  hps: 84835.5586
 }
}
dps_results: {
 key: "TestBlood-AllItems-RevitalizingPrimalDiamond"
 value: {
  dps: 111883.58045
  tps: 677491.10477
  dtps: 97145.18814
  hps: 85413.27779
 }
}
dps_results: {
 key: "TestBlood-AllItems-RuneofCinderglacier-3369"
 value: {
  dps: 110923.25783
  tps: 672730.62748
  dtps: 97936.74403
  hps: 83256.76681
 }
}
dps_results: {
 key: "TestBlood-AllItems-RuneofRazorice-3370"
 value: {
  dps: 111683.00406
  tps: 679896.79376
  dtps: 97922.5581
  hps: 82851.54177
 }
}
dps_results: {
 key: "TestBlood-AllItems-RuneofSpellbreaking-3595"
 value: {
  dps: 108690.66764
  tps: 658950.43878
  dtps: 97922.5581
  hps: 82851.54177
 }
}
dps_results: {
 key: "TestBlood-AllItems-RuneofSpellshattering-3367"
 value: {
  dps: 108690.66764
  tps: 658950.43878
  dtps: 97922.5581
  hps: 82851.54177
 }
}
dps_results: {
 key: "TestBlood-AllItems-RuneofSwordbreaking-3594"
 value: {
  dps: 109822.95027
  tps: 664600.11633
  dtps: 94628.0897
  hps: 80449.50088
 }
}
dps_results: {
 key: "TestBlood-AllItems-RuneofSwordshattering-3365"
 value: {
  dps: 110186.95124
  tps: 666551.1701
  dtps: 91962.4726
  hps: 79392.84717
 }
}
dps_results: {
 key: "TestBlood-AllItems-RuneoftheNerubianCarapace-3883"
 value: {
  dps: 108934.74935
  tps: 660189.87545
  dtps: 96745.6436
  hps: 82475.58391
 }
}
dps_results: {
 key: "TestBlood-AllItems-RuneoftheStoneskinGargoyle-3847"
 value: {
  dps: 109045.85304
  tps: 659951.52943
  dtps: 95995.27367
  hps: 81482.41321
 }
}
dps_results: {
 key: "TestBlood-AllItems-SinisterPrimalDiamond"
 value: {
  dps: 111761.09231
  tps: 675916.79462
  dtps: 97112.65017
  hps: 85327.97513
 }
}
dps_results: {
 key: "TestBlood-AllItems-SynapseSprings(MarkII)-4898"
 value: {
  dps: 112072.01194
  tps: 676513.03464
  dtps: 95438.03997
  hps: 84021.87213
 }
}
dps_results: {
 key: "TestBlood-AllItems-TalismanofBloodlust-96864"
 value: {
  dps: 113836.28574
  tps: 685110.81286
  dtps: 95587.25341
  hps: 85569.06998
 }
}
dps_results: {
 key: "TestBlood-AllItems-TyrannicalPrimalDiamond"
 value: {
  dps: 111209.19915
  tps: 672718.4553
  dtps: 97095.22443
  hps: 85195.41733
 }
}
dps_results: {
 key: "TestBlood-AllItems-UnerringVisionofLeiShen-96930"
 value: {
  dps: 114855.11624
  tps: 695179.10483
  dtps: 96027.24414
  hps: 82248.63544
 }
}
dps_results: {
 key: "TestBlood-AllItems-Wushoolay'sFinalChoice-96785"
 value: {
  dps: 110842.76428
  tps: 668723.31183
  dtps: 95710.35712
  hps: 82204.55077
 }
}
dps_results: {
 key: "TestBlood-AllItems-Xing-Ho,BreathofYu'lon-102246"
 value: {
  dps: 111925.90877
  tps: 678698.97117
  dtps: 96300.44125
  hps: 88643.99522
 }
}
dps_results: {
 key: "TestBlood-AllItems-ZenAlchemistStone-75274"
 value: {
  dps: 114280.01169
  tps: 688807.22979
  dtps: 94057.44865
  hps: 83613.22926
 }
}
dps_results: {
 key: "TestBlood-Average-Default"
 value: {
  dps: 112144.9943
  tps: 676424.03308
  dtps: 94701.38737
  hps: 84647.1213
 }
}
dps_results: {
 key: "TestBlood-Settings-Orc-p1-Basic-defensive-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 990406.3158
  tps: 6.08896515572e+06
  dtps: 1.92095517611e+06
  hps: 562598.43197
 }
}
dps_results: {
 key: "TestBlood-Settings-Orc-p1-Basic-defensive-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 115705.76916
  tps: 699402.16161
  dtps: 94958.9767
  hps: 84936.21747
 }
}
dps_results: {
 key: "TestBlood-Settings-Orc-p1-Basic-defensive-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 131638.19523
  tps: 741042.04588
  dtps: 94095.28505
  hps: 101494.04436
 }
}
dps_results: {
 key: "TestBlood-Settings-Orc-p1-Basic-defensive-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 768886.55485
  tps: 4.75503925482e+06
  dtps: 2.15891540051e+06
  hps: 581954.0669
 }
}
dps_results: {
 key: "TestBlood-Settings-Orc-p1-Basic-defensive-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 95034.27398
  tps: 578640.58003
  dtps: 96550.01557
  hps: 73451.55016
 }
}
dps_results: {
 key: "TestBlood-Settings-Orc-p1-Basic-defensive-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 96653.90974
  tps: 562794.95778
  dtps: 95786.30209
  hps: 82001.36206
 }
}
dps_results: {
 key: "TestBlood-Settings-Worgen-p1-Basic-defensive-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 992394.58513
  tps: 6.0836688608e+06
  dtps: 1.91975502158e+06
  hps: 563580.84273
 }
}
dps_results: {
 key: "TestBlood-Settings-Worgen-p1-Basic-defensive-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 115256.96736
  tps: 697681.33894
  dtps: 95206.45997
  hps: 84240.65045
 }
}
dps_results: {
 key: "TestBlood-Settings-Worgen-p1-Basic-defensive-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 129464.45586
  tps: 735039.18193
  dtps: 94389.50802
  hps: 100848.46855
 }
}
dps_results: {
 key: "TestBlood-Settings-Worgen-p1-Basic-defensive-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 771272.02402
  tps: 4.7661396779e+06
  dtps: 2.15836406756e+06
  hps: 577615.93267
 }
}
dps_results: {
 key: "TestBlood-Settings-Worgen-p1-Basic-defensive-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 94783.40601
  tps: 578373.07575
  dtps: 96345.05874
  hps: 73242.93564
 }
}
dps_results: {
 key: "TestBlood-Settings-Worgen-p1-Basic-defensive-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 94810.80982
  tps: 557423.82741
  dtps: 95785.73884
  hps: 81882.1168
 }
}
dps_results: {
 key: "TestBlood-SwitchInFrontOfTarget-Default"
 value: {
  dps: 115705.76916
  tps: 699402.16161
  dtps: 94958.9767
  hps: 84936.21747
 }
}
