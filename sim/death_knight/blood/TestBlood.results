character_stats_results: {
 key: "TestBlood-CharacterStats-Default"
 value: {
  final_stats: 12541.2
  final_stats: 941.85
  final_stats: 36048.99375
  final_stats: 36.75
  final_stats: 71
  final_stats: 2561
  final_stats: 552
  final_stats: 512
  final_stats: 2555
  final_stats: 2804.00009
  final_stats: 14140.63264
  final_stats: 15919
  final_stats: 27865.64
  final_stats: 0
  final_stats: 0
  final_stats: 0
  final_stats: 0
  final_stats: 54367.428
  final_stats: 0
  final_stats: 651088.9125
  final_stats: 0
  final_stats: 0
  final_stats: 7.53235
  final_stats: 15.04706
  final_stats: 11.01419
  final_stats: 5.92
  final_stats: 0
 }
}
dps_results: {
 key: "TestBlood-AllItems-AgilePrimalDiamond"
 value: {
  dps: 122840.14237
  tps: 743644.83644
  dtps: 97145.18814
  hps: 85413.27779
 }
}
dps_results: {
 key: "TestBlood-AllItems-AssuranceofConsequence-105472"
 value: {
  dps: 121692.65478
  tps: 735216.74168
  dtps: 96129.98259
  hps: 81856.04352
 }
}
dps_results: {
 key: "TestBlood-AllItems-AusterePrimalDiamond"
 value: {
  dps: 122081.64194
  tps: 736820.35828
  dtps: 95998.56582
  hps: 85344.62356
 }
}
dps_results: {
 key: "TestBlood-AllItems-BattlegearoftheLostCatacomb"
 value: {
  dps: 124742.59721
  tps: 753415.36536
  dtps: 97515.19865
  hps: 72886.47902
 }
}
dps_results: {
 key: "TestBlood-AllItems-BattleplateofCyclopeanDread"
 value: {
  dps: 128212.48807
  tps: 771789.21194
  dtps: 92155.8915
  hps: 79830.28261
 }
}
dps_results: {
 key: "TestBlood-AllItems-BattleplateoftheAll-ConsumingMaw"
 value: {
  dps: 131499.46823
  tps: 782999.78359
  dtps: 93229.81707
  hps: 74196.0118
 }
}
dps_results: {
 key: "TestBlood-AllItems-BurningPrimalDiamond"
 value: {
  dps: 122815.85703
  tps: 743489.55554
  dtps: 97145.18814
  hps: 85413.27779
 }
}
dps_results: {
 key: "TestBlood-AllItems-CapacitivePrimalDiamond"
 value: {
  dps: 122680.38614
  tps: 741796.89838
  dtps: 97112.65017
  hps: 85327.97513
 }
}
dps_results: {
 key: "TestBlood-AllItems-CourageousPrimalDiamond"
 value: {
  dps: 122074.24834
  tps: 738245.82611
  dtps: 97095.22443
  hps: 85195.41733
 }
}
dps_results: {
 key: "TestBlood-AllItems-DelicateVialoftheSanguinaire-96895"
 value: {
  dps: 124373.50548
  tps: 752407.75215
  dtps: 94898.87087
  hps: 81647.0736
 }
}
dps_results: {
 key: "TestBlood-AllItems-DestructivePrimalDiamond"
 value: {
  dps: 122832.78518
  tps: 743131.36795
  dtps: 97131.85204
  hps: 85303.27553
 }
}
dps_results: {
 key: "TestBlood-AllItems-EffulgentPrimalDiamond"
 value: {
  dps: 122184.76358
  tps: 738428.79548
  dtps: 96970.79621
  hps: 85650.97739
 }
}
dps_results: {
 key: "TestBlood-AllItems-EmberPrimalDiamond"
 value: {
  dps: 122074.24834
  tps: 738245.82611
  dtps: 97095.22443
  hps: 85195.41733
 }
}
dps_results: {
 key: "TestBlood-AllItems-EnchantWeapon-BloodyDancingSteel-5125"
 value: {
  dps: 121471.92267
  tps: 733121.7393
  dtps: 96697.30393
  hps: 82008.17958
 }
}
dps_results: {
 key: "TestBlood-AllItems-EnchantWeapon-Colossus-4445"
 value: {
  dps: 119501.16171
  tps: 723438.4613
  dtps: 96819.35503
  hps: 81768.91862
 }
}
dps_results: {
 key: "TestBlood-AllItems-EnchantWeapon-DancingSteel-4444"
 value: {
  dps: 121126.17407
  tps: 732268.35774
  dtps: 97196.4336
  hps: 82556.82389
 }
}
dps_results: {
 key: "TestBlood-AllItems-EnchantWeapon-JadeSpirit-4442"
 value: {
  dps: 119069.31863
  tps: 722105.43846
  dtps: 97922.5581
  hps: 82851.54177
 }
}
dps_results: {
 key: "TestBlood-AllItems-EnchantWeapon-River'sSong-4446"
 value: {
  dps: 120807.47025
  tps: 731620.71017
  dtps: 97573.73918
  hps: 81934.19598
 }
}
dps_results: {
 key: "TestBlood-AllItems-EnchantWeapon-SpiritofConquest-5124"
 value: {
  dps: 119069.31863
  tps: 722105.43846
  dtps: 97922.5581
  hps: 82851.54177
 }
}
dps_results: {
 key: "TestBlood-AllItems-EnchantWeapon-Windsong-4441"
 value: {
  dps: 120413.83227
  tps: 728797.60077
  dtps: 97419.42788
  hps: 83778.10871
 }
}
dps_results: {
 key: "TestBlood-AllItems-EnigmaticPrimalDiamond"
 value: {
  dps: 122832.78518
  tps: 743131.36795
  dtps: 97131.85204
  hps: 85303.27553
 }
}
dps_results: {
 key: "TestBlood-AllItems-EternalPrimalDiamond"
 value: {
  dps: 122587.65256
  tps: 741339.36552
  dtps: 97112.65017
  hps: 85310.78415
 }
}
dps_results: {
 key: "TestBlood-AllItems-EvilEyeofGalakras-105491"
 value: {
  dps: 133797.99573
  tps: 760838.62233
  dtps: 84608.93914
  hps: 84470.24333
 }
}
dps_results: {
 key: "TestBlood-AllItems-FabledFeatherofJi-Kun-96842"
 value: {
  dps: 133888.26207
  tps: 801152.03306
  dtps: 86929.01154
  hps: 77662.29447
 }
}
dps_results: {
 key: "TestBlood-AllItems-Fen-Yu,FuryofXuen-102248"
 value: {
  dps: 129172.16316
  tps: 783866.42185
  dtps: 96224.24148
  hps: 88680.61678
 }
}
dps_results: {
 key: "TestBlood-AllItems-FleetPrimalDiamond"
 value: {
  dps: 122304.0604
  tps: 739208.60423
  dtps: 96938.04327
  hps: 86773.17047
 }
}
dps_results: {
 key: "TestBlood-AllItems-ForlornPrimalDiamond"
 value: {
  dps: 122074.24834
  tps: 738245.82611
  dtps: 97095.22443
  hps: 85195.41733
 }
}
dps_results: {
 key: "TestBlood-AllItems-GazeoftheTwins-96915"
 value: {
  dps: 128361.7886
  tps: 776157.36352
  dtps: 93294.5142
  hps: 80539.69999
 }
}
dps_results: {
 key: "TestBlood-AllItems-Gong-Lu,StrengthofXuen-102249"
 value: {
  dps: 132243.09261
  tps: 799998.50109
  dtps: 93379.33967
  hps: 87374.82891
 }
}
dps_results: {
 key: "TestBlood-AllItems-Horridon'sLastGasp-96757"
 value: {
  dps: 121812.19487
  tps: 736115.26498
  dtps: 96254.28979
  hps: 81880.3953
 }
}
dps_results: {
 key: "TestBlood-AllItems-ImpassivePrimalDiamond"
 value: {
  dps: 122832.78518
  tps: 743131.36795
  dtps: 97131.85204
  hps: 85303.27553
 }
}
dps_results: {
 key: "TestBlood-AllItems-IndomitablePrimalDiamond"
 value: {
  dps: 122184.76358
  tps: 738428.79548
  dtps: 96970.79621
  hps: 85650.97739
 }
}
dps_results: {
 key: "TestBlood-AllItems-InscribedBagofHydra-Spawn-96828"
 value: {
  dps: 121679.1005
  tps: 735759.9179
  dtps: 95123.20544
  hps: 81313.42872
 }
}
dps_results: {
 key: "TestBlood-AllItems-Ji-Kun'sRisingWinds-96843"
 value: {
  dps: 123105.78774
  tps: 745038.95039
  dtps: 95817.87731
  hps: 84420.39993
 }
}
dps_results: {
 key: "TestBlood-AllItems-PhaseFingers-4697"
 value: {
  dps: 123058.9427
  tps: 742976.05049
  dtps: 95475.78192
  hps: 83810.36535
 }
}
dps_results: {
 key: "TestBlood-AllItems-PlateofCyclopeanDread"
 value: {
  dps: 127549.27633
  tps: 768097.42495
  dtps: 86866.94543
  hps: 79832.26657
 }
}
dps_results: {
 key: "TestBlood-AllItems-PlateoftheAll-ConsumingMaw"
 value: {
  dps: 137995.37191
  tps: 832594.37778
  dtps: 89650.67557
  hps: 73827.4867
 }
}
dps_results: {
 key: "TestBlood-AllItems-PlateoftheLostCatacomb"
 value: {
  dps: 122771.86319
  tps: 745010.22008
  dtps: 95596.86611
  hps: 77728.29758
 }
}
dps_results: {
 key: "TestBlood-AllItems-PowerfulPrimalDiamond"
 value: {
  dps: 122184.76358
  tps: 738428.79548
  dtps: 96970.79621
  hps: 85650.97739
 }
}
dps_results: {
 key: "TestBlood-AllItems-PriceofProgress-81266"
 value: {
  dps: 121565.25925
  tps: 734387.21099
  dtps: 96129.98259
  hps: 81856.04352
 }
}
dps_results: {
 key: "TestBlood-AllItems-Primordius'TalismanofRage-96873"
 value: {
  dps: 129009.762
  tps: 776331.43885
  dtps: 93677.16988
  hps: 80923.46686
 }
}
dps_results: {
 key: "TestBlood-AllItems-Qian-Le,CourageofNiuzao-102245"
 value: {
  dps: 123294.4926
  tps: 747699.86967
  dtps: 93725.0799
  hps: 85996.24894
 }
}
dps_results: {
 key: "TestBlood-AllItems-Qian-Ying,FortitudeofNiuzao-102250"
 value: {
  dps: 125554.58776
  tps: 758741.85299
  dtps: 90239.09416
  hps: 83877.08688
 }
}
dps_results: {
 key: "TestBlood-AllItems-Renataki'sSoulCharm-96741"
 value: {
  dps: 123619.41392
  tps: 748162.72769
  dtps: 95939.06688
  hps: 83233.05553
 }
}
dps_results: {
 key: "TestBlood-AllItems-ReverberatingPrimalDiamond"
 value: {
  dps: 123499.41619
  tps: 748837.56666
  dtps: 96762.76202
  hps: 84835.5586
 }
}
dps_results: {
 key: "TestBlood-AllItems-RevitalizingPrimalDiamond"
 value: {
  dps: 122815.85703
  tps: 743489.55554
  dtps: 97145.18814
  hps: 85413.27779
 }
}
dps_results: {
 key: "TestBlood-AllItems-RuneofCinderglacier-3369"
 value: {
  dps: 121348.1809
  tps: 736156.06554
  dtps: 97936.74403
  hps: 83256.76681
 }
}
dps_results: {
 key: "TestBlood-AllItems-RuneofRazorice-3370"
 value: {
  dps: 122061.65506
  tps: 743051.79343
  dtps: 97922.5581
  hps: 82851.54177
 }
}
dps_results: {
 key: "TestBlood-AllItems-RuneofSpellbreaking-3595"
 value: {
  dps: 119069.31863
  tps: 722105.43846
  dtps: 97922.5581
  hps: 82851.54177
 }
}
dps_results: {
 key: "TestBlood-AllItems-RuneofSpellshattering-3367"
 value: {
  dps: 119069.31863
  tps: 722105.43846
  dtps: 97922.5581
  hps: 82851.54177
 }
}
dps_results: {
 key: "TestBlood-AllItems-RuneofSwordbreaking-3594"
 value: {
  dps: 120424.57438
  tps: 729022.77115
  dtps: 94628.0897
  hps: 80449.50088
 }
}
dps_results: {
 key: "TestBlood-AllItems-RuneofSwordshattering-3365"
 value: {
  dps: 120914.62217
  tps: 731524.76109
  dtps: 91962.4726
  hps: 79392.84717
 }
}
dps_results: {
 key: "TestBlood-AllItems-RuneoftheNerubianCarapace-3883"
 value: {
  dps: 119404.6579
  tps: 724011.97865
  dtps: 96745.6436
  hps: 82475.58391
 }
}
dps_results: {
 key: "TestBlood-AllItems-RuneoftheStoneskinGargoyle-3847"
 value: {
  dps: 119522.61405
  tps: 723760.82261
  dtps: 95995.27367
  hps: 81482.41321
 }
}
dps_results: {
 key: "TestBlood-AllItems-SinisterPrimalDiamond"
 value: {
  dps: 122680.38614
  tps: 741796.89838
  dtps: 97112.65017
  hps: 85327.97513
 }
}
dps_results: {
 key: "TestBlood-AllItems-SynapseSprings(MarkII)-4898"
 value: {
  dps: 122958.24466
  tps: 742238.50951
  dtps: 95438.03997
  hps: 84021.87213
 }
}
dps_results: {
 key: "TestBlood-AllItems-TalismanofBloodlust-96864"
 value: {
  dps: 125057.47774
  tps: 752961.75021
  dtps: 95587.25341
  hps: 85569.06998
 }
}
dps_results: {
 key: "TestBlood-AllItems-TyrannicalPrimalDiamond"
 value: {
  dps: 122074.24834
  tps: 738245.82611
  dtps: 97095.22443
  hps: 85195.41733
 }
}
dps_results: {
 key: "TestBlood-AllItems-UnerringVisionofLeiShen-96930"
 value: {
  dps: 126055.47281
  tps: 762747.12388
  dtps: 96027.24414
  hps: 82248.63544
 }
}
dps_results: {
 key: "TestBlood-AllItems-Wushoolay'sFinalChoice-96785"
 value: {
  dps: 121673.49974
  tps: 734312.68351
  dtps: 95710.35712
  hps: 82204.55077
 }
}
dps_results: {
 key: "TestBlood-AllItems-Xing-Ho,BreathofYu'lon-102246"
 value: {
  dps: 122746.87034
  tps: 744680.96019
  dtps: 96300.44125
  hps: 88643.99522
 }
}
dps_results: {
 key: "TestBlood-AllItems-ZenAlchemistStone-75274"
 value: {
  dps: 125289.2213
  tps: 755643.99987
  dtps: 94057.44865
  hps: 83613.22926
 }
}
dps_results: {
 key: "TestBlood-Average-Default"
 value: {
  dps: 122943.74417
  tps: 741669.3539
  dtps: 94701.38737
  hps: 84647.1213
 }
}
dps_results: {
 key: "TestBlood-Settings-Orc-p1-Basic-defensive-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 1.0551811613e+06
  tps: 6.48703992523e+06
  dtps: 1.92095517611e+06
  hps: 562598.43197
 }
}
dps_results: {
 key: "TestBlood-Settings-Orc-p1-Basic-defensive-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 126711.28876
  tps: 766336.26151
  dtps: 94958.9767
  hps: 84936.21747
 }
}
dps_results: {
 key: "TestBlood-Settings-Orc-p1-Basic-defensive-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 142751.98471
  tps: 809504.60608
  dtps: 94095.28505
  hps: 101494.04436
 }
}
dps_results: {
 key: "TestBlood-Settings-Orc-p1-Basic-defensive-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 818891.56005
  tps: 5.05844966854e+06
  dtps: 2.15891540051e+06
  hps: 581954.0669
 }
}
dps_results: {
 key: "TestBlood-Settings-Orc-p1-Basic-defensive-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 104270.41198
  tps: 634647.3275
  dtps: 96550.01557
  hps: 73451.55016
 }
}
dps_results: {
 key: "TestBlood-Settings-Orc-p1-Basic-defensive-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 105402.22711
  tps: 616110.63867
  dtps: 95786.30209
  hps: 82001.36206
 }
}
dps_results: {
 key: "TestBlood-Settings-Worgen-p1-Basic-defensive-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 1.05754361143e+06
  tps: 6.48456199516e+06
  dtps: 1.91975502158e+06
  hps: 563580.84273
 }
}
dps_results: {
 key: "TestBlood-Settings-Worgen-p1-Basic-defensive-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 126367.14521
  tps: 765307.47663
  dtps: 95206.45997
  hps: 84240.65045
 }
}
dps_results: {
 key: "TestBlood-Settings-Worgen-p1-Basic-defensive-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 140428.06542
  tps: 802805.45268
  dtps: 94389.50802
  hps: 100848.46855
 }
}
dps_results: {
 key: "TestBlood-Settings-Worgen-p1-Basic-defensive-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 821102.21366
  tps: 5.06985974702e+06
  dtps: 2.15836406756e+06
  hps: 577615.93267
 }
}
dps_results: {
 key: "TestBlood-Settings-Worgen-p1-Basic-defensive-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 103863.27322
  tps: 633741.4796
  dtps: 96345.05874
  hps: 73242.93564
 }
}
dps_results: {
 key: "TestBlood-Settings-Worgen-p1-Basic-defensive-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 103495.72424
  tps: 610860.01181
  dtps: 95785.73884
  hps: 81882.1168
 }
}
dps_results: {
 key: "TestBlood-SwitchInFrontOfTarget-Default"
 value: {
  dps: 126711.28876
  tps: 766336.26151
  dtps: 94958.9767
  hps: 84936.21747
 }
}
