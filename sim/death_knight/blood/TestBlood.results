character_stats_results: {
 key: "TestBlood-CharacterStats-Default"
 value: {
  final_stats: 12541.2
  final_stats: 941.85
  final_stats: 36048.99375
  final_stats: 36.75
  final_stats: 71
  final_stats: 2561
  final_stats: 552
  final_stats: 512
  final_stats: 2555
  final_stats: 2804.00009
  final_stats: 14140.63264
  final_stats: 15919
  final_stats: 27865.64
  final_stats: 0
  final_stats: 0
  final_stats: 0
  final_stats: 0
  final_stats: 54367.428
  final_stats: 0
  final_stats: 651088.9125
  final_stats: 0
  final_stats: 0
  final_stats: 7.53235
  final_stats: 15.04706
  final_stats: 11.01419
  final_stats: 5.92
  final_stats: 0
 }
}
dps_results: {
 key: "TestBlood-AllItems-AgilePrimalDiamond"
 value: {
  dps: 122420.66984
  tps: 739074.82999
  dtps: 97040.3575
  hps: 85580.68155
 }
}
dps_results: {
 key: "TestBlood-AllItems-AssuranceofConsequence-105472"
 value: {
  dps: 121231.15289
  tps: 732476.49507
  dtps: 95888.21787
  hps: 82739.08751
 }
}
dps_results: {
 key: "TestBlood-AllItems-AusterePrimalDiamond"
 value: {
  dps: 121728.9655
  tps: 734385.66379
  dtps: 96047.80644
  hps: 85094.51199
 }
}
dps_results: {
 key: "TestBlood-AllItems-BattlegearoftheLostCatacomb"
 value: {
  dps: 123132.52567
  tps: 744328.04499
  dtps: 97312.54542
  hps: 72099.2091
 }
}
dps_results: {
 key: "TestBlood-AllItems-BattleplateofCyclopeanDread"
 value: {
  dps: 127650.36376
  tps: 767669.98384
  dtps: 92071.18605
  hps: 79072.85986
 }
}
dps_results: {
 key: "TestBlood-AllItems-BattleplateoftheAll-ConsumingMaw"
 value: {
  dps: 132079.34395
  tps: 785844.1859
  dtps: 93207.40706
  hps: 74056.57155
 }
}
dps_results: {
 key: "TestBlood-AllItems-BurningPrimalDiamond"
 value: {
  dps: 122402.82702
  tps: 739053.62445
  dtps: 97040.3575
  hps: 85580.68155
 }
}
dps_results: {
 key: "TestBlood-AllItems-CapacitivePrimalDiamond"
 value: {
  dps: 122202.36016
  tps: 737100.93853
  dtps: 97037.36544
  hps: 85630.7005
 }
}
dps_results: {
 key: "TestBlood-AllItems-CourageousPrimalDiamond"
 value: {
  dps: 121672.84002
  tps: 733992.27718
  dtps: 97037.36544
  hps: 85510.35258
 }
}
dps_results: {
 key: "TestBlood-AllItems-DelicateVialoftheSanguinaire-96895"
 value: {
  dps: 123577.79434
  tps: 748978.02441
  dtps: 94872.91061
  hps: 82443.20903
 }
}
dps_results: {
 key: "TestBlood-AllItems-DestructivePrimalDiamond"
 value: {
  dps: 122517.26691
  tps: 739214.90658
  dtps: 96978.47009
  hps: 85563.76599
 }
}
dps_results: {
 key: "TestBlood-AllItems-EffulgentPrimalDiamond"
 value: {
  dps: 121687.79444
  tps: 733747.35261
  dtps: 96945.03986
  hps: 85270.57363
 }
}
dps_results: {
 key: "TestBlood-AllItems-EmberPrimalDiamond"
 value: {
  dps: 121672.84002
  tps: 733992.27718
  dtps: 97037.36544
  hps: 85510.35258
 }
}
dps_results: {
 key: "TestBlood-AllItems-EnchantWeapon-BloodyDancingSteel-5125"
 value: {
  dps: 119938.51417
  tps: 726426.33638
  dtps: 96630.01714
  hps: 82585.18429
 }
}
dps_results: {
 key: "TestBlood-AllItems-EnchantWeapon-Colossus-4445"
 value: {
  dps: 118524.09785
  tps: 716761.37073
  dtps: 97036.56952
  hps: 81866.25511
 }
}
dps_results: {
 key: "TestBlood-AllItems-EnchantWeapon-DancingSteel-4444"
 value: {
  dps: 119646.59863
  tps: 725180.27286
  dtps: 96807.14929
  hps: 82062.07105
 }
}
dps_results: {
 key: "TestBlood-AllItems-EnchantWeapon-JadeSpirit-4442"
 value: {
  dps: 117666.9049
  tps: 712444.6388
  dtps: 97852.51155
  hps: 82426.32114
 }
}
dps_results: {
 key: "TestBlood-AllItems-EnchantWeapon-River'sSong-4446"
 value: {
  dps: 119569.77607
  tps: 723262.54708
  dtps: 97511.63206
  hps: 82428.68442
 }
}
dps_results: {
 key: "TestBlood-AllItems-EnchantWeapon-SpiritofConquest-5124"
 value: {
  dps: 117666.9049
  tps: 712444.6388
  dtps: 97852.51155
  hps: 82426.32114
 }
}
dps_results: {
 key: "TestBlood-AllItems-EnchantWeapon-Windsong-4441"
 value: {
  dps: 120186.0419
  tps: 726093.00222
  dtps: 97805.26239
  hps: 84002.17038
 }
}
dps_results: {
 key: "TestBlood-AllItems-EnigmaticPrimalDiamond"
 value: {
  dps: 122517.26691
  tps: 739214.90658
  dtps: 96978.47009
  hps: 85563.76599
 }
}
dps_results: {
 key: "TestBlood-AllItems-EternalPrimalDiamond"
 value: {
  dps: 122121.23432
  tps: 736698.84593
  dtps: 97037.36544
  hps: 85609.21177
 }
}
dps_results: {
 key: "TestBlood-AllItems-EvilEyeofGalakras-105491"
 value: {
  dps: 132788.86295
  tps: 754204.40988
  dtps: 84560.11023
  hps: 83691.40988
 }
}
dps_results: {
 key: "TestBlood-AllItems-FabledFeatherofJi-Kun-96842"
 value: {
  dps: 132880.43158
  tps: 800412.6423
  dtps: 87389.65309
  hps: 78884.32711
 }
}
dps_results: {
 key: "TestBlood-AllItems-Fen-Yu,FuryofXuen-102248"
 value: {
  dps: 127952.25499
  tps: 778489.35437
  dtps: 96526.51917
  hps: 88832.12095
 }
}
dps_results: {
 key: "TestBlood-AllItems-FleetPrimalDiamond"
 value: {
  dps: 121914.52088
  tps: 735591.57279
  dtps: 96788.7263
  hps: 85826.25083
 }
}
dps_results: {
 key: "TestBlood-AllItems-ForlornPrimalDiamond"
 value: {
  dps: 121672.84002
  tps: 733992.27718
  dtps: 97037.36544
  hps: 85510.35258
 }
}
dps_results: {
 key: "TestBlood-AllItems-GazeoftheTwins-96915"
 value: {
  dps: 126449.99352
  tps: 764593.34642
  dtps: 92945.70978
  hps: 81138.39632
 }
}
dps_results: {
 key: "TestBlood-AllItems-Gong-Lu,StrengthofXuen-102249"
 value: {
  dps: 131289.90313
  tps: 798166.82563
  dtps: 93523.11937
  hps: 87238.24207
 }
}
dps_results: {
 key: "TestBlood-AllItems-Horridon'sLastGasp-96757"
 value: {
  dps: 121188.66703
  tps: 732390.84008
  dtps: 95841.77162
  hps: 82687.06543
 }
}
dps_results: {
 key: "TestBlood-AllItems-ImpassivePrimalDiamond"
 value: {
  dps: 122517.26691
  tps: 739214.90658
  dtps: 96978.47009
  hps: 85563.76599
 }
}
dps_results: {
 key: "TestBlood-AllItems-IndomitablePrimalDiamond"
 value: {
  dps: 121687.79444
  tps: 733747.35261
  dtps: 96945.03986
  hps: 85270.57363
 }
}
dps_results: {
 key: "TestBlood-AllItems-InscribedBagofHydra-Spawn-96828"
 value: {
  dps: 121629.82316
  tps: 734461.33785
  dtps: 94922.50739
  hps: 82087.32184
 }
}
dps_results: {
 key: "TestBlood-AllItems-Ji-Kun'sRisingWinds-96843"
 value: {
  dps: 122993.37926
  tps: 745085.84781
  dtps: 96011.16315
  hps: 84508.19537
 }
}
dps_results: {
 key: "TestBlood-AllItems-PhaseFingers-4697"
 value: {
  dps: 122767.03904
  tps: 742355.09187
  dtps: 95525.52333
  hps: 85034.13937
 }
}
dps_results: {
 key: "TestBlood-AllItems-PlateofCyclopeanDread"
 value: {
  dps: 127276.43431
  tps: 766858.8455
  dtps: 86752.92703
  hps: 80239.00132
 }
}
dps_results: {
 key: "TestBlood-AllItems-PlateoftheAll-ConsumingMaw"
 value: {
  dps: 137840.34563
  tps: 832340.20781
  dtps: 89604.41024
  hps: 73882.72868
 }
}
dps_results: {
 key: "TestBlood-AllItems-PlateoftheLostCatacomb"
 value: {
  dps: 121810.34109
  tps: 735984.78876
  dtps: 95504.74785
  hps: 77369.87153
 }
}
dps_results: {
 key: "TestBlood-AllItems-PowerfulPrimalDiamond"
 value: {
  dps: 121687.79444
  tps: 733747.35261
  dtps: 96945.03986
  hps: 85270.57363
 }
}
dps_results: {
 key: "TestBlood-AllItems-PriceofProgress-81266"
 value: {
  dps: 121103.8939
  tps: 731665.02576
  dtps: 95888.21787
  hps: 82739.08751
 }
}
dps_results: {
 key: "TestBlood-AllItems-Primordius'TalismanofRage-96873"
 value: {
  dps: 128642.1226
  tps: 775460.18948
  dtps: 93377.63499
  hps: 82464.45024
 }
}
dps_results: {
 key: "TestBlood-AllItems-Qian-Le,CourageofNiuzao-102245"
 value: {
  dps: 122002.32074
  tps: 739440.94028
  dtps: 93756.91501
  hps: 86562.62674
 }
}
dps_results: {
 key: "TestBlood-AllItems-Qian-Ying,FortitudeofNiuzao-102250"
 value: {
  dps: 124824.20102
  tps: 754914.13331
  dtps: 90072.30419
  hps: 83653.48886
 }
}
dps_results: {
 key: "TestBlood-AllItems-Renataki'sSoulCharm-96741"
 value: {
  dps: 123876.44562
  tps: 750469.54434
  dtps: 95966.66148
  hps: 83331.82913
 }
}
dps_results: {
 key: "TestBlood-AllItems-ReverberatingPrimalDiamond"
 value: {
  dps: 122347.14079
  tps: 738757.42608
  dtps: 96619.95152
  hps: 85388.16663
 }
}
dps_results: {
 key: "TestBlood-AllItems-RevitalizingPrimalDiamond"
 value: {
  dps: 122402.82702
  tps: 739053.62445
  dtps: 97040.3575
  hps: 85580.68155
 }
}
dps_results: {
 key: "TestBlood-AllItems-RuneofCinderglacier-3369"
 value: {
  dps: 119325.19783
  tps: 723385.1214
  dtps: 97768.34571
  hps: 82872.33138
 }
}
dps_results: {
 key: "TestBlood-AllItems-RuneofRazorice-3370"
 value: {
  dps: 120702.80978
  tps: 733695.97296
  dtps: 97852.51155
  hps: 82426.32114
 }
}
dps_results: {
 key: "TestBlood-AllItems-RuneofSpellbreaking-3595"
 value: {
  dps: 117666.9049
  tps: 712444.6388
  dtps: 97852.51155
  hps: 82426.32114
 }
}
dps_results: {
 key: "TestBlood-AllItems-RuneofSpellshattering-3367"
 value: {
  dps: 117666.9049
  tps: 712444.6388
  dtps: 97852.51155
  hps: 82426.32114
 }
}
dps_results: {
 key: "TestBlood-AllItems-RuneofSwordbreaking-3594"
 value: {
  dps: 118841.74143
  tps: 720496.45468
  dtps: 94871.18698
  hps: 81385.82662
 }
}
dps_results: {
 key: "TestBlood-AllItems-RuneofSwordshattering-3365"
 value: {
  dps: 120209.55424
  tps: 727674.70392
  dtps: 92129.69851
  hps: 80223.08805
 }
}
dps_results: {
 key: "TestBlood-AllItems-RuneoftheNerubianCarapace-3883"
 value: {
  dps: 118417.59278
  tps: 715383.7348
  dtps: 97075.50991
  hps: 81942.70584
 }
}
dps_results: {
 key: "TestBlood-AllItems-RuneoftheStoneskinGargoyle-3847"
 value: {
  dps: 118318.94737
  tps: 714998.68111
  dtps: 95967.44269
  hps: 81043.68974
 }
}
dps_results: {
 key: "TestBlood-AllItems-SinisterPrimalDiamond"
 value: {
  dps: 122202.36016
  tps: 737100.93853
  dtps: 97037.36544
  hps: 85630.7005
 }
}
dps_results: {
 key: "TestBlood-AllItems-SynapseSprings(MarkII)-4898"
 value: {
  dps: 122914.41185
  tps: 742064.21574
  dtps: 95516.7295
  hps: 84956.11635
 }
}
dps_results: {
 key: "TestBlood-AllItems-TalismanofBloodlust-96864"
 value: {
  dps: 124608.3687
  tps: 752492.24411
  dtps: 95640.3066
  hps: 86076.03708
 }
}
dps_results: {
 key: "TestBlood-AllItems-TyrannicalPrimalDiamond"
 value: {
  dps: 121672.84002
  tps: 733992.27718
  dtps: 97037.36544
  hps: 85510.35258
 }
}
dps_results: {
 key: "TestBlood-AllItems-UnerringVisionofLeiShen-96930"
 value: {
  dps: 124954.89966
  tps: 756601.67156
  dtps: 96002.43518
  hps: 83563.86896
 }
}
dps_results: {
 key: "TestBlood-AllItems-Wushoolay'sFinalChoice-96785"
 value: {
  dps: 121798.02396
  tps: 733226.42034
  dtps: 95914.95045
  hps: 81940.25823
 }
}
dps_results: {
 key: "TestBlood-AllItems-Xing-Ho,BreathofYu'lon-102246"
 value: {
  dps: 122356.20324
  tps: 740051.04049
  dtps: 96327.2485
  hps: 89641.81533
 }
}
dps_results: {
 key: "TestBlood-AllItems-ZenAlchemistStone-75274"
 value: {
  dps: 123961.42777
  tps: 746237.48046
  dtps: 94295.44421
  hps: 82652.55347
 }
}
dps_results: {
 key: "TestBlood-Average-Default"
 value: {
  dps: 122512.60971
  tps: 739950.43849
  dtps: 94681.46977
  hps: 84743.033
 }
}
dps_results: {
 key: "TestBlood-Settings-Orc-p1-Basic-defensive-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 1.05232033402e+06
  tps: 6.43715894693e+06
  dtps: 1.92041941557e+06
  hps: 568787.55651
 }
}
dps_results: {
 key: "TestBlood-Settings-Orc-p1-Basic-defensive-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 126266.13542
  tps: 762595.3406
  dtps: 95280.989
  hps: 85517.04088
 }
}
dps_results: {
 key: "TestBlood-Settings-Orc-p1-Basic-defensive-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 139048.90267
  tps: 787982.56449
  dtps: 94365.41402
  hps: 101629.35275
 }
}
dps_results: {
 key: "TestBlood-Settings-Orc-p1-Basic-defensive-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 830905.87386
  tps: 5.10998173994e+06
  dtps: 2.15753385462e+06
  hps: 579881.7802
 }
}
dps_results: {
 key: "TestBlood-Settings-Orc-p1-Basic-defensive-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 104176.46591
  tps: 633459.87881
  dtps: 96606.68922
  hps: 73842.9489
 }
}
dps_results: {
 key: "TestBlood-Settings-Orc-p1-Basic-defensive-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 101571.40787
  tps: 598640.01489
  dtps: 96238.94362
  hps: 82493.78522
 }
}
dps_results: {
 key: "TestBlood-Settings-Worgen-p1-Basic-defensive-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 1.0520947954e+06
  tps: 6.46374985656e+06
  dtps: 1.92093647022e+06
  hps: 566508.74453
 }
}
dps_results: {
 key: "TestBlood-Settings-Worgen-p1-Basic-defensive-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 125699.60298
  tps: 761369.8039
  dtps: 95306.81717
  hps: 85198.03401
 }
}
dps_results: {
 key: "TestBlood-Settings-Worgen-p1-Basic-defensive-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 137029.2201
  tps: 783166.80636
  dtps: 94033.33788
  hps: 100450.62489
 }
}
dps_results: {
 key: "TestBlood-Settings-Worgen-p1-Basic-defensive-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 821108.35817
  tps: 5.06966890021e+06
  dtps: 2.15781628935e+06
  hps: 582624.22161
 }
}
dps_results: {
 key: "TestBlood-Settings-Worgen-p1-Basic-defensive-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 103858.5522
  tps: 633796.95536
  dtps: 96700.63681
  hps: 74383.42676
 }
}
dps_results: {
 key: "TestBlood-Settings-Worgen-p1-Basic-defensive-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 100132.9278
  tps: 595621.15075
  dtps: 96283.31626
  hps: 82635.03495
 }
}
dps_results: {
 key: "TestBlood-SwitchInFrontOfTarget-Default"
 value: {
  dps: 126266.13542
  tps: 762595.3406
  dtps: 95280.989
  hps: 85517.04088
 }
}
