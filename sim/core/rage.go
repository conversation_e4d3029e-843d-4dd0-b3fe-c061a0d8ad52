package core

import (
	"fmt"

	"github.com/wowsims/mop/sim/core/proto"
)

const ThreatPerRageGained = 5
const BaseRageHitFactor = 1.75

type rageBar struct {
	unit *Unit

	maxRage     float64
	currentRage float64

	// hitFactor is the quantity which multiplies the base swing speed of a
	// MH weapon when calculating the Rage generated by that weapon's
	// swings. For OH weapons, hitFactor / 2 is used.
	startingHitFactor float64
	currentHitFactor  float64

	RageRefundMetrics     *ResourceMetrics
	EncounterStartMetrics *ResourceMetrics
}

type RageBarOptions struct {
	BaseRageMultiplier float64
	MaxRage            float64
}

func (unit *Unit) EnableRageBar(options RageBarOptions) {
	unit.SetCurrentPowerBar(RageBar)
	unit.RegisterAura(Aura{
		Label:    "RageBar",
		Duration: NeverExpires,
		OnReset: func(aura *Aura, sim *Simulation) {
			aura.Activate(sim)
		},
		OnSpellHitDealt: func(aura *Aura, sim *Simulation, spell *Spell, result *SpellResult) {
			if unit.GetCurrentPowerBar() != RageBar {
				return
			}
			if !result.Landed() {
				return
			}

			hitFactor := unit.rageBar.currentHitFactor
			var speed float64
			if spell.ProcMask == ProcMaskMeleeMHAuto {
				speed = unit.AutoAttacks.MH().SwingSpeed
			} else if spell.ProcMask == ProcMaskMeleeOHAuto {
				// OH hits generate 50% of the rage they would if they were MH hits
				hitFactor /= 2
				speed = unit.AutoAttacks.OH().SwingSpeed
			} else {
				return
			}

			// rage in mop is normalized so it only depends on weapon swing speed and some multipliers
			generatedRage := hitFactor * speed

			var metrics *ResourceMetrics
			if spell.Cost != nil {
				metrics = spell.Cost.ResourceCostImpl.(*RageCost).ResourceMetrics
			} else {
				if spell.ResourceMetrics == nil {
					spell.ResourceMetrics = spell.Unit.NewRageMetrics(spell.ActionID)
				}
				metrics = spell.ResourceMetrics
			}
			unit.AddRage(sim, generatedRage, metrics)
		},
	})

	// Not a real spell, just holds metrics from rage gain threat.
	unit.RegisterSpell(SpellConfig{
		ActionID: ActionID{OtherID: proto.OtherAction_OtherActionRageGain},
	})

	maxRage := max(100.0, options.MaxRage)

	unit.rageBar = rageBar{
		unit:                  unit,
		maxRage:               maxRage,
		startingHitFactor:     BaseRageHitFactor * options.BaseRageMultiplier,
		RageRefundMetrics:     unit.NewRageMetrics(ActionID{OtherID: proto.OtherAction_OtherActionRefund}),
		EncounterStartMetrics: unit.NewRageMetrics(encounterStartActionID),
	}
}

func (unit *Unit) HasRageBar() bool {
	return unit.rageBar.unit != nil
}

func (rb *rageBar) CurrentRage() float64 {
	return rb.currentRage
}

func (rb *rageBar) MaximumRage() float64 {
	return rb.maxRage
}

// Call this within the OnGain and OnExpire callbacks for Battle Stance, Raging
// Whirlwind, etc.
func (rb *rageBar) MultiplyAutoAttackRageGen(multiplier float64) {
	rb.currentHitFactor *= multiplier
}

func (rb *rageBar) AddRage(sim *Simulation, amount float64, metrics *ResourceMetrics) {
	if amount < 0 {
		panic("Trying to add negative rage!")
	}

	newRage := min(rb.currentRage+amount, rb.maxRage)
	metrics.AddEvent(amount, newRage-rb.currentRage)

	if sim.Log != nil {
		rb.unit.Log(sim, "Gained %0.3f rage from %s (%0.3f --> %0.3f) of %0.0f total.", amount, metrics.ActionID, rb.currentRage, newRage, 100.0)
	}

	rb.currentRage = newRage
	if !sim.Options.Interactive {
		rb.unit.ReactToEvent(sim)
	}
}

func (rb *rageBar) SpendRage(sim *Simulation, amount float64, metrics *ResourceMetrics) {
	if amount < 0 {
		panic("Trying to spend negative rage!")
	}

	newRage := rb.currentRage - amount
	metrics.AddEvent(-amount, -amount)

	if sim.Log != nil {
		rb.unit.Log(sim, "Spent %0.3f rage from %s (%0.3f --> %0.3f) of %0.0f total.", amount, metrics.ActionID, rb.currentRage, newRage, 100.0)
	}

	rb.currentRage = newRage
}

func (rb *rageBar) ResetRageBar(sim *Simulation, rageToKeep float64) {
	if rb.currentRage > rageToKeep {
		rb.SpendRage(sim, rb.currentRage-rageToKeep, rb.EncounterStartMetrics)
	} else if rageToKeep > rb.currentRage {
		rb.AddRage(sim, rageToKeep-rb.currentRage, rb.EncounterStartMetrics)
	}
}

func (rb *rageBar) reset(_ *Simulation) {
	if rb.unit == nil {
		return
	}

	rb.currentRage = 0
	rb.currentHitFactor = rb.startingHitFactor
}

func (rb *rageBar) doneIteration() {
	if rb.unit == nil {
		return
	}

	rageGainSpell := rb.unit.GetSpell(ActionID{OtherID: proto.OtherAction_OtherActionRageGain})

	for _, resourceMetrics := range rb.unit.Metrics.resources {
		if resourceMetrics.Type != proto.ResourceType_ResourceTypeRage {
			continue
		}
		if resourceMetrics.ActionID.SameActionIgnoreTag(ActionID{OtherID: proto.OtherAction_OtherActionDamageTaken}) {
			continue
		}
		if resourceMetrics.ActionID.SameActionIgnoreTag(ActionID{OtherID: proto.OtherAction_OtherActionRefund}) {
			continue
		}
		if resourceMetrics.ActualGainForCurrentIteration() <= 0 {
			continue
		}

		// Need to exclude rage gained from white hits. Rather than have a manual list of all IDs that would
		// apply here (autos, WF attack, sword spec procs, etc), just check if the effect caused any damage.
		sourceSpell := rb.unit.GetSpell(resourceMetrics.ActionID)
		if sourceSpell != nil && sourceSpell.SpellMetrics[0].TotalDamage > 0 {
			continue
		}

		rageGainSpell.SpellMetrics[0].Casts += resourceMetrics.EventsForCurrentIteration()
		rageGainSpell.ApplyAOEThreatIgnoreMultipliers(resourceMetrics.ActualGainForCurrentIteration() * ThreatPerRageGained)
	}
}

type RageCostOptions struct {
	Cost int32

	Refund        float64
	RefundMetrics *ResourceMetrics // Optional, will default to unit.RageRefundMetrics if not supplied.
}
type RageCost struct {
	Refund          float64
	RefundMetrics   *ResourceMetrics
	ResourceMetrics *ResourceMetrics
}

func newRageCost(spell *Spell, options RageCostOptions) *SpellCost {
	if options.Refund > 0 && options.RefundMetrics == nil {
		options.RefundMetrics = spell.Unit.RageRefundMetrics
	}

	return &SpellCost{
		spell:           spell,
		BaseCost:        options.Cost,
		PercentModifier: 1,
		ResourceCostImpl: &RageCost{
			Refund:          options.Refund,
			RefundMetrics:   options.RefundMetrics,
			ResourceMetrics: spell.Unit.NewRageMetrics(spell.ActionID),
		},
	}
}

func (rc *RageCost) MeetsRequirement(_ *Simulation, spell *Spell) bool {
	spell.CurCast.Cost = spell.Cost.GetCurrentCost()
	return spell.Unit.CurrentRage() >= spell.CurCast.Cost
}
func (rc *RageCost) CostFailureReason(sim *Simulation, spell *Spell) string {
	return fmt.Sprintf("not enough rage (Current Rage = %0.03f, Rage Cost = %0.03f)", spell.Unit.CurrentRage(), spell.CurCast.Cost)
}
func (rc *RageCost) SpendCost(sim *Simulation, spell *Spell) {
	if spell.CurCast.Cost > 0 {
		spell.Unit.SpendRage(sim, spell.CurCast.Cost, rc.ResourceMetrics)
	}
}
func (rc *RageCost) IssueRefund(sim *Simulation, spell *Spell) {
	if rc.Refund > 0 && spell.CurCast.Cost > 0 {
		spell.Unit.AddRage(sim, rc.Refund*spell.CurCast.Cost, rc.RefundMetrics)
	}
}

func (spell *Spell) RageMetrics() *ResourceMetrics {
	return spell.Cost.ResourceCostImpl.(*RageCost).ResourceMetrics
}
