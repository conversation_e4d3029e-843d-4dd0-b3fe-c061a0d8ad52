package core

import (
	"fmt"
	"math"

	"github.com/wowsims/mop/sim/core/stats"
)

type SpellResult struct {
	// Target of the spell.
	Target *Unit

	// Results
	Outcome HitOutcome
	Damage  float64 // Damage done by this cast.
	Threat  float64 // The amount of threat generated by this cast.

	ArmorMultiplier  float64 // Armor multiplier
	PreOutcomeDamage float64 // Damage done by this cast before Outcome is applied

	inUse bool
}

type SpellResultSlice []*SpellResult

func (results SpellResultSlice) NumLandedHits() int32 {
	var numLandedHits int32

	for _, result := range results {
		if result.Landed() {
			numLandedHits++
		}
	}

	return numLandedHits
}

func (results SpellResultSlice) AnyLanded() bool {
	for _, result := range results {
		if result.Landed() {
			return true
		}
	}

	return false
}

type SpellResultCache map[*Unit]*SpellResult

func (resultCache SpellResultCache) Get(target *Unit) *SpellResult {
	result, ok := resultCache[target]

	if ok && !result.inUse {
		return result
	}

	result = &SpellResult{}

	if !ok {
		resultCache[target] = result
	}

	return result
}

func (spell *Spell) NewResult(target *Unit) *SpellResult {
	result := spell.resultCache.Get(target)
	result.Target = target
	result.Damage = 0
	result.Threat = 0
	result.Outcome = OutcomeEmpty // for blocks
	result.inUse = true
	result.PreOutcomeDamage = 0

	return result
}
func (spell *Spell) DisposeResult(result *SpellResult) {
	result.inUse = false
}

func (result *SpellResult) Landed() bool {
	return result.Outcome.Matches(OutcomeLanded)
}

func (result *SpellResult) DidCrit() bool {
	return result.Outcome.Matches(OutcomeCrit)
}

func (result *SpellResult) DidGlance() bool {
	return result.Outcome.Matches(OutcomeGlance)
}

func (result *SpellResult) DidBlock() bool {
	return result.Outcome.Matches(OutcomeBlock)
}

func (result *SpellResult) DamageString() string {
	outcomeStr := result.Outcome.String()
	if !result.Landed() {
		return outcomeStr
	}
	return fmt.Sprintf("%s for %0.3f damage", outcomeStr, result.Damage)
}
func (result *SpellResult) HealingString() string {
	return fmt.Sprintf("%s for %0.3f healing", result.Outcome.String(), result.Damage)
}

func (spell *Spell) ThreatFromDamage(sim *Simulation, outcome HitOutcome, damage float64, attackTable *AttackTable) float64 {
	if outcome.Matches(OutcomeLanded) {
		threat := (damage*spell.ThreatMultiplier + spell.FlatThreatBonus) * spell.Unit.PseudoStats.ThreatMultiplier

		if attackTable.ThreatDoneByCasterExtraMultiplier != nil {
			for i := range attackTable.ThreatDoneByCasterExtraMultiplier {
				if attackTable.ThreatDoneByCasterExtraMultiplier[i] != nil {
					threat *= attackTable.ThreatDoneByCasterExtraMultiplier[i](sim, spell, attackTable)
				}
			}
		}

		return threat
	} else {
		return 0
	}
}

func (spell *Spell) MeleeAttackPower() float64 {
	return spell.Unit.GetAttackPowerValue(spell)
}

func (spell *Spell) RangedAttackPower() float64 {
	return spell.Unit.stats[stats.RangedAttackPower]
}

func (spell *Spell) DodgeSuppression() float64 {
	expertiseRating := spell.Unit.stats[stats.ExpertiseRating] + spell.BonusExpertiseRating
	return expertiseRating / ExpertisePerQuarterPercentReduction / 400
}

// MoP reworked Parry. Rather than being innately ~2x Dodge chance, expertise now applies to Dodge first (down to 0), and then Parry.
// The base chance for Dodge/Parry are both 7.5%, assuming a +3 target. The 7.5% Dodge chance must be fully suppressed before Parry will go down.
// This makes the effect of each point of Expertise linear when attacking from the front
func (spell *Spell) ParrySuppression(attackTable *AttackTable) float64 {
	return max(0, spell.DodgeSuppression()-attackTable.BaseDodgeChance)
}

func (spell *Spell) PhysicalHitChance(attackTable *AttackTable) float64 {
	hitPercent := spell.Unit.stats[stats.PhysicalHitPercent] + spell.BonusHitPercent
	return hitPercent / 100
}
func (spell *Spell) PhysicalHitCheck(sim *Simulation, attackTable *AttackTable) bool {
	return sim.Proc(1.0-spell.GetPhysicalMissChance(attackTable), "Physical Hit Roll")
}
func (spell *Spell) PhysicalCritChance(attackTable *AttackTable) float64 {
	critPercent := spell.Unit.stats[stats.PhysicalCritPercent] + spell.BonusCritPercent
	return critPercent/100 - attackTable.MeleeCritSuppression
}
func (spell *Spell) PhysicalCritCheck(sim *Simulation, attackTable *AttackTable) bool {
	return sim.RandomFloat("Physical Crit Roll") < spell.PhysicalCritChance(attackTable)
}

func (spell *Spell) BonusDamage() float64 {
	var bonusDamage float64

	if spell.SpellSchool.Matches(SpellSchoolPhysical) {
		bonusDamage = spell.Unit.PseudoStats.BonusDamage
	} else {
		bonusDamage = spell.SpellPower()
	}

	return bonusDamage
}

func (spell *Spell) SpellPower() float64 {
	return spell.Unit.GetSpellPowerValue(spell)
}

func (spell *Spell) SpellHitChance(target *Unit) float64 {
	hitPercent := spell.Unit.stats[stats.SpellHitPercent] + spell.BonusHitPercent
	return hitPercent / 100
}
func (spell *Spell) SpellChanceToMiss(attackTable *AttackTable) float64 {
	return math.Max(0, attackTable.BaseSpellMissChance-spell.SpellHitChance(attackTable.Defender))
}
func (spell *Spell) MagicHitCheck(sim *Simulation, attackTable *AttackTable) bool {
	return sim.Proc(1.0-spell.SpellChanceToMiss(attackTable), "Magical Hit Roll")
}

func (spell *Spell) SpellCritChance(target *Unit) float64 {
	attackTable := spell.Unit.AttackTables[target.UnitIndex]
	critPercent := spell.Unit.stats[stats.SpellCritPercent] +
		spell.BonusCritPercent +
		attackTable.BonusSpellCritPercent
	return critPercent/100 - attackTable.SpellCritSuppression
}
func (spell *Spell) MagicCritCheck(sim *Simulation, target *Unit) bool {
	critChance := spell.SpellCritChance(target)
	return sim.RandomFloat("Magical Crit Roll") < critChance
}

func (spell *Spell) HealingPower(target *Unit) float64 {
	return spell.SpellPower() + target.PseudoStats.BonusHealingTaken
}
func (spell *Spell) HealingCritChance() float64 {
	return (spell.Unit.GetStat(stats.SpellCritPercent) + spell.BonusCritPercent) / 100
}

func (spell *Spell) HealingCritCheck(sim *Simulation) bool {
	critChance := spell.HealingCritChance()
	return sim.RandomFloat("Healing Crit Roll") < critChance
}

func (spell *Spell) ApplyPostOutcomeDamageModifiers(sim *Simulation, result *SpellResult, isPeriodic bool) {
	for i := range result.Target.DynamicDamageTakenModifiers {
		result.Target.DynamicDamageTakenModifiers[i](sim, spell, result, isPeriodic)
	}
	if spell.Flags.Matches(SpellFlagAoE) {
		result.Damage *= sim.Encounter.AOECapMultiplier()
	}
	result.Damage = max(0, result.Damage)
}

func (spell *Spell) ApplyPostOutcomeHealingModifiers(sim *Simulation, result *SpellResult) {
	for i := range result.Target.DynamicHealingTakenModifiers {
		result.Target.DynamicHealingTakenModifiers[i](sim, spell, result)
	}
	result.Damage = max(0, result.Damage)
}

// For spells that do no damage but still have a hit/miss check.
func (spell *Spell) CalcOutcome(sim *Simulation, target *Unit, outcomeApplier OutcomeApplier) *SpellResult {
	attackTable := spell.Unit.AttackTables[target.UnitIndex]
	result := spell.NewResult(target)

	outcomeApplier(sim, result, attackTable)
	result.Threat = spell.ThreatFromDamage(sim, result.Outcome, result.Damage, attackTable)
	return result
}

func (spell *Spell) calcDamageInternal(sim *Simulation, target *Unit, baseDamage float64, attackerMultiplier float64, isPeriodic bool, outcomeApplier OutcomeApplier) *SpellResult {
	attackTable := spell.Unit.AttackTables[target.UnitIndex]

	result := spell.NewResult(target)
	result.Damage = baseDamage

	if sim.Log == nil {
		result.Damage *= attackerMultiplier
		result.applyArmor(spell, isPeriodic, attackTable)
		result.applyTargetModifiers(sim, spell, attackTable, isPeriodic)

		outcomeApplier(sim, result, attackTable)

		spell.ApplyPostOutcomeDamageModifiers(sim, result, isPeriodic)
	} else {
		result.Damage *= attackerMultiplier
		afterAttackMods := result.Damage
		result.applyArmor(spell, isPeriodic, attackTable)
		afterArmor := result.Damage
		result.applyTargetModifiers(sim, spell, attackTable, isPeriodic)
		afterTargetMods := result.Damage

		outcomeApplier(sim, result, attackTable)
		afterOutcome := result.Damage

		spell.ApplyPostOutcomeDamageModifiers(sim, result, isPeriodic)
		afterPostOutcome := result.Damage

		spell.Unit.Log(
			sim,
			"%s %s [DEBUG] MAP: %0.01f, RAP: %0.01f, SP: %0.01f, BaseDamage:%0.01f, AfterAttackerMods:%0.01f, AfterArmor:%0.01f, AfterTargetMods:%0.01f, AfterOutcome:%0.01f, AfterPostOutcome:%0.01f",
			target.LogLabel(), spell.ActionID, spell.Unit.GetStat(stats.AttackPower), spell.Unit.GetStat(stats.RangedAttackPower), spell.SpellPower(), baseDamage, afterAttackMods, afterArmor, afterTargetMods, afterOutcome, afterPostOutcome)
	}

	result.Threat = spell.ThreatFromDamage(sim, result.Outcome, result.Damage, attackTable)

	return result
}
func (spell *Spell) CalcDamage(sim *Simulation, target *Unit, baseDamage float64, outcomeApplier OutcomeApplier) *SpellResult {
	attackerMultiplier := spell.AttackerDamageMultiplier(spell.Unit.AttackTables[target.UnitIndex], false)
	if spell.BonusCoefficient > 0 {
		baseDamage += spell.BonusCoefficient * spell.BonusDamage()
	}
	return spell.calcDamageInternal(sim, target, baseDamage, attackerMultiplier, false, outcomeApplier)
}
func (spell *Spell) CalcPeriodicDamage(sim *Simulation, target *Unit, baseDamage float64, outcomeApplier OutcomeApplier) *SpellResult {
	attackerMultiplier := spell.AttackerDamageMultiplier(spell.Unit.AttackTables[target.UnitIndex], true)

	var dot *Dot
	if spell.aoeDot != nil {
		dot = spell.aoeDot
	} else {
		dot = spell.Dot(target)
	}
	if dot.BonusCoefficient > 0 {
		baseDamage += dot.BonusCoefficient * spell.BonusDamage()
	}
	attackerMultiplier *= dot.PeriodicDamageMultiplier
	return spell.calcDamageInternal(sim, target, baseDamage, attackerMultiplier, true, outcomeApplier)
}
func (dot *Dot) CalcSnapshotDamage(sim *Simulation, target *Unit, outcomeApplier OutcomeApplier) *SpellResult {
	return dot.Spell.calcDamageInternal(sim, target, dot.SnapshotBaseDamage, dot.SnapshotAttackerMultiplier, true, outcomeApplier)
}

func (spell *Spell) DealOutcome(sim *Simulation, result *SpellResult) {
	spell.DealDamage(sim, result)
}
func (spell *Spell) CalcAndDealOutcome(sim *Simulation, target *Unit, outcomeApplier OutcomeApplier) *SpellResult {
	result := spell.CalcOutcome(sim, target, outcomeApplier)
	spell.DealDamage(sim, result)
	return result
}

// Applies the fully computed spell result to the sim.
func (spell *Spell) dealDamageInternal(sim *Simulation, isPeriodic bool, result *SpellResult) {
	if sim.CurrentTime >= 0 {
		spell.SpellMetrics[result.Target.UnitIndex].TotalDamage += result.Damage
		if isPeriodic {
			spell.SpellMetrics[result.Target.UnitIndex].TotalTickDamage += result.Damage
		}

		if result.DidCrit() {
			if result.DidBlock() {
				spell.SpellMetrics[result.Target.UnitIndex].TotalCritBlockDamage += result.Damage
			} else {
				spell.SpellMetrics[result.Target.UnitIndex].TotalCritDamage += result.Damage
				if isPeriodic {
					spell.SpellMetrics[result.Target.UnitIndex].TotalCritTickDamage += result.Damage
				}
			}
		} else if result.DidGlance() {
			if result.DidBlock() {
				spell.SpellMetrics[result.Target.UnitIndex].TotalGlanceBlockDamage += result.Damage
			} else {
				spell.SpellMetrics[result.Target.UnitIndex].TotalGlanceDamage += result.Damage
			}
		} else if result.DidBlock() {
			spell.SpellMetrics[result.Target.UnitIndex].TotalBlockDamage += result.Damage
		}
		spell.SpellMetrics[result.Target.UnitIndex].TotalThreat += result.Threat
	}

	// Mark total damage done in raid so far for health based fights.
	// Don't include damage done by EnemyUnits to Players
	if result.Target.Type == EnemyUnit {
		sim.Encounter.DamageTaken += result.Damage
	}

	if sim.Log != nil && !spell.Flags.Matches(SpellFlagNoLogs) {
		if isPeriodic {
			spell.Unit.Log(sim, "%s %s tick %s (SpellSchool: %d). (Threat: %0.3f)", result.Target.LogLabel(), spell.ActionID, result.DamageString(), spell.SpellSchool, result.Threat)
		} else {
			spell.Unit.Log(sim, "%s %s %s (SpellSchool: %d). (Threat: %0.3f)", result.Target.LogLabel(), spell.ActionID, result.DamageString(), spell.SpellSchool, result.Threat)
		}
	}

	if !spell.Flags.Matches(SpellFlagNoOnDamageDealt) {
		if isPeriodic {
			spell.Unit.OnPeriodicDamageDealt(sim, spell, result)
			result.Target.OnPeriodicDamageTaken(sim, spell, result)
		} else {
			spell.Unit.OnSpellHitDealt(sim, spell, result)
			result.Target.OnSpellHitTaken(sim, spell, result)
		}
	}

	spell.DisposeResult(result)
}
func (spell *Spell) DealDamage(sim *Simulation, result *SpellResult) {
	spell.dealDamageInternal(sim, false, result)
}
func (spell *Spell) DealPeriodicDamage(sim *Simulation, result *SpellResult) {
	spell.dealDamageInternal(sim, true, result)
}

func (spell *Spell) CalcAndDealDamage(sim *Simulation, target *Unit, baseDamage float64, outcomeApplier OutcomeApplier) *SpellResult {
	result := spell.CalcDamage(sim, target, baseDamage, outcomeApplier)
	spell.DealDamage(sim, result)
	return result
}

type BaseDamageCalculator func(*Simulation, *Spell) float64
type SpellResultIteration func(*Simulation, *Unit, float64, OutcomeApplier) *SpellResult

func fixedBaseDamageFactory(baseDamage float64) BaseDamageCalculator {
	return func(_ *Simulation, _ *Spell) float64 {
		return baseDamage
	}
}

func (spell *Spell) aoeIteration(sim *Simulation, outcomeApplier OutcomeApplier, baseDamageCalculator BaseDamageCalculator, singleResultCalculator SpellResultIteration) SpellResultSlice {
	spell.resultSlice = spell.resultSlice[:0]

	for _, aoeTarget := range sim.Encounter.ActiveTargetUnits {
		baseDamage := baseDamageCalculator(sim, spell)
		spell.resultSlice = append(spell.resultSlice, singleResultCalculator(sim, aoeTarget, baseDamage, outcomeApplier))
	}

	return spell.resultSlice
}

func (spell *Spell) CalcAndDealAoeDamageWithVariance(sim *Simulation, outcomeApplier OutcomeApplier, baseDamageCalculator BaseDamageCalculator) SpellResultSlice {
	return spell.aoeIteration(sim, outcomeApplier, baseDamageCalculator, spell.CalcAndDealDamage)
}

func (spell *Spell) CalcAndDealAoeDamage(sim *Simulation, baseDamage float64, outcomeApplier OutcomeApplier) SpellResultSlice {
	return spell.CalcAndDealAoeDamageWithVariance(sim, outcomeApplier, fixedBaseDamageFactory(baseDamage))
}

func (spell *Spell) CalcAndDealPeriodicAoeDamage(sim *Simulation, baseDamage float64, outcomeApplier OutcomeApplier) SpellResultSlice {
	return spell.aoeIteration(sim, outcomeApplier, fixedBaseDamageFactory(baseDamage), spell.CalcAndDealPeriodicDamage)
}

func (spell *Spell) cleaveIteration(sim *Simulation, firstTarget *Unit, maxTargets int32, outcomeApplier OutcomeApplier, baseDamageCalculator BaseDamageCalculator, singleResultCalculator SpellResultIteration) SpellResultSlice {
	spell.resultSlice = spell.resultSlice[:0]
	numTargets := min(maxTargets, sim.Environment.ActiveTargetCount())
	curTarget := firstTarget

	for range numTargets {
		baseDamage := baseDamageCalculator(sim, spell)
		spell.resultSlice = append(spell.resultSlice, singleResultCalculator(sim, curTarget, baseDamage, outcomeApplier))
		curTarget = sim.Environment.NextActiveTargetUnit(curTarget)
	}

	return spell.resultSlice
}

func (spell *Spell) CalcAndDealCleaveDamageWithVariance(sim *Simulation, firstTarget *Unit, maxTargets int32, outcomeApplier OutcomeApplier, baseDamageCalculator BaseDamageCalculator) SpellResultSlice {
	return spell.cleaveIteration(sim, firstTarget, maxTargets, outcomeApplier, baseDamageCalculator, spell.CalcAndDealDamage)
}

func (spell *Spell) CalcAndDealCleaveDamage(sim *Simulation, firstTarget *Unit, maxTargets int32, baseDamage float64, outcomeApplier OutcomeApplier) SpellResultSlice {
	return spell.CalcAndDealCleaveDamageWithVariance(sim, firstTarget, maxTargets, outcomeApplier, fixedBaseDamageFactory(baseDamage))
}

// Use CalcAoeDamage + DealBatchedAoeDamage instead of CalcAndDealAoeDamage in situations where you want to block procs
// on early targets from influencing the damage calculation on later targets.
func (spell *Spell) CalcAoeDamage(sim *Simulation, baseDamage float64, outcomeApplier OutcomeApplier) SpellResultSlice {
	return spell.CalcAoeDamageWithVariance(sim, outcomeApplier, fixedBaseDamageFactory(baseDamage))
}
func (spell *Spell) CalcAoeDamageWithVariance(sim *Simulation, outcomeApplier OutcomeApplier, baseDamageCalculator BaseDamageCalculator) SpellResultSlice {
	return spell.aoeIteration(sim, outcomeApplier, baseDamageCalculator, spell.CalcDamage)
}

func (spell *Spell) CalcCleaveDamage(sim *Simulation, firstTarget *Unit, maxTargets int32, baseDamage float64, outcomeApplier OutcomeApplier) SpellResultSlice {
	return spell.CalcCleaveDamageWithVariance(sim, firstTarget, maxTargets, outcomeApplier, fixedBaseDamageFactory(baseDamage))
}
func (spell *Spell) CalcCleaveDamageWithVariance(sim *Simulation, firstTarget *Unit, maxTargets int32, outcomeApplier OutcomeApplier, baseDamageCalculator BaseDamageCalculator) SpellResultSlice {
	return spell.cleaveIteration(sim, firstTarget, maxTargets, outcomeApplier, baseDamageCalculator, spell.CalcDamage)
}

func (spell *Spell) DealBatchedAoeDamage(sim *Simulation) {
	for _, result := range spell.resultSlice {
		spell.DealDamage(sim, result)
	}
}

func (spell *Spell) CalcPeriodicAoeDamage(sim *Simulation, baseDamage float64, outcomeApplier OutcomeApplier) SpellResultSlice {
	return spell.aoeIteration(sim, outcomeApplier, fixedBaseDamageFactory(baseDamage), spell.CalcPeriodicDamage)
}

func (spell *Spell) DealBatchedPeriodicDamage(sim *Simulation) {
	for _, result := range spell.resultSlice {
		spell.DealPeriodicDamage(sim, result)
	}
}

func (spell *Spell) CalcAndDealPeriodicDamage(sim *Simulation, target *Unit, baseDamage float64, outcomeApplier OutcomeApplier) *SpellResult {
	result := spell.CalcPeriodicDamage(sim, target, baseDamage, outcomeApplier)
	spell.DealPeriodicDamage(sim, result)
	return result
}
func (dot *Dot) CalcAndDealPeriodicSnapshotDamage(sim *Simulation, target *Unit, outcomeApplier OutcomeApplier) *SpellResult {
	result := dot.CalcSnapshotDamage(sim, target, outcomeApplier)
	dot.Spell.DealPeriodicDamage(sim, result)
	return result
}

func (dot *Dot) Snapshot(target *Unit, baseDamage float64) {
	dot.SnapshotBaseDamage = baseDamage
	if dot.BonusCoefficient > 0 {
		dot.SnapshotBaseDamage += dot.BonusCoefficient * dot.Spell.BonusDamage()
	}
	attackTable := dot.Spell.Unit.AttackTables[target.UnitIndex]
	dot.SnapshotCritChance = dot.Spell.SpellCritChance(target)
	dot.SnapshotAttackerMultiplier = dot.Spell.AttackerDamageMultiplier(attackTable, true) *
		dot.PeriodicDamageMultiplier
}

func (dot *Dot) SnapshotPhysical(target *Unit, baseDamage float64) {
	dot.SnapshotBaseDamage = baseDamage
	// At this time, not aware of any physical-scaling DoTs that need BonusCoefficient
	attackTable := dot.Spell.Unit.AttackTables[target.UnitIndex]
	dot.SnapshotCritChance = dot.Spell.PhysicalCritChance(attackTable)
	dot.SnapshotAttackerMultiplier = dot.Spell.AttackerDamageMultiplier(attackTable, true) *
		dot.PeriodicDamageMultiplier
}

func (spell *Spell) calcHealingInternal(sim *Simulation, target *Unit, baseHealing float64, casterMultiplier float64, outcomeApplier OutcomeApplier) *SpellResult {
	attackTable := spell.Unit.AttackTables[target.UnitIndex]

	result := spell.NewResult(target)
	result.Damage = baseHealing

	if sim.Log == nil {
		result.Damage *= casterMultiplier
		result.Damage = spell.applyTargetHealingModifiers(result.Damage, attackTable)
		outcomeApplier(sim, result, attackTable)
		spell.ApplyPostOutcomeHealingModifiers(sim, result)
	} else {
		result.Damage *= casterMultiplier
		afterCasterMods := result.Damage
		result.Damage = spell.applyTargetHealingModifiers(result.Damage, attackTable)
		afterTargetMods := result.Damage
		outcomeApplier(sim, result, attackTable)
		afterOutcome := result.Damage

		spell.ApplyPostOutcomeHealingModifiers(sim, result)
		afterPostOutcome := result.Damage

		spell.Unit.Log(
			sim,
			"%s %s [DEBUG] HealingPower: %0.01f, BaseHealing:%0.01f, AfterCasterMods:%0.01f, AfterTargetMods:%0.01f, AfterOutcome:%0.01f, AfterPostOutcome:%0.01f",
			target.LogLabel(), spell.ActionID, spell.HealingPower(target), baseHealing, afterCasterMods, afterTargetMods, afterOutcome, afterPostOutcome)
	}

	result.Threat = spell.ThreatFromDamage(sim, result.Outcome, result.Damage, attackTable)

	return result
}
func (spell *Spell) calcHealing(sim *Simulation, target *Unit, baseHealing float64, outcomeApplier OutcomeApplier, isPeriodic bool) *SpellResult {
	if spell.BonusCoefficient > 0 {
		baseHealing += spell.BonusCoefficient * spell.HealingPower(target)
	}
	return spell.calcHealingInternal(sim, target, baseHealing, spell.casterHealingMultiplier(isPeriodic), outcomeApplier)
}
func (spell *Spell) CalcHealing(sim *Simulation, target *Unit, baseHealing float64, outcomeApplier OutcomeApplier) *SpellResult {
	return spell.calcHealing(sim, target, baseHealing, outcomeApplier, false)
}
func (spell *Spell) CalcPeriodicHealing(sim *Simulation, target *Unit, baseHealing float64, outcomeApplier OutcomeApplier) *SpellResult {
	return spell.calcHealing(sim, target, baseHealing, outcomeApplier, true)
}
func (dot *Dot) CalcSnapshotHealing(sim *Simulation, target *Unit, outcomeApplier OutcomeApplier) *SpellResult {
	return dot.Spell.calcHealingInternal(sim, target, dot.SnapshotBaseDamage, dot.SnapshotAttackerMultiplier, outcomeApplier)
}

func (dot *Dot) SnapshotHeal(target *Unit, baseHealing float64) {
	dot.SnapshotBaseDamage = baseHealing
	if dot.BonusCoefficient > 0 {
		dot.SnapshotBaseDamage += dot.BonusCoefficient * dot.Spell.HealingPower(target)
	}
	dot.SnapshotCritChance = dot.Spell.SpellCritChance(target)
	dot.SnapshotAttackerMultiplier = dot.CasterPeriodicHealingMultiplier()
}

// Applies the fully computed spell result to the sim.
func (spell *Spell) dealHealingInternal(sim *Simulation, isPeriodic bool, result *SpellResult) {
	if result.DidCrit() {
		spell.SpellMetrics[result.Target.UnitIndex].TotalCritHealing += result.Damage
	}
	spell.SpellMetrics[result.Target.UnitIndex].TotalHealing += result.Damage
	spell.SpellMetrics[result.Target.UnitIndex].TotalThreat += result.Threat
	if result.Target.HasHealthBar() {
		result.Target.GainHealth(sim, result.Damage, spell.HealthMetrics(result.Target))
	}

	if sim.Log != nil && !spell.Flags.Matches(SpellFlagNoLogs) {
		if isPeriodic {
			spell.Unit.Log(sim, "%s %s tick %s. (Threat: %0.3f)", result.Target.LogLabel(), spell.ActionID, result.HealingString(), result.Threat)
		} else {
			spell.Unit.Log(sim, "%s %s %s. (Threat: %0.3f)", result.Target.LogLabel(), spell.ActionID, result.HealingString(), result.Threat)
		}
	}

	if isPeriodic {
		spell.Unit.OnPeriodicHealDealt(sim, spell, result)
		result.Target.OnPeriodicHealTaken(sim, spell, result)
	} else {
		spell.Unit.OnHealDealt(sim, spell, result)
		result.Target.OnHealTaken(sim, spell, result)
	}

	spell.DisposeResult(result)
}
func (spell *Spell) DealHealing(sim *Simulation, result *SpellResult) {
	spell.dealHealingInternal(sim, false, result)
}
func (spell *Spell) DealPeriodicHealing(sim *Simulation, result *SpellResult) {
	spell.dealHealingInternal(sim, true, result)
}

func (spell *Spell) CalcAndDealHealing(sim *Simulation, target *Unit, baseHealing float64, outcomeApplier OutcomeApplier) *SpellResult {
	result := spell.CalcHealing(sim, target, baseHealing, outcomeApplier)
	spell.DealHealing(sim, result)
	return result
}
func (spell *Spell) CalcAndDealPeriodicHealing(sim *Simulation, target *Unit, baseHealing float64, outcomeApplier OutcomeApplier) *SpellResult {
	result := spell.CalcPeriodicHealing(sim, target, baseHealing, outcomeApplier)
	spell.DealPeriodicHealing(sim, result)
	return result
}
func (dot *Dot) CalcAndDealPeriodicSnapshotHealing(sim *Simulation, target *Unit, outcomeApplier OutcomeApplier) *SpellResult {
	result := dot.CalcSnapshotHealing(sim, target, outcomeApplier)
	dot.Spell.DealPeriodicHealing(sim, result)
	return result
}

func (spell *Spell) WaitTravelTime(sim *Simulation, callback func(*Simulation)) {
	StartDelayedAction(sim, DelayedActionOptions{
		DoAt:     sim.CurrentTime + spell.TravelTime(),
		OnAction: callback,
	})
}

// Returns the combined attacker modifiers.
func (spell *Spell) AttackerDamageMultiplier(attackTable *AttackTable, isDot bool) float64 {
	damageMultiplierAdditive := TernaryFloat64(isDot && !spell.Flags.Matches(SpellFlagIgnoreAttackerModifiers),
		spell.DamageMultiplierAdditive+spell.Unit.PseudoStats.DotDamageMultiplierAdditive-1,
		spell.DamageMultiplierAdditive)

	return spell.attackerDamageMultiplierInternal(attackTable) *
		spell.DamageMultiplier *
		damageMultiplierAdditive
}
func (spell *Spell) attackerDamageMultiplierInternal(attackTable *AttackTable) float64 {
	if spell.Flags.Matches(SpellFlagIgnoreAttackerModifiers) {
		return 1
	}

	return spell.Unit.PseudoStats.DamageDealtMultiplier *
		spell.Unit.PseudoStats.SchoolDamageDealtMultiplier[spell.SchoolIndex] *
		attackTable.DamageDealtMultiplier
}

func (result *SpellResult) applyTargetModifiers(sim *Simulation, spell *Spell, attackTable *AttackTable, isPeriodic bool) {
	if spell.Flags.Matches(SpellFlagIgnoreTargetModifiers) {
		return
	}

	result.Damage *= spell.TargetDamageMultiplier(sim, attackTable, isPeriodic)
}
func (spell *Spell) TargetDamageMultiplier(sim *Simulation, attackTable *AttackTable, isPeriodic bool) float64 {
	if spell.Flags.Matches(SpellFlagIgnoreTargetModifiers) {
		return 1
	}

	multiplier := attackTable.Defender.PseudoStats.DamageTakenMultiplier *
		attackTable.Defender.PseudoStats.SchoolDamageTakenMultiplier[spell.SchoolIndex] *
		attackTable.DamageTakenMultiplier

	if spell.Flags.Matches(SpellFlagDisease) {
		multiplier *= attackTable.Defender.PseudoStats.DiseaseDamageTakenMultiplier
	}

	if isPeriodic && spell.SpellSchool.Matches(SpellSchoolPhysical) {
		multiplier *= attackTable.Defender.PseudoStats.PeriodicPhysicalDamageTakenMultiplier
	}

	if spell.Flags.Matches(SpellFlagRanged) {
		multiplier *= attackTable.RangedDamageTakenMultiplier
	}

	if attackTable.DamageDoneByCasterMultiplier != nil {
		multiplier *= attackTable.DamageDoneByCasterMultiplier(sim, spell, attackTable)
	}

	if attackTable.DamageDoneByCasterExtraMultiplier != nil {
		for i := range attackTable.DamageDoneByCasterExtraMultiplier {
			if attackTable.DamageDoneByCasterExtraMultiplier[i] != nil {
				multiplier *= attackTable.DamageDoneByCasterExtraMultiplier[i](sim, spell, attackTable)
			}
		}
	}

	return multiplier
}

func (spell *Spell) casterHealingMultiplier(isPeriodic bool) float64 {
	if spell.Flags.Matches(SpellFlagIgnoreAttackerModifiers) {
		return 1
	}

	multiplier := spell.DamageMultiplier * spell.DamageMultiplierAdditive * spell.Unit.PseudoStats.HealingDealtMultiplier

	if isPeriodic {
		multiplier *= spell.Unit.PseudoStats.PeriodicHealingDealtMultiplier
	}

	return multiplier
}
func (spell *Spell) CasterHealingMultiplier() float64 {
	return spell.casterHealingMultiplier(false)
}
func (dot *Dot) CasterPeriodicHealingMultiplier() float64 {
	return dot.Spell.casterHealingMultiplier(true) *
		dot.PeriodicDamageMultiplier
}
func (spell *Spell) applyTargetHealingModifiers(damage float64, attackTable *AttackTable) float64 {
	if spell.Flags.Matches(SpellFlagIgnoreTargetModifiers) {
		return damage
	}

	return damage *
		attackTable.Defender.PseudoStats.HealingTakenMultiplier *
		attackTable.HealingDealtMultiplier
}
