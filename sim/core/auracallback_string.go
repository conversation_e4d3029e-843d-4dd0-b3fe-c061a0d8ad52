// Code generated by "stringer -type=AuraCallback"; DO NOT EDIT.

package core

import "strconv"

func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[CallbackEmpty-0]
	_ = x[CallbackOnSpellHitDealt-2]
	_ = x[CallbackOnSpellHitTaken-4]
	_ = x[CallbackOnPeriodicDamageDealt-8]
	_ = x[CallbackOnHealDealt-16]
	_ = x[CallbackOnPeriodicHealDealt-32]
	_ = x[CallbackOnCastComplete-64]
	_ = x[CallbackOnApplyEffects-128]
	_ = x[CallbackLast-256]
}

const (
	_AuraCallback_name_0 = "CallbackEmpty"
	_AuraCallback_name_1 = "CallbackOnSpellHitDealt"
	_AuraCallback_name_2 = "CallbackOnSpellHitTaken"
	_AuraCallback_name_3 = "CallbackOnPeriodicDamageDealt"
	_AuraCallback_name_4 = "CallbackOnHealDealt"
	_AuraCallback_name_5 = "CallbackOnPeriodicHealDealt"
	_AuraCallback_name_6 = "CallbackOnCastComplete"
	_AuraCallback_name_7 = "CallbackOnApplyEffects"
	_AuraCallback_name_8 = "CalbackLast"
)

func (i AuraCallback) String() string {
	switch {
	case i == 0:
		return _AuraCallback_name_0
	case i == 2:
		return _AuraCallback_name_1
	case i == 4:
		return _AuraCallback_name_2
	case i == 8:
		return _AuraCallback_name_3
	case i == 16:
		return _AuraCallback_name_4
	case i == 32:
		return _AuraCallback_name_5
	case i == 64:
		return _AuraCallback_name_6
	case i == 128:
		return _AuraCallback_name_7
	case i == 256:
		return _AuraCallback_name_8
	default:
		return "AuraCallback(" + strconv.FormatInt(int64(i), 10) + ")"
	}
}
