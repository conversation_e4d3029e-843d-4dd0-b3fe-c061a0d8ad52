character_stats_results: {
 key: "TestWindwalker-CharacterStats-Default"
 value: {
  final_stats: 183.75
  final_stats: 19658.6775
  final_stats: 22349.8
  final_stats: 257.25
  final_stats: 271
  final_stats: 2554
  final_stats: 5831
  final_stats: 5520
  final_stats: 2550
  final_stats: 0
  final_stats: 0
  final_stats: 6303
  final_stats: 43616.2155
  final_stats: 0
  final_stats: 0
  final_stats: 0
  final_stats: 0
  final_stats: 18644
  final_stats: 0
  final_stats: 459300.2
  final_stats: 300000
  final_stats: 0
  final_stats: 7.51176
  final_stats: 15.01176
  final_stats: 37.80634
  final_stats: 16.56833
  final_stats: 0
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-AgilePrimalDiamond"
 value: {
  dps: 152187.00464
  tps: 142420.33898
  hps: 7488.92079
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-AlacrityofXuen-103989"
 value: {
  dps: 150701.91993
  tps: 141335.26601
  hps: 7060.31228
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-ArcaneBadgeoftheShieldwall-93347"
 value: {
  dps: 145937.60051
  tps: 136945.64641
  hps: 7153.31734
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-ArmorofSevenSacredSeals"
 value: {
  dps: 139673.10665
  tps: 129930.98618
  hps: 6831.79183
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-ArmoroftheRedCrane"
 value: {
  dps: 132363.9308
  tps: 123421.8599
  hps: 6965.45809
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-ArrowflightMedallion-93258"
 value: {
  dps: 151401.27801
  tps: 141644.63188
  hps: 7690.16036
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-AssuranceofConsequence-105472"
 value: {
  dps: 156941.6001
  tps: 147290.96307
  hps: 7225.69266
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-AusterePrimalDiamond"
 value: {
  dps: 148569.03483
  tps: 138890.54502
  hps: 7345.36654
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-BadJuju-96781"
 value: {
  dps: 155241.75817
  tps: 145068.186
  hps: 7802.82487
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-BadgeofKypariZar-84079"
 value: {
  dps: 146051.33506
  tps: 136878.68781
  hps: 7232.52833
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-BattlegearofSevenSacredSeals"
 value: {
  dps: 137984.41698
  tps: 128755.85597
  hps: 6917.17931
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-BattlegearoftheRedCrane"
 value: {
  dps: 138650.05509
  tps: 129638.26447
  hps: 6831.21931
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-BlossomofPureSnow-89081"
 value: {
  dps: 146159.77653
  tps: 136908.45693
  hps: 7316.46911
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-BraidofTenSongs-84072"
 value: {
  dps: 147248.72042
  tps: 138236.30245
  hps: 7154.59539
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Brawler'sStatue-87571"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-BreathoftheHydra-96827"
 value: {
  dps: 148511.10283
  tps: 139441.82367
  hps: 7108.90859
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-BroochofMunificentDeeds-87500"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-BrutalTalismanoftheShado-PanAssault-94508"
 value: {
  dps: 147080.49403
  tps: 137488.67772
  hps: 7446.99938
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-BurningPrimalDiamond"
 value: {
  dps: 150888.24394
  tps: 141199.37464
  hps: 7401.29352
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CapacitivePrimalDiamond"
 value: {
  dps: 149363.85002
  tps: 139671.2371
  hps: 7382.70895
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CarbonicCarbuncle-81138"
 value: {
  dps: 147663.9991
  tps: 138301.82414
  hps: 7454.96279
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Cha-Ye'sEssenceofBrilliance-96888"
 value: {
  dps: 147380.98453
  tps: 138133.69291
  hps: 7362.23101
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CharmofTenSongs-84071"
 value: {
  dps: 147188.74605
  tps: 138072.26345
  hps: 7210.42651
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CommunalIdolofDestruction-101168"
 value: {
  dps: 147123.1959
  tps: 138040.51452
  hps: 7157.67843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CommunalStoneofDestruction-101171"
 value: {
  dps: 145381.65543
  tps: 136335.95015
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CommunalStoneofWisdom-101183"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-ContemplationofChi-Ji-103688"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-ContemplationofChi-Ji-103988"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Coren'sColdChromiumCoaster-87574"
 value: {
  dps: 148752.29772
  tps: 139196.22346
  hps: 7503.29717
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CoreofDecency-87497"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CourageousPrimalDiamond"
 value: {
  dps: 148569.03483
  tps: 138890.54502
  hps: 7345.36654
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CraftedDreadfulGladiator'sBadgeofConquest-93419"
 value: {
  dps: 147348.7775
  tps: 137855.57674
  hps: 7400.24784
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CraftedDreadfulGladiator'sBadgeofDominance-93600"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CraftedDreadfulGladiator'sBadgeofVictory-93606"
 value: {
  dps: 144958.31416
  tps: 135735.92951
  hps: 7273.2631
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CraftedDreadfulGladiator'sEmblemofCruelty-93485"
 value: {
  dps: 145953.68341
  tps: 136821.76119
  hps: 7249.07763
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CraftedDreadfulGladiator'sEmblemofMeditation-93487"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CraftedDreadfulGladiator'sEmblemofTenacity-93486"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CraftedDreadfulGladiator'sInsigniaofConquest-93424"
 value: {
  dps: 149179.9194
  tps: 139654.91213
  hps: 7491.44444
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CraftedDreadfulGladiator'sInsigniaofDominance-93601"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CraftedDreadfulGladiator'sInsigniaofVictory-93611"
 value: {
  dps: 145456.35989
  tps: 136202.91175
  hps: 7331.44955
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CraftedMalevolentGladiator'sBadgeofConquest-98755"
 value: {
  dps: 147941.87083
  tps: 138370.44166
  hps: 7440.55737
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CraftedMalevolentGladiator'sBadgeofDominance-98910"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CraftedMalevolentGladiator'sBadgeofVictory-98912"
 value: {
  dps: 145182.13625
  tps: 135925.44834
  hps: 7292.48445
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CraftedMalevolentGladiator'sEmblemofCruelty-98811"
 value: {
  dps: 145747.95742
  tps: 136584.49722
  hps: 7246.21411
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CraftedMalevolentGladiator'sEmblemofMeditation-98813"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CraftedMalevolentGladiator'sEmblemofTenacity-98812"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CraftedMalevolentGladiator'sInsigniaofConquest-98760"
 value: {
  dps: 150134.16665
  tps: 140422.14448
  hps: 7593.05985
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CraftedMalevolentGladiator'sInsigniaofDominance-98911"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CraftedMalevolentGladiator'sInsigniaofVictory-98917"
 value: {
  dps: 145869.64006
  tps: 136557.5839
  hps: 7361.24853
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CurseofHubris-102307"
 value: {
  dps: 149210.14287
  tps: 139538.1286
  hps: 7435.75197
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CurseofHubris-104649"
 value: {
  dps: 149914.77571
  tps: 140157.24319
  hps: 7479.24602
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CurseofHubris-104898"
 value: {
  dps: 148602.70016
  tps: 138991.08697
  hps: 7417.60128
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CurseofHubris-105147"
 value: {
  dps: 148024.25612
  tps: 138482.25132
  hps: 7379.45683
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CurseofHubris-105396"
 value: {
  dps: 149452.63979
  tps: 139723.1739
  hps: 7456.50042
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CurseofHubris-105645"
 value: {
  dps: 150077.67314
  tps: 140284.48132
  hps: 7482.80054
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CutstitcherMedallion-93255"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Daelo'sFinalWords-87496"
 value: {
  dps: 145647.03331
  tps: 136173.25443
  hps: 7248.91561
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-DarkglowEmbroidery(Rank3)-4893"
 value: {
  dps: 150385.65563
  tps: 140862.51846
  hps: 7340.88147
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-DarkmistVortex-87172"
 value: {
  dps: 149563.6935
  tps: 140305.45923
  hps: 7179.13285
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-DeadeyeBadgeoftheShieldwall-93346"
 value: {
  dps: 148714.04976
  tps: 139339.39382
  hps: 7427.91561
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-DelicateVialoftheSanguinaire-96895"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-DestructivePrimalDiamond"
 value: {
  dps: 149430.36744
  tps: 139744.9113
  hps: 7390.19
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-DisciplineofXuen-103986"
 value: {
  dps: 155101.63963
  tps: 145331.25257
  hps: 7716.54568
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Dominator'sArcaneBadge-93342"
 value: {
  dps: 145937.60051
  tps: 136945.64641
  hps: 7153.31734
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Dominator'sDeadeyeBadge-93341"
 value: {
  dps: 148714.04976
  tps: 139339.39382
  hps: 7427.91561
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Dominator'sDurableBadge-93345"
 value: {
  dps: 145053.04974
  tps: 136003.17496
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Dominator'sKnightlyBadge-93344"
 value: {
  dps: 146392.23853
  tps: 137206.22519
  hps: 7290.71442
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Dominator'sMendingBadge-93343"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-DreadfulGladiator'sBadgeofConquest-84344"
 value: {
  dps: 147348.7775
  tps: 137855.57674
  hps: 7400.24784
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-DreadfulGladiator'sBadgeofDominance-84488"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-DreadfulGladiator'sBadgeofVictory-84490"
 value: {
  dps: 144958.31416
  tps: 135735.92951
  hps: 7273.2631
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-DreadfulGladiator'sEmblemofCruelty-84399"
 value: {
  dps: 145953.68341
  tps: 136821.76119
  hps: 7249.07763
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-DreadfulGladiator'sEmblemofMeditation-84401"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-DreadfulGladiator'sEmblemofTenacity-84400"
 value: {
  dps: 145020.05834
  tps: 135980.93479
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-DreadfulGladiator'sInsigniaofConquest-84349"
 value: {
  dps: 149647.17332
  tps: 140106.19779
  hps: 7542.72529
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-DreadfulGladiator'sInsigniaofDominance-84489"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-DreadfulGladiator'sInsigniaofVictory-84495"
 value: {
  dps: 145484.10861
  tps: 136219.2819
  hps: 7328.21991
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-DurableBadgeoftheShieldwall-93350"
 value: {
  dps: 145053.04974
  tps: 136003.17496
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-EffulgentPrimalDiamond"
 value: {
  dps: 148569.03483
  tps: 138890.54502
  hps: 7345.36654
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-EmberPrimalDiamond"
 value: {
  dps: 148569.03483
  tps: 138890.54502
  hps: 7345.36654
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-EmblemofKypariZar-84077"
 value: {
  dps: 147103.79774
  tps: 138033.86162
  hps: 7277.17994
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-EmblemoftheCatacombs-83733"
 value: {
  dps: 147094.04373
  tps: 137995.28283
  hps: 7155.53217
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-EmptyFruitBarrel-81133"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-EnchantWeapon-BloodyDancingSteel-5125"
 value: {
  dps: 152669.222
  tps: 142878.95963
  hps: 7526.03888
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-EnchantWeapon-Colossus-4445"
 value: {
  dps: 149859.41048
  tps: 140442.97185
  hps: 7301.30656
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-EnchantWeapon-JadeSpirit-4442"
 value: {
  dps: 149859.41048
  tps: 140442.97185
  hps: 7301.30656
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-EnchantWeapon-River'sSong-4446"
 value: {
  dps: 149860.4649
  tps: 140444.02627
  hps: 7301.30656
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-EnchantWeapon-SpiritofConquest-5124"
 value: {
  dps: 149859.73856
  tps: 140443.29993
  hps: 7301.30656
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-EnchantWeapon-Windsong-4441"
 value: {
  dps: 151526.64816
  tps: 142117.51202
  hps: 7362.98438
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-EnigmaticPrimalDiamond"
 value: {
  dps: 149430.36744
  tps: 139744.9113
  hps: 7390.19
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-EssenceofTerror-87175"
 value: {
  dps: 146062.14782
  tps: 136997.8421
  hps: 7091.74072
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-EternalPrimalDiamond"
 value: {
  dps: 148569.03483
  tps: 138890.54502
  hps: 7345.36654
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-EvilEyeofGalakras-105491"
 value: {
  dps: 145209.52137
  tps: 135972.17287
  hps: 7302.54454
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-FabledFeatherofJi-Kun-96842"
 value: {
  dps: 156669.84937
  tps: 146241.21296
  hps: 8154.84921
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-FearwurmBadge-84074"
 value: {
  dps: 147934.12508
  tps: 138708.07182
  hps: 7260.24739
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-FearwurmRelic-84070"
 value: {
  dps: 147163.69946
  tps: 137995.63062
  hps: 7194.22872
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-FelsoulIdolofDestruction-101263"
 value: {
  dps: 147744.19396
  tps: 138646.86435
  hps: 7190.85465
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-FelsoulStoneofDestruction-101266"
 value: {
  dps: 145634.43749
  tps: 136582.21632
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Fire-CharmArmor"
 value: {
  dps: 145339.12692
  tps: 135626.81934
  hps: 7595.40701
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Fire-CharmBattlegear"
 value: {
  dps: 151347.92741
  tps: 141791.78662
  hps: 7495.39219
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-FlashfrozenResinGlobule-100951"
 value: {
  dps: 144066.08367
  tps: 134929.44564
  hps: 7156.29911
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-FlashfrozenResinGlobule-81263"
 value: {
  dps: 144643.15128
  tps: 135508.12953
  hps: 7163.75272
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-FlashingSteelTalisman-81265"
 value: {
  dps: 149624.93
  tps: 139698.23662
  hps: 7458.69327
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-FleetPrimalDiamond"
 value: {
  dps: 149270.74282
  tps: 139589.70769
  hps: 7345.36654
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-ForlornPrimalDiamond"
 value: {
  dps: 148569.03483
  tps: 138890.54502
  hps: 7345.36654
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-FortitudeoftheZandalari-94516"
 value: {
  dps: 146067.33563
  tps: 137024.45714
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-FortitudeoftheZandalari-95677"
 value: {
  dps: 145671.02553
  tps: 136629.56798
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-FortitudeoftheZandalari-96049"
 value: {
  dps: 146202.62221
  tps: 137159.25866
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-FortitudeoftheZandalari-96421"
 value: {
  dps: 146369.74093
  tps: 137325.77818
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-FortitudeoftheZandalari-96793"
 value: {
  dps: 146520.94357
  tps: 137476.4387
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GazeoftheTwins-96915"
 value: {
  dps: 150130.28501
  tps: 140537.56233
  hps: 7553.53375
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Gerp'sPerfectArrow-87495"
 value: {
  dps: 148685.31678
  tps: 139052.08958
  hps: 7462.81489
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sBadgeofConquest-100195"
 value: {
  dps: 150170.59088
  tps: 140293.20513
  hps: 7567.48936
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sBadgeofConquest-100603"
 value: {
  dps: 150170.59088
  tps: 140293.20513
  hps: 7567.48936
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sBadgeofConquest-102856"
 value: {
  dps: 150170.59088
  tps: 140293.20513
  hps: 7567.48936
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sBadgeofConquest-103145"
 value: {
  dps: 150170.59088
  tps: 140293.20513
  hps: 7567.48936
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sBadgeofDominance-100490"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sBadgeofDominance-100576"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sBadgeofDominance-102830"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sBadgeofDominance-103308"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sBadgeofVictory-100500"
 value: {
  dps: 145958.53811
  tps: 136582.85786
  hps: 7359.16014
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sBadgeofVictory-100579"
 value: {
  dps: 145958.53811
  tps: 136582.85786
  hps: 7359.16014
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sBadgeofVictory-102833"
 value: {
  dps: 145958.53811
  tps: 136582.85786
  hps: 7359.16014
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sBadgeofVictory-103314"
 value: {
  dps: 145958.53811
  tps: 136582.85786
  hps: 7359.16014
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sEmblemofCruelty-100305"
 value: {
  dps: 146973.74911
  tps: 137764.73526
  hps: 7334.4025
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sEmblemofCruelty-100626"
 value: {
  dps: 146973.74911
  tps: 137764.73526
  hps: 7334.4025
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sEmblemofCruelty-102877"
 value: {
  dps: 146973.74911
  tps: 137764.73526
  hps: 7334.4025
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sEmblemofCruelty-103210"
 value: {
  dps: 146973.74911
  tps: 137764.73526
  hps: 7334.4025
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sEmblemofMeditation-100307"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sEmblemofMeditation-100559"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sEmblemofMeditation-102813"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sEmblemofMeditation-103212"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sEmblemofTenacity-100306"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sEmblemofTenacity-100652"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sEmblemofTenacity-102903"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sEmblemofTenacity-103211"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sInsigniaofConquest-103150"
 value: {
  dps: 153014.39489
  tps: 142992.04544
  hps: 7755.31888
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sInsigniaofDominance-103309"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sInsigniaofVictory-103319"
 value: {
  dps: 147026.98367
  tps: 137586.95604
  hps: 7483.53385
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Hawkmaster'sTalon-89082"
 value: {
  dps: 150983.45942
  tps: 141475.39752
  hps: 7386.8259
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Heart-LesionDefenderIdol-100999"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Heart-LesionDefenderStone-101002"
 value: {
  dps: 145706.1189
  tps: 136649.83151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Heart-LesionIdolofBattle-100991"
 value: {
  dps: 147727.24623
  tps: 138322.70109
  hps: 7450.36871
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Heart-LesionStoneofBattle-100990"
 value: {
  dps: 147058.86439
  tps: 137837.07995
  hps: 7322.87501
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-HeartofFire-81181"
 value: {
  dps: 145080.5394
  tps: 136041.199
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-HeartwarmerMedallion-93260"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-HelmbreakerMedallion-93261"
 value: {
  dps: 148217.95762
  tps: 138760.39108
  hps: 7494.69527
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Horridon'sLastGasp-96757"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-ImpassivePrimalDiamond"
 value: {
  dps: 149430.36744
  tps: 139744.9113
  hps: 7390.19
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-IndomitablePrimalDiamond"
 value: {
  dps: 148569.03483
  tps: 138890.54502
  hps: 7345.36654
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-InscribedBagofHydra-Spawn-96828"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-InsigniaofKypariZar-84078"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-IronBellyWok-89083"
 value: {
  dps: 147917.35168
  tps: 138679.41785
  hps: 7207.61732
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-IronProtectorTalisman-85181"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-JadeBanditFigurine-86043"
 value: {
  dps: 150983.45942
  tps: 141475.39752
  hps: 7386.8259
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-JadeBanditFigurine-86772"
 value: {
  dps: 150271.19756
  tps: 140757.91501
  hps: 7443.56791
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-JadeCharioteerFigurine-86042"
 value: {
  dps: 147917.35168
  tps: 138679.41785
  hps: 7207.61732
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-JadeCharioteerFigurine-86771"
 value: {
  dps: 147632.95469
  tps: 138342.96712
  hps: 7284.71995
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-JadeCourtesanFigurine-86045"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-JadeCourtesanFigurine-86774"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-JadeMagistrateFigurine-86044"
 value: {
  dps: 146159.77653
  tps: 136908.45693
  hps: 7316.46911
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-JadeMagistrateFigurine-86773"
 value: {
  dps: 145868.24088
  tps: 136665.21461
  hps: 7280.54865
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-JadeWarlordFigurine-86046"
 value: {
  dps: 145495.37517
  tps: 136440.35304
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-JadeWarlordFigurine-86775"
 value: {
  dps: 145293.82778
  tps: 136241.15105
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Ji-Kun'sRisingWinds-96843"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-KnightlyBadgeoftheShieldwall-93349"
 value: {
  dps: 146392.23853
  tps: 137206.22519
  hps: 7290.71442
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-KnotofTenSongs-84073"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Kor'kronBookofHurting-92785"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Lao-Chin'sLiquidCourage-89079"
 value: {
  dps: 145495.37517
  tps: 136440.35304
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-LeiShen'sFinalOrders-87072"
 value: {
  dps: 148448.97088
  tps: 139090.83314
  hps: 7361.91792
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-LessonsoftheDarkmaster-81268"
 value: {
  dps: 145276.11644
  tps: 135990.28181
  hps: 7298.47497
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-LightdrinkerIdolofRage-101200"
 value: {
  dps: 150753.01704
  tps: 141106.65414
  hps: 7524.35756
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-LightdrinkerStoneofRage-101203"
 value: {
  dps: 150612.35762
  tps: 141138.19147
  hps: 7499.37285
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-LightoftheCosmos-87065"
 value: {
  dps: 146121.30527
  tps: 137058.38669
  hps: 7154.33634
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-LightweaveEmbroidery(Rank3)-4892"
 value: {
  dps: 150385.65563
  tps: 140862.51846
  hps: 7340.88147
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-LordBlastington'sScopeofDoom-4699"
 value: {
  dps: 147123.049
  tps: 137882.6438
  hps: 7305.24137
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MalevolentGladiator'sBadgeofConquest-84934"
 value: {
  dps: 148241.64275
  tps: 138628.10117
  hps: 7458.36347
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MalevolentGladiator'sBadgeofConquest-91452"
 value: {
  dps: 147941.87083
  tps: 138370.44166
  hps: 7440.55737
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MalevolentGladiator'sBadgeofDominance-84940"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MalevolentGladiator'sBadgeofDominance-91753"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MalevolentGladiator'sBadgeofVictory-84942"
 value: {
  dps: 145281.00618
  tps: 136009.16534
  hps: 7300.97518
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MalevolentGladiator'sBadgeofVictory-91763"
 value: {
  dps: 145182.13625
  tps: 135925.44834
  hps: 7292.48445
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MalevolentGladiator'sEmblemofCruelty-84936"
 value: {
  dps: 145816.16535
  tps: 136650.01059
  hps: 7267.54786
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MalevolentGladiator'sEmblemofCruelty-91562"
 value: {
  dps: 145747.95742
  tps: 136584.49722
  hps: 7246.21411
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MalevolentGladiator'sEmblemofMeditation-84939"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MalevolentGladiator'sEmblemofMeditation-91564"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MalevolentGladiator'sEmblemofTenacity-84938"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MalevolentGladiator'sEmblemofTenacity-91563"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MalevolentGladiator'sInsigniaofConquest-91457"
 value: {
  dps: 149774.47791
  tps: 140052.99969
  hps: 7555.59055
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MalevolentGladiator'sInsigniaofDominance-91754"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MalevolentGladiator'sInsigniaofVictory-91768"
 value: {
  dps: 145848.91119
  tps: 136548.3724
  hps: 7356.00165
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MarkoftheCatacombs-83731"
 value: {
  dps: 146301.302
  tps: 137192.83082
  hps: 7240.77252
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MarkoftheHardenedGrunt-92783"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MedallionofMystifyingVapors-93257"
 value: {
  dps: 145614.53823
  tps: 136558.1294
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MedallionoftheCatacombs-83734"
 value: {
  dps: 144714.46983
  tps: 135676.44195
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MendingBadgeoftheShieldwall-93348"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MirrorScope-4700"
 value: {
  dps: 149859.73856
  tps: 140443.29993
  hps: 7301.30656
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MistdancerDefenderIdol-101089"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MistdancerDefenderStone-101087"
 value: {
  dps: 145276.26054
  tps: 136227.39541
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MistdancerIdolofRage-101113"
 value: {
  dps: 151028.26079
  tps: 141415.61831
  hps: 7560.22667
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MistdancerStoneofRage-101117"
 value: {
  dps: 150414.43981
  tps: 140946.12237
  hps: 7499.37285
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MistdancerStoneofWisdom-101107"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MithrilWristwatch-87572"
 value: {
  dps: 145644.24295
  tps: 136498.8006
  hps: 7243.24328
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MountainsageIdolofDestruction-101069"
 value: {
  dps: 147668.38451
  tps: 138551.49147
  hps: 7202.74831
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MountainsageStoneofDestruction-101072"
 value: {
  dps: 145768.03024
  tps: 136716.21494
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-OathswornDefenderIdol-101303"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-OathswornDefenderStone-101306"
 value: {
  dps: 145432.7309
  tps: 136387.18963
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-OathswornIdolofBattle-101295"
 value: {
  dps: 147750.34091
  tps: 138344.01096
  hps: 7438.73035
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-OathswornStoneofBattle-101294"
 value: {
  dps: 146959.15744
  tps: 137732.44475
  hps: 7322.87501
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-PhaseFingers-4697"
 value: {
  dps: 153194.34883
  tps: 143503.99139
  hps: 7615.79842
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-PouchofWhiteAsh-103639"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-PowerfulPrimalDiamond"
 value: {
  dps: 148569.03483
  tps: 138890.54502
  hps: 7345.36654
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-PriceofProgress-81266"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-PridefulGladiator'sBadgeofConquest-102659"
 value: {
  dps: 151965.26188
  tps: 141845.76476
  hps: 7688.93243
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-PridefulGladiator'sBadgeofConquest-103342"
 value: {
  dps: 151965.26188
  tps: 141845.76476
  hps: 7688.93243
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-PridefulGladiator'sBadgeofDominance-102633"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-PridefulGladiator'sBadgeofDominance-103505"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-PridefulGladiator'sBadgeofVictory-102636"
 value: {
  dps: 146622.72563
  tps: 137145.25116
  hps: 7416.19911
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-PridefulGladiator'sBadgeofVictory-103511"
 value: {
  dps: 146622.72563
  tps: 137145.25116
  hps: 7416.19911
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-PridefulGladiator'sEmblemofCruelty-102680"
 value: {
  dps: 147711.34017
  tps: 138430.70296
  hps: 7375.10072
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-PridefulGladiator'sEmblemofCruelty-103407"
 value: {
  dps: 147711.34017
  tps: 138430.70296
  hps: 7375.10072
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-PridefulGladiator'sEmblemofMeditation-102616"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-PridefulGladiator'sEmblemofMeditation-103409"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-PridefulGladiator'sEmblemofTenacity-102706"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-PridefulGladiator'sEmblemofTenacity-103408"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-PridefulGladiator'sInsigniaofConquest-103347"
 value: {
  dps: 156166.01344
  tps: 145817.39561
  hps: 8040.66518
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-PridefulGladiator'sInsigniaofDominance-103506"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-PridefulGladiator'sInsigniaofVictory-103516"
 value: {
  dps: 147948.081
  tps: 138381.40685
  hps: 7557.37393
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Primordius'TalismanofRage-96873"
 value: {
  dps: 150238.93923
  tps: 140703.75908
  hps: 7620.64806
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Qin-xi'sPolarizingSeal-87075"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-RelicofChi-Ji-79330"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-RelicofKypariZar-84075"
 value: {
  dps: 147099.74254
  tps: 137924.92748
  hps: 7191.94845
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-RelicofNiuzao-79329"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-RelicofXuen-79327"
 value: {
  dps: 146782.82652
  tps: 137402.65232
  hps: 7451.08474
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-RelicofYu'lon-79331"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Renataki'sSoulCharm-96741"
 value: {
  dps: 174141.82615
  tps: 161945.51674
  hps: 9027.22079
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-ResolveofNiuzao-103690"
 value: {
  dps: 145565.97948
  tps: 136524.89857
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-ResolveofNiuzao-103990"
 value: {
  dps: 146369.74093
  tps: 137325.77818
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-ReverberatingPrimalDiamond"
 value: {
  dps: 151249.30585
  tps: 141523.66261
  hps: 7433.77358
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-RevitalizingPrimalDiamond"
 value: {
  dps: 150888.24394
  tps: 141199.37464
  hps: 7401.29352
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SI:7Operative'sManual-92784"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-ScrollofReveredAncestors-89080"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SearingWords-81267"
 value: {
  dps: 151128.57869
  tps: 141209.62182
  hps: 7591.99237
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Shock-ChargerMedallion-93259"
 value: {
  dps: 146092.02419
  tps: 137029.42276
  hps: 7176.34665
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SigilofCompassion-83736"
 value: {
  dps: 144714.46983
  tps: 135676.44195
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SigilofDevotion-83740"
 value: {
  dps: 146764.2141
  tps: 137648.10153
  hps: 7240.77252
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SigilofFidelity-83737"
 value: {
  dps: 147104.72309
  tps: 137919.08407
  hps: 7229.70366
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SigilofGrace-83738"
 value: {
  dps: 147499.52937
  tps: 138443.9712
  hps: 7163.94802
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SigilofKypariZar-84076"
 value: {
  dps: 146775.01118
  tps: 137654.92439
  hps: 7240.77252
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SigilofPatience-83739"
 value: {
  dps: 144999.74414
  tps: 135957.74354
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SigiloftheCatacombs-83732"
 value: {
  dps: 146932.08633
  tps: 137783.00408
  hps: 7224.38075
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SinisterPrimalDiamond"
 value: {
  dps: 149363.85002
  tps: 139671.2371
  hps: 7382.70895
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SkullrenderMedallion-93256"
 value: {
  dps: 148217.95762
  tps: 138760.39108
  hps: 7494.69527
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SpiritsoftheSun-87163"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SpringrainIdolofDestruction-101023"
 value: {
  dps: 147325.7911
  tps: 138206.27719
  hps: 7138.36637
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SpringrainIdolofRage-101009"
 value: {
  dps: 150998.38758
  tps: 141356.13918
  hps: 7550.91461
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SpringrainStoneofDestruction-101026"
 value: {
  dps: 145843.1858
  tps: 136790.00664
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SpringrainStoneofRage-101012"
 value: {
  dps: 150442.1611
  tps: 140971.15297
  hps: 7499.37285
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SpringrainStoneofWisdom-101041"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Static-Caster'sMedallion-93254"
 value: {
  dps: 146092.02419
  tps: 137029.42276
  hps: 7176.34665
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SteadfastFootman'sMedallion-92782"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SteadfastTalismanoftheShado-PanAssault-94507"
 value: {
  dps: 146067.33563
  tps: 137024.45714
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-StreamtalkerIdolofDestruction-101222"
 value: {
  dps: 148007.33982
  tps: 138957.38915
  hps: 7053.01058
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-StreamtalkerIdolofRage-101217"
 value: {
  dps: 151380.52108
  tps: 141724.19714
  hps: 7552.48206
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-StreamtalkerStoneofDestruction-101225"
 value: {
  dps: 145846.62471
  tps: 136794.60301
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-StreamtalkerStoneofRage-101220"
 value: {
  dps: 150637.42381
  tps: 141169.7019
  hps: 7499.37285
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-StreamtalkerStoneofWisdom-101250"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-StuffofNightmares-87160"
 value: {
  dps: 145801.53729
  tps: 136759.6118
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SunsoulDefenderIdol-101160"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SunsoulDefenderStone-101163"
 value: {
  dps: 145689.04093
  tps: 136632.68012
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SunsoulIdolofBattle-101152"
 value: {
  dps: 147812.12613
  tps: 138403.2346
  hps: 7443.7217
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SunsoulStoneofBattle-101151"
 value: {
  dps: 147079.52129
  tps: 137852.91873
  hps: 7322.87501
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SunsoulStoneofWisdom-101138"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SymboloftheCatacombs-83735"
 value: {
  dps: 146263.29825
  tps: 137098.1706
  hps: 7212.67617
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SynapseSprings(MarkII)-4898"
 value: {
  dps: 154753.88214
  tps: 144780.38879
  hps: 7689.58879
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TalismanofBloodlust-96864"
 value: {
  dps: 155788.1385
  tps: 146026.06293
  hps: 7635.43233
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TerrorintheMists-87167"
 value: {
  dps: 153299.99483
  tps: 143317.16464
  hps: 7767.8092
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Thousand-YearPickledEgg-87573"
 value: {
  dps: 144620.2496
  tps: 135545.84638
  hps: 7092.2794
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TrailseekerIdolofRage-101054"
 value: {
  dps: 150659.20397
  tps: 140997.08308
  hps: 7546.14486
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TrailseekerStoneofRage-101057"
 value: {
  dps: 150333.71726
  tps: 140862.57377
  hps: 7499.37285
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sBadgeofConquest-100043"
 value: {
  dps: 148886.4405
  tps: 139204.80347
  hps: 7494.19416
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sBadgeofConquest-91099"
 value: {
  dps: 148886.4405
  tps: 139204.80347
  hps: 7494.19416
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sBadgeofConquest-94373"
 value: {
  dps: 148886.4405
  tps: 139204.80347
  hps: 7494.19416
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sBadgeofConquest-99772"
 value: {
  dps: 148886.4405
  tps: 139204.80347
  hps: 7494.19416
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sBadgeofDominance-100016"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sBadgeofDominance-91400"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sBadgeofDominance-94346"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sBadgeofDominance-99937"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sBadgeofVictory-100019"
 value: {
  dps: 145479.95915
  tps: 136177.62653
  hps: 7318.06083
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sBadgeofVictory-91410"
 value: {
  dps: 145479.95915
  tps: 136177.62653
  hps: 7318.06083
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sBadgeofVictory-94349"
 value: {
  dps: 145479.95915
  tps: 136177.62653
  hps: 7318.06083
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sBadgeofVictory-99943"
 value: {
  dps: 145479.95915
  tps: 136177.62653
  hps: 7318.06083
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sEmblemofCruelty-100066"
 value: {
  dps: 146156.76702
  tps: 136984.24969
  hps: 7293.1041
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sEmblemofCruelty-91209"
 value: {
  dps: 146156.76702
  tps: 136984.24969
  hps: 7293.1041
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sEmblemofCruelty-94396"
 value: {
  dps: 146156.76702
  tps: 136984.24969
  hps: 7293.1041
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sEmblemofCruelty-99838"
 value: {
  dps: 146156.76702
  tps: 136984.24969
  hps: 7293.1041
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sEmblemofMeditation-91211"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sEmblemofMeditation-94329"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sEmblemofMeditation-99840"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sEmblemofMeditation-99990"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sEmblemofTenacity-100092"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sEmblemofTenacity-91210"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sEmblemofTenacity-94422"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sEmblemofTenacity-99839"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sInsigniaofConquest-100026"
 value: {
  dps: 150983.96239
  tps: 141154.76334
  hps: 7628.45192
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sInsigniaofDominance-100152"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sInsigniaofVictory-100085"
 value: {
  dps: 146230.15233
  tps: 136861.32656
  hps: 7390.47984
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalPrimalDiamond"
 value: {
  dps: 148569.03483
  tps: 138890.54502
  hps: 7345.36654
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-UnerringVisionofLeiShen-96930"
 value: {
  dps: 145747.55519
  tps: 136584.29563
  hps: 7288.97204
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-VaporshieldMedallion-93262"
 value: {
  dps: 145614.53823
  tps: 136558.1294
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-VialofDragon'sBlood-87063"
 value: {
  dps: 145671.02553
  tps: 136629.56798
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-VialofIchorousBlood-100963"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-VialofIchorousBlood-81264"
 value: {
  dps: 143732.44842
  tps: 134697.94151
  hps: 7167.98843
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-ViciousTalismanoftheShado-PanAssault-94511"
 value: {
  dps: 154162.7354
  tps: 144000.37455
  hps: 7727.24183
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-VisionofthePredator-81192"
 value: {
  dps: 145038.28218
  tps: 135896.94018
  hps: 7237.50391
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-VolatileTalismanoftheShado-PanAssault-94510"
 value: {
  dps: 146017.92484
  tps: 136954.97352
  hps: 7110.21608
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-WindsweptPages-81125"
 value: {
  dps: 149861.63828
  tps: 140409.63494
  hps: 7371.16347
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-WoundripperMedallion-93253"
 value: {
  dps: 151401.27801
  tps: 141644.63188
  hps: 7690.16036
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Wushoolay'sFinalChoice-96785"
 value: {
  dps: 145256.8195
  tps: 136132.28612
  hps: 7126.22462
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-YaungolFireCarrier-86518"
 value: {
  dps: 152187.00464
  tps: 142420.33898
  hps: 7488.92079
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Yu'lon'sBite-103987"
 value: {
  dps: 146434.9631
  tps: 137170.22148
  hps: 7392.96372
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-ZenAlchemistStone-75274"
 value: {
  dps: 151543.62122
  tps: 141857.32483
  hps: 7640.13398
 }
}
dps_results: {
 key: "TestWindwalker-Average-Default"
 value: {
  dps: 154286.71173
  tps: 144127.69036
  hps: 7787.62559
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_bis_2h-Basic-default-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 1.71723743537e+06
  tps: 422783.12798
  hps: 8128.9221
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_bis_2h-Basic-default-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 147309.52215
  tps: 136789.52424
  hps: 7459.71776
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_bis_2h-Basic-default-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 187352.33427
  tps: 155234.14753
  hps: 8642.29547
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_bis_2h-Basic-default-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 1.37728444007e+06
  tps: 330608.922
  hps: 6737.21429
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_bis_2h-Basic-default-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 112509.98149
  tps: 105572.64183
  hps: 6362.79959
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_bis_2h-Basic-default-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 127394.79984
  tps: 109665.91601
  hps: 8130.73726
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_bis_dw-Basic-default-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 1.73168946196e+06
  tps: 426025.83346
  hps: 8438.09646
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_bis_dw-Basic-default-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 154009.5375
  tps: 143903.68874
  hps: 7896.76241
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_bis_dw-Basic-default-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 196898.3329
  tps: 166447.4279
  hps: 8977.10539
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_bis_dw-Basic-default-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 1.39011753285e+06
  tps: 333176.40188
  hps: 6958.49928
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_bis_dw-Basic-default-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 117128.98348
  tps: 110375.47131
  hps: 6647.48988
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_bis_dw-Basic-default-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 132865.91218
  tps: 115580.92136
  hps: 8284.14404
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_prebis_2h-Basic-default-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 1.18809270471e+06
  tps: 284522.85821
  hps: 6748.98609
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_prebis_2h-Basic-default-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 101027.11623
  tps: 92389.79428
  hps: 6094.10755
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_prebis_2h-Basic-default-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 139471.57541
  tps: 113209.22783
  hps: 7261.79299
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_prebis_2h-Basic-default-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 947204.24827
  tps: 221990.0986
  hps: 5445.23933
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_prebis_2h-Basic-default-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 76302.41441
  tps: 70718.99233
  hps: 5069.5672
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_prebis_2h-Basic-default-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 92047.56517
  tps: 77623.33863
  hps: 6443.8022
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_prebis_dw-Basic-default-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 1.21094829828e+06
  tps: 291362.0493
  hps: 6656.98908
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_prebis_dw-Basic-default-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 107313.16938
  tps: 99118.90914
  hps: 6326.26857
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_prebis_dw-Basic-default-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 145675.39401
  tps: 120655.82725
  hps: 7145.11232
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_prebis_dw-Basic-default-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 966679.02386
  tps: 226599.49044
  hps: 5473.89131
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_prebis_dw-Basic-default-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 79850.11299
  tps: 74492.48065
  hps: 5240.87295
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_prebis_dw-Basic-default-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 94956.46319
  tps: 81024.43022
  hps: 6746.35377
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_bis_2h-Basic-default-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 1.68942258848e+06
  tps: 423535.90753
  hps: 8012.71292
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_bis_2h-Basic-default-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 147055.7489
  tps: 136842.89441
  hps: 7343.09043
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_bis_2h-Basic-default-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 188056.72835
  tps: 157362.39796
  hps: 8661.16874
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_bis_2h-Basic-default-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 1.3557970079e+06
  tps: 331530.08403
  hps: 6598.01794
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_bis_2h-Basic-default-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 113030.37381
  tps: 106298.31618
  hps: 6215.51442
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_bis_2h-Basic-default-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 127110.5009
  tps: 109993.0643
  hps: 7008.60912
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_bis_dw-Basic-default-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 1.71559937233e+06
  tps: 430328.66995
  hps: 8289.21314
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_bis_dw-Basic-default-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 153877.09406
  tps: 143849.20208
  hps: 7579.2441
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_bis_dw-Basic-default-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 197636.53207
  tps: 167462.18329
  hps: 8398.6486
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_bis_dw-Basic-default-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 1.38002115931e+06
  tps: 337032.37591
  hps: 6911.34848
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_bis_dw-Basic-default-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 117942.60173
  tps: 111434.53121
  hps: 6497.33791
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_bis_dw-Basic-default-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 130938.97588
  tps: 114420.19319
  hps: 7335.18177
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_prebis_2h-Basic-default-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 1.16989525448e+06
  tps: 285112.1916
  hps: 6499.62979
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_prebis_2h-Basic-default-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 101509.5039
  tps: 93130.49737
  hps: 6027.44683
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_prebis_2h-Basic-default-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 138795.89756
  tps: 113043.03286
  hps: 7050.14799
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_prebis_2h-Basic-default-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 935251.05438
  tps: 222740.7508
  hps: 5399.81658
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_prebis_2h-Basic-default-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 76597.38596
  tps: 71157.0746
  hps: 4984.70623
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_prebis_2h-Basic-default-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 91199.10623
  tps: 77445.21681
  hps: 6216.11492
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_prebis_dw-Basic-default-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 1.19835102367e+06
  tps: 293114.91841
  hps: 6665.81181
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_prebis_dw-Basic-default-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 106734.08094
  tps: 98600.97189
  hps: 6166.98118
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_prebis_dw-Basic-default-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 145515.7334
  tps: 120432.96961
  hps: 7098.82677
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_prebis_dw-Basic-default-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 953066.1833
  tps: 227986.13859
  hps: 5281.22482
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_prebis_dw-Basic-default-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 79324.53434
  tps: 74039.32903
  hps: 5100.1338
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_prebis_dw-Basic-default-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 94646.46808
  tps: 81224.96228
  hps: 6229.00632
 }
}
dps_results: {
 key: "TestWindwalker-SwitchInFrontOfTarget-Default"
 value: {
  dps: 132779.4685
  tps: 123071.33595
  hps: 7584.20883
 }
}
