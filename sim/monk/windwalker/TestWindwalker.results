character_stats_results: {
 key: "TestWindwalker-CharacterStats-Default"
 value: {
  final_stats: 183.75
  final_stats: 20037.9375
  final_stats: 22349.8
  final_stats: 257.25
  final_stats: 271
  final_stats: 2554
  final_stats: 5669
  final_stats: 5924
  final_stats: 2225
  final_stats: 18537.18167
  final_stats: 2e-05
  final_stats: 6386
  final_stats: 44450.5875
  final_stats: 0
  final_stats: 0
  final_stats: 0
  final_stats: 0
  final_stats: 18644
  final_stats: 0
  final_stats: 459300.2
  final_stats: 300000
  final_stats: 0
  final_stats: 7.51176
  final_stats: 14.05588
  final_stats: 37.83745
  final_stats: 16.29833
  final_stats: 0
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-AgilePrimalDiamond"
 value: {
  dps: 158960.98038
  tps: 148646.44582
  hps: 8091.89449
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-AlacrityofXuen-103989"
 value: {
  dps: 154653.90132
  tps: 144596.03089
  hps: 7996.22245
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-ArcaneBadgeoftheShieldwall-93347"
 value: {
  dps: 149965.29599
  tps: 140345.4999
  hps: 7680.78235
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-ArmorofSevenSacredSeals"
 value: {
  dps: 147198.46073
  tps: 136710.96247
  hps: 7374.58481
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-ArmoroftheRedCrane"
 value: {
  dps: 138722.78083
  tps: 129209.49548
  hps: 7237.12912
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-ArrowflightMedallion-93258"
 value: {
  dps: 155191.40308
  tps: 144850.27416
  hps: 8160.42327
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-AssuranceofConsequence-105472"
 value: {
  dps: 161810.36144
  tps: 151482.45693
  hps: 7904.17716
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-AusterePrimalDiamond"
 value: {
  dps: 155432.53567
  tps: 145222.98962
  hps: 7949.73784
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-BadJuju-96781"
 value: {
  dps: 160702.63774
  tps: 149803.77134
  hps: 8285.64677
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-BadgeofKypariZar-84079"
 value: {
  dps: 150563.19725
  tps: 140843.15886
  hps: 7669.6304
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-BattlegearofSevenSacredSeals"
 value: {
  dps: 142740.32315
  tps: 132857.19386
  hps: 7447.3128
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-BattlegearoftheRedCrane"
 value: {
  dps: 144866.41626
  tps: 135352.0658
  hps: 7254.36922
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-BlossomofPureSnow-89081"
 value: {
  dps: 149938.35325
  tps: 140134.61319
  hps: 7736.15328
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-BraidofTenSongs-84072"
 value: {
  dps: 150904.11043
  tps: 141327.09792
  hps: 7651.94013
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Brawler'sStatue-87571"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-BreathoftheHydra-96827"
 value: {
  dps: 151980.85086
  tps: 142171.3269
  hps: 7692.81631
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-BroochofMunificentDeeds-87500"
 value: {
  dps: 148041.56384
  tps: 138461.00722
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-BrutalTalismanoftheShado-PanAssault-94508"
 value: {
  dps: 152912.21866
  tps: 142604.03482
  hps: 7931.22433
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-BurningPrimalDiamond"
 value: {
  dps: 157985.29313
  tps: 147760.8775
  hps: 8016.5674
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CapacitivePrimalDiamond"
 value: {
  dps: 156154.34334
  tps: 145919.04913
  hps: 7968.25008
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CarbonicCarbuncle-81138"
 value: {
  dps: 151795.19392
  tps: 141839.3722
  hps: 7841.98379
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Cha-Ye'sEssenceofBrilliance-96888"
 value: {
  dps: 151496.08637
  tps: 141664.33487
  hps: 7790.72071
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CharmofTenSongs-84071"
 value: {
  dps: 150164.00441
  tps: 140496.68224
  hps: 7729.36642
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CommunalIdolofDestruction-101168"
 value: {
  dps: 151764.81375
  tps: 142068.38669
  hps: 7697.8452
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CommunalStoneofDestruction-101171"
 value: {
  dps: 150152.59027
  tps: 140538.58054
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CommunalStoneofWisdom-101183"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-ContemplationofChi-Ji-103688"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-ContemplationofChi-Ji-103988"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Coren'sColdChromiumCoaster-87574"
 value: {
  dps: 152947.21498
  tps: 142773.45246
  hps: 7970.0077
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CoreofDecency-87497"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CourageousPrimalDiamond"
 value: {
  dps: 155428.24416
  tps: 145218.69811
  hps: 7949.73784
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CraftedDreadfulGladiator'sBadgeofConquest-93419"
 value: {
  dps: 151518.66274
  tps: 141417.8375
  hps: 7861.94872
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CraftedDreadfulGladiator'sBadgeofDominance-93600"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CraftedDreadfulGladiator'sBadgeofVictory-93606"
 value: {
  dps: 149334.76817
  tps: 139553.81446
  hps: 7720.04098
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CraftedDreadfulGladiator'sEmblemofCruelty-93485"
 value: {
  dps: 149492.27864
  tps: 139793.08135
  hps: 7668.9018
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CraftedDreadfulGladiator'sEmblemofMeditation-93487"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CraftedDreadfulGladiator'sEmblemofTenacity-93486"
 value: {
  dps: 148043.29422
  tps: 138462.73761
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CraftedDreadfulGladiator'sInsigniaofConquest-93424"
 value: {
  dps: 154592.18778
  tps: 144342.37207
  hps: 7993.71258
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CraftedDreadfulGladiator'sInsigniaofDominance-93601"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CraftedDreadfulGladiator'sInsigniaofVictory-93611"
 value: {
  dps: 149892.42342
  tps: 140065.26924
  hps: 7766.58026
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CraftedMalevolentGladiator'sBadgeofConquest-98755"
 value: {
  dps: 152112.23213
  tps: 141927.93252
  hps: 7911.41312
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CraftedMalevolentGladiator'sBadgeofDominance-98910"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CraftedMalevolentGladiator'sBadgeofVictory-98912"
 value: {
  dps: 149572.91303
  tps: 139755.37023
  hps: 7743.59546
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CraftedMalevolentGladiator'sEmblemofCruelty-98811"
 value: {
  dps: 149783.89885
  tps: 140060.98244
  hps: 7685.27764
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CraftedMalevolentGladiator'sEmblemofMeditation-98813"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CraftedMalevolentGladiator'sEmblemofTenacity-98812"
 value: {
  dps: 148045.62823
  tps: 138465.07162
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CraftedMalevolentGladiator'sInsigniaofConquest-98760"
 value: {
  dps: 155750.82734
  tps: 145415.26906
  hps: 8093.28877
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CraftedMalevolentGladiator'sInsigniaofDominance-98911"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CraftedMalevolentGladiator'sInsigniaofVictory-98917"
 value: {
  dps: 150267.83353
  tps: 140392.70248
  hps: 7799.97506
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CurseofHubris-102307"
 value: {
  dps: 152393.30348
  tps: 142235.63919
  hps: 7838.77485
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CurseofHubris-104649"
 value: {
  dps: 152850.19837
  tps: 142607.07815
  hps: 7860.394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CurseofHubris-104898"
 value: {
  dps: 151867.99107
  tps: 141780.38726
  hps: 7820.78205
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CurseofHubris-105147"
 value: {
  dps: 151588.73736
  tps: 141532.37828
  hps: 7782.75433
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CurseofHubris-105396"
 value: {
  dps: 152608.69152
  tps: 142401.12542
  hps: 7847.45583
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CurseofHubris-105645"
 value: {
  dps: 153025.30935
  tps: 142754.92837
  hps: 7868.27204
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CutstitcherMedallion-93255"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Daelo'sFinalWords-87496"
 value: {
  dps: 150260.12397
  tps: 140204.6347
  hps: 7739.79615
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-DarkglowEmbroidery(Rank3)-4893"
 value: {
  dps: 156834.67803
  tps: 146782.1879
  hps: 7938.60817
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-DarkmistVortex-87172"
 value: {
  dps: 153910.25552
  tps: 143889.87888
  hps: 7909.82567
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-DeadeyeBadgeoftheShieldwall-93346"
 value: {
  dps: 153288.53958
  tps: 143355.20861
  hps: 7873.03111
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-DelicateVialoftheSanguinaire-96895"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-DestructivePrimalDiamond"
 value: {
  dps: 156403.09179
  tps: 146154.1154
  hps: 7972.62121
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-DisciplineofXuen-103986"
 value: {
  dps: 159776.56443
  tps: 149400.23699
  hps: 8168.62271
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Dominator'sArcaneBadge-93342"
 value: {
  dps: 149965.29599
  tps: 140345.4999
  hps: 7680.78235
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Dominator'sDeadeyeBadge-93341"
 value: {
  dps: 153288.53958
  tps: 143355.20861
  hps: 7873.03111
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Dominator'sDurableBadge-93345"
 value: {
  dps: 149594.89419
  tps: 139999.13923
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Dominator'sKnightlyBadge-93344"
 value: {
  dps: 150988.12331
  tps: 141250.25417
  hps: 7725.31831
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Dominator'sMendingBadge-93343"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-DreadfulGladiator'sBadgeofConquest-84344"
 value: {
  dps: 151518.66274
  tps: 141417.8375
  hps: 7861.94872
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-DreadfulGladiator'sBadgeofDominance-84488"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-DreadfulGladiator'sBadgeofVictory-84490"
 value: {
  dps: 149334.76817
  tps: 139553.81446
  hps: 7720.04098
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-DreadfulGladiator'sEmblemofCruelty-84399"
 value: {
  dps: 149492.27864
  tps: 139793.08135
  hps: 7668.9018
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-DreadfulGladiator'sEmblemofMeditation-84401"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-DreadfulGladiator'sEmblemofTenacity-84400"
 value: {
  dps: 149400.21695
  tps: 139815.09462
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-DreadfulGladiator'sInsigniaofConquest-84349"
 value: {
  dps: 155006.53004
  tps: 144755.61762
  hps: 8032.14595
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-DreadfulGladiator'sInsigniaofDominance-84489"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-DreadfulGladiator'sInsigniaofVictory-84495"
 value: {
  dps: 149854.96389
  tps: 140013.71092
  hps: 7761.19852
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-DurableBadgeoftheShieldwall-93350"
 value: {
  dps: 149594.89419
  tps: 139999.13923
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-EffulgentPrimalDiamond"
 value: {
  dps: 155432.53567
  tps: 145222.98962
  hps: 7949.73784
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-EmberPrimalDiamond"
 value: {
  dps: 155428.24416
  tps: 145218.69811
  hps: 7949.73784
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-EmblemofKypariZar-84077"
 value: {
  dps: 150598.59261
  tps: 140928.7478
  hps: 7721.10875
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-EmblemoftheCatacombs-83733"
 value: {
  dps: 150705.97637
  tps: 141163.71712
  hps: 7638.67996
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-EmptyFruitBarrel-81133"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-EnchantWeapon-BloodyDancingSteel-5125"
 value: {
  dps: 158964.22857
  tps: 148686.58259
  hps: 8100.36968
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-EnchantWeapon-Colossus-4445"
 value: {
  dps: 155313.88872
  tps: 145427.39444
  hps: 7866.03458
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-EnchantWeapon-JadeSpirit-4442"
 value: {
  dps: 155313.88872
  tps: 145427.39444
  hps: 7866.03458
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-EnchantWeapon-River'sSong-4446"
 value: {
  dps: 155313.88872
  tps: 145427.39444
  hps: 7866.03458
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-EnchantWeapon-SpiritofConquest-5124"
 value: {
  dps: 155324.35283
  tps: 145437.85856
  hps: 7866.03458
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-EnchantWeapon-Windsong-4441"
 value: {
  dps: 156450.16838
  tps: 146300.39497
  hps: 7922.04978
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-EnigmaticPrimalDiamond"
 value: {
  dps: 156403.09179
  tps: 146154.1154
  hps: 7972.62121
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-EssenceofTerror-87175"
 value: {
  dps: 150837.72879
  tps: 141114.87688
  hps: 7645.06892
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-EternalPrimalDiamond"
 value: {
  dps: 155428.24416
  tps: 145218.69811
  hps: 7949.73784
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-EvilEyeofGalakras-105491"
 value: {
  dps: 149553.6443
  tps: 139745.76537
  hps: 7720.32374
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-FabledFeatherofJi-Kun-96842"
 value: {
  dps: 160589.51287
  tps: 149483.00589
  hps: 8632.85618
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-FearwurmBadge-84074"
 value: {
  dps: 150952.24022
  tps: 141258.72614
  hps: 7718.69449
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-FearwurmRelic-84070"
 value: {
  dps: 150724.55672
  tps: 140971.98837
  hps: 7734.76744
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-FelsoulIdolofDestruction-101263"
 value: {
  dps: 151591.65796
  tps: 141918.44988
  hps: 7683.95891
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-FelsoulStoneofDestruction-101266"
 value: {
  dps: 150458.02228
  tps: 140842.94346
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Fen-Yu,FuryofXuen-102248"
 value: {
  dps: 174476.59825
  tps: 164021.93974
  hps: 8344.37272
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Fire-CharmArmor"
 value: {
  dps: 151683.35616
  tps: 141152.78874
  hps: 7957.87259
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Fire-CharmBattlegear"
 value: {
  dps: 157706.31832
  tps: 147340.11765
  hps: 7932.35922
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-FlashfrozenResinGlobule-100951"
 value: {
  dps: 148719.70546
  tps: 139025.91536
  hps: 7612.56387
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-FlashfrozenResinGlobule-81263"
 value: {
  dps: 149064.53168
  tps: 139348.74997
  hps: 7627.45279
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-FlashingSteelTalisman-81265"
 value: {
  dps: 153455.69709
  tps: 142878.82411
  hps: 7896.48894
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-FleetPrimalDiamond"
 value: {
  dps: 156215.10117
  tps: 146002.61748
  hps: 7949.73784
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-ForlornPrimalDiamond"
 value: {
  dps: 155428.24416
  tps: 145218.69811
  hps: 7949.73784
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-FortitudeoftheZandalari-94516"
 value: {
  dps: 150514.3108
  tps: 140925.47496
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-FortitudeoftheZandalari-95677"
 value: {
  dps: 150092.71602
  tps: 140505.28544
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-FortitudeoftheZandalari-96049"
 value: {
  dps: 150658.2287
  tps: 141068.91314
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-FortitudeoftheZandalari-96421"
 value: {
  dps: 150836.00964
  tps: 141246.1015
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-FortitudeoftheZandalari-96793"
 value: {
  dps: 150996.85905
  tps: 141406.41477
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GazeoftheTwins-96915"
 value: {
  dps: 154563.17232
  tps: 144357.10649
  hps: 8137.71317
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Gerp'sPerfectArrow-87495"
 value: {
  dps: 153134.6969
  tps: 142890.626
  hps: 7912.52263
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Gong-Lu,StrengthofXuen-102249"
 value: {
  dps: 170137.27536
  tps: 160025.58387
  hps: 8007.60308
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sBadgeofConquest-100195"
 value: {
  dps: 154344.52205
  tps: 143845.48286
  hps: 8082.99695
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sBadgeofConquest-100603"
 value: {
  dps: 154344.52205
  tps: 143845.48286
  hps: 8082.99695
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sBadgeofConquest-102856"
 value: {
  dps: 154344.52205
  tps: 143845.48286
  hps: 8082.99695
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sBadgeofConquest-103145"
 value: {
  dps: 154344.52205
  tps: 143845.48286
  hps: 8082.99695
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sBadgeofDominance-100490"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sBadgeofDominance-100576"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sBadgeofDominance-102830"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sBadgeofDominance-103308"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sBadgeofVictory-100500"
 value: {
  dps: 150398.99817
  tps: 140454.53391
  hps: 7825.30204
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sBadgeofVictory-100579"
 value: {
  dps: 150398.99817
  tps: 140454.53391
  hps: 7825.30204
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sBadgeofVictory-102833"
 value: {
  dps: 150398.99817
  tps: 140454.53391
  hps: 7825.30204
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sBadgeofVictory-103314"
 value: {
  dps: 150398.99817
  tps: 140454.53391
  hps: 7825.30204
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sEmblemofCruelty-100305"
 value: {
  dps: 150897.19617
  tps: 141099.60386
  hps: 7759.8931
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sEmblemofCruelty-100626"
 value: {
  dps: 150897.19617
  tps: 141099.60386
  hps: 7759.8931
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sEmblemofCruelty-102877"
 value: {
  dps: 150897.19617
  tps: 141099.60386
  hps: 7759.8931
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sEmblemofCruelty-103210"
 value: {
  dps: 150897.19617
  tps: 141099.60386
  hps: 7759.8931
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sEmblemofMeditation-100307"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sEmblemofMeditation-100559"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sEmblemofMeditation-102813"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sEmblemofMeditation-103212"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sEmblemofTenacity-100306"
 value: {
  dps: 148053.7436
  tps: 138473.18699
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sEmblemofTenacity-100652"
 value: {
  dps: 148053.7436
  tps: 138473.18699
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sEmblemofTenacity-102903"
 value: {
  dps: 148053.7436
  tps: 138473.18699
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sEmblemofTenacity-103211"
 value: {
  dps: 148053.7436
  tps: 138473.18699
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sInsigniaofConquest-103150"
 value: {
  dps: 159972.64826
  tps: 149200.11865
  hps: 8289.35961
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sInsigniaofDominance-103309"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sInsigniaofVictory-103319"
 value: {
  dps: 151430.74616
  tps: 141422.92037
  hps: 7904.02217
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Hawkmaster'sTalon-89082"
 value: {
  dps: 154797.65438
  tps: 144603.2209
  hps: 8011.22342
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Heart-LesionDefenderIdol-100999"
 value: {
  dps: 148050.99377
  tps: 138470.43715
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Heart-LesionDefenderStone-101002"
 value: {
  dps: 150451.32915
  tps: 140840.05539
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Heart-LesionIdolofBattle-100991"
 value: {
  dps: 151923.08217
  tps: 141927.94503
  hps: 7867.38362
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Heart-LesionStoneofBattle-100990"
 value: {
  dps: 152206.65598
  tps: 142416.3956
  hps: 7760.50779
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-HeartofFire-81181"
 value: {
  dps: 149464.55671
  tps: 139879.21993
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-HeartwarmerMedallion-93260"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-HelmbreakerMedallion-93261"
 value: {
  dps: 152025.62424
  tps: 141993.68983
  hps: 7945.90088
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Horridon'sLastGasp-96757"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-ImpassivePrimalDiamond"
 value: {
  dps: 156403.09179
  tps: 146154.1154
  hps: 7972.62121
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-IndomitablePrimalDiamond"
 value: {
  dps: 155432.53567
  tps: 145222.98962
  hps: 7949.73784
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-InscribedBagofHydra-Spawn-96828"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-InsigniaofKypariZar-84078"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-IronBellyWok-89083"
 value: {
  dps: 151729.74138
  tps: 141841.23614
  hps: 7812.84471
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-IronProtectorTalisman-85181"
 value: {
  dps: 148045.56116
  tps: 138465.00455
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-JadeBanditFigurine-86043"
 value: {
  dps: 154797.65438
  tps: 144603.2209
  hps: 8011.22342
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-JadeBanditFigurine-86772"
 value: {
  dps: 153415.78064
  tps: 143470.9182
  hps: 7920.9145
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-JadeCharioteerFigurine-86042"
 value: {
  dps: 151729.74138
  tps: 141841.23614
  hps: 7812.84471
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-JadeCharioteerFigurine-86771"
 value: {
  dps: 150463.19331
  tps: 140769.96101
  hps: 7746.19017
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-JadeCourtesanFigurine-86045"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-JadeCourtesanFigurine-86774"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-JadeMagistrateFigurine-86044"
 value: {
  dps: 149938.35325
  tps: 140134.61319
  hps: 7736.15328
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-JadeMagistrateFigurine-86773"
 value: {
  dps: 149672.67903
  tps: 139886.61208
  hps: 7720.04741
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-JadeWarlordFigurine-86046"
 value: {
  dps: 150140.60755
  tps: 140539.76202
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-JadeWarlordFigurine-86775"
 value: {
  dps: 149899.3649
  tps: 140300.83891
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Ji-Kun'sRisingWinds-96843"
 value: {
  dps: 149642.88049
  tps: 139960.08428
  hps: 7674.33533
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-KnightlyBadgeoftheShieldwall-93349"
 value: {
  dps: 150988.12331
  tps: 141250.25417
  hps: 7725.31831
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-KnotofTenSongs-84073"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Kor'kronBookofHurting-92785"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Lao-Chin'sLiquidCourage-89079"
 value: {
  dps: 150140.60755
  tps: 140539.76202
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-LeiShen'sFinalOrders-87072"
 value: {
  dps: 152987.30053
  tps: 142948.11516
  hps: 7844.20868
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-LessonsoftheDarkmaster-81268"
 value: {
  dps: 151398.60536
  tps: 141435.36087
  hps: 7826.65638
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-LightdrinkerIdolofRage-101200"
 value: {
  dps: 156229.98506
  tps: 145939.91717
  hps: 8034.1167
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-LightdrinkerStoneofRage-101203"
 value: {
  dps: 155213.02214
  tps: 145159.24574
  hps: 7946.92901
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-LightoftheCosmos-87065"
 value: {
  dps: 150520.81078
  tps: 140805.9623
  hps: 7618.43427
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-LightweaveEmbroidery(Rank3)-4892"
 value: {
  dps: 156834.67803
  tps: 146782.1879
  hps: 7938.60817
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-LordBlastington'sScopeofDoom-4699"
 value: {
  dps: 155324.35283
  tps: 145437.85856
  hps: 7866.03458
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MalevolentGladiator'sBadgeofConquest-84934"
 value: {
  dps: 152404.25891
  tps: 142184.87412
  hps: 7933.26325
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MalevolentGladiator'sBadgeofConquest-91452"
 value: {
  dps: 152112.23213
  tps: 141927.93252
  hps: 7911.41312
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MalevolentGladiator'sBadgeofDominance-84940"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MalevolentGladiator'sBadgeofDominance-91753"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MalevolentGladiator'sBadgeofVictory-84942"
 value: {
  dps: 149678.10981
  tps: 139844.40436
  hps: 7754.00028
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MalevolentGladiator'sBadgeofVictory-91763"
 value: {
  dps: 149572.91303
  tps: 139755.37023
  hps: 7743.59546
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MalevolentGladiator'sEmblemofCruelty-84936"
 value: {
  dps: 149912.70695
  tps: 140180.05862
  hps: 7685.27764
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MalevolentGladiator'sEmblemofCruelty-91562"
 value: {
  dps: 149783.89885
  tps: 140060.98244
  hps: 7685.27764
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MalevolentGladiator'sEmblemofMeditation-84939"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MalevolentGladiator'sEmblemofMeditation-91564"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MalevolentGladiator'sEmblemofTenacity-84938"
 value: {
  dps: 148046.6611
  tps: 138466.10448
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MalevolentGladiator'sEmblemofTenacity-91563"
 value: {
  dps: 148045.62823
  tps: 138465.07162
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MalevolentGladiator'sInsigniaofConquest-91457"
 value: {
  dps: 156070.33769
  tps: 145712.69463
  hps: 8111.45301
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MalevolentGladiator'sInsigniaofDominance-91754"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MalevolentGladiator'sInsigniaofVictory-91768"
 value: {
  dps: 150243.5891
  tps: 140374.93911
  hps: 7793.78472
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MarkoftheCatacombs-83731"
 value: {
  dps: 150311.72665
  tps: 140624.04399
  hps: 7643.42556
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MarkoftheHardenedGrunt-92783"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MedallionofMystifyingVapors-93257"
 value: {
  dps: 150260.05586
  tps: 140657.83892
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MedallionoftheCatacombs-83734"
 value: {
  dps: 149075.13181
  tps: 139491.09307
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MendingBadgeoftheShieldwall-93348"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MirrorScope-4700"
 value: {
  dps: 155324.35283
  tps: 145437.85856
  hps: 7866.03458
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MistdancerDefenderIdol-101089"
 value: {
  dps: 148050.99377
  tps: 138470.43715
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MistdancerDefenderStone-101087"
 value: {
  dps: 150615.59076
  tps: 141000.81822
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MistdancerIdolofRage-101113"
 value: {
  dps: 157759.92963
  tps: 147427.69548
  hps: 8044.2956
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MistdancerStoneofRage-101117"
 value: {
  dps: 154761.96033
  tps: 144712.40192
  hps: 7946.92901
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MistdancerStoneofWisdom-101107"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MithrilWristwatch-87572"
 value: {
  dps: 149626.18858
  tps: 139911.35654
  hps: 7678.25074
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MountainsageIdolofDestruction-101069"
 value: {
  dps: 151595.48451
  tps: 141903.95028
  hps: 7703.93339
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MountainsageStoneofDestruction-101072"
 value: {
  dps: 150218.64808
  tps: 140605.48215
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-OathswornDefenderIdol-101303"
 value: {
  dps: 148050.99377
  tps: 138470.43715
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-OathswornDefenderStone-101306"
 value: {
  dps: 150317.4496
  tps: 140710.63618
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-OathswornIdolofBattle-101295"
 value: {
  dps: 151950.99265
  tps: 141950.01611
  hps: 7869.37974
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-OathswornStoneofBattle-101294"
 value: {
  dps: 151915.81895
  tps: 142129.69902
  hps: 7760.50779
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-PhaseFingers-4697"
 value: {
  dps: 159284.8826
  tps: 148947.96905
  hps: 8130.51473
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-PouchofWhiteAsh-103639"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-PowerfulPrimalDiamond"
 value: {
  dps: 155432.53567
  tps: 145222.98962
  hps: 7949.73784
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-PriceofProgress-81266"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-PridefulGladiator'sBadgeofConquest-102659"
 value: {
  dps: 156283.85872
  tps: 145520.96868
  hps: 8229.78156
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-PridefulGladiator'sBadgeofConquest-103342"
 value: {
  dps: 156283.85872
  tps: 145520.96868
  hps: 8229.78156
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-PridefulGladiator'sBadgeofDominance-102633"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-PridefulGladiator'sBadgeofDominance-103505"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-PridefulGladiator'sBadgeofVictory-102636"
 value: {
  dps: 151105.6882
  tps: 141052.64658
  hps: 7895.19947
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-PridefulGladiator'sBadgeofVictory-103511"
 value: {
  dps: 151105.6882
  tps: 141052.64658
  hps: 7895.19947
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-PridefulGladiator'sEmblemofCruelty-102680"
 value: {
  dps: 151684.50599
  tps: 141836.62629
  hps: 7808.26586
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-PridefulGladiator'sEmblemofCruelty-103407"
 value: {
  dps: 151684.50599
  tps: 141836.62629
  hps: 7808.26586
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-PridefulGladiator'sEmblemofMeditation-102616"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-PridefulGladiator'sEmblemofMeditation-103409"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-PridefulGladiator'sEmblemofTenacity-102706"
 value: {
  dps: 148060.69197
  tps: 138480.13536
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-PridefulGladiator'sEmblemofTenacity-103408"
 value: {
  dps: 148060.69197
  tps: 138480.13536
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-PridefulGladiator'sInsigniaofConquest-103347"
 value: {
  dps: 163850.28653
  tps: 152783.49335
  hps: 8603.33821
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-PridefulGladiator'sInsigniaofDominance-103506"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-PridefulGladiator'sInsigniaofVictory-103516"
 value: {
  dps: 152486.45892
  tps: 142329.02392
  hps: 8026.6229
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Primordius'TalismanofRage-96873"
 value: {
  dps: 154466.47422
  tps: 144335.24984
  hps: 8079.01187
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Qian-Le,CourageofNiuzao-102245"
 value: {
  dps: 165397.22262
  tps: 154650.2241
  hps: 8348.30553
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Qian-Ying,FortitudeofNiuzao-102250"
 value: {
  dps: 158136.47155
  tps: 147962.72534
  hps: 7957.20964
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Qin-xi'sPolarizingSeal-87075"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-RelicofChi-Ji-79330"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-RelicofKypariZar-84075"
 value: {
  dps: 150853.63709
  tps: 141198.63932
  hps: 7756.84347
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-RelicofNiuzao-79329"
 value: {
  dps: 148049.69262
  tps: 138469.13601
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-RelicofXuen-79327"
 value: {
  dps: 151200.60154
  tps: 141252.59772
  hps: 7887.51195
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-RelicofXuen-79328"
 value: {
  dps: 158105.21957
  tps: 147549.04753
  hps: 8232.53617
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-RelicofYu'lon-79331"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Renataki'sSoulCharm-96741"
 value: {
  dps: 178418.75721
  tps: 165237.07708
  hps: 9519.24585
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-ResolveofNiuzao-103690"
 value: {
  dps: 149980.968
  tps: 140393.9099
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-ResolveofNiuzao-103990"
 value: {
  dps: 150836.00964
  tps: 141246.1015
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-ReverberatingPrimalDiamond"
 value: {
  dps: 158362.99193
  tps: 148100.61608
  hps: 8052.43744
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-RevitalizingPrimalDiamond"
 value: {
  dps: 157985.29313
  tps: 147760.8775
  hps: 8016.5674
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SI:7Operative'sManual-92784"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-ScrollofReveredAncestors-89080"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SearingWords-81267"
 value: {
  dps: 157214.6673
  tps: 146624.89883
  hps: 8045.38252
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Shock-ChargerMedallion-93259"
 value: {
  dps: 150603.16568
  tps: 141060.23748
  hps: 7638.97737
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SigilofCompassion-83736"
 value: {
  dps: 149075.13181
  tps: 139491.09307
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SigilofDevotion-83740"
 value: {
  dps: 150590.63709
  tps: 140901.37118
  hps: 7643.42556
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SigilofFidelity-83737"
 value: {
  dps: 151047.2864
  tps: 141357.20793
  hps: 7728.62473
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SigilofGrace-83738"
 value: {
  dps: 151189.33132
  tps: 141480.62397
  hps: 7693.86576
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SigilofKypariZar-84076"
 value: {
  dps: 150650.11889
  tps: 140954.57945
  hps: 7653.46389
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SigilofPatience-83739"
 value: {
  dps: 149397.59792
  tps: 139806.58926
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SigiloftheCatacombs-83732"
 value: {
  dps: 151176.3058
  tps: 141418.22158
  hps: 7678.9425
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SinisterPrimalDiamond"
 value: {
  dps: 156154.34334
  tps: 145919.04913
  hps: 7968.25008
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SkullrenderMedallion-93256"
 value: {
  dps: 152025.62424
  tps: 141993.68983
  hps: 7945.90088
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SpiritsoftheSun-87163"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SpringrainIdolofDestruction-101023"
 value: {
  dps: 151605.05412
  tps: 141904.80049
  hps: 7664.99584
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SpringrainIdolofRage-101009"
 value: {
  dps: 157118.92434
  tps: 146827.0777
  hps: 8038.85377
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SpringrainStoneofDestruction-101026"
 value: {
  dps: 150020.44283
  tps: 140407.93975
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SpringrainStoneofRage-101012"
 value: {
  dps: 155125.66286
  tps: 145070.71529
  hps: 7946.92901
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SpringrainStoneofWisdom-101041"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Static-Caster'sMedallion-93254"
 value: {
  dps: 150603.16568
  tps: 141060.23748
  hps: 7638.97737
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SteadfastFootman'sMedallion-92782"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SteadfastTalismanoftheShado-PanAssault-94507"
 value: {
  dps: 150514.3108
  tps: 140925.47496
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-StreamtalkerIdolofDestruction-101222"
 value: {
  dps: 150110.02722
  tps: 140362.04053
  hps: 7715.16801
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-StreamtalkerIdolofRage-101217"
 value: {
  dps: 157650.21383
  tps: 147414.85885
  hps: 8034.29105
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-StreamtalkerStoneofDestruction-101225"
 value: {
  dps: 150253.54595
  tps: 140650.2655
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-StreamtalkerStoneofRage-101220"
 value: {
  dps: 155084.11332
  tps: 145032.34573
  hps: 7946.92901
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-StreamtalkerStoneofWisdom-101250"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-StuffofNightmares-87160"
 value: {
  dps: 150231.55446
  tps: 140643.6611
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SunsoulDefenderIdol-101160"
 value: {
  dps: 148050.99377
  tps: 138470.43715
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SunsoulDefenderStone-101163"
 value: {
  dps: 150294.23705
  tps: 140685.86805
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SunsoulIdolofBattle-101152"
 value: {
  dps: 151954.21954
  tps: 141950.31701
  hps: 7883.15635
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SunsoulStoneofBattle-101151"
 value: {
  dps: 151882.73013
  tps: 142090.76246
  hps: 7760.50779
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SunsoulStoneofWisdom-101138"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SymboloftheCatacombs-83735"
 value: {
  dps: 150572.13489
  tps: 140857.67594
  hps: 7663.53109
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SynapseSprings(MarkII)-4898"
 value: {
  dps: 161240.6008
  tps: 150616.85026
  hps: 8254.53143
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TalismanofBloodlust-96864"
 value: {
  dps: 160478.467
  tps: 150010.06387
  hps: 8324.98124
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Thousand-YearPickledEgg-87573"
 value: {
  dps: 148700.99891
  tps: 139139.48704
  hps: 7643.24394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TrailseekerIdolofRage-101054"
 value: {
  dps: 156987.25089
  tps: 146693.0159
  hps: 8043.2695
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TrailseekerStoneofRage-101057"
 value: {
  dps: 155207.64668
  tps: 145156.30174
  hps: 7946.92901
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sBadgeofConquest-100043"
 value: {
  dps: 153029.36975
  tps: 142719.44048
  hps: 7977.23161
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sBadgeofConquest-91099"
 value: {
  dps: 153029.36975
  tps: 142719.44048
  hps: 7977.23161
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sBadgeofConquest-94373"
 value: {
  dps: 153029.36975
  tps: 142719.44048
  hps: 7977.23161
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sBadgeofConquest-99772"
 value: {
  dps: 153029.36975
  tps: 142719.44048
  hps: 7977.23161
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sBadgeofDominance-100016"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sBadgeofDominance-91400"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sBadgeofDominance-94346"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sBadgeofDominance-99937"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sBadgeofVictory-100019"
 value: {
  dps: 149889.79413
  tps: 140023.56505
  hps: 7774.93759
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sBadgeofVictory-91410"
 value: {
  dps: 149889.79413
  tps: 140023.56505
  hps: 7774.93759
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sBadgeofVictory-94349"
 value: {
  dps: 149889.79413
  tps: 140023.56505
  hps: 7774.93759
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sBadgeofVictory-99943"
 value: {
  dps: 149889.79413
  tps: 140023.56505
  hps: 7774.93759
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sEmblemofCruelty-100066"
 value: {
  dps: 150144.92237
  tps: 140395.21452
  hps: 7702.89449
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sEmblemofCruelty-91209"
 value: {
  dps: 150144.92237
  tps: 140395.21452
  hps: 7702.89449
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sEmblemofCruelty-94396"
 value: {
  dps: 150144.92237
  tps: 140395.21452
  hps: 7702.89449
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sEmblemofCruelty-99838"
 value: {
  dps: 150144.92237
  tps: 140395.21452
  hps: 7702.89449
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sEmblemofMeditation-91211"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sEmblemofMeditation-94329"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sEmblemofMeditation-99840"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sEmblemofMeditation-99990"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sEmblemofTenacity-100092"
 value: {
  dps: 148048.74024
  tps: 138468.18363
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sEmblemofTenacity-91210"
 value: {
  dps: 148048.74024
  tps: 138468.18363
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sEmblemofTenacity-94422"
 value: {
  dps: 148048.74024
  tps: 138468.18363
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sEmblemofTenacity-99839"
 value: {
  dps: 148048.74024
  tps: 138468.18363
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sInsigniaofConquest-100026"
 value: {
  dps: 157330.82039
  tps: 146863.10797
  hps: 8154.03113
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sInsigniaofDominance-100152"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sInsigniaofVictory-100085"
 value: {
  dps: 150658.1889
  tps: 140727.90331
  hps: 7837.8368
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalPrimalDiamond"
 value: {
  dps: 155428.24416
  tps: 145218.69811
  hps: 7949.73784
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-UnerringVisionofLeiShen-96930"
 value: {
  dps: 150314.27887
  tps: 140636.10695
  hps: 7774.66745
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-VaporshieldMedallion-93262"
 value: {
  dps: 150260.05586
  tps: 140657.83892
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-VialofDragon'sBlood-87063"
 value: {
  dps: 150092.71602
  tps: 140505.28544
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-VialofIchorousBlood-100963"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-VialofIchorousBlood-81264"
 value: {
  dps: 148030.45718
  tps: 138449.90056
  hps: 7591.03394
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-ViciousTalismanoftheShado-PanAssault-94511"
 value: {
  dps: 161206.13031
  tps: 150163.23898
  hps: 8296.67543
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-VisionofthePredator-81192"
 value: {
  dps: 149974.53915
  tps: 140188.46107
  hps: 7658.64341
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-VolatileTalismanoftheShado-PanAssault-94510"
 value: {
  dps: 150349.4337
  tps: 140861.49676
  hps: 7671.96503
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-WindsweptPages-81125"
 value: {
  dps: 153266.64749
  tps: 143219.92111
  hps: 7978.54987
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-WoundripperMedallion-93253"
 value: {
  dps: 155191.40308
  tps: 144850.27416
  hps: 8160.42327
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Wushoolay'sFinalChoice-96785"
 value: {
  dps: 149008.87521
  tps: 139329.6499
  hps: 7602.83542
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Xing-Ho,BreathofYu'lon-102246"
 value: {
  dps: 157718.0822
  tps: 147794.64358
  hps: 7753.76855
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-YaungolFireCarrier-86518"
 value: {
  dps: 158960.98038
  tps: 148646.44582
  hps: 8091.89449
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Yu'lon'sBite-103987"
 value: {
  dps: 151587.6934
  tps: 141608.57113
  hps: 7864.78258
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-ZenAlchemistStone-75274"
 value: {
  dps: 156664.53093
  tps: 146381.28455
  hps: 8024.60725
 }
}
dps_results: {
 key: "TestWindwalker-Average-Default"
 value: {
  dps: 160638.88344
  tps: 149841.31502
  hps: 8407.67243
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_bis_2h-Basic-default-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 1.10519774605e+06
  tps: 414303.41402
  hps: 7485.17857
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_bis_2h-Basic-default-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 153435.63195
  tps: 142009.92993
  hps: 8035.13316
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_bis_2h-Basic-default-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 193875.52539
  tps: 159203.76236
  hps: 9708.81277
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_bis_2h-Basic-default-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 871824.16809
  tps: 320856.87506
  hps: 6452.21733
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_bis_2h-Basic-default-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 117660.79259
  tps: 110543.70711
  hps: 6736.80613
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_bis_2h-Basic-default-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 132381.68275
  tps: 113767.03906
  hps: 8879.74695
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_bis_dw-Basic-default-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 1.11664186059e+06
  tps: 418086.97575
  hps: 7919.50572
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_bis_dw-Basic-default-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 162894.10845
  tps: 152024.69368
  hps: 8362.2513
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_bis_dw-Basic-default-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 207141.76766
  tps: 174644.92003
  hps: 9938.29493
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_bis_dw-Basic-default-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 893914.98218
  tps: 330342.20788
  hps: 6638.68403
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_bis_dw-Basic-default-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 121943.16069
  tps: 115069.58486
  hps: 6967.6916
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_bis_dw-Basic-default-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 134480.16832
  tps: 116306.37545
  hps: 8292.35461
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_prebis_2h-Basic-default-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 766492.00067
  tps: 278793.85556
  hps: 6154.07398
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_prebis_2h-Basic-default-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 104501.66041
  tps: 95242.23021
  hps: 6345.91166
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_prebis_2h-Basic-default-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 142192.17053
  tps: 115132.76303
  hps: 7998.71417
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_prebis_2h-Basic-default-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 600373.9322
  tps: 215643.78392
  hps: 5065.6927
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_prebis_2h-Basic-default-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 77448.78799
  tps: 71642.3808
  hps: 5239.34395
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_prebis_2h-Basic-default-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 90347.06016
  tps: 75492.66798
  hps: 6321.63042
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_prebis_dw-Basic-default-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 769335.18893
  tps: 280335.8513
  hps: 6323.75003
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_prebis_dw-Basic-default-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 110906.25743
  tps: 102070.81706
  hps: 6548.99118
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_prebis_dw-Basic-default-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 150109.32485
  tps: 124049.78906
  hps: 7945.41846
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_prebis_dw-Basic-default-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 612962.56173
  tps: 222640.79479
  hps: 5200.53752
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_prebis_dw-Basic-default-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 81103.69097
  tps: 75622.16882
  hps: 5379.07325
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_prebis_dw-Basic-default-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 93572.73698
  tps: 79303.3909
  hps: 6460.445
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_bis_2h-Basic-default-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 1.09605471876e+06
  tps: 412570.02841
  hps: 7576.21047
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_bis_2h-Basic-default-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 154332.60382
  tps: 143094.11272
  hps: 7894.95496
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_bis_2h-Basic-default-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 196661.38674
  tps: 162941.19867
  hps: 9752.49352
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_bis_2h-Basic-default-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 870469.08823
  tps: 323177.59526
  hps: 6235.08032
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_bis_2h-Basic-default-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 116276.7026
  tps: 109304.65706
  hps: 6612.2441
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_bis_2h-Basic-default-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 131808.17289
  tps: 113800.38074
  hps: 8669.65777
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_bis_dw-Basic-default-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 1.08320321604e+06
  tps: 410581.5379
  hps: 7793.95815
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_bis_dw-Basic-default-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 161025.26498
  tps: 150437.22403
  hps: 8214.14388
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_bis_dw-Basic-default-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 204887.01863
  tps: 173315.9429
  hps: 9897.99441
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_bis_dw-Basic-default-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 876473.86578
  tps: 328183.27929
  hps: 6600.87022
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_bis_dw-Basic-default-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 120378.43025
  tps: 113699.37472
  hps: 6919.25238
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_bis_dw-Basic-default-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 132438.27363
  tps: 115132.58864
  hps: 8459.75664
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_prebis_2h-Basic-default-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 760546.58935
  tps: 280197.56985
  hps: 5968.55289
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_prebis_2h-Basic-default-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 104906.86214
  tps: 95740.41778
  hps: 6279.38993
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_prebis_2h-Basic-default-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 141094.35981
  tps: 114203.71773
  hps: 7887.65368
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_prebis_2h-Basic-default-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 601620.86203
  tps: 220575.52677
  hps: 4991.07792
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_prebis_2h-Basic-default-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 77543.9268
  tps: 71962.90226
  hps: 5171.66612
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_prebis_2h-Basic-default-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 90991.72253
  tps: 76772.34509
  hps: 6638.22717
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_prebis_dw-Basic-default-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 767267.29173
  tps: 282341.93107
  hps: 6096.60522
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_prebis_dw-Basic-default-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 110829.03936
  tps: 102060.75316
  hps: 6441.97228
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_prebis_dw-Basic-default-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 149229.06345
  tps: 123421.1145
  hps: 7412.0634
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_prebis_dw-Basic-default-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 601300.11492
  tps: 219058.15051
  hps: 5121.64605
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_prebis_dw-Basic-default-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 81099.10746
  tps: 75714.94517
  hps: 5295.91254
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_prebis_dw-Basic-default-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 94372.19295
  tps: 80652.61807
  hps: 6596.52873
 }
}
dps_results: {
 key: "TestWindwalker-SwitchInFrontOfTarget-Default"
 value: {
  dps: 138332.0217
  tps: 127995.68971
  hps: 8228.30454
 }
}
