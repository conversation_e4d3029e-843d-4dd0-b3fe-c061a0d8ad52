character_stats_results: {
 key: "TestWindwalker-CharacterStats-Default"
 value: {
  final_stats: 183.75
  final_stats: 20037.9375
  final_stats: 22349.8
  final_stats: 257.25
  final_stats: 271
  final_stats: 2554
  final_stats: 5669
  final_stats: 5924
  final_stats: 2225
  final_stats: 18537.18167
  final_stats: 2e-05
  final_stats: 6386
  final_stats: 44450.5875
  final_stats: 0
  final_stats: 0
  final_stats: 0
  final_stats: 0
  final_stats: 18644
  final_stats: 0
  final_stats: 459300.2
  final_stats: 300000
  final_stats: 0
  final_stats: 7.51176
  final_stats: 14.05588
  final_stats: 37.83745
  final_stats: 16.29833
  final_stats: 0
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-AgilePrimalDiamond"
 value: {
  dps: 158503.65469
  tps: 148223.32016
  hps: 8194.05379
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-AlacrityofXuen-103989"
 value: {
  dps: 155771.64917
  tps: 145753.58766
  hps: 7940.75882
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-ArcaneBadgeoftheShieldwall-93347"
 value: {
  dps: 149417.32912
  tps: 139792.44783
  hps: 7629.04493
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-ArmorofSevenSacredSeals"
 value: {
  dps: 147549.9954
  tps: 137091.47826
  hps: 7424.47255
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-ArmoroftheRedCrane"
 value: {
  dps: 138130.05023
  tps: 128659.05141
  hps: 7233.79885
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-ArrowflightMedallion-93258"
 value: {
  dps: 155596.77784
  tps: 145315.08844
  hps: 8224.19062
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-AssuranceofConsequence-105472"
 value: {
  dps: 162357.57912
  tps: 152104.35379
  hps: 8000.76421
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-AusterePrimalDiamond"
 value: {
  dps: 154951.29
  tps: 144780.9289
  hps: 8050.74443
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-BadJuju-96781"
 value: {
  dps: 160971.2804
  tps: 150066.192
  hps: 8323.56965
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-BadgeofKypariZar-84079"
 value: {
  dps: 150876.18495
  tps: 141196.20547
  hps: 7740.06316
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-BattlegearofSevenSacredSeals"
 value: {
  dps: 142065.03189
  tps: 132246.52452
  hps: 7428.15991
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-BattlegearoftheRedCrane"
 value: {
  dps: 145187.16536
  tps: 135712.52978
  hps: 7197.79271
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-BlossomofPureSnow-89081"
 value: {
  dps: 150343.88868
  tps: 140578.93818
  hps: 7803.51298
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-BraidofTenSongs-84072"
 value: {
  dps: 149884.84639
  tps: 140259.33662
  hps: 7665.61196
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Brawler'sStatue-87571"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-BreathoftheHydra-96827"
 value: {
  dps: 151060.35242
  tps: 141316.93571
  hps: 7661.9782
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-BroochofMunificentDeeds-87500"
 value: {
  dps: 148365.59622
  tps: 138811.71269
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-BrutalTalismanoftheShado-PanAssault-94508"
 value: {
  dps: 152377.56856
  tps: 142106.38282
  hps: 7906.37891
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-BurningPrimalDiamond"
 value: {
  dps: 157472.12332
  tps: 147287.63757
  hps: 8117.99852
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CapacitivePrimalDiamond"
 value: {
  dps: 155667.59851
  tps: 145460.35284
  hps: 8072.93343
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CarbonicCarbuncle-81138"
 value: {
  dps: 151965.72068
  tps: 142036.24093
  hps: 7916.23057
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Cha-Ye'sEssenceofBrilliance-96888"
 value: {
  dps: 151666.17357
  tps: 141870.67863
  hps: 7859.34958
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CharmofTenSongs-84071"
 value: {
  dps: 150791.65329
  tps: 141132.46218
  hps: 7727.22186
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CommunalIdolofDestruction-101168"
 value: {
  dps: 151072.75489
  tps: 141452.19114
  hps: 7702.43325
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CommunalStoneofDestruction-101171"
 value: {
  dps: 150846.36667
  tps: 141262.44983
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CommunalStoneofWisdom-101183"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-ContemplationofChi-Ji-103688"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-ContemplationofChi-Ji-103988"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Coren'sColdChromiumCoaster-87574"
 value: {
  dps: 153450.49295
  tps: 143314.49496
  hps: 8014.22169
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CoreofDecency-87497"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CourageousPrimalDiamond"
 value: {
  dps: 154946.7837
  tps: 144776.42259
  hps: 8050.74443
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CraftedDreadfulGladiator'sBadgeofConquest-93419"
 value: {
  dps: 151709.58835
  tps: 141657.17052
  hps: 7920.46463
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CraftedDreadfulGladiator'sBadgeofDominance-93600"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CraftedDreadfulGladiator'sBadgeofVictory-93606"
 value: {
  dps: 149649.07006
  tps: 139898.43614
  hps: 7782.48775
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CraftedDreadfulGladiator'sEmblemofCruelty-93485"
 value: {
  dps: 149957.78069
  tps: 140294.32266
  hps: 7735.33227
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CraftedDreadfulGladiator'sEmblemofMeditation-93487"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CraftedDreadfulGladiator'sEmblemofTenacity-93486"
 value: {
  dps: 148367.89478
  tps: 138814.01126
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CraftedDreadfulGladiator'sInsigniaofConquest-93424"
 value: {
  dps: 154522.99732
  tps: 144368.11962
  hps: 8025.47396
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CraftedDreadfulGladiator'sInsigniaofDominance-93601"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CraftedDreadfulGladiator'sInsigniaofVictory-93611"
 value: {
  dps: 150187.20305
  tps: 140393.83133
  hps: 7822.83056
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CraftedMalevolentGladiator'sBadgeofConquest-98755"
 value: {
  dps: 152321.15678
  tps: 142185.03184
  hps: 7968.55886
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CraftedMalevolentGladiator'sBadgeofDominance-98910"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CraftedMalevolentGladiator'sBadgeofVictory-98912"
 value: {
  dps: 149886.10416
  tps: 140099.54698
  hps: 7805.38977
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CraftedMalevolentGladiator'sEmblemofCruelty-98811"
 value: {
  dps: 150213.57391
  tps: 140526.01141
  hps: 7749.74135
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CraftedMalevolentGladiator'sEmblemofMeditation-98813"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CraftedMalevolentGladiator'sEmblemofTenacity-98812"
 value: {
  dps: 148370.99517
  tps: 138817.11165
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CraftedMalevolentGladiator'sInsigniaofConquest-98760"
 value: {
  dps: 155378.22738
  tps: 145114.093
  hps: 8087.04321
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CraftedMalevolentGladiator'sInsigniaofDominance-98911"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CraftedMalevolentGladiator'sInsigniaofVictory-98917"
 value: {
  dps: 150642.87412
  tps: 140799.43672
  hps: 7876.67732
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CurseofHubris-102307"
 value: {
  dps: 152888.84347
  tps: 142773.8093
  hps: 7906.84339
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CurseofHubris-104649"
 value: {
  dps: 153513.62341
  tps: 143319.8179
  hps: 7928.7807
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CurseofHubris-104898"
 value: {
  dps: 152370.20647
  tps: 142319.54497
  hps: 7895.87748
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CurseofHubris-105147"
 value: {
  dps: 152039.57011
  tps: 142031.06902
  hps: 7847.96695
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CurseofHubris-105396"
 value: {
  dps: 153172.4181
  tps: 143019.95406
  hps: 7911.90352
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CurseofHubris-105645"
 value: {
  dps: 153706.11401
  tps: 143485.82462
  hps: 7941.4007
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-CutstitcherMedallion-93255"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Daelo'sFinalWords-87496"
 value: {
  dps: 150166.78264
  tps: 140173.62687
  hps: 7767.08347
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-DarkglowEmbroidery(Rank3)-4893"
 value: {
  dps: 156381.10612
  tps: 146367.34197
  hps: 8035.05847
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-DarkmistVortex-87172"
 value: {
  dps: 153899.54992
  tps: 143935.62735
  hps: 7892.56312
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-DeadeyeBadgeoftheShieldwall-93346"
 value: {
  dps: 153562.97942
  tps: 143658.56525
  hps: 7940.20342
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-DelicateVialoftheSanguinaire-96895"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-DestructivePrimalDiamond"
 value: {
  dps: 155871.25512
  tps: 145644.81356
  hps: 8077.60472
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-DisciplineofXuen-103986"
 value: {
  dps: 160160.4903
  tps: 149832.58298
  hps: 8237.00258
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Dominator'sArcaneBadge-93342"
 value: {
  dps: 149417.32912
  tps: 139792.44783
  hps: 7629.04493
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Dominator'sDeadeyeBadge-93341"
 value: {
  dps: 153562.97942
  tps: 143658.56525
  hps: 7940.20342
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Dominator'sDurableBadge-93345"
 value: {
  dps: 149853.44111
  tps: 140286.33546
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Dominator'sKnightlyBadge-93344"
 value: {
  dps: 151247.85305
  tps: 141539.51852
  hps: 7791.88718
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Dominator'sMendingBadge-93343"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-DreadfulGladiator'sBadgeofConquest-84344"
 value: {
  dps: 151709.58835
  tps: 141657.17052
  hps: 7920.46463
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-DreadfulGladiator'sBadgeofDominance-84488"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-DreadfulGladiator'sBadgeofVictory-84490"
 value: {
  dps: 149649.07006
  tps: 139898.43614
  hps: 7782.48775
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-DreadfulGladiator'sEmblemofCruelty-84399"
 value: {
  dps: 149957.78069
  tps: 140294.32266
  hps: 7735.33227
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-DreadfulGladiator'sEmblemofMeditation-84401"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-DreadfulGladiator'sEmblemofTenacity-84400"
 value: {
  dps: 148367.89478
  tps: 138814.01126
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-DreadfulGladiator'sInsigniaofConquest-84349"
 value: {
  dps: 154750.34437
  tps: 144585.74331
  hps: 8033.21722
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-DreadfulGladiator'sInsigniaofDominance-84489"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-DreadfulGladiator'sInsigniaofVictory-84495"
 value: {
  dps: 150213.04914
  tps: 140402.59538
  hps: 7827.88806
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-DurableBadgeoftheShieldwall-93350"
 value: {
  dps: 149853.44111
  tps: 140286.33546
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-EffulgentPrimalDiamond"
 value: {
  dps: 154951.29
  tps: 144780.9289
  hps: 8050.74443
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-EmberPrimalDiamond"
 value: {
  dps: 154946.7837
  tps: 144776.42259
  hps: 8050.74443
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-EmblemofKypariZar-84077"
 value: {
  dps: 151103.92149
  tps: 141450.63156
  hps: 7751.20659
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-EmblemoftheCatacombs-83733"
 value: {
  dps: 150079.64097
  tps: 140520.06288
  hps: 7667.00178
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-EmptyFruitBarrel-81133"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-EnchantWeapon-BloodyDancingSteel-5125"
 value: {
  dps: 158695.41996
  tps: 148436.59099
  hps: 8167.74215
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-EnchantWeapon-Colossus-4445"
 value: {
  dps: 154761.37599
  tps: 144909.06699
  hps: 7942.10916
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-EnchantWeapon-JadeSpirit-4442"
 value: {
  dps: 154760.0143
  tps: 144907.70531
  hps: 7942.10916
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-EnchantWeapon-River'sSong-4446"
 value: {
  dps: 154760.0143
  tps: 144907.70531
  hps: 7942.10916
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-EnchantWeapon-SpiritofConquest-5124"
 value: {
  dps: 154760.0143
  tps: 144907.70531
  hps: 7942.10916
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-EnchantWeapon-Windsong-4441"
 value: {
  dps: 157099.53547
  tps: 147041.50258
  hps: 7936.91238
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-EnigmaticPrimalDiamond"
 value: {
  dps: 155871.25512
  tps: 145644.81356
  hps: 8077.60472
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-EssenceofTerror-87175"
 value: {
  dps: 150483.89621
  tps: 140858.56819
  hps: 7682.99263
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-EternalPrimalDiamond"
 value: {
  dps: 154946.7837
  tps: 144776.42259
  hps: 8050.74443
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-EvilEyeofGalakras-105491"
 value: {
  dps: 149892.69553
  tps: 140121.60455
  hps: 7788.7689
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-FabledFeatherofJi-Kun-96842"
 value: {
  dps: 153650.67225
  tps: 143481.86309
  hps: 8017.24853
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-FearwurmBadge-84074"
 value: {
  dps: 151400.39536
  tps: 141673.32798
  hps: 7717.95226
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-FearwurmRelic-84070"
 value: {
  dps: 150204.76919
  tps: 140482.07609
  hps: 7670.48084
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-FelsoulIdolofDestruction-101263"
 value: {
  dps: 152131.16823
  tps: 142516.85665
  hps: 7685.5237
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-FelsoulStoneofDestruction-101266"
 value: {
  dps: 150727.6551
  tps: 141143.73826
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Fen-Yu,FuryofXuen-102248"
 value: {
  dps: 174912.87238
  tps: 164366.59344
  hps: 8376.45477
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Fire-CharmArmor"
 value: {
  dps: 150054.5329
  tps: 139582.79374
  hps: 7925.95489
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Fire-CharmBattlegear"
 value: {
  dps: 157584.41607
  tps: 147267.94308
  hps: 7917.76232
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-FlashfrozenResinGlobule-100951"
 value: {
  dps: 148676.23237
  tps: 139039.90172
  hps: 7653.83217
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-FlashfrozenResinGlobule-81263"
 value: {
  dps: 148669.68571
  tps: 139035.05581
  hps: 7643.09741
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-FlashingSteelTalisman-81265"
 value: {
  dps: 153046.15973
  tps: 142552.83835
  hps: 7924.31881
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-FleetPrimalDiamond"
 value: {
  dps: 155726.21841
  tps: 145554.00244
  hps: 8050.74443
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-ForlornPrimalDiamond"
 value: {
  dps: 154946.7837
  tps: 144776.42259
  hps: 8050.74443
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-FortitudeoftheZandalari-94516"
 value: {
  dps: 150809.28508
  tps: 141248.19886
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-FortitudeoftheZandalari-95677"
 value: {
  dps: 150392.00344
  tps: 140832.13977
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-FortitudeoftheZandalari-96049"
 value: {
  dps: 150951.73062
  tps: 141390.22707
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-FortitudeoftheZandalari-96421"
 value: {
  dps: 151127.69276
  tps: 141565.67368
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-FortitudeoftheZandalari-96793"
 value: {
  dps: 151286.8966
  tps: 141724.41109
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GazeoftheTwins-96915"
 value: {
  dps: 155102.56428
  tps: 144946.07815
  hps: 8167.77626
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Gerp'sPerfectArrow-87495"
 value: {
  dps: 153477.55337
  tps: 143272.38305
  hps: 7977.22815
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Gong-Lu,StrengthofXuen-102249"
 value: {
  dps: 169317.04146
  tps: 159249.49932
  hps: 8002.70759
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sBadgeofConquest-100195"
 value: {
  dps: 154575.41064
  tps: 144133.28165
  hps: 8135.3898
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sBadgeofConquest-100603"
 value: {
  dps: 154575.41064
  tps: 144133.28165
  hps: 8135.3898
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sBadgeofConquest-102856"
 value: {
  dps: 154575.41064
  tps: 144133.28165
  hps: 8135.3898
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sBadgeofConquest-103145"
 value: {
  dps: 154575.41064
  tps: 144133.28165
  hps: 8135.3898
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sBadgeofDominance-100490"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sBadgeofDominance-100576"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sBadgeofDominance-102830"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sBadgeofDominance-103308"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sBadgeofVictory-100500"
 value: {
  dps: 150708.33627
  tps: 140797.16726
  hps: 7884.83307
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sBadgeofVictory-100579"
 value: {
  dps: 150708.33627
  tps: 140797.16726
  hps: 7884.83307
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sBadgeofVictory-102833"
 value: {
  dps: 150708.33627
  tps: 140797.16726
  hps: 7884.83307
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sBadgeofVictory-103314"
 value: {
  dps: 150708.33627
  tps: 140797.16726
  hps: 7884.83307
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sEmblemofCruelty-100305"
 value: {
  dps: 151222.10652
  tps: 141460.81
  hps: 7824.03864
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sEmblemofCruelty-100626"
 value: {
  dps: 151222.10652
  tps: 141460.81
  hps: 7824.03864
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sEmblemofCruelty-102877"
 value: {
  dps: 151222.10652
  tps: 141460.81
  hps: 7824.03864
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sEmblemofCruelty-103210"
 value: {
  dps: 151222.10652
  tps: 141460.81
  hps: 7824.03864
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sEmblemofMeditation-100307"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sEmblemofMeditation-100559"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sEmblemofMeditation-102813"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sEmblemofMeditation-103212"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sEmblemofTenacity-100306"
 value: {
  dps: 148381.77527
  tps: 138827.89174
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sEmblemofTenacity-100652"
 value: {
  dps: 148381.77527
  tps: 138827.89174
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sEmblemofTenacity-102903"
 value: {
  dps: 148381.77527
  tps: 138827.89174
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sEmblemofTenacity-103211"
 value: {
  dps: 148381.77527
  tps: 138827.89174
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sInsigniaofConquest-103150"
 value: {
  dps: 160433.22931
  tps: 149764.54295
  hps: 8327.44842
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sInsigniaofDominance-103309"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-GrievousGladiator'sInsigniaofVictory-103319"
 value: {
  dps: 151775.21048
  tps: 141792.77835
  hps: 7964.60864
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Hawkmaster'sTalon-89082"
 value: {
  dps: 154494.85078
  tps: 144323.72574
  hps: 8039.62867
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Heart-LesionDefenderIdol-100999"
 value: {
  dps: 148378.12251
  tps: 138824.23898
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Heart-LesionDefenderStone-101002"
 value: {
  dps: 150787.55747
  tps: 141205.6116
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Heart-LesionIdolofBattle-100991"
 value: {
  dps: 152311.22219
  tps: 142356.8745
  hps: 7935.46728
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Heart-LesionStoneofBattle-100990"
 value: {
  dps: 152476.22218
  tps: 142715.6936
  hps: 7827.22041
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-HeartofFire-81181"
 value: {
  dps: 149770.27055
  tps: 140212.22842
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-HeartwarmerMedallion-93260"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-HelmbreakerMedallion-93261"
 value: {
  dps: 152415.09795
  tps: 142424.19724
  hps: 8008.89939
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Horridon'sLastGasp-96757"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-ImpassivePrimalDiamond"
 value: {
  dps: 155871.25512
  tps: 145644.81356
  hps: 8077.60472
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-IndomitablePrimalDiamond"
 value: {
  dps: 154951.29
  tps: 144780.9289
  hps: 8050.74443
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-InscribedBagofHydra-Spawn-96828"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-InsigniaofKypariZar-84078"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-IronBellyWok-89083"
 value: {
  dps: 151278.02168
  tps: 141402.92122
  hps: 7841.26806
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-IronProtectorTalisman-85181"
 value: {
  dps: 148370.90608
  tps: 138817.02256
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-JadeBanditFigurine-86043"
 value: {
  dps: 154494.85078
  tps: 144323.72574
  hps: 8039.62867
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-JadeBanditFigurine-86772"
 value: {
  dps: 153720.01802
  tps: 143787.2606
  hps: 7972.99542
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-JadeCharioteerFigurine-86042"
 value: {
  dps: 151278.02168
  tps: 141402.92122
  hps: 7841.26806
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-JadeCharioteerFigurine-86771"
 value: {
  dps: 151019.60363
  tps: 141334.12859
  hps: 7796.97863
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-JadeCourtesanFigurine-86045"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-JadeCourtesanFigurine-86774"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-JadeMagistrateFigurine-86044"
 value: {
  dps: 150343.88868
  tps: 140578.93818
  hps: 7803.51298
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-JadeMagistrateFigurine-86773"
 value: {
  dps: 150126.46478
  tps: 140376.42978
  hps: 7786.60416
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-JadeWarlordFigurine-86046"
 value: {
  dps: 150385.573
  tps: 140814.0387
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-JadeWarlordFigurine-86775"
 value: {
  dps: 150152.95318
  tps: 140583.43682
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Ji-Kun'sRisingWinds-96843"
 value: {
  dps: 149099.56083
  tps: 139456.80995
  hps: 7644.42214
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-KnightlyBadgeoftheShieldwall-93349"
 value: {
  dps: 151247.85305
  tps: 141539.51852
  hps: 7791.88718
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-KnotofTenSongs-84073"
 value: {
  dps: 148351.65463
  tps: 138797.7711
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Kor'kronBookofHurting-92785"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Lao-Chin'sLiquidCourage-89079"
 value: {
  dps: 150385.573
  tps: 140814.0387
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-LeiShen'sFinalOrders-87072"
 value: {
  dps: 152638.77899
  tps: 142681.15379
  hps: 7841.37869
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-LessonsoftheDarkmaster-81268"
 value: {
  dps: 150829.35514
  tps: 140908.68177
  hps: 7792.64463
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-LightdrinkerIdolofRage-101200"
 value: {
  dps: 156096.59776
  tps: 145864.35522
  hps: 8029.71864
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-LightdrinkerStoneofRage-101203"
 value: {
  dps: 155366.03893
  tps: 145350.05245
  hps: 8014.40321
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-LightoftheCosmos-87065"
 value: {
  dps: 150212.15232
  tps: 140574.35373
  hps: 7625.74938
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-LightweaveEmbroidery(Rank3)-4892"
 value: {
  dps: 156381.10612
  tps: 146367.34197
  hps: 8035.05847
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-LordBlastington'sScopeofDoom-4699"
 value: {
  dps: 154760.0143
  tps: 144907.70531
  hps: 7942.10916
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MalevolentGladiator'sBadgeofConquest-84934"
 value: {
  dps: 152657.55877
  tps: 142486.45672
  hps: 7989.80374
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MalevolentGladiator'sBadgeofConquest-91452"
 value: {
  dps: 152321.15678
  tps: 142185.03184
  hps: 7968.55886
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MalevolentGladiator'sBadgeofDominance-84940"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MalevolentGladiator'sBadgeofDominance-91753"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MalevolentGladiator'sBadgeofVictory-84942"
 value: {
  dps: 149990.81028
  tps: 140188.38457
  hps: 7815.50638
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MalevolentGladiator'sBadgeofVictory-91763"
 value: {
  dps: 149886.10416
  tps: 140099.54698
  hps: 7805.38977
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MalevolentGladiator'sEmblemofCruelty-84936"
 value: {
  dps: 150284.63121
  tps: 140586.39765
  hps: 7749.74135
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MalevolentGladiator'sEmblemofCruelty-91562"
 value: {
  dps: 150213.57391
  tps: 140526.01141
  hps: 7749.74135
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MalevolentGladiator'sEmblemofMeditation-84939"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MalevolentGladiator'sEmblemofMeditation-91564"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MalevolentGladiator'sEmblemofTenacity-84938"
 value: {
  dps: 148372.36718
  tps: 138818.48366
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MalevolentGladiator'sEmblemofTenacity-91563"
 value: {
  dps: 148370.99517
  tps: 138817.11165
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MalevolentGladiator'sInsigniaofConquest-91457"
 value: {
  dps: 155151.16447
  tps: 144914.92504
  hps: 8061.22806
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MalevolentGladiator'sInsigniaofDominance-91754"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MalevolentGladiator'sInsigniaofVictory-91768"
 value: {
  dps: 150645.55484
  tps: 140803.49153
  hps: 7879.75437
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MarkoftheCatacombs-83731"
 value: {
  dps: 150584.08387
  tps: 140931.84129
  hps: 7704.83886
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MarkoftheHardenedGrunt-92783"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MedallionofMystifyingVapors-93257"
 value: {
  dps: 150492.31049
  tps: 140919.58311
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MedallionoftheCatacombs-83734"
 value: {
  dps: 149385.64167
  tps: 139828.72879
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MendingBadgeoftheShieldwall-93348"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MirrorScope-4700"
 value: {
  dps: 154760.0143
  tps: 144907.70531
  hps: 7942.10916
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MistdancerDefenderIdol-101089"
 value: {
  dps: 148378.12251
  tps: 138824.23898
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MistdancerDefenderStone-101087"
 value: {
  dps: 150659.42307
  tps: 141075.65236
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MistdancerIdolofRage-101113"
 value: {
  dps: 156977.41558
  tps: 146723.97568
  hps: 8069.4773
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MistdancerStoneofRage-101117"
 value: {
  dps: 154969.73883
  tps: 144961.0006
  hps: 8014.40321
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MistdancerStoneofWisdom-101107"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MithrilWristwatch-87572"
 value: {
  dps: 150134.54528
  tps: 140457.26414
  hps: 7744.68122
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MountainsageIdolofDestruction-101069"
 value: {
  dps: 152123.61576
  tps: 142490.93623
  hps: 7670.13769
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-MountainsageStoneofDestruction-101072"
 value: {
  dps: 150684.69001
  tps: 141104.61195
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-OathswornDefenderIdol-101303"
 value: {
  dps: 148378.12251
  tps: 138824.23898
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-OathswornDefenderStone-101306"
 value: {
  dps: 151059.30993
  tps: 141483.45129
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-OathswornIdolofBattle-101295"
 value: {
  dps: 152377.22692
  tps: 142414.54636
  hps: 7947.41535
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-OathswornStoneofBattle-101294"
 value: {
  dps: 152320.23305
  tps: 142564.94291
  hps: 7827.22041
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-PhaseFingers-4697"
 value: {
  dps: 158375.17011
  tps: 148024.72023
  hps: 8122.86041
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-PouchofWhiteAsh-103639"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-PowerfulPrimalDiamond"
 value: {
  dps: 154951.29
  tps: 144780.9289
  hps: 8050.74443
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-PriceofProgress-81266"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-PridefulGladiator'sBadgeofConquest-102659"
 value: {
  dps: 156499.29241
  tps: 145799.51275
  hps: 8278.10845
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-PridefulGladiator'sBadgeofConquest-103342"
 value: {
  dps: 156499.29241
  tps: 145799.51275
  hps: 8278.10845
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-PridefulGladiator'sBadgeofDominance-102633"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-PridefulGladiator'sBadgeofDominance-103505"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-PridefulGladiator'sBadgeofVictory-102636"
 value: {
  dps: 151411.73015
  tps: 141393.95961
  hps: 7952.79433
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-PridefulGladiator'sBadgeofVictory-103511"
 value: {
  dps: 151411.73015
  tps: 141393.95961
  hps: 7952.79433
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-PridefulGladiator'sEmblemofCruelty-102680"
 value: {
  dps: 151868.56
  tps: 142061.58311
  hps: 7876.57656
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-PridefulGladiator'sEmblemofCruelty-103407"
 value: {
  dps: 151868.56
  tps: 142061.58311
  hps: 7876.57656
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-PridefulGladiator'sEmblemofMeditation-102616"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-PridefulGladiator'sEmblemofMeditation-103409"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-PridefulGladiator'sEmblemofTenacity-102706"
 value: {
  dps: 148391.00516
  tps: 138837.12164
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-PridefulGladiator'sEmblemofTenacity-103408"
 value: {
  dps: 148391.00516
  tps: 138837.12164
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-PridefulGladiator'sInsigniaofConquest-103347"
 value: {
  dps: 163386.86151
  tps: 152373.2971
  hps: 8551.01181
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-PridefulGladiator'sInsigniaofDominance-103506"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-PridefulGladiator'sInsigniaofVictory-103516"
 value: {
  dps: 152875.58518
  tps: 142740.86757
  hps: 8106.22133
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Primordius'TalismanofRage-96873"
 value: {
  dps: 154708.84724
  tps: 144596.86602
  hps: 8157.60831
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Qian-Le,CourageofNiuzao-102245"
 value: {
  dps: 163222.95439
  tps: 152561.90808
  hps: 8369.00091
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Qian-Ying,FortitudeofNiuzao-102250"
 value: {
  dps: 156439.25129
  tps: 146345.81295
  hps: 7973.11859
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Qin-xi'sPolarizingSeal-87075"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-RelicofChi-Ji-79330"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-RelicofKypariZar-84075"
 value: {
  dps: 150442.72193
  tps: 140777.05128
  hps: 7788.09619
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-RelicofNiuzao-79329"
 value: {
  dps: 148376.39413
  tps: 138822.5106
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-RelicofXuen-79327"
 value: {
  dps: 151542.89175
  tps: 141624.38872
  hps: 7951.54367
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-RelicofXuen-79328"
 value: {
  dps: 158323.35451
  tps: 147860.76602
  hps: 8249.05849
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-RelicofYu'lon-79331"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Renataki'sSoulCharm-96741"
 value: {
  dps: 159557.21075
  tps: 148600.50538
  hps: 8319.0308
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-ResolveofNiuzao-103690"
 value: {
  dps: 150281.39867
  tps: 140721.85904
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-ResolveofNiuzao-103990"
 value: {
  dps: 151127.69276
  tps: 141565.67368
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-ReverberatingPrimalDiamond"
 value: {
  dps: 157848.74505
  tps: 147626.50746
  hps: 8154.21531
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-RevitalizingPrimalDiamond"
 value: {
  dps: 157472.12332
  tps: 147287.63757
  hps: 8117.99852
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SI:7Operative'sManual-92784"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-ScrollofReveredAncestors-89080"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SearingWords-81267"
 value: {
  dps: 155948.84223
  tps: 145406.89535
  hps: 8062.92573
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Shock-ChargerMedallion-93259"
 value: {
  dps: 150763.57056
  tps: 141214.69946
  hps: 7660.3603
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SigilofCompassion-83736"
 value: {
  dps: 149384.82968
  tps: 139827.9168
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SigilofDevotion-83740"
 value: {
  dps: 151108.27986
  tps: 141454.12725
  hps: 7704.83886
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SigilofFidelity-83737"
 value: {
  dps: 150163.81154
  tps: 140451.13837
  hps: 7757.90105
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SigilofGrace-83738"
 value: {
  dps: 150886.78748
  tps: 141207.81911
  hps: 7660.04076
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SigilofKypariZar-84076"
 value: {
  dps: 151124.59722
  tps: 141463.9662
  hps: 7715.31902
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SigilofPatience-83739"
 value: {
  dps: 149745.62853
  tps: 140182.65204
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SigiloftheCatacombs-83732"
 value: {
  dps: 151018.9781
  tps: 141304.61637
  hps: 7728.91856
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SinisterPrimalDiamond"
 value: {
  dps: 155667.59851
  tps: 145460.35284
  hps: 8072.93343
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SkullrenderMedallion-93256"
 value: {
  dps: 152415.09795
  tps: 142424.19724
  hps: 8008.89939
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SpiritsoftheSun-87163"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SpringrainIdolofDestruction-101023"
 value: {
  dps: 151027.82736
  tps: 141402.50558
  hps: 7668.98482
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SpringrainIdolofRage-101009"
 value: {
  dps: 156560.16716
  tps: 146365.61623
  hps: 8051.54233
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SpringrainStoneofDestruction-101026"
 value: {
  dps: 150611.24532
  tps: 141028.79977
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SpringrainStoneofRage-101012"
 value: {
  dps: 155389.80352
  tps: 145372.64588
  hps: 8014.40321
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SpringrainStoneofWisdom-101041"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Static-Caster'sMedallion-93254"
 value: {
  dps: 150763.57056
  tps: 141214.69946
  hps: 7660.3603
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SteadfastFootman'sMedallion-92782"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SteadfastTalismanoftheShado-PanAssault-94507"
 value: {
  dps: 150809.28508
  tps: 141248.19886
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-StreamtalkerIdolofDestruction-101222"
 value: {
  dps: 151592.60509
  tps: 141920.96182
  hps: 7680.62713
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-StreamtalkerIdolofRage-101217"
 value: {
  dps: 157278.32333
  tps: 147097.34281
  hps: 8047.0811
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-StreamtalkerStoneofDestruction-101225"
 value: {
  dps: 150603.58386
  tps: 141026.70634
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-StreamtalkerStoneofRage-101220"
 value: {
  dps: 155544.1487
  tps: 145531.86909
  hps: 8014.40321
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-StreamtalkerStoneofWisdom-101250"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-StuffofNightmares-87160"
 value: {
  dps: 150529.42149
  tps: 140969.15521
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SunsoulDefenderIdol-101160"
 value: {
  dps: 148378.12251
  tps: 138824.23898
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SunsoulDefenderStone-101163"
 value: {
  dps: 150850.51775
  tps: 141267.59291
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SunsoulIdolofBattle-101152"
 value: {
  dps: 152367.07208
  tps: 142404.41893
  hps: 7946.12138
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SunsoulStoneofBattle-101151"
 value: {
  dps: 152522.53088
  tps: 142762.74019
  hps: 7827.22041
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SunsoulStoneofWisdom-101138"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SymboloftheCatacombs-83735"
 value: {
  dps: 150960.96086
  tps: 141278.37835
  hps: 7737.42941
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-SynapseSprings(MarkII)-4898"
 value: {
  dps: 160613.17566
  tps: 149964.11061
  hps: 8238.69717
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TalismanofBloodlust-96864"
 value: {
  dps: 160157.17576
  tps: 149778.23319
  hps: 8288.02493
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Thousand-YearPickledEgg-87573"
 value: {
  dps: 148988.64194
  tps: 139445.30538
  hps: 7670.8136
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TrailseekerIdolofRage-101054"
 value: {
  dps: 156649.77362
  tps: 146452.89683
  hps: 8062.22536
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TrailseekerStoneofRage-101057"
 value: {
  dps: 155409.62537
  tps: 145394.41836
  hps: 8014.40321
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sBadgeofConquest-100043"
 value: {
  dps: 153278.54673
  tps: 143021.40375
  hps: 8032.55417
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sBadgeofConquest-91099"
 value: {
  dps: 153278.54673
  tps: 143021.40375
  hps: 8032.55417
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sBadgeofConquest-94373"
 value: {
  dps: 153278.54673
  tps: 143021.40375
  hps: 8032.55417
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sBadgeofConquest-99772"
 value: {
  dps: 153278.54673
  tps: 143021.40375
  hps: 8032.55417
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sBadgeofDominance-100016"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sBadgeofDominance-91400"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sBadgeofDominance-94346"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sBadgeofDominance-99937"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sBadgeofVictory-100019"
 value: {
  dps: 150201.50726
  tps: 140367.14976
  hps: 7835.86372
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sBadgeofVictory-91410"
 value: {
  dps: 150201.50726
  tps: 140367.14976
  hps: 7835.86372
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sBadgeofVictory-94349"
 value: {
  dps: 150201.50726
  tps: 140367.14976
  hps: 7835.86372
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sBadgeofVictory-99943"
 value: {
  dps: 150201.50726
  tps: 140367.14976
  hps: 7835.86372
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sEmblemofCruelty-100066"
 value: {
  dps: 150458.40346
  tps: 140747.27455
  hps: 7767.708
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sEmblemofCruelty-91209"
 value: {
  dps: 150458.40346
  tps: 140747.27455
  hps: 7767.708
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sEmblemofCruelty-94396"
 value: {
  dps: 150458.40346
  tps: 140747.27455
  hps: 7767.708
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sEmblemofCruelty-99838"
 value: {
  dps: 150458.40346
  tps: 140747.27455
  hps: 7767.708
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sEmblemofMeditation-91211"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sEmblemofMeditation-94329"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sEmblemofMeditation-99840"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sEmblemofMeditation-99990"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sEmblemofTenacity-100092"
 value: {
  dps: 148375.12903
  tps: 138821.2455
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sEmblemofTenacity-91210"
 value: {
  dps: 148375.12903
  tps: 138821.2455
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sEmblemofTenacity-94422"
 value: {
  dps: 148375.12903
  tps: 138821.2455
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sEmblemofTenacity-99839"
 value: {
  dps: 148375.12903
  tps: 138821.2455
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sInsigniaofConquest-100026"
 value: {
  dps: 157476.81755
  tps: 147071.71527
  hps: 8175.61622
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sInsigniaofDominance-100152"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalGladiator'sInsigniaofVictory-100085"
 value: {
  dps: 151038.40368
  tps: 141136.4353
  hps: 7916.06493
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-TyrannicalPrimalDiamond"
 value: {
  dps: 154946.7837
  tps: 144776.42259
  hps: 8050.74443
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-UnerringVisionofLeiShen-96930"
 value: {
  dps: 149821.38994
  tps: 140144.13266
  hps: 7800.62617
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-VaporshieldMedallion-93262"
 value: {
  dps: 150492.31049
  tps: 140919.58311
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-VialofDragon'sBlood-87063"
 value: {
  dps: 150392.00344
  tps: 140832.13977
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-VialofIchorousBlood-100963"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-VialofIchorousBlood-81264"
 value: {
  dps: 148350.84264
  tps: 138796.95911
  hps: 7657.05423
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-ViciousTalismanoftheShado-PanAssault-94511"
 value: {
  dps: 160560.64329
  tps: 149603.4515
  hps: 8310.37956
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-VisionofthePredator-81192"
 value: {
  dps: 150212.08354
  tps: 140449.64925
  hps: 7728.60271
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-VolatileTalismanoftheShado-PanAssault-94510"
 value: {
  dps: 150349.2531
  tps: 140861.48913
  hps: 7638.76463
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-WindsweptPages-81125"
 value: {
  dps: 153479.41966
  tps: 143480.92738
  hps: 7956.7209
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-WoundripperMedallion-93253"
 value: {
  dps: 155596.77784
  tps: 145315.08844
  hps: 8224.19062
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Wushoolay'sFinalChoice-96785"
 value: {
  dps: 149462.84415
  tps: 139836.36225
  hps: 7657.83696
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Xing-Ho,BreathofYu'lon-102246"
 value: {
  dps: 157015.6127
  tps: 147116.35289
  hps: 7776.90828
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-YaungolFireCarrier-86518"
 value: {
  dps: 158503.65469
  tps: 148223.32016
  hps: 8194.05379
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-Yu'lon'sBite-103987"
 value: {
  dps: 152097.08063
  tps: 142134.125
  hps: 7934.60309
 }
}
dps_results: {
 key: "TestWindwalker-AllItems-ZenAlchemistStone-75274"
 value: {
  dps: 157059.60959
  tps: 146801.01819
  hps: 8063.47524
 }
}
dps_results: {
 key: "TestWindwalker-Average-Default"
 value: {
  dps: 160235.09506
  tps: 149493.08908
  hps: 8406.61771
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_bis_2h-Basic-default-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 1.10787823964e+06
  tps: 411683.40581
  hps: 7555.66889
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_bis_2h-Basic-default-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 153723.1464
  tps: 142341.8248
  hps: 8053.11006
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_bis_2h-Basic-default-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 196047.77419
  tps: 161507.61218
  hps: 9953.14138
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_bis_2h-Basic-default-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 883628.31238
  tps: 325357.61967
  hps: 6349.56804
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_bis_2h-Basic-default-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 116869.99832
  tps: 109744.62686
  hps: 6702.53208
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_bis_2h-Basic-default-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 130074.69814
  tps: 111504.01094
  hps: 8533.7003
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_bis_dw-Basic-default-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 1.1258415313e+06
  tps: 419217.55037
  hps: 7768.87374
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_bis_dw-Basic-default-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 162229.30884
  tps: 151337.70382
  hps: 8395.37672
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_bis_dw-Basic-default-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 204190.76475
  tps: 171947.73792
  hps: 10181.80663
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_bis_dw-Basic-default-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 906149.94633
  tps: 334158.05916
  hps: 6552.9538
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_bis_dw-Basic-default-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 121545.01592
  tps: 114720.43908
  hps: 6997.06763
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_bis_dw-Basic-default-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 132209.86293
  tps: 114156.62478
  hps: 8715.26769
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_prebis_2h-Basic-default-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 762927.31463
  tps: 276986.4279
  hps: 6275.61068
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_prebis_2h-Basic-default-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 104897.92868
  tps: 95643.02468
  hps: 6379.36274
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_prebis_2h-Basic-default-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 142071.13664
  tps: 115088.03502
  hps: 8046.67086
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_prebis_2h-Basic-default-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 606218.54531
  tps: 217788.18886
  hps: 5128.56373
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_prebis_2h-Basic-default-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 77569.51212
  tps: 71758.37639
  hps: 5218.1076
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_prebis_2h-Basic-default-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 90383.18201
  tps: 75523.80806
  hps: 6309.67705
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_prebis_dw-Basic-default-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 771893.44768
  tps: 282403.01432
  hps: 6195.56336
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_prebis_dw-Basic-default-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 110022.34128
  tps: 101249.18479
  hps: 6541.36753
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_prebis_dw-Basic-default-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 146884.75413
  tps: 121009.09847
  hps: 8211.44832
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_prebis_dw-Basic-default-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 615764.27737
  tps: 223276.82959
  hps: 5204.6361
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_prebis_dw-Basic-default-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 81243.28755
  tps: 75775.16066
  hps: 5384.54267
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Orc-p1_prebis_dw-Basic-default-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 92057.05726
  tps: 77791.07182
  hps: 6265.63367
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_bis_2h-Basic-default-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 1.08387407604e+06
  tps: 408002.21842
  hps: 7568.06612
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_bis_2h-Basic-default-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 154187.20284
  tps: 143072.85383
  hps: 7947.15609
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_bis_2h-Basic-default-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 195146.61766
  tps: 161665.62788
  hps: 9880.00931
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_bis_2h-Basic-default-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 859337.25927
  tps: 320082.70781
  hps: 6095.63813
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_bis_2h-Basic-default-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 116659.23228
  tps: 109698.84896
  hps: 6644.1453
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_bis_2h-Basic-default-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 130258.48663
  tps: 112171.17404
  hps: 8400.95909
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_bis_dw-Basic-default-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 1.08054640906e+06
  tps: 412677.33877
  hps: 7903.11957
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_bis_dw-Basic-default-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 160690.25095
  tps: 150142.72808
  hps: 8316.81731
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_bis_dw-Basic-default-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 202369.74797
  tps: 171105.77214
  hps: 9765.57422
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_bis_dw-Basic-default-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 866170.58996
  tps: 324200.23646
  hps: 6484.31038
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_bis_dw-Basic-default-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 120439.4943
  tps: 113777.86641
  hps: 6879.27125
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_bis_dw-Basic-default-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 131680.19501
  tps: 114381.35743
  hps: 8334.2669
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_prebis_2h-Basic-default-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 763023.88116
  tps: 279948.08483
  hps: 5972.8871
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_prebis_2h-Basic-default-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 104778.84215
  tps: 95690.63534
  hps: 6272.55748
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_prebis_2h-Basic-default-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 140323.10741
  tps: 113597.56104
  hps: 7806.11464
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_prebis_2h-Basic-default-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 609557.73703
  tps: 223985.85123
  hps: 4979.33696
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_prebis_2h-Basic-default-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 77201.14278
  tps: 71597.47352
  hps: 5132.50445
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_prebis_2h-Basic-default-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 89817.51211
  tps: 75643.47134
  hps: 6649.83416
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_prebis_dw-Basic-default-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 773651.61687
  tps: 287613.62365
  hps: 6112.5987
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_prebis_dw-Basic-default-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 110463.58765
  tps: 101740.13863
  hps: 6437.61671
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_prebis_dw-Basic-default-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 147624.8219
  tps: 121938.6581
  hps: 8040.24671
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_prebis_dw-Basic-default-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 613008.70624
  tps: 225438.70474
  hps: 5081.50961
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_prebis_dw-Basic-default-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 81747.6935
  tps: 76390.90496
  hps: 5324.1731
 }
}
dps_results: {
 key: "TestWindwalker-Settings-Troll-p1_prebis_dw-Basic-default-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 92695.52105
  tps: 79013.03774
  hps: 6541.00717
 }
}
dps_results: {
 key: "TestWindwalker-SwitchInFrontOfTarget-Default"
 value: {
  dps: 137495.52817
  tps: 127212.26829
  hps: 8237.87952
 }
}
