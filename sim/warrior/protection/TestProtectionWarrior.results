character_stats_results: {
 key: "TestProtectionWarrior-CharacterStats-Default"
 value: {
  final_stats: 10881.15
  final_stats: 139.65
  final_stats: 21337.47
  final_stats: 40.95
  final_stats: 69.01
  final_stats: 2551
  final_stats: 1412
  final_stats: 0
  final_stats: 2330
  final_stats: 4520.00001
  final_stats: 14900.63141
  final_stats: 4718
  final_stats: 24213.53
  final_stats: 0
  final_stats: 0
  final_stats: 0
  final_stats: 704
  final_stats: 47299
  final_stats: 0
  final_stats: 445127.58
  final_stats: 0
  final_stats: 0
  final_stats: 7.50294
  final_stats: 14.35588
  final_stats: 12.3673
  final_stats: 7.35333
  final_stats: 24.52433
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-AgilePrimalDiamond"
 value: {
  dps: 107872.16603
  tps: 668370.82626
  dtps: 26322.65089
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-AlacrityofXuen-103989"
 value: {
  dps: 107936.26667
  tps: 670031.0353
  dtps: 25835.36963
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-ArcaneBadgeoftheShieldwall-93347"
 value: {
  dps: 104842.36382
  tps: 650757.58298
  dtps: 26507.30907
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-ArrowflightMedallion-93258"
 value: {
  dps: 107251.047
  tps: 666734.36799
  dtps: 26343.78336
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-AssuranceofConsequence-105472"
 value: {
  dps: 105752.7467
  tps: 656152.32632
  dtps: 26550.97762
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-AusterePrimalDiamond"
 value: {
  dps: 105882.53761
  tps: 655921.2248
  dtps: 26422.54849
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-BadJuju-96781"
 value: {
  dps: 104975.24729
  tps: 651205.85908
  dtps: 26307.30593
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-BadgeofKypariZar-84079"
 value: {
  dps: 105882.91111
  tps: 657980.76618
  dtps: 26615.47528
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-BattleplateofResoundingRings"
 value: {
  dps: 108331.37264
  tps: 672809.41078
  dtps: 27271.66032
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-BattleplateoftheLastMogu"
 value: {
  dps: 121829.57042
  tps: 757846.86108
  dtps: 26626.24995
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-BattleplateofthePrehistoricMarauder"
 value: {
  dps: 110261.19438
  tps: 685504.53562
  dtps: 26819.50798
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-BlossomofPureSnow-89081"
 value: {
  dps: 107090.74312
  tps: 665075.28526
  dtps: 26381.85747
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-BottleofInfiniteStars-87057"
 value: {
  dps: 104069.09268
  tps: 645740.41031
  dtps: 26579.83216
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-BraidofTenSongs-84072"
 value: {
  dps: 103980.83252
  tps: 645423.5633
  dtps: 26550.44221
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-Brawler'sStatue-87571"
 value: {
  dps: 106499.48061
  tps: 660511.79523
  dtps: 26270.86479
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-BreathoftheHydra-96827"
 value: {
  dps: 105097.15077
  tps: 653953.67864
  dtps: 26804.65931
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-BroochofMunificentDeeds-87500"
 value: {
  dps: 104563.72505
  tps: 646804.45967
  dtps: 26963.70476
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-BrutalTalismanoftheShado-PanAssault-94508"
 value: {
  dps: 113720.39675
  tps: 704968.59645
  dtps: 25881.68502
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-BurningPrimalDiamond"
 value: {
  dps: 107863.97506
  tps: 668313.48946
  dtps: 26322.65089
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-CapacitivePrimalDiamond"
 value: {
  dps: 106401.49801
  tps: 659137.02702
  dtps: 26291.35451
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-Cha-Ye'sEssenceofBrilliance-96888"
 value: {
  dps: 107966.43803
  tps: 670648.50118
  dtps: 26773.73456
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-CharmofTenSongs-84071"
 value: {
  dps: 105848.09191
  tps: 658047.61398
  dtps: 26730.40979
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-CommunalIdolofDestruction-101168"
 value: {
  dps: 104318.17719
  tps: 647046.00242
  dtps: 26480.41126
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-CommunalStoneofDestruction-101171"
 value: {
  dps: 104205.29213
  tps: 644533.3206
  dtps: 25646.50336
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-CommunalStoneofWisdom-101183"
 value: {
  dps: 104909.21765
  tps: 650839.75133
  dtps: 26595.20556
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-ContemplationofChi-Ji-103688"
 value: {
  dps: 104909.21765
  tps: 650839.75133
  dtps: 26595.20556
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-ContemplationofChi-Ji-103988"
 value: {
  dps: 104909.21765
  tps: 650839.75133
  dtps: 26595.20556
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-Coren'sColdChromiumCoaster-87574"
 value: {
  dps: 107075.24857
  tps: 663666.16928
  dtps: 26773.59667
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-CoreofDecency-87497"
 value: {
  dps: 104909.21765
  tps: 650839.75133
  dtps: 26595.20556
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-CourageousPrimalDiamond"
 value: {
  dps: 106381.24196
  tps: 659758.59463
  dtps: 26322.65089
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-CraftedDreadfulGladiator'sBadgeofConquest-93419"
 value: {
  dps: 104318.3881
  tps: 646909.76365
  dtps: 26477.57521
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-CraftedDreadfulGladiator'sBadgeofDominance-93600"
 value: {
  dps: 103935.9714
  tps: 644252.96624
  dtps: 26516.73867
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-CraftedDreadfulGladiator'sBadgeofVictory-93606"
 value: {
  dps: 107192.43162
  tps: 663619.64162
  dtps: 26178.72041
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-CraftedDreadfulGladiator'sEmblemofCruelty-93485"
 value: {
  dps: 106329.89215
  tps: 659511.54167
  dtps: 26607.27024
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-CraftedDreadfulGladiator'sEmblemofMeditation-93487"
 value: {
  dps: 105172.16967
  tps: 652194.81853
  dtps: 26736.6155
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-CraftedDreadfulGladiator'sEmblemofTenacity-93486"
 value: {
  dps: 104647.36734
  tps: 648927.75557
  dtps: 26880.58023
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-CraftedDreadfulGladiator'sInsigniaofConquest-93424"
 value: {
  dps: 104640.26533
  tps: 648845.04469
  dtps: 26637.69579
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-CraftedDreadfulGladiator'sInsigniaofDominance-93601"
 value: {
  dps: 104909.21765
  tps: 650839.75133
  dtps: 26595.20556
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-CraftedDreadfulGladiator'sInsigniaofVictory-93611"
 value: {
  dps: 107046.92712
  tps: 663555.79497
  dtps: 26147.84259
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-CraftedMalevolentGladiator'sBadgeofConquest-98755"
 value: {
  dps: 104318.3881
  tps: 646909.76365
  dtps: 26477.57521
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-CraftedMalevolentGladiator'sBadgeofDominance-98910"
 value: {
  dps: 103935.9714
  tps: 644252.96624
  dtps: 26516.73867
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-CraftedMalevolentGladiator'sBadgeofVictory-98912"
 value: {
  dps: 107433.18359
  tps: 665045.47058
  dtps: 26241.19588
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-CraftedMalevolentGladiator'sEmblemofCruelty-98811"
 value: {
  dps: 106866.36109
  tps: 663240.74234
  dtps: 26609.23402
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-CraftedMalevolentGladiator'sEmblemofMeditation-98813"
 value: {
  dps: 105172.16967
  tps: 652194.81853
  dtps: 26736.6155
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-CraftedMalevolentGladiator'sEmblemofTenacity-98812"
 value: {
  dps: 104611.43146
  tps: 648650.74457
  dtps: 26917.56303
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-CraftedMalevolentGladiator'sInsigniaofConquest-98760"
 value: {
  dps: 105425.05918
  tps: 653354.91834
  dtps: 26566.29408
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-CraftedMalevolentGladiator'sInsigniaofDominance-98911"
 value: {
  dps: 104909.21765
  tps: 650839.75133
  dtps: 26595.20556
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-CraftedMalevolentGladiator'sInsigniaofVictory-98917"
 value: {
  dps: 109012.76425
  tps: 677081.12252
  dtps: 26033.75988
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-CurseofHubris-102307"
 value: {
  dps: 107849.36555
  tps: 669909.956
  dtps: 27093.3437
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-CurseofHubris-104649"
 value: {
  dps: 107653.56685
  tps: 668481.63705
  dtps: 27119.6313
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-CurseofHubris-104898"
 value: {
  dps: 106559.35782
  tps: 661361.60237
  dtps: 26999.01045
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-CurseofHubris-105147"
 value: {
  dps: 105894.54442
  tps: 657332.16015
  dtps: 27134.25357
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-CurseofHubris-105396"
 value: {
  dps: 107604.2447
  tps: 668225.33206
  dtps: 27052.04725
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-CurseofHubris-105645"
 value: {
  dps: 107650.88861
  tps: 668051.20032
  dtps: 27275.31835
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-CutstitcherMedallion-93255"
 value: {
  dps: 104830.10871
  tps: 650394.43401
  dtps: 26395.43442
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-Daelo'sFinalWords-87496"
 value: {
  dps: 107897.998
  tps: 668474.02295
  dtps: 26344.31162
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-DarkglowEmbroidery(Rank3)-4893"
 value: {
  dps: 107816.27062
  tps: 668371.94254
  dtps: 26409.80713
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-DarkmistVortex-87172"
 value: {
  dps: 106848.71163
  tps: 659778.96924
  dtps: 26319.82722
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-DeadeyeBadgeoftheShieldwall-93346"
 value: {
  dps: 103924.40882
  tps: 645673.10763
  dtps: 24818.5142
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-DelicateVialoftheSanguinaire-96895"
 value: {
  dps: 107489.80588
  tps: 667662.24754
  dtps: 25950.32791
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-DestructivePrimalDiamond"
 value: {
  dps: 106284.42621
  tps: 658357.43997
  dtps: 26354.22786
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-DisciplineofXuen-103986"
 value: {
  dps: 105900.61375
  tps: 657821.4558
  dtps: 25183.1957
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-Dominator'sArcaneBadge-93342"
 value: {
  dps: 104842.36382
  tps: 650757.58298
  dtps: 26507.30907
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-Dominator'sDeadeyeBadge-93341"
 value: {
  dps: 103924.40882
  tps: 645673.10763
  dtps: 24818.5142
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-Dominator'sDurableBadge-93345"
 value: {
  dps: 105427.24461
  tps: 654040.57508
  dtps: 24616.09389
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-Dominator'sKnightlyBadge-93344"
 value: {
  dps: 106580.58575
  tps: 661492.91707
  dtps: 24641.78404
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-Dominator'sMendingBadge-93343"
 value: {
  dps: 104830.10871
  tps: 650394.43401
  dtps: 26395.43442
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-DreadfulGladiator'sBadgeofConquest-84344"
 value: {
  dps: 104318.3881
  tps: 646909.76365
  dtps: 26477.57521
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-DreadfulGladiator'sBadgeofDominance-84488"
 value: {
  dps: 103935.9714
  tps: 644252.96624
  dtps: 26516.73867
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-DreadfulGladiator'sBadgeofVictory-84490"
 value: {
  dps: 107192.43162
  tps: 663619.64162
  dtps: 26178.72041
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-DreadfulGladiator'sEmblemofCruelty-84399"
 value: {
  dps: 106329.89215
  tps: 659511.54167
  dtps: 26607.27024
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-DreadfulGladiator'sEmblemofMeditation-84401"
 value: {
  dps: 105172.16967
  tps: 652194.81853
  dtps: 26736.6155
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-DreadfulGladiator'sEmblemofTenacity-84400"
 value: {
  dps: 104647.36734
  tps: 648927.75557
  dtps: 26880.58023
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-DreadfulGladiator'sInsigniaofConquest-84349"
 value: {
  dps: 105237.67006
  tps: 653122.84824
  dtps: 26581.8039
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-DreadfulGladiator'sInsigniaofDominance-84489"
 value: {
  dps: 104909.21765
  tps: 650839.75133
  dtps: 26595.20556
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-DreadfulGladiator'sInsigniaofVictory-84495"
 value: {
  dps: 107823.46853
  tps: 669315.90393
  dtps: 26038.50166
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-DurableBadgeoftheShieldwall-93350"
 value: {
  dps: 105427.24461
  tps: 654040.57508
  dtps: 24616.09389
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-EffulgentPrimalDiamond"
 value: {
  dps: 105757.27115
  tps: 655856.39026
  dtps: 26517.98341
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-EmberPrimalDiamond"
 value: {
  dps: 106381.24196
  tps: 659758.59463
  dtps: 26322.65089
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-EmblemofKypariZar-84077"
 value: {
  dps: 104764.44624
  tps: 650364.41146
  dtps: 27018.88476
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-EmblemoftheCatacombs-83733"
 value: {
  dps: 103852.57551
  tps: 643917.12996
  dtps: 25454.36815
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-EmptyFruitBarrel-81133"
 value: {
  dps: 104909.21765
  tps: 650839.75133
  dtps: 26595.20556
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-EnchantWeapon-BloodyDancingSteel-5125"
 value: {
  dps: 110315.53343
  tps: 684914.22976
  dtps: 26087.3843
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-EnchantWeapon-Colossus-4445"
 value: {
  dps: 106731.08934
  tps: 660791.92969
  dtps: 26166.20844
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-EnchantWeapon-DancingSteel-4444"
 value: {
  dps: 110454.46984
  tps: 685562.93282
  dtps: 26356.9484
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-EnchantWeapon-JadeSpirit-4442"
 value: {
  dps: 108000.59962
  tps: 670444.78272
  dtps: 26719.51916
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-EnchantWeapon-SpiritofConquest-5124"
 value: {
  dps: 108000.59962
  tps: 670444.78272
  dtps: 26719.51916
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-EnchantWeapon-Windsong-4441"
 value: {
  dps: 107916.77641
  tps: 669247.83798
  dtps: 25548.98482
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-EnigmaticPrimalDiamond"
 value: {
  dps: 106284.42621
  tps: 658357.43997
  dtps: 26354.22786
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-EssenceofTerror-87175"
 value: {
  dps: 104909.21765
  tps: 650839.75133
  dtps: 26595.20556
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-EternalPrimalDiamond"
 value: {
  dps: 106555.39326
  tps: 660725.12968
  dtps: 26222.80157
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-EvilEyeofGalakras-105491"
 value: {
  dps: 119335.59122
  tps: 704989.28947
  dtps: 26292.93176
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-FabledFeatherofJi-Kun-96842"
 value: {
  dps: 107486.00755
  tps: 669121.58409
  dtps: 26170.30459
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-FearwurmBadge-84074"
 value: {
  dps: 105213.40896
  tps: 653891.95093
  dtps: 26794.14014
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-FearwurmRelic-84070"
 value: {
  dps: 106623.58562
  tps: 661558.28979
  dtps: 26577.25986
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-FelsoulIdolofDestruction-101263"
 value: {
  dps: 104318.17719
  tps: 647046.00242
  dtps: 26480.41126
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-FelsoulStoneofDestruction-101266"
 value: {
  dps: 104105.22394
  tps: 644257.68237
  dtps: 25577.47332
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-Fen-Yu,FuryofXuen-102248"
 value: {
  dps: 111240.83084
  tps: 693747.37184
  dtps: 26476.6288
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-FlashfrozenResinGlobule-100951"
 value: {
  dps: 104441.33487
  tps: 646979.56436
  dtps: 26559.18745
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-FlashfrozenResinGlobule-81263"
 value: {
  dps: 104441.33487
  tps: 646979.56436
  dtps: 26559.18745
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-FlashingSteelTalisman-81265"
 value: {
  dps: 105249.77332
  tps: 652739.82167
  dtps: 26349.83409
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-FleetPrimalDiamond"
 value: {
  dps: 106593.00064
  tps: 660859.18035
  dtps: 26249.5928
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-ForlornPrimalDiamond"
 value: {
  dps: 106381.24196
  tps: 659758.59463
  dtps: 26322.65089
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-FortitudeoftheZandalari-94516"
 value: {
  dps: 104343.16686
  tps: 646595.49409
  dtps: 26664.69098
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-FortitudeoftheZandalari-95677"
 value: {
  dps: 104264.18418
  tps: 646195.33796
  dtps: 26710.96374
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-FortitudeoftheZandalari-96049"
 value: {
  dps: 104343.54131
  tps: 646624.11331
  dtps: 26677.12926
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-FortitudeoftheZandalari-96421"
 value: {
  dps: 104343.54131
  tps: 646624.11331
  dtps: 26677.12926
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-FortitudeoftheZandalari-96793"
 value: {
  dps: 104215.53452
  tps: 645869.97644
  dtps: 26588.69126
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-GazeoftheTwins-96915"
 value: {
  dps: 110960.89998
  tps: 688698.7462
  dtps: 26000.83826
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-Gerp'sPerfectArrow-87495"
 value: {
  dps: 105807.87374
  tps: 655785.13859
  dtps: 26463.25873
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-Gong-Lu,StrengthofXuen-102249"
 value: {
  dps: 116126.07899
  tps: 726025.61836
  dtps: 25731.10421
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-GrievousGladiator'sBadgeofConquest-100195"
 value: {
  dps: 104022.26793
  tps: 644846.96398
  dtps: 26509.72529
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-GrievousGladiator'sBadgeofConquest-100603"
 value: {
  dps: 104022.26793
  tps: 644846.96398
  dtps: 26509.72529
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-GrievousGladiator'sBadgeofConquest-102856"
 value: {
  dps: 104022.26793
  tps: 644846.96398
  dtps: 26509.72529
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-GrievousGladiator'sBadgeofConquest-103145"
 value: {
  dps: 104022.26793
  tps: 644846.96398
  dtps: 26509.72529
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-GrievousGladiator'sBadgeofDominance-100490"
 value: {
  dps: 103935.9714
  tps: 644252.96624
  dtps: 26516.73867
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-GrievousGladiator'sBadgeofDominance-100576"
 value: {
  dps: 103935.9714
  tps: 644252.96624
  dtps: 26516.73867
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-GrievousGladiator'sBadgeofDominance-102830"
 value: {
  dps: 103935.9714
  tps: 644252.96624
  dtps: 26516.73867
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-GrievousGladiator'sBadgeofDominance-103308"
 value: {
  dps: 103935.9714
  tps: 644252.96624
  dtps: 26516.73867
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-GrievousGladiator'sBadgeofVictory-100500"
 value: {
  dps: 108046.21987
  tps: 668710.5671
  dtps: 26115.63171
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-GrievousGladiator'sBadgeofVictory-100579"
 value: {
  dps: 108046.21987
  tps: 668710.5671
  dtps: 26115.63171
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-GrievousGladiator'sBadgeofVictory-102833"
 value: {
  dps: 108046.21987
  tps: 668710.5671
  dtps: 26115.63171
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-GrievousGladiator'sBadgeofVictory-103314"
 value: {
  dps: 108046.21987
  tps: 668710.5671
  dtps: 26115.63171
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-GrievousGladiator'sEmblemofCruelty-100305"
 value: {
  dps: 107110.23401
  tps: 664932.32047
  dtps: 26791.11859
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-GrievousGladiator'sEmblemofCruelty-100626"
 value: {
  dps: 107110.23401
  tps: 664932.32047
  dtps: 26791.11859
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-GrievousGladiator'sEmblemofCruelty-102877"
 value: {
  dps: 107110.23401
  tps: 664932.32047
  dtps: 26791.11859
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-GrievousGladiator'sEmblemofCruelty-103210"
 value: {
  dps: 107110.23401
  tps: 664932.32047
  dtps: 26791.11859
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-GrievousGladiator'sEmblemofMeditation-100307"
 value: {
  dps: 105172.16967
  tps: 652194.81853
  dtps: 26736.6155
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-GrievousGladiator'sEmblemofMeditation-100559"
 value: {
  dps: 105172.16967
  tps: 652194.81853
  dtps: 26736.6155
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-GrievousGladiator'sEmblemofMeditation-102813"
 value: {
  dps: 105172.16967
  tps: 652194.81853
  dtps: 26736.6155
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-GrievousGladiator'sEmblemofMeditation-103212"
 value: {
  dps: 105172.16967
  tps: 652194.81853
  dtps: 26736.6155
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-GrievousGladiator'sEmblemofTenacity-100306"
 value: {
  dps: 103783.1386
  tps: 643418.67644
  dtps: 26825.49088
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-GrievousGladiator'sEmblemofTenacity-100652"
 value: {
  dps: 103783.1386
  tps: 643418.67644
  dtps: 26825.49088
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-GrievousGladiator'sEmblemofTenacity-102903"
 value: {
  dps: 103783.1386
  tps: 643418.67644
  dtps: 26825.49088
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-GrievousGladiator'sEmblemofTenacity-103211"
 value: {
  dps: 103783.1386
  tps: 643418.67644
  dtps: 26825.49088
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-GrievousGladiator'sInsigniaofConquest-103150"
 value: {
  dps: 105064.78969
  tps: 652165.69955
  dtps: 26669.51662
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-GrievousGladiator'sInsigniaofDominance-103309"
 value: {
  dps: 104909.21765
  tps: 650839.75133
  dtps: 26595.20556
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-GrievousGladiator'sInsigniaofVictory-103319"
 value: {
  dps: 109982.08616
  tps: 683473.60692
  dtps: 25922.18018
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-Hawkmaster'sTalon-89082"
 value: {
  dps: 103951.51146
  tps: 647136.97671
  dtps: 26763.38422
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-Heart-LesionDefenderIdol-100999"
 value: {
  dps: 105709.37456
  tps: 655197.55717
  dtps: 26480.07524
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-Heart-LesionDefenderStone-101002"
 value: {
  dps: 104627.1232
  tps: 649110.16193
  dtps: 25525.50386
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-Heart-LesionIdolofBattle-100991"
 value: {
  dps: 109714.44225
  tps: 680966.56326
  dtps: 26474.16779
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-Heart-LesionStoneofBattle-100990"
 value: {
  dps: 106025.7442
  tps: 656798.00594
  dtps: 24936.28906
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-HeartofFire-81181"
 value: {
  dps: 104958.409
  tps: 650154.53119
  dtps: 26407.86371
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-HeartwarmerMedallion-93260"
 value: {
  dps: 104830.10871
  tps: 650394.43401
  dtps: 26395.43442
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-HelmbreakerMedallion-93261"
 value: {
  dps: 109210.87463
  tps: 678395.66479
  dtps: 26185.50519
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-Horridon'sLastGasp-96757"
 value: {
  dps: 104909.21765
  tps: 650839.75133
  dtps: 26595.20556
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-ImpassivePrimalDiamond"
 value: {
  dps: 106284.42621
  tps: 658357.43997
  dtps: 26354.22786
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-IndomitablePrimalDiamond"
 value: {
  dps: 105757.27115
  tps: 655856.39026
  dtps: 26517.98341
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-InscribedBagofHydra-Spawn-96828"
 value: {
  dps: 104909.21765
  tps: 650839.75133
  dtps: 26595.20556
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-InsigniaofKypariZar-84078"
 value: {
  dps: 106809.09585
  tps: 663760.11727
  dtps: 26459.86068
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-IronBellyWok-89083"
 value: {
  dps: 106512.39099
  tps: 663517.38845
  dtps: 26175.7401
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-IronProtectorTalisman-85181"
 value: {
  dps: 104663.13282
  tps: 647902.36281
  dtps: 26601.42289
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-JadeBanditFigurine-86043"
 value: {
  dps: 103951.51146
  tps: 647136.97671
  dtps: 26763.38422
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-JadeBanditFigurine-86772"
 value: {
  dps: 103698.61277
  tps: 643663.93095
  dtps: 26617.96574
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-JadeCharioteerFigurine-86042"
 value: {
  dps: 106512.39099
  tps: 663517.38845
  dtps: 26175.7401
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-JadeCharioteerFigurine-86771"
 value: {
  dps: 105663.80406
  tps: 654569.25583
  dtps: 26526.87198
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-JadeCourtesanFigurine-86045"
 value: {
  dps: 104830.10871
  tps: 650394.43401
  dtps: 26395.43442
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-JadeCourtesanFigurine-86774"
 value: {
  dps: 104830.10871
  tps: 650394.43401
  dtps: 26395.43442
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-JadeMagistrateFigurine-86044"
 value: {
  dps: 107090.74312
  tps: 665075.28526
  dtps: 26381.85747
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-JadeMagistrateFigurine-86773"
 value: {
  dps: 107315.94057
  tps: 666182.62493
  dtps: 26343.37223
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-JadeWarlordFigurine-86046"
 value: {
  dps: 104364.17824
  tps: 647742.04712
  dtps: 24883.52166
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-JadeWarlordFigurine-86775"
 value: {
  dps: 104736.16492
  tps: 650543.54811
  dtps: 24770.42648
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-Ji-Kun'sRisingWinds-96843"
 value: {
  dps: 109970.83321
  tps: 682646.89394
  dtps: 26085.50826
  hps: 70.49019
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-KnightlyBadgeoftheShieldwall-93349"
 value: {
  dps: 106580.58575
  tps: 661492.91707
  dtps: 24641.78404
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-KnotofTenSongs-84073"
 value: {
  dps: 106149.67056
  tps: 657761.6635
  dtps: 26373.76935
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-Kor'kronBookofHurting-92785"
 value: {
  dps: 103589.36748
  tps: 642208.17811
  dtps: 26627.75036
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-Lao-Chin'sLiquidCourage-89079"
 value: {
  dps: 104364.17824
  tps: 647742.04712
  dtps: 24883.52166
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-LeiShen'sFinalOrders-87072"
 value: {
  dps: 107348.63617
  tps: 666396.17174
  dtps: 26590.1711
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-LessonsoftheDarkmaster-81268"
 value: {
  dps: 109317.37866
  tps: 677426.55476
  dtps: 26457.5044
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-LightdrinkerIdolofRage-101200"
 value: {
  dps: 104068.13087
  tps: 645594.33079
  dtps: 26567.55672
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-LightdrinkerStoneofRage-101203"
 value: {
  dps: 104638.99966
  tps: 648136.98245
  dtps: 25437.98941
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-LightoftheCosmos-87065"
 value: {
  dps: 104079.93358
  tps: 645254.17807
  dtps: 26710.91875
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-LightweaveEmbroidery(Rank3)-4892"
 value: {
  dps: 107816.27062
  tps: 668371.94254
  dtps: 26409.80713
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-LordBlastington'sScopeofDoom-4699"
 value: {
  dps: 108000.59962
  tps: 670444.78272
  dtps: 26719.51916
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-MalevolentGladiator'sBadgeofConquest-84934"
 value: {
  dps: 104013.31844
  tps: 644784.30793
  dtps: 26509.72529
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-MalevolentGladiator'sBadgeofConquest-91452"
 value: {
  dps: 104318.3881
  tps: 646909.76365
  dtps: 26477.57521
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-MalevolentGladiator'sBadgeofDominance-84940"
 value: {
  dps: 103935.9714
  tps: 644252.96624
  dtps: 26516.73867
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-MalevolentGladiator'sBadgeofDominance-91753"
 value: {
  dps: 103935.9714
  tps: 644252.96624
  dtps: 26516.73867
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-MalevolentGladiator'sBadgeofVictory-84942"
 value: {
  dps: 107576.04009
  tps: 665827.27481
  dtps: 26239.31661
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-MalevolentGladiator'sBadgeofVictory-91763"
 value: {
  dps: 107433.18359
  tps: 665045.47058
  dtps: 26241.19588
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-MalevolentGladiator'sEmblemofCruelty-84936"
 value: {
  dps: 107149.09838
  tps: 665122.15315
  dtps: 26675.14686
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-MalevolentGladiator'sEmblemofCruelty-91562"
 value: {
  dps: 106866.36109
  tps: 663240.74234
  dtps: 26609.23402
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-MalevolentGladiator'sEmblemofMeditation-84939"
 value: {
  dps: 105172.16967
  tps: 652194.81853
  dtps: 26736.6155
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-MalevolentGladiator'sEmblemofMeditation-91564"
 value: {
  dps: 105172.16967
  tps: 652194.81853
  dtps: 26736.6155
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-MalevolentGladiator'sEmblemofTenacity-84938"
 value: {
  dps: 104319.04501
  tps: 646899.34029
  dtps: 26890.00531
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-MalevolentGladiator'sEmblemofTenacity-91563"
 value: {
  dps: 104611.43146
  tps: 648650.74457
  dtps: 26917.56303
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-MalevolentGladiator'sInsigniaofConquest-91457"
 value: {
  dps: 104622.36099
  tps: 648719.70478
  dtps: 26655.60958
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-MalevolentGladiator'sInsigniaofDominance-91754"
 value: {
  dps: 104909.21765
  tps: 650839.75133
  dtps: 26595.20556
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-MalevolentGladiator'sInsigniaofVictory-91768"
 value: {
  dps: 108461.96132
  tps: 673678.3798
  dtps: 26240.13411
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-MarkoftheCatacombs-83731"
 value: {
  dps: 106561.07666
  tps: 661285.66386
  dtps: 26582.50839
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-MarkoftheHardenedGrunt-92783"
 value: {
  dps: 104909.21765
  tps: 650839.75133
  dtps: 26595.20556
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-MedallionofMystifyingVapors-93257"
 value: {
  dps: 105899.98974
  tps: 657706.10346
  dtps: 24440.21123
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-MedallionoftheCatacombs-83734"
 value: {
  dps: 105701.83165
  tps: 655792.84909
  dtps: 26166.53275
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-MendingBadgeoftheShieldwall-93348"
 value: {
  dps: 104830.10871
  tps: 650394.43401
  dtps: 26395.43442
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-MirrorScope-4700"
 value: {
  dps: 108000.59962
  tps: 670444.78272
  dtps: 26719.51916
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-MistdancerDefenderIdol-101089"
 value: {
  dps: 105180.827
  tps: 651684.31706
  dtps: 26729.45074
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-MistdancerDefenderStone-101087"
 value: {
  dps: 105102.54897
  tps: 652808.00723
  dtps: 25647.14177
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-MistdancerIdolofRage-101113"
 value: {
  dps: 104261.26404
  tps: 646519.481
  dtps: 26545.3274
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-MistdancerStoneofRage-101117"
 value: {
  dps: 103988.09166
  tps: 643223.51072
  dtps: 25560.83496
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-MistdancerStoneofWisdom-101107"
 value: {
  dps: 104909.21765
  tps: 650839.75133
  dtps: 26595.20556
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-MithrilWristwatch-87572"
 value: {
  dps: 107005.6208
  tps: 663793.22453
  dtps: 26764.47745
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-MountainsageIdolofDestruction-101069"
 value: {
  dps: 104318.17719
  tps: 647046.00242
  dtps: 26480.41126
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-MountainsageStoneofDestruction-101072"
 value: {
  dps: 104409.39318
  tps: 646485.66774
  dtps: 25561.49777
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-OathswornDefenderIdol-101303"
 value: {
  dps: 105639.98532
  tps: 656129.07709
  dtps: 26388.53277
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-OathswornDefenderStone-101306"
 value: {
  dps: 104759.33125
  tps: 649207.23846
  dtps: 25674.5478
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-OathswornIdolofBattle-101295"
 value: {
  dps: 109840.08818
  tps: 682389.87429
  dtps: 26271.99571
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-OathswornStoneofBattle-101294"
 value: {
  dps: 106864.93636
  tps: 663504.16839
  dtps: 25051.02342
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-PhaseFingers-4697"
 value: {
  dps: 107747.48925
  tps: 666953.53545
  dtps: 26365.86218
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-PlateofResoundingRings"
 value: {
  dps: 105413.87323
  tps: 654938.33403
  dtps: 25833.77294
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-PlateoftheLastMogu"
 value: {
  dps: 110064.42543
  tps: 683039.27618
  dtps: 25991.23504
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-PlateofthePrehistoricMarauder"
 value: {
  dps: 111839.24218
  tps: 697813.62156
  dtps: 27520.08717
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-PouchofWhiteAsh-103639"
 value: {
  dps: 103589.36748
  tps: 642208.17811
  dtps: 26627.75036
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-PowerfulPrimalDiamond"
 value: {
  dps: 105757.27115
  tps: 655856.39026
  dtps: 26517.98341
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-PriceofProgress-81266"
 value: {
  dps: 104909.21765
  tps: 650839.75133
  dtps: 26595.20556
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-PridefulGladiator'sBadgeofConquest-102659"
 value: {
  dps: 104217.46442
  tps: 646109.96763
  dtps: 26486.36241
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-PridefulGladiator'sBadgeofConquest-103342"
 value: {
  dps: 104217.46442
  tps: 646109.96763
  dtps: 26486.36241
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-PridefulGladiator'sBadgeofDominance-102633"
 value: {
  dps: 103935.9714
  tps: 644252.96624
  dtps: 26516.73867
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-PridefulGladiator'sBadgeofDominance-103505"
 value: {
  dps: 103935.9714
  tps: 644252.96624
  dtps: 26516.73867
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-PridefulGladiator'sBadgeofVictory-102636"
 value: {
  dps: 109503.93205
  tps: 677675.23541
  dtps: 25916.79124
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-PridefulGladiator'sBadgeofVictory-103511"
 value: {
  dps: 109503.93205
  tps: 677675.23541
  dtps: 25916.79124
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-PridefulGladiator'sEmblemofCruelty-102680"
 value: {
  dps: 108638.14333
  tps: 675312.25241
  dtps: 26534.1052
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-PridefulGladiator'sEmblemofCruelty-103407"
 value: {
  dps: 108638.14333
  tps: 675312.25241
  dtps: 26534.1052
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-PridefulGladiator'sEmblemofMeditation-102616"
 value: {
  dps: 105172.16967
  tps: 652194.81853
  dtps: 26736.6155
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-PridefulGladiator'sEmblemofMeditation-103409"
 value: {
  dps: 105172.16967
  tps: 652194.81853
  dtps: 26736.6155
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-PridefulGladiator'sEmblemofTenacity-102706"
 value: {
  dps: 103650.51155
  tps: 642342.05871
  dtps: 26816.40716
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-PridefulGladiator'sEmblemofTenacity-103408"
 value: {
  dps: 103650.51155
  tps: 642342.05871
  dtps: 26816.40716
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-PridefulGladiator'sInsigniaofConquest-103347"
 value: {
  dps: 105123.23503
  tps: 652296.84561
  dtps: 26687.60034
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-PridefulGladiator'sInsigniaofDominance-103506"
 value: {
  dps: 104909.21765
  tps: 650839.75133
  dtps: 26595.20556
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-PridefulGladiator'sInsigniaofVictory-103516"
 value: {
  dps: 109946.66442
  tps: 681706.62514
  dtps: 25886.60331
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-Primordius'TalismanofRage-96873"
 value: {
  dps: 112377.69951
  tps: 698203.6772
  dtps: 26107.63501
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-Qian-Le,CourageofNiuzao-102245"
 value: {
  dps: 109737.57088
  tps: 682677.88582
  dtps: 26107.33709
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-Qian-Ying,FortitudeofNiuzao-102250"
 value: {
  dps: 112563.43164
  tps: 699372.19103
  dtps: 25532.23833
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-Qin-xi'sPolarizingSeal-87075"
 value: {
  dps: 104909.21765
  tps: 650839.75133
  dtps: 26595.20556
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-RelicofChi-Ji-79330"
 value: {
  dps: 104909.21765
  tps: 650839.75133
  dtps: 26595.20556
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-RelicofKypariZar-84075"
 value: {
  dps: 103414.88287
  tps: 641017.8483
  dtps: 26991.26425
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-RelicofXuen-79327"
 value: {
  dps: 109267.9398
  tps: 678700.2462
  dtps: 26088.58897
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-RelicofXuen-79328"
 value: {
  dps: 104711.40111
  tps: 649612.11836
  dtps: 26603.16735
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-RelicofYu'lon-79331"
 value: {
  dps: 104909.21765
  tps: 650839.75133
  dtps: 26595.20556
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-Renataki'sSoulCharm-96741"
 value: {
  dps: 110118.34299
  tps: 683752.67
  dtps: 26022.54626
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-ResolveofNiuzao-103690"
 value: {
  dps: 105251.65291
  tps: 652520.01813
  dtps: 26407.56291
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-ResolveofNiuzao-103990"
 value: {
  dps: 106839.86788
  tps: 662713.02705
  dtps: 26035.06092
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-ReverberatingPrimalDiamond"
 value: {
  dps: 108060.49701
  tps: 669738.61417
  dtps: 26379.4982
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-RevitalizingPrimalDiamond"
 value: {
  dps: 107863.97506
  tps: 668313.48946
  dtps: 26322.65089
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-SI:7Operative'sManual-92784"
 value: {
  dps: 103589.36748
  tps: 642208.17811
  dtps: 26627.75036
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-ScrollofReveredAncestors-89080"
 value: {
  dps: 104830.10871
  tps: 650394.43401
  dtps: 26395.43442
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-SearingWords-81267"
 value: {
  dps: 107571.69005
  tps: 667745.65175
  dtps: 26768.78618
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-Shock-ChargerMedallion-93259"
 value: {
  dps: 104190.17661
  tps: 646004.87159
  dtps: 26724.03322
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-SigilofCompassion-83736"
 value: {
  dps: 104426.16604
  tps: 647502.93126
  dtps: 26605.37178
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-SigilofDevotion-83740"
 value: {
  dps: 105763.31577
  tps: 656581.11085
  dtps: 24959.17613
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-SigilofFidelity-83737"
 value: {
  dps: 105065.61626
  tps: 651980.20462
  dtps: 26903.53294
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-SigilofGrace-83738"
 value: {
  dps: 105483.78253
  tps: 655942.91965
  dtps: 26308.11965
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-SigilofKypariZar-84076"
 value: {
  dps: 105527.09775
  tps: 654425.69719
  dtps: 25040.11299
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-SigilofPatience-83739"
 value: {
  dps: 104937.77147
  tps: 650907.83912
  dtps: 24837.03688
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-SigiloftheCatacombs-83732"
 value: {
  dps: 105573.73056
  tps: 656682.80503
  dtps: 26638.11198
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-SinisterPrimalDiamond"
 value: {
  dps: 106401.49801
  tps: 659137.02702
  dtps: 26291.35451
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-SkullrenderMedallion-93256"
 value: {
  dps: 109210.87463
  tps: 678395.66479
  dtps: 26185.50519
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-SpiritsoftheSun-87163"
 value: {
  dps: 104909.21765
  tps: 650839.75133
  dtps: 26595.20556
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-SpringrainIdolofDestruction-101023"
 value: {
  dps: 104318.17719
  tps: 647046.00242
  dtps: 26480.41126
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-SpringrainIdolofRage-101009"
 value: {
  dps: 105123.20972
  tps: 651913.89657
  dtps: 26419.22016
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-SpringrainStoneofDestruction-101026"
 value: {
  dps: 103503.17702
  tps: 640374.12551
  dtps: 25484.74795
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-SpringrainStoneofRage-101012"
 value: {
  dps: 104529.95791
  tps: 647470.25352
  dtps: 25408.70324
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-SpringrainStoneofWisdom-101041"
 value: {
  dps: 104909.21765
  tps: 650839.75133
  dtps: 26595.20556
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-Static-Caster'sMedallion-93254"
 value: {
  dps: 104190.17661
  tps: 646004.87159
  dtps: 26724.03322
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-SteadfastFootman'sMedallion-92782"
 value: {
  dps: 104909.21765
  tps: 650839.75133
  dtps: 26595.20556
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-SteadfastTalismanoftheShado-PanAssault-94507"
 value: {
  dps: 105492.08132
  tps: 654729.56906
  dtps: 26416.7601
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-StreamtalkerIdolofDestruction-101222"
 value: {
  dps: 104318.17719
  tps: 647046.00242
  dtps: 26480.41126
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-StreamtalkerIdolofRage-101217"
 value: {
  dps: 104087.97475
  tps: 645492.72171
  dtps: 26526.72294
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-StreamtalkerStoneofDestruction-101225"
 value: {
  dps: 103253.96137
  tps: 639275.40698
  dtps: 25556.99784
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-StreamtalkerStoneofRage-101220"
 value: {
  dps: 104447.15017
  tps: 646679.24869
  dtps: 25573.40187
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-StreamtalkerStoneofWisdom-101250"
 value: {
  dps: 104909.21765
  tps: 650839.75133
  dtps: 26595.20556
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-StuffofNightmares-87160"
 value: {
  dps: 106338.22333
  tps: 659593.233
  dtps: 26187.67301
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-SunsoulDefenderIdol-101160"
 value: {
  dps: 105014.93086
  tps: 652066.58427
  dtps: 26383.37897
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-SunsoulDefenderStone-101163"
 value: {
  dps: 105830.60335
  tps: 657735.92489
  dtps: 25497.75544
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-SunsoulIdolofBattle-101152"
 value: {
  dps: 110581.38627
  tps: 686942.67825
  dtps: 26173.23993
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-SunsoulStoneofBattle-101151"
 value: {
  dps: 107493.11616
  tps: 666803.44175
  dtps: 24901.70638
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-SunsoulStoneofWisdom-101138"
 value: {
  dps: 104909.21765
  tps: 650839.75133
  dtps: 26595.20556
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-SwordguardEmbroidery(Rank3)-4894"
 value: {
  dps: 109155.53041
  tps: 677470.10784
  dtps: 26370.60729
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-SymboloftheCatacombs-83735"
 value: {
  dps: 105480.82023
  tps: 654734.4864
  dtps: 26533.01684
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-SynapseSprings(MarkII)-4898"
 value: {
  dps: 107747.48925
  tps: 666953.53545
  dtps: 26365.86218
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-TalismanofBloodlust-96864"
 value: {
  dps: 104704.09111
  tps: 650459.95126
  dtps: 26864.03918
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-TerrorintheMists-87167"
 value: {
  dps: 107206.96854
  tps: 666040.03011
  dtps: 26459.609
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-TheGloamingBlade-88149"
 value: {
  dps: 108060.49701
  tps: 669738.61417
  dtps: 26379.4982
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-Thousand-YearPickledEgg-87573"
 value: {
  dps: 104909.21765
  tps: 650839.75133
  dtps: 26595.20556
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-TrailseekerIdolofRage-101054"
 value: {
  dps: 104031.27514
  tps: 644925.64546
  dtps: 26540.81528
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-TrailseekerStoneofRage-101057"
 value: {
  dps: 104560.76337
  tps: 647732.26803
  dtps: 25486.24457
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-TyrannicalGladiator'sBadgeofConquest-100043"
 value: {
  dps: 104013.31844
  tps: 644784.30793
  dtps: 26509.72529
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-TyrannicalGladiator'sBadgeofConquest-91099"
 value: {
  dps: 104013.31844
  tps: 644784.30793
  dtps: 26509.72529
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-TyrannicalGladiator'sBadgeofConquest-94373"
 value: {
  dps: 104013.31844
  tps: 644784.30793
  dtps: 26509.72529
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-TyrannicalGladiator'sBadgeofConquest-99772"
 value: {
  dps: 104013.31844
  tps: 644784.30793
  dtps: 26509.72529
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-TyrannicalGladiator'sBadgeofDominance-100016"
 value: {
  dps: 103935.9714
  tps: 644252.96624
  dtps: 26516.73867
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-TyrannicalGladiator'sBadgeofDominance-91400"
 value: {
  dps: 103935.9714
  tps: 644252.96624
  dtps: 26516.73867
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-TyrannicalGladiator'sBadgeofDominance-94346"
 value: {
  dps: 103935.9714
  tps: 644252.96624
  dtps: 26516.73867
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-TyrannicalGladiator'sBadgeofDominance-99937"
 value: {
  dps: 103935.9714
  tps: 644252.96624
  dtps: 26516.73867
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-TyrannicalGladiator'sBadgeofVictory-100019"
 value: {
  dps: 107220.69839
  tps: 663001.49167
  dtps: 26291.44449
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-TyrannicalGladiator'sBadgeofVictory-91410"
 value: {
  dps: 107220.69839
  tps: 663001.49167
  dtps: 26291.44449
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-TyrannicalGladiator'sBadgeofVictory-94349"
 value: {
  dps: 107220.69839
  tps: 663001.49167
  dtps: 26291.44449
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-TyrannicalGladiator'sBadgeofVictory-99943"
 value: {
  dps: 107220.69839
  tps: 663001.49167
  dtps: 26291.44449
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-TyrannicalGladiator'sEmblemofCruelty-100066"
 value: {
  dps: 106811.56389
  tps: 662438.86851
  dtps: 26699.59835
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-TyrannicalGladiator'sEmblemofCruelty-91209"
 value: {
  dps: 106811.56389
  tps: 662438.86851
  dtps: 26699.59835
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-TyrannicalGladiator'sEmblemofCruelty-94396"
 value: {
  dps: 106811.56389
  tps: 662438.86851
  dtps: 26699.59835
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-TyrannicalGladiator'sEmblemofCruelty-99838"
 value: {
  dps: 106811.56389
  tps: 662438.86851
  dtps: 26699.59835
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-TyrannicalGladiator'sEmblemofMeditation-91211"
 value: {
  dps: 105172.16967
  tps: 652194.81853
  dtps: 26736.6155
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-TyrannicalGladiator'sEmblemofMeditation-94329"
 value: {
  dps: 105172.16967
  tps: 652194.81853
  dtps: 26736.6155
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-TyrannicalGladiator'sEmblemofMeditation-99840"
 value: {
  dps: 105172.16967
  tps: 652194.81853
  dtps: 26736.6155
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-TyrannicalGladiator'sEmblemofMeditation-99990"
 value: {
  dps: 105172.16967
  tps: 652194.81853
  dtps: 26736.6155
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-TyrannicalGladiator'sEmblemofTenacity-100092"
 value: {
  dps: 104114.89254
  tps: 645768.50276
  dtps: 26732.15528
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-TyrannicalGladiator'sEmblemofTenacity-91210"
 value: {
  dps: 104114.89254
  tps: 645768.50276
  dtps: 26732.15528
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-TyrannicalGladiator'sEmblemofTenacity-94422"
 value: {
  dps: 104114.89254
  tps: 645768.50276
  dtps: 26732.15528
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-TyrannicalGladiator'sEmblemofTenacity-99839"
 value: {
  dps: 104114.89254
  tps: 645768.50276
  dtps: 26732.15528
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-TyrannicalGladiator'sInsigniaofConquest-100026"
 value: {
  dps: 105038.30719
  tps: 651216.42722
  dtps: 26559.37842
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-TyrannicalGladiator'sInsigniaofDominance-100152"
 value: {
  dps: 104909.21765
  tps: 650839.75133
  dtps: 26595.20556
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-TyrannicalGladiator'sInsigniaofVictory-100085"
 value: {
  dps: 108183.52439
  tps: 671614.25821
  dtps: 26070.74177
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-TyrannicalPrimalDiamond"
 value: {
  dps: 106381.24196
  tps: 659758.59463
  dtps: 26322.65089
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-UnerringVisionofLeiShen-96930"
 value: {
  dps: 108445.72606
  tps: 673510.71818
  dtps: 26450.62072
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-VaporshieldMedallion-93262"
 value: {
  dps: 105899.98974
  tps: 657706.10346
  dtps: 24440.21123
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-VialofDragon'sBlood-87063"
 value: {
  dps: 106132.14211
  tps: 659070.40668
  dtps: 25873.01204
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-VialofIchorousBlood-100963"
 value: {
  dps: 104909.21765
  tps: 650839.75133
  dtps: 26595.20556
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-VialofIchorousBlood-81264"
 value: {
  dps: 104909.21765
  tps: 650839.75133
  dtps: 26595.20556
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-ViciousTalismanoftheShado-PanAssault-94511"
 value: {
  dps: 105315.29625
  tps: 653499.97636
  dtps: 26620.6449
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-VisionofthePredator-81192"
 value: {
  dps: 104909.21765
  tps: 650839.75133
  dtps: 26595.20556
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-VolatileTalismanoftheShado-PanAssault-94510"
 value: {
  dps: 104909.21765
  tps: 650839.75133
  dtps: 26595.20556
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-WindsweptPages-81125"
 value: {
  dps: 104738.16933
  tps: 649227.85035
  dtps: 26863.03535
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-WoundripperMedallion-93253"
 value: {
  dps: 107251.047
  tps: 666734.36799
  dtps: 26343.78336
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-Wushoolay'sFinalChoice-96785"
 value: {
  dps: 104932.96838
  tps: 650839.75133
  dtps: 26595.20556
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-Xing-Ho,BreathofYu'lon-102246"
 value: {
  dps: 106393.61022
  tps: 658629.89869
  dtps: 26520.41248
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-YaungolFireCarrier-86518"
 value: {
  dps: 108060.49701
  tps: 669738.61417
  dtps: 26379.4982
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-Yu'lon'sBite-103987"
 value: {
  dps: 104909.21765
  tps: 650839.75133
  dtps: 26595.20556
 }
}
dps_results: {
 key: "TestProtectionWarrior-AllItems-ZenAlchemistStone-75274"
 value: {
  dps: 107768.80869
  tps: 667428.44262
  dtps: 26003.96808
 }
}
dps_results: {
 key: "TestProtectionWarrior-Average-Default"
 value: {
  dps: 110512.30898
  tps: 683947.09367
  dtps: 27229.20598
 }
}
dps_results: {
 key: "TestProtectionWarrior-Settings-Human-garajal_default-garajal_default-garajal_default-FullBuffs-15.0yards-LongMultiTarget"
 value: {
  dps: 3.27110712864e+06
  tps: 2.102501470463e+07
  dtps: 1.29005155106e+06
 }
}
dps_results: {
 key: "TestProtectionWarrior-Settings-Human-garajal_default-garajal_default-garajal_default-FullBuffs-15.0yards-LongSingleTarget"
 value: {
  dps: 132075.86921
  tps: 854245.90893
  dtps: 36704.38288
 }
}
dps_results: {
 key: "TestProtectionWarrior-Settings-Human-garajal_default-garajal_default-garajal_default-FullBuffs-15.0yards-ShortSingleTarget"
 value: {
  dps: 125330.49699
  tps: 822545.66364
  dtps: 40151.55863
 }
}
dps_results: {
 key: "TestProtectionWarrior-Settings-Human-garajal_default-garajal_default-garajal_default-NoBuffs-15.0yards-LongMultiTarget"
 value: {
  dps: 2.23624217799e+06
  tps: 1.369350481829e+07
  dtps: 1.39397708898e+06
 }
}
dps_results: {
 key: "TestProtectionWarrior-Settings-Human-garajal_default-garajal_default-garajal_default-NoBuffs-15.0yards-LongSingleTarget"
 value: {
  dps: 113767.04222
  tps: 735412.45349
  dtps: 43633.01206
 }
}
dps_results: {
 key: "TestProtectionWarrior-Settings-Human-garajal_default-garajal_default-garajal_default-NoBuffs-15.0yards-ShortSingleTarget"
 value: {
  dps: 101368.14255
  tps: 661378.41902
  dtps: 51495.96385
 }
}
dps_results: {
 key: "TestProtectionWarrior-Settings-Human-p1_bis-Basic-default-FullBuffs-15.0yards-LongMultiTarget"
 value: {
  dps: 3.9962346047e+06
  tps: 2.568372461707e+07
  dtps: 970183.74781
 }
}
dps_results: {
 key: "TestProtectionWarrior-Settings-Human-p1_bis-Basic-default-FullBuffs-15.0yards-LongSingleTarget"
 value: {
  dps: 154652.2682
  tps: 986500.27859
  dtps: 21701.37748
 }
}
dps_results: {
 key: "TestProtectionWarrior-Settings-Human-p1_bis-Basic-default-FullBuffs-15.0yards-ShortSingleTarget"
 value: {
  dps: 148055.55093
  tps: 952299.83303
  dtps: 22803.39657
 }
}
dps_results: {
 key: "TestProtectionWarrior-Settings-Human-p1_bis-Basic-default-NoBuffs-15.0yards-LongMultiTarget"
 value: {
  dps: 2.75105340139e+06
  tps: 1.687812154501e+07
  dtps: 1.06046327476e+06
 }
}
dps_results: {
 key: "TestProtectionWarrior-Settings-Human-p1_bis-Basic-default-NoBuffs-15.0yards-LongSingleTarget"
 value: {
  dps: 132030.94834
  tps: 845692.24289
  dtps: 27472.29209
 }
}
dps_results: {
 key: "TestProtectionWarrior-Settings-Human-p1_bis-Basic-default-NoBuffs-15.0yards-ShortSingleTarget"
 value: {
  dps: 124451.48702
  tps: 800857.28987
  dtps: 30967.50421
 }
}
dps_results: {
 key: "TestProtectionWarrior-Settings-Orc-p1_bis-Basic-default-FullBuffs-15.0yards-LongMultiTarget"
 value: {
  dps: 4.00197225411e+06
  tps: 2.571105854947e+07
  dtps: 969860.70049
 }
}
dps_results: {
 key: "TestProtectionWarrior-Settings-Orc-p1_bis-Basic-default-FullBuffs-15.0yards-LongSingleTarget"
 value: {
  dps: 155692.26881
  tps: 994617.79125
  dtps: 21263.9694
 }
}
dps_results: {
 key: "TestProtectionWarrior-Settings-Orc-p1_bis-Basic-default-FullBuffs-15.0yards-ShortSingleTarget"
 value: {
  dps: 152287.09598
  tps: 964330.25174
  dtps: 22611.24968
 }
}
dps_results: {
 key: "TestProtectionWarrior-Settings-Orc-p1_bis-Basic-default-NoBuffs-15.0yards-LongMultiTarget"
 value: {
  dps: 2.78180362573e+06
  tps: 1.709616899334e+07
  dtps: 1.06284642046e+06
 }
}
dps_results: {
 key: "TestProtectionWarrior-Settings-Orc-p1_bis-Basic-default-NoBuffs-15.0yards-LongSingleTarget"
 value: {
  dps: 133171.50707
  tps: 851559.41034
  dtps: 27894.46571
 }
}
dps_results: {
 key: "TestProtectionWarrior-Settings-Orc-p1_bis-Basic-default-NoBuffs-15.0yards-ShortSingleTarget"
 value: {
  dps: 123947.28925
  tps: 790989.78735
  dtps: 31238.86154
 }
}
dps_results: {
 key: "TestProtectionWarrior-SwitchInFrontOfTarget-Default"
 value: {
  dps: 108060.49701
  tps: 669738.61417
  dtps: 26379.4982
 }
}
