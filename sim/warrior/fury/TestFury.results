character_stats_results: {
 key: "TestFury-CharacterStats-Default"
 value: {
  final_stats: 20220.9525
  final_stats: 225.75
  final_stats: 24697.2
  final_stats: 120.75
  final_stats: 148
  final_stats: 2550
  final_stats: 12353
  final_stats: 2272.5
  final_stats: 2562
  final_stats: 2e-05
  final_stats: 18621.86605
  final_stats: 7826
  final_stats: 44761.0955
  final_stats: 0
  final_stats: 0
  final_stats: 0
  final_stats: 0
  final_stats: 34190
  final_stats: 0
  final_stats: 492163.8
  final_stats: 0
  final_stats: 0
  final_stats: 7.5
  final_stats: 15.03529
  final_stats: 30.61091
  final_stats: 25.58833
  final_stats: 0
 }
}
dps_results: {
 key: "TestFury-AllItems-AgilePrimalDiamond"
 value: {
  dps: 145988.6081
  tps: 91728.63943
 }
}
dps_results: {
 key: "TestFury-AllItems-AssuranceofConsequence-105472"
 value: {
  dps: 143479.44595
  tps: 90220.60399
 }
}
dps_results: {
 key: "TestFury-AllItems-AusterePrimalDiamond"
 value: {
  dps: 143554.55736
  tps: 90141.41819
 }
}
dps_results: {
 key: "TestFury-AllItems-BattleplateofResoundingRings"
 value: {
  dps: 127473.91523
  tps: 82722.76224
 }
}
dps_results: {
 key: "TestFury-AllItems-BattleplateoftheLastMogu"
 value: {
  dps: 142425.11745
  tps: 90723.88518
 }
}
dps_results: {
 key: "TestFury-AllItems-BattleplateofthePrehistoricMarauder"
 value: {
  dps: 137591.25098
  tps: 88180.11389
 }
}
dps_results: {
 key: "TestFury-AllItems-BurningPrimalDiamond"
 value: {
  dps: 145979.34783
  tps: 91722.22868
 }
}
dps_results: {
 key: "TestFury-AllItems-CapacitivePrimalDiamond"
 value: {
  dps: 143851.95513
  tps: 90422.10303
 }
}
dps_results: {
 key: "TestFury-AllItems-CourageousPrimalDiamond"
 value: {
  dps: 143554.55736
  tps: 90141.41819
 }
}
dps_results: {
 key: "TestFury-AllItems-DelicateVialoftheSanguinaire-96895"
 value: {
  dps: 143353.94765
  tps: 90151.14277
 }
}
dps_results: {
 key: "TestFury-AllItems-DestructivePrimalDiamond"
 value: {
  dps: 143882.90667
  tps: 90372.61297
 }
}
dps_results: {
 key: "TestFury-AllItems-EffulgentPrimalDiamond"
 value: {
  dps: 143554.55736
  tps: 90141.41819
 }
}
dps_results: {
 key: "TestFury-AllItems-EmberPrimalDiamond"
 value: {
  dps: 143554.55736
  tps: 90141.41819
 }
}
dps_results: {
 key: "TestFury-AllItems-EnchantWeapon-BloodyDancingSteel-5125"
 value: {
  dps: 146818.07494
  tps: 92195.03245
 }
}
dps_results: {
 key: "TestFury-AllItems-EnchantWeapon-Colossus-4445"
 value: {
  dps: 143975.03922
  tps: 90433.46688
 }
}
dps_results: {
 key: "TestFury-AllItems-EnchantWeapon-JadeSpirit-4442"
 value: {
  dps: 143977.88879
  tps: 90433.46688
 }
}
dps_results: {
 key: "TestFury-AllItems-EnchantWeapon-River'sSong-4446"
 value: {
  dps: 143978.15814
  tps: 90433.46688
 }
}
dps_results: {
 key: "TestFury-AllItems-EnchantWeapon-SpiritofConquest-5124"
 value: {
  dps: 143975.03922
  tps: 90433.46688
 }
}
dps_results: {
 key: "TestFury-AllItems-EnchantWeapon-Windsong-4441"
 value: {
  dps: 147236.95403
  tps: 92958.64424
 }
}
dps_results: {
 key: "TestFury-AllItems-EnigmaticPrimalDiamond"
 value: {
  dps: 143882.90667
  tps: 90372.61297
 }
}
dps_results: {
 key: "TestFury-AllItems-EternalPrimalDiamond"
 value: {
  dps: 143554.55736
  tps: 90141.41819
 }
}
dps_results: {
 key: "TestFury-AllItems-EvilEyeofGalakras-105491"
 value: {
  dps: 161051.6319
  tps: 106495.61884
 }
}
dps_results: {
 key: "TestFury-AllItems-FabledFeatherofJi-Kun-96842"
 value: {
  dps: 144331.44318
  tps: 91797.58839
 }
}
dps_results: {
 key: "TestFury-AllItems-Fen-Yu,FuryofXuen-102248"
 value: {
  dps: 150729.2399
  tps: 96488.46066
 }
}
dps_results: {
 key: "TestFury-AllItems-FleetPrimalDiamond"
 value: {
  dps: 144608.19008
  tps: 90804.83792
 }
}
dps_results: {
 key: "TestFury-AllItems-ForlornPrimalDiamond"
 value: {
  dps: 143554.55736
  tps: 90141.41819
 }
}
dps_results: {
 key: "TestFury-AllItems-GazeoftheTwins-96915"
 value: {
  dps: 154492.84748
  tps: 96610.57823
 }
}
dps_results: {
 key: "TestFury-AllItems-Gong-Lu,StrengthofXuen-102249"
 value: {
  dps: 158692.41395
  tps: 102448.33382
 }
}
dps_results: {
 key: "TestFury-AllItems-Horridon'sLastGasp-96757"
 value: {
  dps: 143353.94765
  tps: 90151.14277
 }
}
dps_results: {
 key: "TestFury-AllItems-ImpassivePrimalDiamond"
 value: {
  dps: 143882.90667
  tps: 90372.61297
 }
}
dps_results: {
 key: "TestFury-AllItems-IndomitablePrimalDiamond"
 value: {
  dps: 143554.55736
  tps: 90141.41819
 }
}
dps_results: {
 key: "TestFury-AllItems-InscribedBagofHydra-Spawn-96828"
 value: {
  dps: 143353.94765
  tps: 90151.14277
 }
}
dps_results: {
 key: "TestFury-AllItems-Ji-Kun'sRisingWinds-96843"
 value: {
  dps: 143353.94765
  tps: 90151.14277
 }
}
dps_results: {
 key: "TestFury-AllItems-PhaseFingers-4697"
 value: {
  dps: 146145.08481
  tps: 91819.89078
 }
}
dps_results: {
 key: "TestFury-AllItems-PlateofResoundingRings"
 value: {
  dps: 110936.60546
  tps: 72314.29655
 }
}
dps_results: {
 key: "TestFury-AllItems-PlateoftheLastMogu"
 value: {
  dps: 118835.25796
  tps: 76846.72829
 }
}
dps_results: {
 key: "TestFury-AllItems-PlateofthePrehistoricMarauder"
 value: {
  dps: 125377.26125
  tps: 80621.23417
 }
}
dps_results: {
 key: "TestFury-AllItems-PowerfulPrimalDiamond"
 value: {
  dps: 143554.55736
  tps: 90141.41819
 }
}
dps_results: {
 key: "TestFury-AllItems-PriceofProgress-81266"
 value: {
  dps: 143353.94765
  tps: 90151.14277
 }
}
dps_results: {
 key: "TestFury-AllItems-Primordius'TalismanofRage-96873"
 value: {
  dps: 154354.22256
  tps: 97344.86223
 }
}
dps_results: {
 key: "TestFury-AllItems-Qian-Le,CourageofNiuzao-102245"
 value: {
  dps: 146690.04809
  tps: 92457.31035
 }
}
dps_results: {
 key: "TestFury-AllItems-Qian-Ying,FortitudeofNiuzao-102250"
 value: {
  dps: 148441.91496
  tps: 94084.70983
 }
}
dps_results: {
 key: "TestFury-AllItems-Renataki'sSoulCharm-96741"
 value: {
  dps: 143392.66547
  tps: 90254.32411
 }
}
dps_results: {
 key: "TestFury-AllItems-ReverberatingPrimalDiamond"
 value: {
  dps: 146745.68086
  tps: 92180.81592
 }
}
dps_results: {
 key: "TestFury-AllItems-RevitalizingPrimalDiamond"
 value: {
  dps: 145979.34783
  tps: 91722.22868
 }
}
dps_results: {
 key: "TestFury-AllItems-SinisterPrimalDiamond"
 value: {
  dps: 143851.95513
  tps: 90422.10303
 }
}
dps_results: {
 key: "TestFury-AllItems-SynapseSprings(MarkII)-4898"
 value: {
  dps: 147713.09191
  tps: 92863.32368
 }
}
dps_results: {
 key: "TestFury-AllItems-TalismanofBloodlust-96864"
 value: {
  dps: 145383.71123
  tps: 92394.90503
 }
}
dps_results: {
 key: "TestFury-AllItems-TheGloamingBlade-88149"
 value: {
  dps: 146745.68086
  tps: 92180.81592
 }
}
dps_results: {
 key: "TestFury-AllItems-TyrannicalPrimalDiamond"
 value: {
  dps: 143554.55736
  tps: 90141.41819
 }
}
dps_results: {
 key: "TestFury-AllItems-UnerringVisionofLeiShen-96930"
 value: {
  dps: 146478.43303
  tps: 91895.18986
 }
}
dps_results: {
 key: "TestFury-AllItems-Wushoolay'sFinalChoice-96785"
 value: {
  dps: 144331.44318
  tps: 91797.58839
 }
}
dps_results: {
 key: "TestFury-AllItems-Xing-Ho,BreathofYu'lon-102246"
 value: {
  dps: 146033.94681
  tps: 92667.1873
 }
}
dps_results: {
 key: "TestFury-AllItems-YaungolFireCarrier-86518"
 value: {
  dps: 146745.68086
  tps: 92180.81592
 }
}
dps_results: {
 key: "TestFury-AllItems-ZenAlchemistStone-75274"
 value: {
  dps: 150227.65868
  tps: 94459.99913
 }
}
dps_results: {
 key: "TestFury-Average-Default"
 value: {
  dps: 147015.09971
  tps: 93108.93279
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_smf-DefaultTalents-Basic-default-FullBuffs-5.0yards-LongMultiTarget"
 value: {
  dps: 350987.02674
  tps: 237784.74311
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_smf-DefaultTalents-Basic-default-FullBuffs-5.0yards-LongSingleTarget"
 value: {
  dps: 141403.57105
  tps: 90038.82427
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_smf-DefaultTalents-Basic-default-FullBuffs-5.0yards-ShortSingleTarget"
 value: {
  dps: 173402.73496
  tps: 111457.92763
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_smf-DefaultTalents-Basic-default-NoBuffs-5.0yards-LongMultiTarget"
 value: {
  dps: 275188.24801
  tps: 181586.51185
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_smf-DefaultTalents-Basic-default-NoBuffs-5.0yards-LongSingleTarget"
 value: {
  dps: 101806.50928
  tps: 65870.4992
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_smf-DefaultTalents-Basic-default-NoBuffs-5.0yards-ShortSingleTarget"
 value: {
  dps: 111450.37916
  tps: 72286.11254
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_smf-Single-Minded Fury-Basic-default-FullBuffs-5.0yards-LongMultiTarget"
 value: {
  dps: 334589.73246
  tps: 232250.7942
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_smf-Single-Minded Fury-Basic-default-FullBuffs-5.0yards-LongSingleTarget"
 value: {
  dps: 140589.37178
  tps: 91078.39925
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_smf-Single-Minded Fury-Basic-default-FullBuffs-5.0yards-ShortSingleTarget"
 value: {
  dps: 172160.95579
  tps: 111926.67508
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_smf-Single-Minded Fury-Basic-default-NoBuffs-5.0yards-LongMultiTarget"
 value: {
  dps: 251371.14691
  tps: 176735.69187
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_smf-Single-Minded Fury-Basic-default-NoBuffs-5.0yards-LongSingleTarget"
 value: {
  dps: 101194.33287
  tps: 66648.37621
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_smf-Single-Minded Fury-Basic-default-NoBuffs-5.0yards-ShortSingleTarget"
 value: {
  dps: 109756.04552
  tps: 70992.08802
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_tg-DefaultTalents-Basic-default-FullBuffs-5.0yards-LongMultiTarget"
 value: {
  dps: 384006.076
  tps: 250778.94091
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_tg-DefaultTalents-Basic-default-FullBuffs-5.0yards-LongSingleTarget"
 value: {
  dps: 148316.23278
  tps: 93224.24882
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_tg-DefaultTalents-Basic-default-FullBuffs-5.0yards-ShortSingleTarget"
 value: {
  dps: 181903.7254
  tps: 114784.42862
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_tg-DefaultTalents-Basic-default-NoBuffs-5.0yards-LongMultiTarget"
 value: {
  dps: 303861.55885
  tps: 193508.3041
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_tg-DefaultTalents-Basic-default-NoBuffs-5.0yards-LongSingleTarget"
 value: {
  dps: 108905.31915
  tps: 69991.57864
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_tg-DefaultTalents-Basic-default-NoBuffs-5.0yards-ShortSingleTarget"
 value: {
  dps: 120868.9604
  tps: 75902.49981
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_tg-Single-Minded Fury-Basic-default-FullBuffs-5.0yards-LongMultiTarget"
 value: {
  dps: 346361.20845
  tps: 247507.60267
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_tg-Single-Minded Fury-Basic-default-FullBuffs-5.0yards-LongSingleTarget"
 value: {
  dps: 146985.60536
  tps: 95706.37828
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_tg-Single-Minded Fury-Basic-default-FullBuffs-5.0yards-ShortSingleTarget"
 value: {
  dps: 179301.10176
  tps: 113875.5884
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_tg-Single-Minded Fury-Basic-default-NoBuffs-5.0yards-LongMultiTarget"
 value: {
  dps: 264828.14303
  tps: 192302.93188
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_tg-Single-Minded Fury-Basic-default-NoBuffs-5.0yards-LongSingleTarget"
 value: {
  dps: 107024.67506
  tps: 70428.77401
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_tg-Single-Minded Fury-Basic-default-NoBuffs-5.0yards-ShortSingleTarget"
 value: {
  dps: 116625.87828
  tps: 75543.11121
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_smf-DefaultTalents-Basic-default-FullBuffs-5.0yards-LongMultiTarget"
 value: {
  dps: 345159.63105
  tps: 237357.03329
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_smf-DefaultTalents-Basic-default-FullBuffs-5.0yards-LongSingleTarget"
 value: {
  dps: 142454.11267
  tps: 90394.33276
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_smf-DefaultTalents-Basic-default-FullBuffs-5.0yards-ShortSingleTarget"
 value: {
  dps: 175139.81723
  tps: 108946.36557
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_smf-DefaultTalents-Basic-default-NoBuffs-5.0yards-LongMultiTarget"
 value: {
  dps: 277350.18614
  tps: 179016.06224
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_smf-DefaultTalents-Basic-default-NoBuffs-5.0yards-LongSingleTarget"
 value: {
  dps: 102254.98128
  tps: 66480.85253
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_smf-DefaultTalents-Basic-default-NoBuffs-5.0yards-ShortSingleTarget"
 value: {
  dps: 112491.23935
  tps: 71131.76952
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_smf-Single-Minded Fury-Basic-default-FullBuffs-5.0yards-LongMultiTarget"
 value: {
  dps: 339943.88113
  tps: 235705.55042
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_smf-Single-Minded Fury-Basic-default-FullBuffs-5.0yards-LongSingleTarget"
 value: {
  dps: 142549.86642
  tps: 91506.44047
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_smf-Single-Minded Fury-Basic-default-FullBuffs-5.0yards-ShortSingleTarget"
 value: {
  dps: 175473.93331
  tps: 110629.9778
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_smf-Single-Minded Fury-Basic-default-NoBuffs-5.0yards-LongMultiTarget"
 value: {
  dps: 252498.11217
  tps: 177882.88377
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_smf-Single-Minded Fury-Basic-default-NoBuffs-5.0yards-LongSingleTarget"
 value: {
  dps: 102954.18973
  tps: 67228.52502
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_smf-Single-Minded Fury-Basic-default-NoBuffs-5.0yards-ShortSingleTarget"
 value: {
  dps: 110339.22198
  tps: 70839.59811
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_tg-DefaultTalents-Basic-default-FullBuffs-5.0yards-LongMultiTarget"
 value: {
  dps: 394826.77542
  tps: 254229.77458
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_tg-DefaultTalents-Basic-default-FullBuffs-5.0yards-LongSingleTarget"
 value: {
  dps: 147796.30324
  tps: 92502.94346
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_tg-DefaultTalents-Basic-default-FullBuffs-5.0yards-ShortSingleTarget"
 value: {
  dps: 177652.01043
  tps: 111769.03997
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_tg-DefaultTalents-Basic-default-NoBuffs-5.0yards-LongMultiTarget"
 value: {
  dps: 309277.13009
  tps: 192427.08856
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_tg-DefaultTalents-Basic-default-NoBuffs-5.0yards-LongSingleTarget"
 value: {
  dps: 110967.11793
  tps: 70320.12493
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_tg-DefaultTalents-Basic-default-NoBuffs-5.0yards-ShortSingleTarget"
 value: {
  dps: 120040.1344
  tps: 74696.80061
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_tg-Single-Minded Fury-Basic-default-FullBuffs-5.0yards-LongMultiTarget"
 value: {
  dps: 346335.20873
  tps: 245793.68896
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_tg-Single-Minded Fury-Basic-default-FullBuffs-5.0yards-LongSingleTarget"
 value: {
  dps: 146750.90057
  tps: 95216.98962
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_tg-Single-Minded Fury-Basic-default-FullBuffs-5.0yards-ShortSingleTarget"
 value: {
  dps: 176526.06321
  tps: 112664.47766
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_tg-Single-Minded Fury-Basic-default-NoBuffs-5.0yards-LongMultiTarget"
 value: {
  dps: 262975.33636
  tps: 190578.30634
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_tg-Single-Minded Fury-Basic-default-NoBuffs-5.0yards-LongSingleTarget"
 value: {
  dps: 107983.50759
  tps: 70784.90421
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_tg-Single-Minded Fury-Basic-default-NoBuffs-5.0yards-ShortSingleTarget"
 value: {
  dps: 116953.82843
  tps: 74434.02513
 }
}
dps_results: {
 key: "TestFury-SwitchInFrontOfTarget-Default"
 value: {
  dps: 132862.32859
  tps: 85253.37061
 }
}
