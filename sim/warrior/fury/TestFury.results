character_stats_results: {
 key: "TestFury-CharacterStats-Default"
 value: {
  final_stats: 20220.9525
  final_stats: 225.75
  final_stats: 24697.2
  final_stats: 120.75
  final_stats: 148
  final_stats: 2550
  final_stats: 12353
  final_stats: 2272.5
  final_stats: 2562
  final_stats: 2e-05
  final_stats: 18621.86605
  final_stats: 7826
  final_stats: 44761.0955
  final_stats: 0
  final_stats: 0
  final_stats: 0
  final_stats: 0
  final_stats: 34190
  final_stats: 0
  final_stats: 492163.8
  final_stats: 0
  final_stats: 0
  final_stats: 7.5
  final_stats: 15.03529
  final_stats: 30.61091
  final_stats: 25.58833
  final_stats: 0
 }
}
dps_results: {
 key: "TestFury-AllItems-AgilePrimalDiamond"
 value: {
  dps: 141579.4385
  tps: 88717.08786
 }
}
dps_results: {
 key: "TestFury-AllItems-AssuranceofConsequence-105472"
 value: {
  dps: 139253.30198
  tps: 87288.41432
 }
}
dps_results: {
 key: "TestFury-AllItems-AusterePrimalDiamond"
 value: {
  dps: 139259.92306
  tps: 87214.10564
 }
}
dps_results: {
 key: "TestFury-AllItems-BattleplateofResoundingRings"
 value: {
  dps: 124503.51073
  tps: 80455.28563
 }
}
dps_results: {
 key: "TestFury-AllItems-BattleplateoftheLastMogu"
 value: {
  dps: 141370.04418
  tps: 89688.12016
 }
}
dps_results: {
 key: "TestFury-AllItems-BattleplateofthePrehistoricMarauder"
 value: {
  dps: 133585.13423
  tps: 84751.62607
 }
}
dps_results: {
 key: "TestFury-AllItems-BurningPrimalDiamond"
 value: {
  dps: 141567.52086
  tps: 88705.17022
 }
}
dps_results: {
 key: "TestFury-AllItems-CapacitivePrimalDiamond"
 value: {
  dps: 140318.36749
  tps: 88183.94299
 }
}
dps_results: {
 key: "TestFury-AllItems-CourageousPrimalDiamond"
 value: {
  dps: 139259.92306
  tps: 87214.10564
 }
}
dps_results: {
 key: "TestFury-AllItems-DelicateVialoftheSanguinaire-96895"
 value: {
  dps: 139026.61828
  tps: 87174.59468
 }
}
dps_results: {
 key: "TestFury-AllItems-DestructivePrimalDiamond"
 value: {
  dps: 140264.09886
  tps: 88399.61789
 }
}
dps_results: {
 key: "TestFury-AllItems-EffulgentPrimalDiamond"
 value: {
  dps: 139259.92306
  tps: 87214.10564
 }
}
dps_results: {
 key: "TestFury-AllItems-EmberPrimalDiamond"
 value: {
  dps: 139259.92306
  tps: 87214.10564
 }
}
dps_results: {
 key: "TestFury-AllItems-EnchantWeapon-BloodyDancingSteel-5125"
 value: {
  dps: 142457.27245
  tps: 89247.49645
 }
}
dps_results: {
 key: "TestFury-AllItems-EnchantWeapon-Colossus-4445"
 value: {
  dps: 139673.64837
  tps: 87532.94037
 }
}
dps_results: {
 key: "TestFury-AllItems-EnchantWeapon-JadeSpirit-4442"
 value: {
  dps: 139677.11326
  tps: 87532.94037
 }
}
dps_results: {
 key: "TestFury-AllItems-EnchantWeapon-River'sSong-4446"
 value: {
  dps: 139679.59511
  tps: 87532.94037
 }
}
dps_results: {
 key: "TestFury-AllItems-EnchantWeapon-SpiritofConquest-5124"
 value: {
  dps: 139673.4285
  tps: 87532.94037
 }
}
dps_results: {
 key: "TestFury-AllItems-EnchantWeapon-Windsong-4441"
 value: {
  dps: 141297.66608
  tps: 89217.15882
 }
}
dps_results: {
 key: "TestFury-AllItems-EnigmaticPrimalDiamond"
 value: {
  dps: 140264.09886
  tps: 88399.61789
 }
}
dps_results: {
 key: "TestFury-AllItems-EternalPrimalDiamond"
 value: {
  dps: 139259.92306
  tps: 87214.10564
 }
}
dps_results: {
 key: "TestFury-AllItems-EvilEyeofGalakras-105491"
 value: {
  dps: 156834.32094
  tps: 103282.02011
 }
}
dps_results: {
 key: "TestFury-AllItems-FabledFeatherofJi-Kun-96842"
 value: {
  dps: 140369.22251
  tps: 89184.07151
 }
}
dps_results: {
 key: "TestFury-AllItems-FleetPrimalDiamond"
 value: {
  dps: 140280.01216
  tps: 87858.01769
 }
}
dps_results: {
 key: "TestFury-AllItems-ForlornPrimalDiamond"
 value: {
  dps: 139259.92306
  tps: 87214.10564
 }
}
dps_results: {
 key: "TestFury-AllItems-GazeoftheTwins-96915"
 value: {
  dps: 149640.30832
  tps: 93253.9133
 }
}
dps_results: {
 key: "TestFury-AllItems-Horridon'sLastGasp-96757"
 value: {
  dps: 139026.61828
  tps: 87174.59468
 }
}
dps_results: {
 key: "TestFury-AllItems-ImpassivePrimalDiamond"
 value: {
  dps: 140264.09886
  tps: 88399.61789
 }
}
dps_results: {
 key: "TestFury-AllItems-IndomitablePrimalDiamond"
 value: {
  dps: 139259.92306
  tps: 87214.10564
 }
}
dps_results: {
 key: "TestFury-AllItems-InscribedBagofHydra-Spawn-96828"
 value: {
  dps: 139026.61828
  tps: 87174.59468
 }
}
dps_results: {
 key: "TestFury-AllItems-Ji-Kun'sRisingWinds-96843"
 value: {
  dps: 139026.61828
  tps: 87174.59468
 }
}
dps_results: {
 key: "TestFury-AllItems-PhaseFingers-4697"
 value: {
  dps: 141725.24899
  tps: 88800.31411
 }
}
dps_results: {
 key: "TestFury-AllItems-PlateofResoundingRings"
 value: {
  dps: 107990.56428
  tps: 70385.82148
 }
}
dps_results: {
 key: "TestFury-AllItems-PlateoftheLastMogu"
 value: {
  dps: 116564.60998
  tps: 75080.4306
 }
}
dps_results: {
 key: "TestFury-AllItems-PlateofthePrehistoricMarauder"
 value: {
  dps: 120820.98658
  tps: 78133.89861
 }
}
dps_results: {
 key: "TestFury-AllItems-PowerfulPrimalDiamond"
 value: {
  dps: 139259.92306
  tps: 87214.10564
 }
}
dps_results: {
 key: "TestFury-AllItems-PriceofProgress-81266"
 value: {
  dps: 139026.61828
  tps: 87174.59468
 }
}
dps_results: {
 key: "TestFury-AllItems-Primordius'TalismanofRage-96873"
 value: {
  dps: 151487.34378
  tps: 94479.92941
 }
}
dps_results: {
 key: "TestFury-AllItems-Renataki'sSoulCharm-96741"
 value: {
  dps: 139949.09209
  tps: 87846.96279
 }
}
dps_results: {
 key: "TestFury-AllItems-ReverberatingPrimalDiamond"
 value: {
  dps: 142309.18972
  tps: 89151.93281
 }
}
dps_results: {
 key: "TestFury-AllItems-RevitalizingPrimalDiamond"
 value: {
  dps: 141567.52086
  tps: 88705.17022
 }
}
dps_results: {
 key: "TestFury-AllItems-SinisterPrimalDiamond"
 value: {
  dps: 140318.36749
  tps: 88183.94299
 }
}
dps_results: {
 key: "TestFury-AllItems-SynapseSprings(MarkII)-4898"
 value: {
  dps: 143255.47202
  tps: 89826.76466
 }
}
dps_results: {
 key: "TestFury-AllItems-TalismanofBloodlust-96864"
 value: {
  dps: 141338.93121
  tps: 90004.43264
 }
}
dps_results: {
 key: "TestFury-AllItems-TheGloamingBlade-88149"
 value: {
  dps: 142309.18972
  tps: 89151.93281
 }
}
dps_results: {
 key: "TestFury-AllItems-TyrannicalPrimalDiamond"
 value: {
  dps: 139259.92306
  tps: 87214.10564
 }
}
dps_results: {
 key: "TestFury-AllItems-UnerringVisionofLeiShen-96930"
 value: {
  dps: 143584.72852
  tps: 89747.89425
 }
}
dps_results: {
 key: "TestFury-AllItems-Wushoolay'sFinalChoice-96785"
 value: {
  dps: 140369.22251
  tps: 89184.07151
 }
}
dps_results: {
 key: "TestFury-AllItems-YaungolFireCarrier-86518"
 value: {
  dps: 142309.18972
  tps: 89151.93281
 }
}
dps_results: {
 key: "TestFury-AllItems-ZenAlchemistStone-75274"
 value: {
  dps: 145660.39162
  tps: 91339.85127
 }
}
dps_results: {
 key: "TestFury-Average-Default"
 value: {
  dps: 143977.52189
  tps: 90762.22966
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_smf-DefaultTalents-Basic-default-FullBuffs-5.0yards-LongMultiTarget"
 value: {
  dps: 345592.36295
  tps: 227910.93901
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_smf-DefaultTalents-Basic-default-FullBuffs-5.0yards-LongSingleTarget"
 value: {
  dps: 135210.96734
  tps: 85352.06227
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_smf-DefaultTalents-Basic-default-FullBuffs-5.0yards-ShortSingleTarget"
 value: {
  dps: 168393.56882
  tps: 107209.00681
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_smf-DefaultTalents-Basic-default-NoBuffs-5.0yards-LongMultiTarget"
 value: {
  dps: 276142.10316
  tps: 173825.67598
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_smf-DefaultTalents-Basic-default-NoBuffs-5.0yards-LongSingleTarget"
 value: {
  dps: 98016.04182
  tps: 62766.75693
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_smf-DefaultTalents-Basic-default-NoBuffs-5.0yards-ShortSingleTarget"
 value: {
  dps: 108281.65014
  tps: 69242.72762
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_smf-Single-Minded Fury-Basic-default-FullBuffs-5.0yards-LongMultiTarget"
 value: {
  dps: 329792.70427
  tps: 229767.18181
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_smf-Single-Minded Fury-Basic-default-FullBuffs-5.0yards-LongSingleTarget"
 value: {
  dps: 134161.55504
  tps: 86848.72179
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_smf-Single-Minded Fury-Basic-default-FullBuffs-5.0yards-ShortSingleTarget"
 value: {
  dps: 166227.34703
  tps: 107099.54619
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_smf-Single-Minded Fury-Basic-default-NoBuffs-5.0yards-LongMultiTarget"
 value: {
  dps: 249332.60389
  tps: 175118.73166
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_smf-Single-Minded Fury-Basic-default-NoBuffs-5.0yards-LongSingleTarget"
 value: {
  dps: 97782.81743
  tps: 63757.49889
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_smf-Single-Minded Fury-Basic-default-NoBuffs-5.0yards-ShortSingleTarget"
 value: {
  dps: 106029.74964
  tps: 68761.8773
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_tg-DefaultTalents-Basic-default-FullBuffs-5.0yards-LongMultiTarget"
 value: {
  dps: 378524.07202
  tps: 256566.52517
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_tg-DefaultTalents-Basic-default-FullBuffs-5.0yards-LongSingleTarget"
 value: {
  dps: 143839.19289
  tps: 90178.38336
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_tg-DefaultTalents-Basic-default-FullBuffs-5.0yards-ShortSingleTarget"
 value: {
  dps: 175842.88683
  tps: 110069.48591
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_tg-DefaultTalents-Basic-default-NoBuffs-5.0yards-LongMultiTarget"
 value: {
  dps: 296353.74034
  tps: 186500.30264
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_tg-DefaultTalents-Basic-default-NoBuffs-5.0yards-LongSingleTarget"
 value: {
  dps: 106991.78301
  tps: 68347.9519
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_tg-DefaultTalents-Basic-default-NoBuffs-5.0yards-ShortSingleTarget"
 value: {
  dps: 117573.11164
  tps: 74336.78018
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_tg-Single-Minded Fury-Basic-default-FullBuffs-5.0yards-LongMultiTarget"
 value: {
  dps: 345514.21166
  tps: 247381.86912
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_tg-Single-Minded Fury-Basic-default-FullBuffs-5.0yards-LongSingleTarget"
 value: {
  dps: 140986.82168
  tps: 91965.03625
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_tg-Single-Minded Fury-Basic-default-FullBuffs-5.0yards-ShortSingleTarget"
 value: {
  dps: 171033.11794
  tps: 110093.4239
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_tg-Single-Minded Fury-Basic-default-NoBuffs-5.0yards-LongMultiTarget"
 value: {
  dps: 262843.89952
  tps: 190150.54685
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_tg-Single-Minded Fury-Basic-default-NoBuffs-5.0yards-LongSingleTarget"
 value: {
  dps: 104568.75657
  tps: 68437.95818
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_tg-Single-Minded Fury-Basic-default-NoBuffs-5.0yards-ShortSingleTarget"
 value: {
  dps: 113275.33911
  tps: 73712.56825
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_smf-DefaultTalents-Basic-default-FullBuffs-5.0yards-LongMultiTarget"
 value: {
  dps: 355128.98081
  tps: 229460.54147
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_smf-DefaultTalents-Basic-default-FullBuffs-5.0yards-LongSingleTarget"
 value: {
  dps: 136262.36395
  tps: 85930.75002
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_smf-DefaultTalents-Basic-default-FullBuffs-5.0yards-ShortSingleTarget"
 value: {
  dps: 167347.68927
  tps: 104086.34828
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_smf-DefaultTalents-Basic-default-NoBuffs-5.0yards-LongMultiTarget"
 value: {
  dps: 275080.63408
  tps: 174583.29666
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_smf-DefaultTalents-Basic-default-NoBuffs-5.0yards-LongSingleTarget"
 value: {
  dps: 98746.7422
  tps: 63481.00837
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_smf-DefaultTalents-Basic-default-NoBuffs-5.0yards-ShortSingleTarget"
 value: {
  dps: 107970.56626
  tps: 68833.099
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_smf-Single-Minded Fury-Basic-default-FullBuffs-5.0yards-LongMultiTarget"
 value: {
  dps: 328758.93641
  tps: 227072.87393
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_smf-Single-Minded Fury-Basic-default-FullBuffs-5.0yards-LongSingleTarget"
 value: {
  dps: 134867.25637
  tps: 86396.60138
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_smf-Single-Minded Fury-Basic-default-FullBuffs-5.0yards-ShortSingleTarget"
 value: {
  dps: 163335.67941
  tps: 104467.55718
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_smf-Single-Minded Fury-Basic-default-NoBuffs-5.0yards-LongMultiTarget"
 value: {
  dps: 249636.75356
  tps: 175174.55153
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_smf-Single-Minded Fury-Basic-default-NoBuffs-5.0yards-LongSingleTarget"
 value: {
  dps: 99462.2593
  tps: 64273.51244
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_smf-Single-Minded Fury-Basic-default-NoBuffs-5.0yards-ShortSingleTarget"
 value: {
  dps: 105265.4464
  tps: 67388.24565
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_tg-DefaultTalents-Basic-default-FullBuffs-5.0yards-LongMultiTarget"
 value: {
  dps: 383295.69997
  tps: 253899.15434
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_tg-DefaultTalents-Basic-default-FullBuffs-5.0yards-LongSingleTarget"
 value: {
  dps: 143547.76107
  tps: 89534.65046
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_tg-DefaultTalents-Basic-default-FullBuffs-5.0yards-ShortSingleTarget"
 value: {
  dps: 176123.0524
  tps: 108491.59975
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_tg-DefaultTalents-Basic-default-NoBuffs-5.0yards-LongMultiTarget"
 value: {
  dps: 297601.61809
  tps: 195408.22838
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_tg-DefaultTalents-Basic-default-NoBuffs-5.0yards-LongSingleTarget"
 value: {
  dps: 109361.82062
  tps: 69559.65109
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_tg-DefaultTalents-Basic-default-NoBuffs-5.0yards-ShortSingleTarget"
 value: {
  dps: 119901.2291
  tps: 75111.98272
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_tg-Single-Minded Fury-Basic-default-FullBuffs-5.0yards-LongMultiTarget"
 value: {
  dps: 344259.62276
  tps: 245740.00427
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_tg-Single-Minded Fury-Basic-default-FullBuffs-5.0yards-LongSingleTarget"
 value: {
  dps: 142139.63021
  tps: 91462.68834
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_tg-Single-Minded Fury-Basic-default-FullBuffs-5.0yards-ShortSingleTarget"
 value: {
  dps: 171419.5849
  tps: 108225.99803
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_tg-Single-Minded Fury-Basic-default-NoBuffs-5.0yards-LongMultiTarget"
 value: {
  dps: 264902.23399
  tps: 191361.45626
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_tg-Single-Minded Fury-Basic-default-NoBuffs-5.0yards-LongSingleTarget"
 value: {
  dps: 106121.26402
  tps: 69518.59173
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_tg-Single-Minded Fury-Basic-default-NoBuffs-5.0yards-ShortSingleTarget"
 value: {
  dps: 113588.68824
  tps: 73217.35216
 }
}
dps_results: {
 key: "TestFury-SwitchInFrontOfTarget-Default"
 value: {
  dps: 128848.66193
  tps: 82963.75795
 }
}
