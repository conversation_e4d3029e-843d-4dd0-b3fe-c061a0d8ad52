character_stats_results: {
 key: "TestFury-CharacterStats-Default"
 value: {
  final_stats: 20220.9525
  final_stats: 225.75
  final_stats: 24697.2
  final_stats: 120.75
  final_stats: 148
  final_stats: 2550
  final_stats: 12353
  final_stats: 2272.5
  final_stats: 2562
  final_stats: 2e-05
  final_stats: 18621.86605
  final_stats: 7826
  final_stats: 44761.0955
  final_stats: 0
  final_stats: 0
  final_stats: 0
  final_stats: 0
  final_stats: 34190
  final_stats: 0
  final_stats: 492163.8
  final_stats: 0
  final_stats: 0
  final_stats: 7.5
  final_stats: 15.03529
  final_stats: 30.61091
  final_stats: 25.58833
  final_stats: 0
 }
}
dps_results: {
 key: "TestFury-AllItems-AgilePrimalDiamond"
 value: {
  dps: 144523.56145
  tps: 91254.09093
 }
}
dps_results: {
 key: "TestFury-AllItems-AssuranceofConsequence-105472"
 value: {
  dps: 142001.48519
  tps: 89743.62451
 }
}
dps_results: {
 key: "TestFury-AllItems-AusterePrimalDiamond"
 value: {
  dps: 142127.74313
  tps: 89684.10099
 }
}
dps_results: {
 key: "TestFury-AllItems-BattleplateofResoundingRings"
 value: {
  dps: 127800.47557
  tps: 82544.33692
 }
}
dps_results: {
 key: "TestFury-AllItems-BattleplateoftheLastMogu"
 value: {
  dps: 143133.41494
  tps: 90579.78156
 }
}
dps_results: {
 key: "TestFury-AllItems-BattleplateofthePrehistoricMarauder"
 value: {
  dps: 137240.84017
  tps: 87755.35712
 }
}
dps_results: {
 key: "TestFury-AllItems-BurningPrimalDiamond"
 value: {
  dps: 144514.30118
  tps: 91247.68017
 }
}
dps_results: {
 key: "TestFury-AllItems-CapacitivePrimalDiamond"
 value: {
  dps: 143152.73401
  tps: 90171.86305
 }
}
dps_results: {
 key: "TestFury-AllItems-CourageousPrimalDiamond"
 value: {
  dps: 142127.74313
  tps: 89684.10099
 }
}
dps_results: {
 key: "TestFury-AllItems-DelicateVialoftheSanguinaire-96895"
 value: {
  dps: 141917.01509
  tps: 89682.89726
 }
}
dps_results: {
 key: "TestFury-AllItems-DestructivePrimalDiamond"
 value: {
  dps: 143508.3062
  tps: 90072.55568
 }
}
dps_results: {
 key: "TestFury-AllItems-EffulgentPrimalDiamond"
 value: {
  dps: 142127.74313
  tps: 89684.10099
 }
}
dps_results: {
 key: "TestFury-AllItems-EmberPrimalDiamond"
 value: {
  dps: 142127.74313
  tps: 89684.10099
 }
}
dps_results: {
 key: "TestFury-AllItems-EnchantWeapon-BloodyDancingSteel-5125"
 value: {
  dps: 145217.13552
  tps: 91685.96547
 }
}
dps_results: {
 key: "TestFury-AllItems-EnchantWeapon-Colossus-4445"
 value: {
  dps: 142474.83406
  tps: 89962.50856
 }
}
dps_results: {
 key: "TestFury-AllItems-EnchantWeapon-JadeSpirit-4442"
 value: {
  dps: 142474.83406
  tps: 89962.50856
 }
}
dps_results: {
 key: "TestFury-AllItems-EnchantWeapon-River'sSong-4446"
 value: {
  dps: 142477.72977
  tps: 89962.50856
 }
}
dps_results: {
 key: "TestFury-AllItems-EnchantWeapon-SpiritofConquest-5124"
 value: {
  dps: 142474.83406
  tps: 89962.50856
 }
}
dps_results: {
 key: "TestFury-AllItems-EnchantWeapon-Windsong-4441"
 value: {
  dps: 146811.62036
  tps: 92284.99432
 }
}
dps_results: {
 key: "TestFury-AllItems-EnigmaticPrimalDiamond"
 value: {
  dps: 143508.3062
  tps: 90072.55568
 }
}
dps_results: {
 key: "TestFury-AllItems-EternalPrimalDiamond"
 value: {
  dps: 142127.74313
  tps: 89684.10099
 }
}
dps_results: {
 key: "TestFury-AllItems-EvilEyeofGalakras-105491"
 value: {
  dps: 160721.29526
  tps: 105936.05266
 }
}
dps_results: {
 key: "TestFury-AllItems-FabledFeatherofJi-Kun-96842"
 value: {
  dps: 145091.89514
  tps: 91896.39471
 }
}
dps_results: {
 key: "TestFury-AllItems-Fen-Yu,FuryofXuen-102248"
 value: {
  dps: 151039.16023
  tps: 97223.55044
 }
}
dps_results: {
 key: "TestFury-AllItems-FleetPrimalDiamond"
 value: {
  dps: 143164.78572
  tps: 90337.87926
 }
}
dps_results: {
 key: "TestFury-AllItems-ForlornPrimalDiamond"
 value: {
  dps: 142127.74313
  tps: 89684.10099
 }
}
dps_results: {
 key: "TestFury-AllItems-GazeoftheTwins-96915"
 value: {
  dps: 153413.49873
  tps: 95815.88116
 }
}
dps_results: {
 key: "TestFury-AllItems-Gong-Lu,StrengthofXuen-102249"
 value: {
  dps: 159254.40077
  tps: 102783.36693
 }
}
dps_results: {
 key: "TestFury-AllItems-Horridon'sLastGasp-96757"
 value: {
  dps: 141917.01509
  tps: 89682.89726
 }
}
dps_results: {
 key: "TestFury-AllItems-ImpassivePrimalDiamond"
 value: {
  dps: 143508.3062
  tps: 90072.55568
 }
}
dps_results: {
 key: "TestFury-AllItems-IndomitablePrimalDiamond"
 value: {
  dps: 142127.74313
  tps: 89684.10099
 }
}
dps_results: {
 key: "TestFury-AllItems-InscribedBagofHydra-Spawn-96828"
 value: {
  dps: 141917.01509
  tps: 89682.89726
 }
}
dps_results: {
 key: "TestFury-AllItems-Ji-Kun'sRisingWinds-96843"
 value: {
  dps: 141917.01509
  tps: 89682.89726
 }
}
dps_results: {
 key: "TestFury-AllItems-PhaseFingers-4697"
 value: {
  dps: 144675.75409
  tps: 91344.95046
 }
}
dps_results: {
 key: "TestFury-AllItems-PlateofResoundingRings"
 value: {
  dps: 110315.22141
  tps: 71954.12574
 }
}
dps_results: {
 key: "TestFury-AllItems-PlateoftheLastMogu"
 value: {
  dps: 120493.44842
  tps: 77276.25507
 }
}
dps_results: {
 key: "TestFury-AllItems-PlateofthePrehistoricMarauder"
 value: {
  dps: 125550.21205
  tps: 80483.29321
 }
}
dps_results: {
 key: "TestFury-AllItems-PowerfulPrimalDiamond"
 value: {
  dps: 142127.74313
  tps: 89684.10099
 }
}
dps_results: {
 key: "TestFury-AllItems-PriceofProgress-81266"
 value: {
  dps: 141917.01509
  tps: 89682.89726
 }
}
dps_results: {
 key: "TestFury-AllItems-Primordius'TalismanofRage-96873"
 value: {
  dps: 153285.60587
  tps: 96485.20039
 }
}
dps_results: {
 key: "TestFury-AllItems-Qian-Le,CourageofNiuzao-102245"
 value: {
  dps: 147056.49965
  tps: 92102.88523
 }
}
dps_results: {
 key: "TestFury-AllItems-Qian-Ying,FortitudeofNiuzao-102250"
 value: {
  dps: 148767.77272
  tps: 93819.45515
 }
}
dps_results: {
 key: "TestFury-AllItems-Renataki'sSoulCharm-96741"
 value: {
  dps: 141983.14843
  tps: 89526.8817
 }
}
dps_results: {
 key: "TestFury-AllItems-ReverberatingPrimalDiamond"
 value: {
  dps: 145272.42793
  tps: 91704.42761
 }
}
dps_results: {
 key: "TestFury-AllItems-RevitalizingPrimalDiamond"
 value: {
  dps: 144514.30118
  tps: 91247.68017
 }
}
dps_results: {
 key: "TestFury-AllItems-SinisterPrimalDiamond"
 value: {
  dps: 143152.73401
  tps: 90171.86305
 }
}
dps_results: {
 key: "TestFury-AllItems-SynapseSprings(MarkII)-4898"
 value: {
  dps: 146242.80287
  tps: 92380.92044
 }
}
dps_results: {
 key: "TestFury-AllItems-TalismanofBloodlust-96864"
 value: {
  dps: 144961.33246
  tps: 92169.93889
 }
}
dps_results: {
 key: "TestFury-AllItems-TheGloamingBlade-88149"
 value: {
  dps: 145272.42793
  tps: 91704.42761
 }
}
dps_results: {
 key: "TestFury-AllItems-TyrannicalPrimalDiamond"
 value: {
  dps: 142127.74313
  tps: 89684.10099
 }
}
dps_results: {
 key: "TestFury-AllItems-UnerringVisionofLeiShen-96930"
 value: {
  dps: 145898.73096
  tps: 91860.09198
 }
}
dps_results: {
 key: "TestFury-AllItems-Wushoolay'sFinalChoice-96785"
 value: {
  dps: 145091.89514
  tps: 91896.39471
 }
}
dps_results: {
 key: "TestFury-AllItems-Xing-Ho,BreathofYu'lon-102246"
 value: {
  dps: 144979.42692
  tps: 92079.26348
 }
}
dps_results: {
 key: "TestFury-AllItems-YaungolFireCarrier-86518"
 value: {
  dps: 145272.42793
  tps: 91704.42761
 }
}
dps_results: {
 key: "TestFury-AllItems-ZenAlchemistStone-75274"
 value: {
  dps: 148698.51037
  tps: 93934.55213
 }
}
dps_results: {
 key: "TestFury-Average-Default"
 value: {
  dps: 147226.09474
  tps: 93068.38295
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_smf-DefaultTalents-Basic-default-FullBuffs-5.0yards-LongMultiTarget"
 value: {
  dps: 360447.70797
  tps: 237775.35649
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_smf-DefaultTalents-Basic-default-FullBuffs-5.0yards-LongSingleTarget"
 value: {
  dps: 141809.78885
  tps: 89884.46243
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_smf-DefaultTalents-Basic-default-FullBuffs-5.0yards-ShortSingleTarget"
 value: {
  dps: 173470.52872
  tps: 111476.73865
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_smf-DefaultTalents-Basic-default-NoBuffs-5.0yards-LongMultiTarget"
 value: {
  dps: 272847.26341
  tps: 180435.3895
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_smf-DefaultTalents-Basic-default-NoBuffs-5.0yards-LongSingleTarget"
 value: {
  dps: 102380.85456
  tps: 65994.76941
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_smf-DefaultTalents-Basic-default-NoBuffs-5.0yards-ShortSingleTarget"
 value: {
  dps: 111478.91349
  tps: 72238.78716
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_smf-Single-Minded Fury-Basic-default-FullBuffs-5.0yards-LongMultiTarget"
 value: {
  dps: 334589.73246
  tps: 232250.7942
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_smf-Single-Minded Fury-Basic-default-FullBuffs-5.0yards-LongSingleTarget"
 value: {
  dps: 140589.37178
  tps: 91078.39925
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_smf-Single-Minded Fury-Basic-default-FullBuffs-5.0yards-ShortSingleTarget"
 value: {
  dps: 172160.95579
  tps: 111926.67508
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_smf-Single-Minded Fury-Basic-default-NoBuffs-5.0yards-LongMultiTarget"
 value: {
  dps: 251371.14691
  tps: 176735.69187
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_smf-Single-Minded Fury-Basic-default-NoBuffs-5.0yards-LongSingleTarget"
 value: {
  dps: 101194.33287
  tps: 66648.37621
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_smf-Single-Minded Fury-Basic-default-NoBuffs-5.0yards-ShortSingleTarget"
 value: {
  dps: 109756.04552
  tps: 70992.08802
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_tg-DefaultTalents-Basic-default-FullBuffs-5.0yards-LongMultiTarget"
 value: {
  dps: 385817.78455
  tps: 253319.20764
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_tg-DefaultTalents-Basic-default-FullBuffs-5.0yards-LongSingleTarget"
 value: {
  dps: 146839.47671
  tps: 92740.39759
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_tg-DefaultTalents-Basic-default-FullBuffs-5.0yards-ShortSingleTarget"
 value: {
  dps: 181930.03688
  tps: 114784.42862
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_tg-DefaultTalents-Basic-default-NoBuffs-5.0yards-LongMultiTarget"
 value: {
  dps: 308440.67266
  tps: 192102.93771
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_tg-DefaultTalents-Basic-default-NoBuffs-5.0yards-LongSingleTarget"
 value: {
  dps: 110395.88498
  tps: 70348.63359
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_tg-DefaultTalents-Basic-default-NoBuffs-5.0yards-ShortSingleTarget"
 value: {
  dps: 120707.44708
  tps: 75785.99554
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_tg-Single-Minded Fury-Basic-default-FullBuffs-5.0yards-LongMultiTarget"
 value: {
  dps: 346361.20845
  tps: 247507.60267
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_tg-Single-Minded Fury-Basic-default-FullBuffs-5.0yards-LongSingleTarget"
 value: {
  dps: 146985.60536
  tps: 95706.37828
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_tg-Single-Minded Fury-Basic-default-FullBuffs-5.0yards-ShortSingleTarget"
 value: {
  dps: 179301.10176
  tps: 113875.5884
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_tg-Single-Minded Fury-Basic-default-NoBuffs-5.0yards-LongMultiTarget"
 value: {
  dps: 264828.14303
  tps: 192302.93188
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_tg-Single-Minded Fury-Basic-default-NoBuffs-5.0yards-LongSingleTarget"
 value: {
  dps: 107024.67506
  tps: 70428.77401
 }
}
dps_results: {
 key: "TestFury-Settings-Troll-p1_fury_tg-Single-Minded Fury-Basic-default-NoBuffs-5.0yards-ShortSingleTarget"
 value: {
  dps: 116625.87828
  tps: 75543.11121
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_smf-DefaultTalents-Basic-default-FullBuffs-5.0yards-LongMultiTarget"
 value: {
  dps: 353200.8128
  tps: 235841.86851
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_smf-DefaultTalents-Basic-default-FullBuffs-5.0yards-LongSingleTarget"
 value: {
  dps: 143242.23401
  tps: 90416.81651
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_smf-DefaultTalents-Basic-default-FullBuffs-5.0yards-ShortSingleTarget"
 value: {
  dps: 175236.45776
  tps: 108957.80055
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_smf-DefaultTalents-Basic-default-NoBuffs-5.0yards-LongMultiTarget"
 value: {
  dps: 276016.45949
  tps: 179979.58562
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_smf-DefaultTalents-Basic-default-NoBuffs-5.0yards-LongSingleTarget"
 value: {
  dps: 102384.47546
  tps: 66342.20824
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_smf-DefaultTalents-Basic-default-NoBuffs-5.0yards-ShortSingleTarget"
 value: {
  dps: 112391.62413
  tps: 71129.15873
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_smf-Single-Minded Fury-Basic-default-FullBuffs-5.0yards-LongMultiTarget"
 value: {
  dps: 339943.88113
  tps: 235705.55042
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_smf-Single-Minded Fury-Basic-default-FullBuffs-5.0yards-LongSingleTarget"
 value: {
  dps: 142549.86642
  tps: 91506.44047
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_smf-Single-Minded Fury-Basic-default-FullBuffs-5.0yards-ShortSingleTarget"
 value: {
  dps: 175473.93331
  tps: 110629.9778
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_smf-Single-Minded Fury-Basic-default-NoBuffs-5.0yards-LongMultiTarget"
 value: {
  dps: 252498.11217
  tps: 177882.88377
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_smf-Single-Minded Fury-Basic-default-NoBuffs-5.0yards-LongSingleTarget"
 value: {
  dps: 102954.18973
  tps: 67228.52502
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_smf-Single-Minded Fury-Basic-default-NoBuffs-5.0yards-ShortSingleTarget"
 value: {
  dps: 110339.22198
  tps: 70839.59811
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_tg-DefaultTalents-Basic-default-FullBuffs-5.0yards-LongMultiTarget"
 value: {
  dps: 383080.35543
  tps: 254913.47009
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_tg-DefaultTalents-Basic-default-FullBuffs-5.0yards-LongSingleTarget"
 value: {
  dps: 147434.22171
  tps: 92357.44327
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_tg-DefaultTalents-Basic-default-FullBuffs-5.0yards-ShortSingleTarget"
 value: {
  dps: 177704.05075
  tps: 111779.17072
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_tg-DefaultTalents-Basic-default-NoBuffs-5.0yards-LongMultiTarget"
 value: {
  dps: 310812.2585
  tps: 192988.793
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_tg-DefaultTalents-Basic-default-NoBuffs-5.0yards-LongSingleTarget"
 value: {
  dps: 109347.60169
  tps: 69616.6659
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_tg-DefaultTalents-Basic-default-NoBuffs-5.0yards-ShortSingleTarget"
 value: {
  dps: 120094.78169
  tps: 74601.8632
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_tg-Single-Minded Fury-Basic-default-FullBuffs-5.0yards-LongMultiTarget"
 value: {
  dps: 346335.20873
  tps: 245793.68896
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_tg-Single-Minded Fury-Basic-default-FullBuffs-5.0yards-LongSingleTarget"
 value: {
  dps: 146750.90057
  tps: 95216.98962
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_tg-Single-Minded Fury-Basic-default-FullBuffs-5.0yards-ShortSingleTarget"
 value: {
  dps: 176526.06321
  tps: 112664.47766
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_tg-Single-Minded Fury-Basic-default-NoBuffs-5.0yards-LongMultiTarget"
 value: {
  dps: 262975.33636
  tps: 190578.30634
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_tg-Single-Minded Fury-Basic-default-NoBuffs-5.0yards-LongSingleTarget"
 value: {
  dps: 107983.50759
  tps: 70784.90421
 }
}
dps_results: {
 key: "TestFury-Settings-Worgen-p1_fury_tg-Single-Minded Fury-Basic-default-NoBuffs-5.0yards-ShortSingleTarget"
 value: {
  dps: 116953.82843
  tps: 74434.02513
 }
}
dps_results: {
 key: "TestFury-SwitchInFrontOfTarget-Default"
 value: {
  dps: 133221.37864
  tps: 84981.29344
 }
}
