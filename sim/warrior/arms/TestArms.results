character_stats_results: {
 key: "TestArms-CharacterStats-Default"
 value: {
  final_stats: 18701.7075
  final_stats: 220.5
  final_stats: 22420.2
  final_stats: 121.8
  final_stats: 149
  final_stats: 2551
  final_stats: 11433
  final_stats: 2272.5
  final_stats: 2553
  final_stats: 2e-05
  final_stats: 17206.43246
  final_stats: 7254
  final_stats: 41418.7565
  final_stats: 0
  final_stats: 0
  final_stats: 0
  final_stats: 0
  final_stats: 34190
  final_stats: 0
  final_stats: 460285.8
  final_stats: 0
  final_stats: 0
  final_stats: 7.50294
  final_stats: 15.01176
  final_stats: 29.07705
  final_stats: 24.055
  final_stats: 0
 }
}
dps_results: {
 key: "TestArms-AllItems-AgilePrimalDiamond"
 value: {
  dps: 145182.15991
  tps: 96643.61815
 }
}
dps_results: {
 key: "TestArms-AllItems-AssuranceofConsequence-105472"
 value: {
  dps: 142250.87192
  tps: 94819.7594
 }
}
dps_results: {
 key: "TestArms-AllItems-AusterePrimalDiamond"
 value: {
  dps: 142805.09806
  tps: 95104.72472
 }
}
dps_results: {
 key: "TestArms-AllItems-BattleplateofResoundingRings"
 value: {
  dps: 131711.82902
  tps: 87951.63601
 }
}
dps_results: {
 key: "TestArms-AllItems-BattleplateoftheLastMogu"
 value: {
  dps: 146627.27589
  tps: 99481.58014
 }
}
dps_results: {
 key: "TestArms-AllItems-BattleplateofthePrehistoricMarauder"
 value: {
  dps: 145170.66702
  tps: 96724.06815
 }
}
dps_results: {
 key: "TestArms-AllItems-BurningPrimalDiamond"
 value: {
  dps: 145103.06804
  tps: 96653.45518
 }
}
dps_results: {
 key: "TestArms-AllItems-CapacitivePrimalDiamond"
 value: {
  dps: 143394.53978
  tps: 95413.83836
 }
}
dps_results: {
 key: "TestArms-AllItems-CourageousPrimalDiamond"
 value: {
  dps: 142805.09806
  tps: 95104.72472
 }
}
dps_results: {
 key: "TestArms-AllItems-DelicateVialoftheSanguinaire-96895"
 value: {
  dps: 142140.07057
  tps: 94771.51483
 }
}
dps_results: {
 key: "TestArms-AllItems-DestructivePrimalDiamond"
 value: {
  dps: 143531.20364
  tps: 95510.82979
 }
}
dps_results: {
 key: "TestArms-AllItems-EffulgentPrimalDiamond"
 value: {
  dps: 142805.09806
  tps: 95104.72472
 }
}
dps_results: {
 key: "TestArms-AllItems-EmberPrimalDiamond"
 value: {
  dps: 142805.09806
  tps: 95104.72472
 }
}
dps_results: {
 key: "TestArms-AllItems-EnchantWeapon-BloodyDancingSteel-5125"
 value: {
  dps: 145757.58579
  tps: 97108.15091
 }
}
dps_results: {
 key: "TestArms-AllItems-EnchantWeapon-Colossus-4445"
 value: {
  dps: 142684.44756
  tps: 95091.40246
 }
}
dps_results: {
 key: "TestArms-AllItems-EnchantWeapon-JadeSpirit-4442"
 value: {
  dps: 142684.44756
  tps: 95091.40246
 }
}
dps_results: {
 key: "TestArms-AllItems-EnchantWeapon-River'sSong-4446"
 value: {
  dps: 142684.44756
  tps: 95091.40246
 }
}
dps_results: {
 key: "TestArms-AllItems-EnchantWeapon-SpiritofConquest-5124"
 value: {
  dps: 142684.44756
  tps: 95091.40246
 }
}
dps_results: {
 key: "TestArms-AllItems-EnchantWeapon-Windsong-4441"
 value: {
  dps: 143554.34129
  tps: 95855.15043
 }
}
dps_results: {
 key: "TestArms-AllItems-EnigmaticPrimalDiamond"
 value: {
  dps: 143531.20364
  tps: 95510.82979
 }
}
dps_results: {
 key: "TestArms-AllItems-EternalPrimalDiamond"
 value: {
  dps: 142805.09806
  tps: 95104.72472
 }
}
dps_results: {
 key: "TestArms-AllItems-EvilEyeofGalakras-105491"
 value: {
  dps: 160581.49384
  tps: 105707.54086
 }
}
dps_results: {
 key: "TestArms-AllItems-FabledFeatherofJi-Kun-96842"
 value: {
  dps: 159456.25724
  tps: 106490.36869
 }
}
dps_results: {
 key: "TestArms-AllItems-FleetPrimalDiamond"
 value: {
  dps: 143154.8331
  tps: 95328.42915
 }
}
dps_results: {
 key: "TestArms-AllItems-ForlornPrimalDiamond"
 value: {
  dps: 142805.09806
  tps: 95104.72472
 }
}
dps_results: {
 key: "TestArms-AllItems-GazeoftheTwins-96915"
 value: {
  dps: 152776.11844
  tps: 101885.49871
 }
}
dps_results: {
 key: "TestArms-AllItems-Horridon'sLastGasp-96757"
 value: {
  dps: 142140.07057
  tps: 94771.51483
 }
}
dps_results: {
 key: "TestArms-AllItems-ImpassivePrimalDiamond"
 value: {
  dps: 143531.20364
  tps: 95510.82979
 }
}
dps_results: {
 key: "TestArms-AllItems-IndomitablePrimalDiamond"
 value: {
  dps: 142805.09806
  tps: 95104.72472
 }
}
dps_results: {
 key: "TestArms-AllItems-InscribedBagofHydra-Spawn-96828"
 value: {
  dps: 142140.07057
  tps: 94771.51483
 }
}
dps_results: {
 key: "TestArms-AllItems-Ji-Kun'sRisingWinds-96843"
 value: {
  dps: 142140.07057
  tps: 94771.51483
 }
}
dps_results: {
 key: "TestArms-AllItems-PhaseFingers-4697"
 value: {
  dps: 145287.25437
  tps: 96770.44066
 }
}
dps_results: {
 key: "TestArms-AllItems-PlateofResoundingRings"
 value: {
  dps: 120288.96395
  tps: 81927.7268
 }
}
dps_results: {
 key: "TestArms-AllItems-PlateoftheLastMogu"
 value: {
  dps: 127899.34996
  tps: 86470.2294
 }
}
dps_results: {
 key: "TestArms-AllItems-PlateofthePrehistoricMarauder"
 value: {
  dps: 132444.5509
  tps: 89857.0294
 }
}
dps_results: {
 key: "TestArms-AllItems-PowerfulPrimalDiamond"
 value: {
  dps: 142805.09806
  tps: 95104.72472
 }
}
dps_results: {
 key: "TestArms-AllItems-PriceofProgress-81266"
 value: {
  dps: 142140.07057
  tps: 94771.51483
 }
}
dps_results: {
 key: "TestArms-AllItems-Primordius'TalismanofRage-96873"
 value: {
  dps: 152438.80171
  tps: 101417.35623
 }
}
dps_results: {
 key: "TestArms-AllItems-Renataki'sSoulCharm-96741"
 value: {
  dps: 142779.76958
  tps: 95279.14018
 }
}
dps_results: {
 key: "TestArms-AllItems-ReverberatingPrimalDiamond"
 value: {
  dps: 145967.94298
  tps: 97202.77831
 }
}
dps_results: {
 key: "TestArms-AllItems-RevitalizingPrimalDiamond"
 value: {
  dps: 145103.06804
  tps: 96653.45518
 }
}
dps_results: {
 key: "TestArms-AllItems-SinisterPrimalDiamond"
 value: {
  dps: 143394.53978
  tps: 95413.83836
 }
}
dps_results: {
 key: "TestArms-AllItems-SynapseSprings(MarkII)-4898"
 value: {
  dps: 147380.91096
  tps: 98022.37407
 }
}
dps_results: {
 key: "TestArms-AllItems-TalismanofBloodlust-96864"
 value: {
  dps: 145359.30344
  tps: 97874.78167
 }
}
dps_results: {
 key: "TestArms-AllItems-TyrannicalPrimalDiamond"
 value: {
  dps: 142805.09806
  tps: 95104.72472
 }
}
dps_results: {
 key: "TestArms-AllItems-UnerringVisionofLeiShen-96930"
 value: {
  dps: 144859.07178
  tps: 96474.73219
 }
}
dps_results: {
 key: "TestArms-AllItems-Wushoolay'sFinalChoice-96785"
 value: {
  dps: 142140.07057
  tps: 94771.51483
 }
}
dps_results: {
 key: "TestArms-AllItems-YaungolFireCarrier-86518"
 value: {
  dps: 156590.6685
  tps: 106419.29109
 }
}
dps_results: {
 key: "TestArms-AllItems-ZenAlchemistStone-75274"
 value: {
  dps: 148329.9574
  tps: 99530.51589
 }
}
dps_results: {
 key: "TestArms-Average-Default"
 value: {
  dps: 147856.36415
  tps: 98454.59259
 }
}
dps_results: {
 key: "TestArms-Settings-Orc-p1_arms_bis-Basic-arms-FullBuffs-9.0yards-LongMultiTarget"
 value: {
  dps: 809031.38208
  tps: 729110.69829
 }
}
dps_results: {
 key: "TestArms-Settings-Orc-p1_arms_bis-Basic-arms-FullBuffs-9.0yards-LongSingleTarget"
 value: {
  dps: 148061.59956
  tps: 98454.71172
 }
}
dps_results: {
 key: "TestArms-Settings-Orc-p1_arms_bis-Basic-arms-FullBuffs-9.0yards-ShortSingleTarget"
 value: {
  dps: 155277.31338
  tps: 104179.31438
 }
}
dps_results: {
 key: "TestArms-Settings-Orc-p1_arms_bis-Basic-arms-NoBuffs-9.0yards-LongMultiTarget"
 value: {
  dps: 606537.47116
  tps: 547032.00049
 }
}
dps_results: {
 key: "TestArms-Settings-Orc-p1_arms_bis-Basic-arms-NoBuffs-9.0yards-LongSingleTarget"
 value: {
  dps: 114892.53331
  tps: 75968.08036
 }
}
dps_results: {
 key: "TestArms-Settings-Orc-p1_arms_bis-Basic-arms-NoBuffs-9.0yards-ShortSingleTarget"
 value: {
  dps: 120335.97814
  tps: 73176.74934
 }
}
dps_results: {
 key: "TestArms-Settings-Orc-p1_prebis_poor-Basic-arms-FullBuffs-9.0yards-LongMultiTarget"
 value: {
  dps: 645095.6445
  tps: 580657.76678
 }
}
dps_results: {
 key: "TestArms-Settings-Orc-p1_prebis_poor-Basic-arms-FullBuffs-9.0yards-LongSingleTarget"
 value: {
  dps: 106503.51675
  tps: 70169.2531
 }
}
dps_results: {
 key: "TestArms-Settings-Orc-p1_prebis_poor-Basic-arms-FullBuffs-9.0yards-ShortSingleTarget"
 value: {
  dps: 111324.67012
  tps: 72876.17107
 }
}
dps_results: {
 key: "TestArms-Settings-Orc-p1_prebis_poor-Basic-arms-NoBuffs-9.0yards-LongMultiTarget"
 value: {
  dps: 465453.35074
  tps: 421013.61275
 }
}
dps_results: {
 key: "TestArms-Settings-Orc-p1_prebis_poor-Basic-arms-NoBuffs-9.0yards-LongSingleTarget"
 value: {
  dps: 79481.36865
  tps: 53455.12993
 }
}
dps_results: {
 key: "TestArms-Settings-Orc-p1_prebis_poor-Basic-arms-NoBuffs-9.0yards-ShortSingleTarget"
 value: {
  dps: 85295.03958
  tps: 51382.17044
 }
}
dps_results: {
 key: "TestArms-Settings-Orc-p1_prebis_rich-Basic-arms-FullBuffs-9.0yards-LongMultiTarget"
 value: {
  dps: 667234.905
  tps: 600943.06113
 }
}
dps_results: {
 key: "TestArms-Settings-Orc-p1_prebis_rich-Basic-arms-FullBuffs-9.0yards-LongSingleTarget"
 value: {
  dps: 109792.54661
  tps: 72253.89051
 }
}
dps_results: {
 key: "TestArms-Settings-Orc-p1_prebis_rich-Basic-arms-FullBuffs-9.0yards-ShortSingleTarget"
 value: {
  dps: 114435.01552
  tps: 76079.64076
 }
}
dps_results: {
 key: "TestArms-Settings-Orc-p1_prebis_rich-Basic-arms-NoBuffs-9.0yards-LongMultiTarget"
 value: {
  dps: 488469.89932
  tps: 442901.97547
 }
}
dps_results: {
 key: "TestArms-Settings-Orc-p1_prebis_rich-Basic-arms-NoBuffs-9.0yards-LongSingleTarget"
 value: {
  dps: 81797.08199
  tps: 55333.90681
 }
}
dps_results: {
 key: "TestArms-Settings-Orc-p1_prebis_rich-Basic-arms-NoBuffs-9.0yards-ShortSingleTarget"
 value: {
  dps: 90225.75138
  tps: 54321.32835
 }
}
dps_results: {
 key: "TestArms-Settings-Worgen-p1_arms_bis-Basic-arms-FullBuffs-9.0yards-LongMultiTarget"
 value: {
  dps: 793965.29678
  tps: 717591.96518
 }
}
dps_results: {
 key: "TestArms-Settings-Worgen-p1_arms_bis-Basic-arms-FullBuffs-9.0yards-LongSingleTarget"
 value: {
  dps: 145579.2822
  tps: 97276.54583
 }
}
dps_results: {
 key: "TestArms-Settings-Worgen-p1_arms_bis-Basic-arms-FullBuffs-9.0yards-ShortSingleTarget"
 value: {
  dps: 155533.974
  tps: 103814.6704
 }
}
dps_results: {
 key: "TestArms-Settings-Worgen-p1_arms_bis-Basic-arms-NoBuffs-9.0yards-LongMultiTarget"
 value: {
  dps: 591750.8938
  tps: 534254.36369
 }
}
dps_results: {
 key: "TestArms-Settings-Worgen-p1_arms_bis-Basic-arms-NoBuffs-9.0yards-LongSingleTarget"
 value: {
  dps: 114113.237
  tps: 76233.4729
 }
}
dps_results: {
 key: "TestArms-Settings-Worgen-p1_arms_bis-Basic-arms-NoBuffs-9.0yards-ShortSingleTarget"
 value: {
  dps: 118551.21922
  tps: 72550.54065
 }
}
dps_results: {
 key: "TestArms-Settings-Worgen-p1_prebis_poor-Basic-arms-FullBuffs-9.0yards-LongMultiTarget"
 value: {
  dps: 636648.35659
  tps: 574656.3797
 }
}
dps_results: {
 key: "TestArms-Settings-Worgen-p1_prebis_poor-Basic-arms-FullBuffs-9.0yards-LongSingleTarget"
 value: {
  dps: 105519.90836
  tps: 70063.13328
 }
}
dps_results: {
 key: "TestArms-Settings-Worgen-p1_prebis_poor-Basic-arms-FullBuffs-9.0yards-ShortSingleTarget"
 value: {
  dps: 112252.00196
  tps: 73860.9395
 }
}
dps_results: {
 key: "TestArms-Settings-Worgen-p1_prebis_poor-Basic-arms-NoBuffs-9.0yards-LongMultiTarget"
 value: {
  dps: 459184.77555
  tps: 416352.22007
 }
}
dps_results: {
 key: "TestArms-Settings-Worgen-p1_prebis_poor-Basic-arms-NoBuffs-9.0yards-LongSingleTarget"
 value: {
  dps: 78865.16439
  tps: 53424.79799
 }
}
dps_results: {
 key: "TestArms-Settings-Worgen-p1_prebis_poor-Basic-arms-NoBuffs-9.0yards-ShortSingleTarget"
 value: {
  dps: 82726.27164
  tps: 51295.12424
 }
}
dps_results: {
 key: "TestArms-Settings-Worgen-p1_prebis_rich-Basic-arms-FullBuffs-9.0yards-LongMultiTarget"
 value: {
  dps: 656846.79095
  tps: 593256.6815
 }
}
dps_results: {
 key: "TestArms-Settings-Worgen-p1_prebis_rich-Basic-arms-FullBuffs-9.0yards-LongSingleTarget"
 value: {
  dps: 108087.63487
  tps: 72045.39766
 }
}
dps_results: {
 key: "TestArms-Settings-Worgen-p1_prebis_rich-Basic-arms-FullBuffs-9.0yards-ShortSingleTarget"
 value: {
  dps: 115281.77652
  tps: 76879.37192
 }
}
dps_results: {
 key: "TestArms-Settings-Worgen-p1_prebis_rich-Basic-arms-NoBuffs-9.0yards-LongMultiTarget"
 value: {
  dps: 480106.86192
  tps: 435753.25432
 }
}
dps_results: {
 key: "TestArms-Settings-Worgen-p1_prebis_rich-Basic-arms-NoBuffs-9.0yards-LongSingleTarget"
 value: {
  dps: 81311.74487
  tps: 55244.49234
 }
}
dps_results: {
 key: "TestArms-Settings-Worgen-p1_prebis_rich-Basic-arms-NoBuffs-9.0yards-ShortSingleTarget"
 value: {
  dps: 87716.50822
  tps: 54267.0406
 }
}
dps_results: {
 key: "TestArms-SwitchInFrontOfTarget-Default"
 value: {
  dps: 133494.78136
  tps: 90188.53419
 }
}
