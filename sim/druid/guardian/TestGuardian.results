character_stats_results: {
 key: "TestGuardian-CharacterStats-Default"
 value: {
  final_stats: 112.35
  final_stats: 9376.5
  final_stats: 27815.634
  final_stats: 173.25
  final_stats: 187
  final_stats: 2553
  final_stats: 6255
  final_stats: 3451.5
  final_stats: 2567
  final_stats: 9399.05696
  final_stats: 0
  final_stats: 4348
  final_stats: 21015.885
  final_stats: 0
  final_stats: 179.575
  final_stats: 0
  final_stats: 0
  final_stats: 91128.57347
  final_stats: 0
  final_stats: 535821.876
  final_stats: 60000
  final_stats: 3000
  final_stats: 7.50882
  final_stats: 15.05882
  final_stats: 31.34947
  final_stats: 18.34338
  final_stats: 0
 }
}
dps_results: {
 key: "TestGuardian-AllItems-AgilePrimalDiamond"
 value: {
  dps: 90938.6026
  tps: 629636.11912
  dtps: 55043.8817
  hps: 24279.09887
 }
}
dps_results: {
 key: "TestGuardian-AllItems-AlacrityofXuen-103989"
 value: {
  dps: 87435.56312
  tps: 605498.35055
  dtps: 55604.58231
  hps: 24075.65112
 }
}
dps_results: {
 key: "TestGuardian-AllItems-ArcaneBadgeoftheShieldwall-93347"
 value: {
  dps: 85581.70111
  tps: 594582.15428
  dtps: 56149.65242
  hps: 21976.20575
 }
}
dps_results: {
 key: "TestGuardian-AllItems-ArrowflightMedallion-93258"
 value: {
  dps: 89184.225
  tps: 617029.9859
  dtps: 54576.48688
  hps: 24676.64888
 }
}
dps_results: {
 key: "TestGuardian-AllItems-AssuranceofConsequence-105472"
 value: {
  dps: 86399.43532
  tps: 598549.55556
  dtps: 49948.27214
  hps: 24486.32037
 }
}
dps_results: {
 key: "TestGuardian-AllItems-AusterePrimalDiamond"
 value: {
  dps: 89287.69965
  tps: 618190.0285
  dtps: 54539.2615
  hps: 24228.77394
 }
}
dps_results: {
 key: "TestGuardian-AllItems-BadJuju-96781"
 value: {
  dps: 88686.0601
  tps: 614125.46709
  dtps: 53455.02036
  hps: 23498.83456
 }
}
dps_results: {
 key: "TestGuardian-AllItems-BadgeofKypariZar-84079"
 value: {
  dps: 86635.43528
  tps: 600172.06248
  dtps: 55204.21578
  hps: 23478.63379
 }
}
dps_results: {
 key: "TestGuardian-AllItems-BlossomofPureSnow-89081"
 value: {
  dps: 87151.36354
  tps: 603119.18492
  dtps: 55822.86782
  hps: 23990.4141
 }
}
dps_results: {
 key: "TestGuardian-AllItems-BottleofInfiniteStars-87057"
 value: {
  dps: 87609.5765
  tps: 607215.0165
  dtps: 54099.32168
  hps: 23147.17865
 }
}
dps_results: {
 key: "TestGuardian-AllItems-BraidofTenSongs-84072"
 value: {
  dps: 85332.28504
  tps: 591834.20289
  dtps: 55126.36205
  hps: 22907.89747
 }
}
dps_results: {
 key: "TestGuardian-AllItems-Brawler'sStatue-87571"
 value: {
  dps: 85249.61405
  tps: 590903.23615
  dtps: 54565.23742
  hps: 22611.3736
 }
}
dps_results: {
 key: "TestGuardian-AllItems-BreathoftheHydra-96827"
 value: {
  dps: 86593.43246
  tps: 600492.98222
  dtps: 56147.26714
  hps: 24737.41802
 }
}
dps_results: {
 key: "TestGuardian-AllItems-BroochofMunificentDeeds-87500"
 value: {
  dps: 85197.17684
  tps: 590536.24852
  dtps: 55993.34292
  hps: 22943.60996
 }
}
dps_results: {
 key: "TestGuardian-AllItems-BrutalTalismanoftheShado-PanAssault-94508"
 value: {
  dps: 89676.78405
  tps: 621243.38957
  dtps: 54968.53853
  hps: 24499.1372
 }
}
dps_results: {
 key: "TestGuardian-AllItems-BurningPrimalDiamond"
 value: {
  dps: 90551.01222
  tps: 626888.47936
  dtps: 55264.61416
  hps: 24136.85777
 }
}
dps_results: {
 key: "TestGuardian-AllItems-CapacitivePrimalDiamond"
 value: {
  dps: 89928.61884
  tps: 622441.83919
  dtps: 54909.50359
  hps: 24525.79471
 }
}
dps_results: {
 key: "TestGuardian-AllItems-CarbonicCarbuncle-81138"
 value: {
  dps: 87682.59262
  tps: 607439.51428
  dtps: 56242.42151
  hps: 23589.29666
 }
}
dps_results: {
 key: "TestGuardian-AllItems-Cha-Ye'sEssenceofBrilliance-96888"
 value: {
  dps: 88497.41533
  tps: 612939.12933
  dtps: 55651.12709
  hps: 25200.63651
 }
}
dps_results: {
 key: "TestGuardian-AllItems-CharmofTenSongs-84071"
 value: {
  dps: 86609.07842
  tps: 600330.9505
  dtps: 55560.19806
  hps: 23116.99779
 }
}
dps_results: {
 key: "TestGuardian-AllItems-CommunalIdolofDestruction-101168"
 value: {
  dps: 85335.51626
  tps: 591505.0146
  dtps: 54938.34918
  hps: 23013.44337
 }
}
dps_results: {
 key: "TestGuardian-AllItems-CommunalStoneofDestruction-101171"
 value: {
  dps: 85232.43232
  tps: 590594.61845
  dtps: 55029.24824
  hps: 22571.1449
 }
}
dps_results: {
 key: "TestGuardian-AllItems-CommunalStoneofWisdom-101183"
 value: {
  dps: 85232.43232
  tps: 590594.61845
  dtps: 55953.71775
  hps: 22571.53866
 }
}
dps_results: {
 key: "TestGuardian-AllItems-ContemplationofChi-Ji-103688"
 value: {
  dps: 85232.43232
  tps: 590594.61845
  dtps: 55953.71775
  hps: 22571.53866
 }
}
dps_results: {
 key: "TestGuardian-AllItems-ContemplationofChi-Ji-103988"
 value: {
  dps: 85240.95508
  tps: 590654.27783
  dtps: 55953.71775
  hps: 22571.53866
 }
}
dps_results: {
 key: "TestGuardian-AllItems-Coren'sColdChromiumCoaster-87574"
 value: {
  dps: 87990.44544
  tps: 609714.43414
  dtps: 55560.7036
  hps: 23958.66498
 }
}
dps_results: {
 key: "TestGuardian-AllItems-CoreofDecency-87497"
 value: {
  dps: 85197.17684
  tps: 590536.24852
  dtps: 55953.71775
  hps: 22571.53866
 }
}
dps_results: {
 key: "TestGuardian-AllItems-CourageousPrimalDiamond"
 value: {
  dps: 89302.44899
  tps: 618205.71538
  dtps: 55264.61416
  hps: 24136.85777
 }
}
dps_results: {
 key: "TestGuardian-AllItems-CraftedDreadfulGladiator'sBadgeofConquest-93419"
 value: {
  dps: 86609.77762
  tps: 600226.54271
  dtps: 55737.72929
  hps: 22760.49076
 }
}
dps_results: {
 key: "TestGuardian-AllItems-CraftedDreadfulGladiator'sBadgeofDominance-93600"
 value: {
  dps: 85224.09375
  tps: 590536.24852
  dtps: 55953.71775
  hps: 22571.53866
 }
}
dps_results: {
 key: "TestGuardian-AllItems-CraftedDreadfulGladiator'sBadgeofVictory-93606"
 value: {
  dps: 85620.56294
  tps: 593373.13811
  dtps: 55914.61456
  hps: 22643.43563
 }
}
dps_results: {
 key: "TestGuardian-AllItems-CraftedDreadfulGladiator'sEmblemofCruelty-93485"
 value: {
  dps: 86935.80338
  tps: 602517.22314
  dtps: 55820.85046
  hps: 23562.86997
 }
}
dps_results: {
 key: "TestGuardian-AllItems-CraftedDreadfulGladiator'sEmblemofMeditation-93487"
 value: {
  dps: 85197.17684
  tps: 590536.24852
  dtps: 55953.71775
  hps: 22623.18178
 }
}
dps_results: {
 key: "TestGuardian-AllItems-CraftedDreadfulGladiator'sEmblemofTenacity-93486"
 value: {
  dps: 85197.17684
  tps: 590536.24852
  dtps: 55993.34292
  hps: 22958.91314
 }
}
dps_results: {
 key: "TestGuardian-AllItems-CraftedDreadfulGladiator'sInsigniaofConquest-93424"
 value: {
  dps: 87235.01639
  tps: 604585.25443
  dtps: 55528.98761
  hps: 22832.28431
 }
}
dps_results: {
 key: "TestGuardian-AllItems-CraftedDreadfulGladiator'sInsigniaofDominance-93601"
 value: {
  dps: 85224.09375
  tps: 590536.24852
  dtps: 55953.71775
  hps: 22571.53866
 }
}
dps_results: {
 key: "TestGuardian-AllItems-CraftedDreadfulGladiator'sInsigniaofVictory-93611"
 value: {
  dps: 85843.59561
  tps: 594967.31247
  dtps: 55929.94847
  hps: 22731.91945
 }
}
dps_results: {
 key: "TestGuardian-AllItems-CraftedMalevolentGladiator'sBadgeofConquest-98755"
 value: {
  dps: 87013.49003
  tps: 603007.03402
  dtps: 55607.07706
  hps: 22795.58059
 }
}
dps_results: {
 key: "TestGuardian-AllItems-CraftedMalevolentGladiator'sBadgeofDominance-98910"
 value: {
  dps: 85224.09375
  tps: 590536.24852
  dtps: 55953.71775
  hps: 22571.53866
 }
}
dps_results: {
 key: "TestGuardian-AllItems-CraftedMalevolentGladiator'sBadgeofVictory-98912"
 value: {
  dps: 85758.79412
  tps: 594317.75843
  dtps: 55903.81592
  hps: 22704.10148
 }
}
dps_results: {
 key: "TestGuardian-AllItems-CraftedMalevolentGladiator'sEmblemofCruelty-98811"
 value: {
  dps: 87199.82692
  tps: 604222.05871
  dtps: 55756.21419
  hps: 23747.68251
 }
}
dps_results: {
 key: "TestGuardian-AllItems-CraftedMalevolentGladiator'sEmblemofMeditation-98813"
 value: {
  dps: 85197.17684
  tps: 590536.24852
  dtps: 55953.71775
  hps: 22623.18178
 }
}
dps_results: {
 key: "TestGuardian-AllItems-CraftedMalevolentGladiator'sEmblemofTenacity-98812"
 value: {
  dps: 85197.17684
  tps: 590536.24852
  dtps: 55993.34292
  hps: 23018.93055
 }
}
dps_results: {
 key: "TestGuardian-AllItems-CraftedMalevolentGladiator'sInsigniaofConquest-98760"
 value: {
  dps: 87820.78288
  tps: 608648.60247
  dtps: 54714.73014
  hps: 23144.9562
 }
}
dps_results: {
 key: "TestGuardian-AllItems-CraftedMalevolentGladiator'sInsigniaofDominance-98911"
 value: {
  dps: 85236.90179
  tps: 590594.61845
  dtps: 55953.71775
  hps: 22571.53866
 }
}
dps_results: {
 key: "TestGuardian-AllItems-CraftedMalevolentGladiator'sInsigniaofVictory-98917"
 value: {
  dps: 85997.3058
  tps: 595964.36914
  dtps: 55899.36359
  hps: 22745.61609
 }
}
dps_results: {
 key: "TestGuardian-AllItems-CurseofHubris-102307"
 value: {
  dps: 89543.88703
  tps: 618668.79163
  dtps: 55657.72288
  hps: 25503.64067
 }
}
dps_results: {
 key: "TestGuardian-AllItems-CurseofHubris-104649"
 value: {
  dps: 90356.77736
  tps: 624251.79846
  dtps: 55735.52042
  hps: 25855.26385
 }
}
dps_results: {
 key: "TestGuardian-AllItems-CurseofHubris-104898"
 value: {
  dps: 89213.80631
  tps: 616321.76873
  dtps: 55703.39449
  hps: 25283.64787
 }
}
dps_results: {
 key: "TestGuardian-AllItems-CurseofHubris-105147"
 value: {
  dps: 88798.56815
  tps: 613739.25303
  dtps: 56229.97466
  hps: 25212.93794
 }
}
dps_results: {
 key: "TestGuardian-AllItems-CurseofHubris-105396"
 value: {
  dps: 89850.44978
  tps: 620782.92875
  dtps: 55699.26586
  hps: 25559.12002
 }
}
dps_results: {
 key: "TestGuardian-AllItems-CurseofHubris-105645"
 value: {
  dps: 90646.84188
  tps: 626247.98257
  dtps: 55454.06659
  hps: 25918.10612
 }
}
dps_results: {
 key: "TestGuardian-AllItems-CutstitcherMedallion-93255"
 value: {
  dps: 85232.43232
  tps: 590594.61845
  dtps: 55953.71775
  hps: 22571.53866
 }
}
dps_results: {
 key: "TestGuardian-AllItems-Daelo'sFinalWords-87496"
 value: {
  dps: 86934.16731
  tps: 601990.92764
  dtps: 55059.58242
  hps: 22743.69182
 }
}
dps_results: {
 key: "TestGuardian-AllItems-DarkglowEmbroidery(Rank3)-4893"
 value: {
  dps: 89289.96395
  tps: 618205.71538
  dtps: 54557.46579
  hps: 24180.52925
 }
}
dps_results: {
 key: "TestGuardian-AllItems-DarkmistVortex-87172"
 value: {
  dps: 87216.71697
  tps: 604432.82194
  dtps: 55332.01142
  hps: 23749.65334
 }
}
dps_results: {
 key: "TestGuardian-AllItems-DeadeyeBadgeoftheShieldwall-93346"
 value: {
  dps: 86828.97861
  tps: 601862.8993
  dtps: 54609.85496
  hps: 23264.23111
 }
}
dps_results: {
 key: "TestGuardian-AllItems-DelicateVialoftheSanguinaire-96895"
 value: {
  dps: 85190.79143
  tps: 590491.53437
  dtps: 53697.19309
  hps: 22557.17889
 }
}
dps_results: {
 key: "TestGuardian-AllItems-DestructivePrimalDiamond"
 value: {
  dps: 90099.71428
  tps: 623639.67454
  dtps: 55088.59687
  hps: 24615.10669
 }
}
dps_results: {
 key: "TestGuardian-AllItems-DisciplineofXuen-103986"
 value: {
  dps: 88501.11349
  tps: 613461.74733
  dtps: 52975.3791
  hps: 23365.84804
 }
}
dps_results: {
 key: "TestGuardian-AllItems-Dominator'sArcaneBadge-93342"
 value: {
  dps: 85581.70111
  tps: 594582.15428
  dtps: 56149.65242
  hps: 21976.20575
 }
}
dps_results: {
 key: "TestGuardian-AllItems-Dominator'sDeadeyeBadge-93341"
 value: {
  dps: 86828.97861
  tps: 601862.8993
  dtps: 54609.85496
  hps: 23264.23111
 }
}
dps_results: {
 key: "TestGuardian-AllItems-Dominator'sDurableBadge-93345"
 value: {
  dps: 85257.6611
  tps: 590959.79432
  dtps: 54748.33128
  hps: 22618.45534
 }
}
dps_results: {
 key: "TestGuardian-AllItems-Dominator'sKnightlyBadge-93344"
 value: {
  dps: 85732.89723
  tps: 594235.68432
  dtps: 55280.02137
  hps: 22720.54356
 }
}
dps_results: {
 key: "TestGuardian-AllItems-Dominator'sMendingBadge-93343"
 value: {
  dps: 85217.12739
  tps: 590594.61845
  dtps: 55953.71775
  hps: 22571.53866
 }
}
dps_results: {
 key: "TestGuardian-AllItems-DreadfulGladiator'sBadgeofConquest-84344"
 value: {
  dps: 86609.77762
  tps: 600226.54271
  dtps: 55737.72929
  hps: 22760.49076
 }
}
dps_results: {
 key: "TestGuardian-AllItems-DreadfulGladiator'sBadgeofDominance-84488"
 value: {
  dps: 85224.09375
  tps: 590536.24852
  dtps: 55953.71775
  hps: 22571.53866
 }
}
dps_results: {
 key: "TestGuardian-AllItems-DreadfulGladiator'sBadgeofVictory-84490"
 value: {
  dps: 85620.56294
  tps: 593373.13811
  dtps: 55914.61456
  hps: 22643.43563
 }
}
dps_results: {
 key: "TestGuardian-AllItems-DreadfulGladiator'sEmblemofCruelty-84399"
 value: {
  dps: 86935.80338
  tps: 602517.22314
  dtps: 55820.85046
  hps: 23562.86997
 }
}
dps_results: {
 key: "TestGuardian-AllItems-DreadfulGladiator'sEmblemofMeditation-84401"
 value: {
  dps: 85197.17684
  tps: 590536.24852
  dtps: 55953.71775
  hps: 22623.18178
 }
}
dps_results: {
 key: "TestGuardian-AllItems-DreadfulGladiator'sEmblemofTenacity-84400"
 value: {
  dps: 85260.46173
  tps: 590979.46319
  dtps: 55239.51613
  hps: 22825.60574
 }
}
dps_results: {
 key: "TestGuardian-AllItems-DreadfulGladiator'sInsigniaofConquest-84349"
 value: {
  dps: 87385.89357
  tps: 605599.43666
  dtps: 55271.79125
  hps: 23135.4265
 }
}
dps_results: {
 key: "TestGuardian-AllItems-DreadfulGladiator'sInsigniaofDominance-84489"
 value: {
  dps: 85212.16964
  tps: 590536.24852
  dtps: 55953.71775
  hps: 22571.53866
 }
}
dps_results: {
 key: "TestGuardian-AllItems-DreadfulGladiator'sInsigniaofVictory-84495"
 value: {
  dps: 85795.54293
  tps: 594599.46934
  dtps: 55910.99056
  hps: 22675.12261
 }
}
dps_results: {
 key: "TestGuardian-AllItems-DurableBadgeoftheShieldwall-93350"
 value: {
  dps: 85257.6611
  tps: 590959.79432
  dtps: 54748.33128
  hps: 22618.45534
 }
}
dps_results: {
 key: "TestGuardian-AllItems-EffulgentPrimalDiamond"
 value: {
  dps: 89287.69965
  tps: 618190.0285
  dtps: 55210.15399
  hps: 24228.37711
 }
}
dps_results: {
 key: "TestGuardian-AllItems-EmberPrimalDiamond"
 value: {
  dps: 89302.44899
  tps: 618205.71538
  dtps: 55264.61416
  hps: 24136.85777
 }
}
dps_results: {
 key: "TestGuardian-AllItems-EmblemofKypariZar-84077"
 value: {
  dps: 86717.63118
  tps: 600879.19997
  dtps: 55823.65207
  hps: 24732.36052
 }
}
dps_results: {
 key: "TestGuardian-AllItems-EmblemoftheCatacombs-83733"
 value: {
  dps: 85329.08013
  tps: 591468.13331
  dtps: 55710.54158
  hps: 23057.08244
 }
}
dps_results: {
 key: "TestGuardian-AllItems-EmptyFruitBarrel-81133"
 value: {
  dps: 85197.17684
  tps: 590536.24852
  dtps: 55953.71775
  hps: 22571.53866
 }
}
dps_results: {
 key: "TestGuardian-AllItems-EnchantWeapon-BloodyDancingSteel-5125"
 value: {
  dps: 90756.89644
  tps: 628226.21738
  dtps: 53990.44998
  hps: 24577.09232
 }
}
dps_results: {
 key: "TestGuardian-AllItems-EnchantWeapon-Colossus-4445"
 value: {
  dps: 89287.69965
  tps: 618190.0285
  dtps: 53830.891
  hps: 24229.73775
 }
}
dps_results: {
 key: "TestGuardian-AllItems-EnchantWeapon-DancingSteel-4444"
 value: {
  dps: 90671.91564
  tps: 627631.01339
  dtps: 53747.82973
  hps: 24241.87932
 }
}
dps_results: {
 key: "TestGuardian-AllItems-EnchantWeapon-JadeSpirit-4442"
 value: {
  dps: 89325.35523
  tps: 618247.28015
  dtps: 54539.2615
  hps: 24228.77394
 }
}
dps_results: {
 key: "TestGuardian-AllItems-EnchantWeapon-River'sSong-4446"
 value: {
  dps: 89221.57214
  tps: 617727.26246
  dtps: 54256.18964
  hps: 24125.09174
 }
}
dps_results: {
 key: "TestGuardian-AllItems-EnchantWeapon-SpiritofConquest-5124"
 value: {
  dps: 89287.69965
  tps: 618190.0285
  dtps: 54539.2615
  hps: 24228.77394
 }
}
dps_results: {
 key: "TestGuardian-AllItems-EnchantWeapon-Windsong-4441"
 value: {
  dps: 89560.79405
  tps: 620076.853
  dtps: 54262.9343
  hps: 25033.5338
 }
}
dps_results: {
 key: "TestGuardian-AllItems-EnigmaticPrimalDiamond"
 value: {
  dps: 90099.71428
  tps: 623639.67454
  dtps: 55088.59687
  hps: 24615.10669
 }
}
dps_results: {
 key: "TestGuardian-AllItems-EssenceofTerror-87175"
 value: {
  dps: 85599.26652
  tps: 593162.80686
  dtps: 55864.2898
  hps: 22964.67373
 }
}
dps_results: {
 key: "TestGuardian-AllItems-EternalPrimalDiamond"
 value: {
  dps: 89177.03436
  tps: 617415.24625
  dtps: 55110.96106
  hps: 24006.70757
 }
}
dps_results: {
 key: "TestGuardian-AllItems-EvilEyeofGalakras-105491"
 value: {
  dps: 85651.08493
  tps: 593519.3936
  dtps: 55909.90406
  hps: 22647.85188
 }
}
dps_results: {
 key: "TestGuardian-AllItems-FabledFeatherofJi-Kun-96842"
 value: {
  dps: 88200.50531
  tps: 611225.18575
  dtps: 55317.25472
  hps: 23097.98315
 }
}
dps_results: {
 key: "TestGuardian-AllItems-FearwurmBadge-84074"
 value: {
  dps: 86673.58517
  tps: 600568.44489
  dtps: 55781.47138
  hps: 24554.40016
 }
}
dps_results: {
 key: "TestGuardian-AllItems-FearwurmRelic-84070"
 value: {
  dps: 86609.59423
  tps: 600162.36636
  dtps: 56107.89209
  hps: 24151.5849
 }
}
dps_results: {
 key: "TestGuardian-AllItems-FelsoulIdolofDestruction-101263"
 value: {
  dps: 85610.5086
  tps: 593430.54396
  dtps: 55041.23734
  hps: 23190.38353
 }
}
dps_results: {
 key: "TestGuardian-AllItems-FelsoulStoneofDestruction-101266"
 value: {
  dps: 85232.43232
  tps: 590594.61845
  dtps: 54981.07017
  hps: 22571.1449
 }
}
dps_results: {
 key: "TestGuardian-AllItems-Fen-Yu,FuryofXuen-102248"
 value: {
  dps: 98648.95593
  tps: 683839.7329
  dtps: 52563.01603
  hps: 26688.32736
 }
}
dps_results: {
 key: "TestGuardian-AllItems-FlashfrozenResinGlobule-100951"
 value: {
  dps: 86483.97074
  tps: 598961.74841
  dtps: 55247.70337
  hps: 22663.85129
 }
}
dps_results: {
 key: "TestGuardian-AllItems-FlashfrozenResinGlobule-81263"
 value: {
  dps: 86483.97074
  tps: 598961.74841
  dtps: 55247.70337
  hps: 22663.85129
 }
}
dps_results: {
 key: "TestGuardian-AllItems-FleetPrimalDiamond"
 value: {
  dps: 89289.96395
  tps: 618205.71538
  dtps: 54892.16186
  hps: 24137.08121
 }
}
dps_results: {
 key: "TestGuardian-AllItems-ForlornPrimalDiamond"
 value: {
  dps: 89302.44899
  tps: 618205.71538
  dtps: 55264.61416
  hps: 24136.85777
 }
}
dps_results: {
 key: "TestGuardian-AllItems-FortitudeoftheZandalari-94516"
 value: {
  dps: 85260.46173
  tps: 590979.46319
  dtps: 54651.22135
  hps: 22824.83933
 }
}
dps_results: {
 key: "TestGuardian-AllItems-FortitudeoftheZandalari-95677"
 value: {
  dps: 85260.46173
  tps: 590979.46319
  dtps: 54892.20204
  hps: 22825.21198
 }
}
dps_results: {
 key: "TestGuardian-AllItems-FortitudeoftheZandalari-96049"
 value: {
  dps: 85260.46173
  tps: 590979.46319
  dtps: 54579.67242
  hps: 22824.83933
 }
}
dps_results: {
 key: "TestGuardian-AllItems-FortitudeoftheZandalari-96421"
 value: {
  dps: 85260.46173
  tps: 590979.46319
  dtps: 54491.54335
  hps: 22824.83933
 }
}
dps_results: {
 key: "TestGuardian-AllItems-FortitudeoftheZandalari-96793"
 value: {
  dps: 85260.46173
  tps: 590979.46319
  dtps: 54412.04923
  hps: 22824.83933
 }
}
dps_results: {
 key: "TestGuardian-AllItems-GazeoftheTwins-96915"
 value: {
  dps: 89396.26569
  tps: 619294.368
  dtps: 55549.2425
  hps: 25087.28488
 }
}
dps_results: {
 key: "TestGuardian-AllItems-Gerp'sPerfectArrow-87495"
 value: {
  dps: 87772.92723
  tps: 607747.91542
  dtps: 54989.44926
  hps: 23574.6494
 }
}
dps_results: {
 key: "TestGuardian-AllItems-Gong-Lu,StrengthofXuen-102249"
 value: {
  dps: 96806.57347
  tps: 671010.18308
  dtps: 53962.93227
  hps: 25300.79665
 }
}
dps_results: {
 key: "TestGuardian-AllItems-GrievousGladiator'sBadgeofConquest-100195"
 value: {
  dps: 87777.14857
  tps: 608041.12113
  dtps: 54547.62271
  hps: 23018.64166
 }
}
dps_results: {
 key: "TestGuardian-AllItems-GrievousGladiator'sBadgeofConquest-100603"
 value: {
  dps: 87777.14857
  tps: 608041.12113
  dtps: 54547.62271
  hps: 23018.64166
 }
}
dps_results: {
 key: "TestGuardian-AllItems-GrievousGladiator'sBadgeofConquest-102856"
 value: {
  dps: 87777.14857
  tps: 608041.12113
  dtps: 54547.62271
  hps: 23018.64166
 }
}
dps_results: {
 key: "TestGuardian-AllItems-GrievousGladiator'sBadgeofConquest-103145"
 value: {
  dps: 87777.14857
  tps: 608041.12113
  dtps: 54547.62271
  hps: 23018.64166
 }
}
dps_results: {
 key: "TestGuardian-AllItems-GrievousGladiator'sBadgeofDominance-100490"
 value: {
  dps: 85232.26211
  tps: 590536.24852
  dtps: 55953.71775
  hps: 22571.53866
 }
}
dps_results: {
 key: "TestGuardian-AllItems-GrievousGladiator'sBadgeofDominance-100576"
 value: {
  dps: 85232.26211
  tps: 590536.24852
  dtps: 55953.71775
  hps: 22571.53866
 }
}
dps_results: {
 key: "TestGuardian-AllItems-GrievousGladiator'sBadgeofDominance-102830"
 value: {
  dps: 85232.26211
  tps: 590536.24852
  dtps: 55953.71775
  hps: 22571.53866
 }
}
dps_results: {
 key: "TestGuardian-AllItems-GrievousGladiator'sBadgeofDominance-103308"
 value: {
  dps: 85232.26211
  tps: 590536.24852
  dtps: 55953.71775
  hps: 22571.53866
 }
}
dps_results: {
 key: "TestGuardian-AllItems-GrievousGladiator'sBadgeofVictory-100500"
 value: {
  dps: 86027.18335
  tps: 596116.16599
  dtps: 55864.9035
  hps: 22749.52612
 }
}
dps_results: {
 key: "TestGuardian-AllItems-GrievousGladiator'sBadgeofVictory-100579"
 value: {
  dps: 86027.18335
  tps: 596116.16599
  dtps: 55864.9035
  hps: 22749.52612
 }
}
dps_results: {
 key: "TestGuardian-AllItems-GrievousGladiator'sBadgeofVictory-102833"
 value: {
  dps: 86027.18335
  tps: 596116.16599
  dtps: 55864.9035
  hps: 22749.52612
 }
}
dps_results: {
 key: "TestGuardian-AllItems-GrievousGladiator'sBadgeofVictory-103314"
 value: {
  dps: 86027.18335
  tps: 596116.16599
  dtps: 55864.9035
  hps: 22749.52612
 }
}
dps_results: {
 key: "TestGuardian-AllItems-GrievousGladiator'sEmblemofCruelty-100305"
 value: {
  dps: 87959.56266
  tps: 609274.01679
  dtps: 55607.36535
  hps: 24713.36059
 }
}
dps_results: {
 key: "TestGuardian-AllItems-GrievousGladiator'sEmblemofCruelty-100626"
 value: {
  dps: 87959.56266
  tps: 609274.01679
  dtps: 55607.36535
  hps: 24713.36059
 }
}
dps_results: {
 key: "TestGuardian-AllItems-GrievousGladiator'sEmblemofCruelty-102877"
 value: {
  dps: 87959.56266
  tps: 609274.01679
  dtps: 55607.36535
  hps: 24713.36059
 }
}
dps_results: {
 key: "TestGuardian-AllItems-GrievousGladiator'sEmblemofCruelty-103210"
 value: {
  dps: 87959.56266
  tps: 609274.01679
  dtps: 55607.36535
  hps: 24713.36059
 }
}
dps_results: {
 key: "TestGuardian-AllItems-GrievousGladiator'sEmblemofMeditation-100307"
 value: {
  dps: 85197.17684
  tps: 590536.24852
  dtps: 55953.71775
  hps: 22623.18178
 }
}
dps_results: {
 key: "TestGuardian-AllItems-GrievousGladiator'sEmblemofMeditation-100559"
 value: {
  dps: 85197.17684
  tps: 590536.24852
  dtps: 55953.71775
  hps: 22623.18178
 }
}
dps_results: {
 key: "TestGuardian-AllItems-GrievousGladiator'sEmblemofMeditation-102813"
 value: {
  dps: 85197.17684
  tps: 590536.24852
  dtps: 55953.71775
  hps: 22623.18178
 }
}
dps_results: {
 key: "TestGuardian-AllItems-GrievousGladiator'sEmblemofMeditation-103212"
 value: {
  dps: 85197.17684
  tps: 590536.24852
  dtps: 55953.71775
  hps: 22623.18178
 }
}
dps_results: {
 key: "TestGuardian-AllItems-GrievousGladiator'sEmblemofTenacity-100306"
 value: {
  dps: 85193.31963
  tps: 590508.94741
  dtps: 56200.83518
  hps: 22943.66533
 }
}
dps_results: {
 key: "TestGuardian-AllItems-GrievousGladiator'sEmblemofTenacity-100652"
 value: {
  dps: 85193.31963
  tps: 590508.94741
  dtps: 56200.83518
  hps: 22943.66533
 }
}
dps_results: {
 key: "TestGuardian-AllItems-GrievousGladiator'sEmblemofTenacity-102903"
 value: {
  dps: 85193.31963
  tps: 590508.94741
  dtps: 56200.83518
  hps: 22943.66533
 }
}
dps_results: {
 key: "TestGuardian-AllItems-GrievousGladiator'sEmblemofTenacity-103211"
 value: {
  dps: 85193.31963
  tps: 590508.94741
  dtps: 56200.83518
  hps: 22943.66533
 }
}
dps_results: {
 key: "TestGuardian-AllItems-GrievousGladiator'sInsigniaofConquest-103150"
 value: {
  dps: 88783.91839
  tps: 615216.69008
  dtps: 54606.24014
  hps: 23152.46385
 }
}
dps_results: {
 key: "TestGuardian-AllItems-GrievousGladiator'sInsigniaofDominance-103309"
 value: {
  dps: 85228.8813
  tps: 590536.24852
  dtps: 55953.71775
  hps: 22571.53866
 }
}
dps_results: {
 key: "TestGuardian-AllItems-GrievousGladiator'sInsigniaofVictory-103319"
 value: {
  dps: 86372.7503
  tps: 598583.0098
  dtps: 55830.32114
  hps: 22831.37366
 }
}
dps_results: {
 key: "TestGuardian-AllItems-Hawkmaster'sTalon-89082"
 value: {
  dps: 87816.31929
  tps: 608515.52273
  dtps: 55220.92378
  hps: 24558.18909
 }
}
dps_results: {
 key: "TestGuardian-AllItems-Heart-LesionDefenderIdol-100999"
 value: {
  dps: 85158.04368
  tps: 590262.24649
  dtps: 55421.69758
  hps: 23136.43173
 }
}
dps_results: {
 key: "TestGuardian-AllItems-Heart-LesionDefenderStone-101002"
 value: {
  dps: 85157.80673
  tps: 590260.58791
  dtps: 55216.49589
  hps: 23135.83887
 }
}
dps_results: {
 key: "TestGuardian-AllItems-Heart-LesionIdolofBattle-100991"
 value: {
  dps: 87936.47426
  tps: 609281.04888
  dtps: 55716.94044
  hps: 23982.00323
 }
}
dps_results: {
 key: "TestGuardian-AllItems-Heart-LesionStoneofBattle-100990"
 value: {
  dps: 85796.62432
  tps: 594668.31545
  dtps: 54979.38525
  hps: 22699.16058
 }
}
dps_results: {
 key: "TestGuardian-AllItems-HeartofFire-81181"
 value: {
  dps: 85258.21323
  tps: 590963.70745
  dtps: 54394.43741
  hps: 22729.78426
 }
}
dps_results: {
 key: "TestGuardian-AllItems-HeartwarmerMedallion-93260"
 value: {
  dps: 85232.43232
  tps: 590594.61845
  dtps: 55953.71775
  hps: 22571.53866
 }
}
dps_results: {
 key: "TestGuardian-AllItems-HelmbreakerMedallion-93261"
 value: {
  dps: 87827.63122
  tps: 607730.33789
  dtps: 55768.15529
  hps: 24153.55517
 }
}
dps_results: {
 key: "TestGuardian-AllItems-Horridon'sLastGasp-96757"
 value: {
  dps: 85240.95508
  tps: 590654.27783
  dtps: 55953.71775
  hps: 22571.53866
 }
}
dps_results: {
 key: "TestGuardian-AllItems-ImpassivePrimalDiamond"
 value: {
  dps: 90099.71428
  tps: 623639.67454
  dtps: 55088.59687
  hps: 24615.10669
 }
}
dps_results: {
 key: "TestGuardian-AllItems-IndomitablePrimalDiamond"
 value: {
  dps: 89287.69965
  tps: 618190.0285
  dtps: 55210.15399
  hps: 24228.37711
 }
}
dps_results: {
 key: "TestGuardian-AllItems-InscribedBagofHydra-Spawn-96828"
 value: {
  dps: 85197.17684
  tps: 590536.24852
  dtps: 55041.83011
  hps: 22571.09869
 }
}
dps_results: {
 key: "TestGuardian-AllItems-InsigniaofKypariZar-84078"
 value: {
  dps: 85197.17684
  tps: 590536.24852
  dtps: 55501.29703
  hps: 22572.17452
 }
}
dps_results: {
 key: "TestGuardian-AllItems-IronBellyWok-89083"
 value: {
  dps: 86516.76368
  tps: 599518.51711
  dtps: 55897.56652
  hps: 23655.12153
 }
}
dps_results: {
 key: "TestGuardian-AllItems-IronProtectorTalisman-85181"
 value: {
  dps: 85197.17684
  tps: 590536.24852
  dtps: 55441.57901
  hps: 22965.75009
 }
}
dps_results: {
 key: "TestGuardian-AllItems-JadeBanditFigurine-86043"
 value: {
  dps: 87816.31929
  tps: 608515.52273
  dtps: 55220.92378
  hps: 24558.18909
 }
}
dps_results: {
 key: "TestGuardian-AllItems-JadeBanditFigurine-86772"
 value: {
  dps: 87344.17676
  tps: 605302.3319
  dtps: 55024.34977
  hps: 23244.00948
 }
}
dps_results: {
 key: "TestGuardian-AllItems-JadeCharioteerFigurine-86042"
 value: {
  dps: 86516.76368
  tps: 599518.51711
  dtps: 55897.56652
  hps: 23655.12153
 }
}
dps_results: {
 key: "TestGuardian-AllItems-JadeCharioteerFigurine-86771"
 value: {
  dps: 86286.38489
  tps: 597989.19831
  dtps: 55977.77044
  hps: 22649.71419
 }
}
dps_results: {
 key: "TestGuardian-AllItems-JadeCourtesanFigurine-86045"
 value: {
  dps: 85232.43232
  tps: 590594.61845
  dtps: 55953.71775
  hps: 22571.53866
 }
}
dps_results: {
 key: "TestGuardian-AllItems-JadeCourtesanFigurine-86774"
 value: {
  dps: 85232.43232
  tps: 590594.61845
  dtps: 55953.71775
  hps: 22571.53866
 }
}
dps_results: {
 key: "TestGuardian-AllItems-JadeMagistrateFigurine-86044"
 value: {
  dps: 87151.36354
  tps: 603119.18492
  dtps: 55822.86782
  hps: 23990.4141
 }
}
dps_results: {
 key: "TestGuardian-AllItems-JadeMagistrateFigurine-86773"
 value: {
  dps: 86975.20432
  tps: 601933.47325
  dtps: 55758.53372
  hps: 23687.77071
 }
}
dps_results: {
 key: "TestGuardian-AllItems-JadeWarlordFigurine-86046"
 value: {
  dps: 85253.80389
  tps: 590932.49321
  dtps: 55299.71702
  hps: 22899.17492
 }
}
dps_results: {
 key: "TestGuardian-AllItems-JadeWarlordFigurine-86775"
 value: {
  dps: 85218.29099
  tps: 590684.13371
  dtps: 55359.04749
  hps: 23150.06475
 }
}
dps_results: {
 key: "TestGuardian-AllItems-Ji-Kun'sRisingWinds-96843"
 value: {
  dps: 89360.77788
  tps: 619085.38725
  dtps: 55030.72841
  hps: 26174.7481
 }
}
dps_results: {
 key: "TestGuardian-AllItems-KnightlyBadgeoftheShieldwall-93349"
 value: {
  dps: 85732.89723
  tps: 594235.68432
  dtps: 55280.02137
  hps: 22720.54356
 }
}
dps_results: {
 key: "TestGuardian-AllItems-KnotofTenSongs-84073"
 value: {
  dps: 85197.17684
  tps: 590536.24852
  dtps: 55463.42582
  hps: 22571.29783
 }
}
dps_results: {
 key: "TestGuardian-AllItems-Kor'kronBookofHurting-92785"
 value: {
  dps: 85197.17684
  tps: 590536.24852
  dtps: 55953.71775
  hps: 22571.53866
 }
}
dps_results: {
 key: "TestGuardian-AllItems-Lao-Chin'sLiquidCourage-89079"
 value: {
  dps: 85253.80389
  tps: 590932.49321
  dtps: 55299.71702
  hps: 22899.17492
 }
}
dps_results: {
 key: "TestGuardian-AllItems-LeiShen'sFinalOrders-87072"
 value: {
  dps: 86124.31749
  tps: 597103.04412
  dtps: 55293.60082
  hps: 22908.42284
 }
}
dps_results: {
 key: "TestGuardian-AllItems-LessonsoftheDarkmaster-81268"
 value: {
  dps: 87757.556
  tps: 607896.93735
  dtps: 55086.11005
  hps: 23579.83531
 }
}
dps_results: {
 key: "TestGuardian-AllItems-LightdrinkerIdolofRage-101200"
 value: {
  dps: 87284.68625
  tps: 604911.78535
  dtps: 54021.98649
  hps: 23439.01648
 }
}
dps_results: {
 key: "TestGuardian-AllItems-LightdrinkerStoneofRage-101203"
 value: {
  dps: 87214.70181
  tps: 604536.45547
  dtps: 53778.77624
  hps: 23428.81724
 }
}
dps_results: {
 key: "TestGuardian-AllItems-LightoftheCosmos-87065"
 value: {
  dps: 85413.34016
  tps: 592287.21654
  dtps: 55350.3316
  hps: 22833.58881
 }
}
dps_results: {
 key: "TestGuardian-AllItems-LightweaveEmbroidery(Rank3)-4892"
 value: {
  dps: 89302.44899
  tps: 618205.71538
  dtps: 54557.46579
  hps: 24180.52925
 }
}
dps_results: {
 key: "TestGuardian-AllItems-LordBlastington'sScopeofDoom-4699"
 value: {
  dps: 89287.69965
  tps: 618190.0285
  dtps: 54539.2615
  hps: 24228.77394
 }
}
dps_results: {
 key: "TestGuardian-AllItems-MalevolentGladiator'sBadgeofConquest-84934"
 value: {
  dps: 87119.91856
  tps: 603731.81904
  dtps: 55475.40018
  hps: 22834.5572
 }
}
dps_results: {
 key: "TestGuardian-AllItems-MalevolentGladiator'sBadgeofConquest-91452"
 value: {
  dps: 87013.49003
  tps: 603007.03402
  dtps: 55607.07706
  hps: 22795.58059
 }
}
dps_results: {
 key: "TestGuardian-AllItems-MalevolentGladiator'sBadgeofDominance-84940"
 value: {
  dps: 85224.09375
  tps: 590536.24852
  dtps: 55953.71775
  hps: 22571.53866
 }
}
dps_results: {
 key: "TestGuardian-AllItems-MalevolentGladiator'sBadgeofDominance-91753"
 value: {
  dps: 85224.09375
  tps: 590536.24852
  dtps: 55953.71775
  hps: 22571.53866
 }
}
dps_results: {
 key: "TestGuardian-AllItems-MalevolentGladiator'sBadgeofVictory-84942"
 value: {
  dps: 85792.97181
  tps: 594546.77439
  dtps: 55902.79409
  hps: 22709.93347
 }
}
dps_results: {
 key: "TestGuardian-AllItems-MalevolentGladiator'sBadgeofVictory-91763"
 value: {
  dps: 85758.79412
  tps: 594317.75843
  dtps: 55903.81592
  hps: 22704.10148
 }
}
dps_results: {
 key: "TestGuardian-AllItems-MalevolentGladiator'sEmblemofCruelty-84936"
 value: {
  dps: 87287.09169
  tps: 604832.96083
  dtps: 55754.02035
  hps: 23800.22855
 }
}
dps_results: {
 key: "TestGuardian-AllItems-MalevolentGladiator'sEmblemofCruelty-91562"
 value: {
  dps: 87199.82692
  tps: 604222.05871
  dtps: 55756.21419
  hps: 23747.68251
 }
}
dps_results: {
 key: "TestGuardian-AllItems-MalevolentGladiator'sEmblemofMeditation-84939"
 value: {
  dps: 85197.17684
  tps: 590536.24852
  dtps: 55953.71775
  hps: 22623.18178
 }
}
dps_results: {
 key: "TestGuardian-AllItems-MalevolentGladiator'sEmblemofMeditation-91564"
 value: {
  dps: 85197.17684
  tps: 590536.24852
  dtps: 55953.71775
  hps: 22623.18178
 }
}
dps_results: {
 key: "TestGuardian-AllItems-MalevolentGladiator'sEmblemofTenacity-84938"
 value: {
  dps: 85197.17684
  tps: 590536.24852
  dtps: 55993.34292
  hps: 23045.43367
 }
}
dps_results: {
 key: "TestGuardian-AllItems-MalevolentGladiator'sEmblemofTenacity-91563"
 value: {
  dps: 85197.17684
  tps: 590536.24852
  dtps: 55993.34292
  hps: 23018.93055
 }
}
dps_results: {
 key: "TestGuardian-AllItems-MalevolentGladiator'sInsigniaofConquest-91457"
 value: {
  dps: 87619.65488
  tps: 607195.98435
  dtps: 55218.3595
  hps: 22958.45705
 }
}
dps_results: {
 key: "TestGuardian-AllItems-MalevolentGladiator'sInsigniaofDominance-91754"
 value: {
  dps: 85236.53816
  tps: 590592.07301
  dtps: 55953.71775
  hps: 22571.53866
 }
}
dps_results: {
 key: "TestGuardian-AllItems-MalevolentGladiator'sInsigniaofVictory-91768"
 value: {
  dps: 85919.51462
  tps: 595435.16718
  dtps: 55907.50689
  hps: 22688.0617
 }
}
dps_results: {
 key: "TestGuardian-AllItems-MarkoftheCatacombs-83731"
 value: {
  dps: 86599.0711
  tps: 600159.96154
  dtps: 55160.68494
  hps: 23316.33911
 }
}
dps_results: {
 key: "TestGuardian-AllItems-MarkoftheHardenedGrunt-92783"
 value: {
  dps: 85197.17684
  tps: 590536.24852
  dtps: 55953.71775
  hps: 22571.53866
 }
}
dps_results: {
 key: "TestGuardian-AllItems-MedallionofMystifyingVapors-93257"
 value: {
  dps: 85228.37696
  tps: 590754.57769
  dtps: 54132.37229
  hps: 22427.16072
 }
}
dps_results: {
 key: "TestGuardian-AllItems-MedallionoftheCatacombs-83734"
 value: {
  dps: 85197.17684
  tps: 590536.24852
  dtps: 55414.3092
  hps: 22571.30377
 }
}
dps_results: {
 key: "TestGuardian-AllItems-MendingBadgeoftheShieldwall-93348"
 value: {
  dps: 85217.12739
  tps: 590594.61845
  dtps: 55953.71775
  hps: 22571.53866
 }
}
dps_results: {
 key: "TestGuardian-AllItems-MirrorScope-4700"
 value: {
  dps: 89287.69965
  tps: 618190.0285
  dtps: 54539.2615
  hps: 24228.77394
 }
}
dps_results: {
 key: "TestGuardian-AllItems-MistdancerDefenderIdol-101089"
 value: {
  dps: 85158.04368
  tps: 590262.24649
  dtps: 55037.72155
  hps: 23136.75706
 }
}
dps_results: {
 key: "TestGuardian-AllItems-MistdancerDefenderStone-101087"
 value: {
  dps: 85157.80673
  tps: 590260.58791
  dtps: 55159.37573
  hps: 23135.83887
 }
}
dps_results: {
 key: "TestGuardian-AllItems-MistdancerIdolofRage-101113"
 value: {
  dps: 87499.96067
  tps: 606484.37068
  dtps: 54797.38028
  hps: 23090.1799
 }
}
dps_results: {
 key: "TestGuardian-AllItems-MistdancerStoneofRage-101117"
 value: {
  dps: 87246.91054
  tps: 604762.11047
  dtps: 53746.91359
  hps: 23512.56382
 }
}
dps_results: {
 key: "TestGuardian-AllItems-MistdancerStoneofWisdom-101107"
 value: {
  dps: 85232.43232
  tps: 590594.61845
  dtps: 55953.71775
  hps: 22571.53866
 }
}
dps_results: {
 key: "TestGuardian-AllItems-MithrilWristwatch-87572"
 value: {
  dps: 87092.21008
  tps: 603612.14578
  dtps: 55651.62323
  hps: 23582.02194
 }
}
dps_results: {
 key: "TestGuardian-AllItems-MountainsageIdolofDestruction-101069"
 value: {
  dps: 85533.51073
  tps: 592890.40496
  dtps: 54927.35809
  hps: 22840.03083
 }
}
dps_results: {
 key: "TestGuardian-AllItems-MountainsageStoneofDestruction-101072"
 value: {
  dps: 85232.43232
  tps: 590594.61845
  dtps: 54963.34205
  hps: 22571.53866
 }
}
dps_results: {
 key: "TestGuardian-AllItems-OathswornDefenderIdol-101303"
 value: {
  dps: 85167.90988
  tps: 590331.15215
  dtps: 55021.4666
  hps: 22913.49731
 }
}
dps_results: {
 key: "TestGuardian-AllItems-OathswornDefenderStone-101306"
 value: {
  dps: 85160.60737
  tps: 590280.25677
  dtps: 55203.0704
  hps: 23291.3873
 }
}
dps_results: {
 key: "TestGuardian-AllItems-OathswornIdolofBattle-101295"
 value: {
  dps: 87913.71304
  tps: 609117.15077
  dtps: 55732.08154
  hps: 23865.2292
 }
}
dps_results: {
 key: "TestGuardian-AllItems-OathswornStoneofBattle-101294"
 value: {
  dps: 85796.62432
  tps: 594668.31545
  dtps: 54985.94501
  hps: 22699.55434
 }
}
dps_results: {
 key: "TestGuardian-AllItems-PhaseFingers-4697"
 value: {
  dps: 89289.96395
  tps: 618205.71538
  dtps: 54089.02055
  hps: 24198.45722
 }
}
dps_results: {
 key: "TestGuardian-AllItems-PouchofWhiteAsh-103639"
 value: {
  dps: 85197.17684
  tps: 590536.24852
  dtps: 55953.71775
  hps: 22571.53866
 }
}
dps_results: {
 key: "TestGuardian-AllItems-PowerfulPrimalDiamond"
 value: {
  dps: 89287.69965
  tps: 618190.0285
  dtps: 55210.15399
  hps: 24228.37711
 }
}
dps_results: {
 key: "TestGuardian-AllItems-PriceofProgress-81266"
 value: {
  dps: 85217.12739
  tps: 590594.61845
  dtps: 55953.71775
  hps: 22571.53866
 }
}
dps_results: {
 key: "TestGuardian-AllItems-PridefulGladiator'sBadgeofConquest-102659"
 value: {
  dps: 88483.14654
  tps: 612815.45593
  dtps: 54182.52227
  hps: 23421.86109
 }
}
dps_results: {
 key: "TestGuardian-AllItems-PridefulGladiator'sBadgeofConquest-103342"
 value: {
  dps: 88483.14654
  tps: 612815.45593
  dtps: 54182.52227
  hps: 23421.86109
 }
}
dps_results: {
 key: "TestGuardian-AllItems-PridefulGladiator'sBadgeofDominance-102633"
 value: {
  dps: 85232.26211
  tps: 590536.24852
  dtps: 55953.71775
  hps: 22571.53866
 }
}
dps_results: {
 key: "TestGuardian-AllItems-PridefulGladiator'sBadgeofDominance-103505"
 value: {
  dps: 85232.26211
  tps: 590536.24852
  dtps: 55953.71775
  hps: 22571.53866
 }
}
dps_results: {
 key: "TestGuardian-AllItems-PridefulGladiator'sBadgeofVictory-102636"
 value: {
  dps: 86256.78195
  tps: 597654.64745
  dtps: 55858.26253
  hps: 22788.99146
 }
}
dps_results: {
 key: "TestGuardian-AllItems-PridefulGladiator'sBadgeofVictory-103511"
 value: {
  dps: 86256.78195
  tps: 597654.64745
  dtps: 55858.26253
  hps: 22788.99146
 }
}
dps_results: {
 key: "TestGuardian-AllItems-PridefulGladiator'sEmblemofCruelty-102680"
 value: {
  dps: 88770.66458
  tps: 614852.12145
  dtps: 55709.27531
  hps: 25364.74466
 }
}
dps_results: {
 key: "TestGuardian-AllItems-PridefulGladiator'sEmblemofCruelty-103407"
 value: {
  dps: 88770.66458
  tps: 614852.12145
  dtps: 55709.27531
  hps: 25364.74466
 }
}
dps_results: {
 key: "TestGuardian-AllItems-PridefulGladiator'sEmblemofMeditation-102616"
 value: {
  dps: 85197.17684
  tps: 590536.24852
  dtps: 55953.71775
  hps: 22623.18178
 }
}
dps_results: {
 key: "TestGuardian-AllItems-PridefulGladiator'sEmblemofMeditation-103409"
 value: {
  dps: 85197.17684
  tps: 590536.24852
  dtps: 55953.71775
  hps: 22623.18178
 }
}
dps_results: {
 key: "TestGuardian-AllItems-PridefulGladiator'sEmblemofTenacity-102706"
 value: {
  dps: 85242.47872
  tps: 590853.31706
  dtps: 56143.09508
  hps: 23242.5692
 }
}
dps_results: {
 key: "TestGuardian-AllItems-PridefulGladiator'sEmblemofTenacity-103408"
 value: {
  dps: 85242.47872
  tps: 590853.31706
  dtps: 56143.09508
  hps: 23242.5692
 }
}
dps_results: {
 key: "TestGuardian-AllItems-PridefulGladiator'sInsigniaofConquest-103347"
 value: {
  dps: 89999.50107
  tps: 623591.61399
  dtps: 54556.68269
  hps: 23780.55664
 }
}
dps_results: {
 key: "TestGuardian-AllItems-PridefulGladiator'sInsigniaofDominance-103506"
 value: {
  dps: 85228.71967
  tps: 590536.24852
  dtps: 55953.71775
  hps: 22571.53866
 }
}
dps_results: {
 key: "TestGuardian-AllItems-PridefulGladiator'sInsigniaofVictory-103516"
 value: {
  dps: 86652.39839
  tps: 600424.98215
  dtps: 55862.27624
  hps: 22824.8898
 }
}
dps_results: {
 key: "TestGuardian-AllItems-Primordius'TalismanofRage-96873"
 value: {
  dps: 89501.03606
  tps: 619823.76703
  dtps: 55637.59328
  hps: 25454.26203
 }
}
dps_results: {
 key: "TestGuardian-AllItems-Qian-Le,CourageofNiuzao-102245"
 value: {
  dps: 94508.30342
  tps: 653875.71333
  dtps: 50328.50396
  hps: 26738.39041
 }
}
dps_results: {
 key: "TestGuardian-AllItems-Qian-Ying,FortitudeofNiuzao-102250"
 value: {
  dps: 90164.43479
  tps: 624305.97675
  dtps: 51168.80722
  hps: 25008.7425
 }
}
dps_results: {
 key: "TestGuardian-AllItems-Qin-xi'sPolarizingSeal-87075"
 value: {
  dps: 85205.15177
  tps: 590592.07301
  dtps: 55953.71775
  hps: 22571.53866
 }
}
dps_results: {
 key: "TestGuardian-AllItems-RegaliaoftheEternalBlossom"
 value: {
  dps: 78227.40415
  tps: 542055.31124
  dtps: 53195.35956
  hps: 20329.08563
 }
}
dps_results: {
 key: "TestGuardian-AllItems-RegaliaoftheHauntedForest"
 value: {
  dps: 79927.42794
  tps: 553605.73137
  dtps: 51763.54015
  hps: 23205.99778
 }
}
dps_results: {
 key: "TestGuardian-AllItems-RegaliaoftheShatteredVale"
 value: {
  dps: 82160.66459
  tps: 570325.38159
  dtps: 51456.97956
  hps: 26001.45301
 }
}
dps_results: {
 key: "TestGuardian-AllItems-RelicofChi-Ji-79330"
 value: {
  dps: 85232.43232
  tps: 590594.61845
  dtps: 55953.71775
  hps: 22571.53866
 }
}
dps_results: {
 key: "TestGuardian-AllItems-RelicofKypariZar-84075"
 value: {
  dps: 86534.46728
  tps: 599930.36962
  dtps: 56404.50391
  hps: 23879.61068
 }
}
dps_results: {
 key: "TestGuardian-AllItems-RelicofNiuzao-79329"
 value: {
  dps: 85362.4668
  tps: 591693.23209
  dtps: 54671.70916
  hps: 22802.12663
 }
}
dps_results: {
 key: "TestGuardian-AllItems-RelicofXuen-79327"
 value: {
  dps: 86279.28246
  tps: 597902.656
  dtps: 55865.28765
  hps: 22822.39291
 }
}
dps_results: {
 key: "TestGuardian-AllItems-RelicofXuen-79328"
 value: {
  dps: 88267.9529
  tps: 611769.22041
  dtps: 54483.02932
  hps: 23609.67897
 }
}
dps_results: {
 key: "TestGuardian-AllItems-RelicofYu'lon-79331"
 value: {
  dps: 85232.43232
  tps: 590594.61845
  dtps: 55953.71775
  hps: 22571.53866
 }
}
dps_results: {
 key: "TestGuardian-AllItems-Renataki'sSoulCharm-96741"
 value: {
  dps: 101762.62598
  tps: 705130.4076
  dtps: 50609.54877
  hps: 27467.36063
 }
}
dps_results: {
 key: "TestGuardian-AllItems-ResolveofNiuzao-103690"
 value: {
  dps: 85298.2715
  tps: 591243.96182
  dtps: 53900.05094
  hps: 22604.36279
 }
}
dps_results: {
 key: "TestGuardian-AllItems-ResolveofNiuzao-103990"
 value: {
  dps: 85311.38424
  tps: 591335.75933
  dtps: 52738.49928
  hps: 22475.50575
 }
}
dps_results: {
 key: "TestGuardian-AllItems-ReverberatingPrimalDiamond"
 value: {
  dps: 90670.66993
  tps: 627803.51427
  dtps: 55262.5285
  hps: 24167.12594
 }
}
dps_results: {
 key: "TestGuardian-AllItems-RevitalizingPrimalDiamond"
 value: {
  dps: 90537.8851
  tps: 626888.47936
  dtps: 55264.61416
  hps: 24136.85777
 }
}
dps_results: {
 key: "TestGuardian-AllItems-SI:7Operative'sManual-92784"
 value: {
  dps: 85197.17684
  tps: 590536.24852
  dtps: 55953.71775
  hps: 22571.53866
 }
}
dps_results: {
 key: "TestGuardian-AllItems-ScrollofReveredAncestors-89080"
 value: {
  dps: 85232.43232
  tps: 590594.61845
  dtps: 55953.71775
  hps: 22571.53866
 }
}
dps_results: {
 key: "TestGuardian-AllItems-Shock-ChargerMedallion-93259"
 value: {
  dps: 85730.08249
  tps: 594218.17838
  dtps: 55152.67762
  hps: 23200.55355
 }
}
dps_results: {
 key: "TestGuardian-AllItems-SigilofCompassion-83736"
 value: {
  dps: 85197.17684
  tps: 590536.24852
  dtps: 55414.3092
  hps: 22571.30377
 }
}
dps_results: {
 key: "TestGuardian-AllItems-SigilofDevotion-83740"
 value: {
  dps: 86599.0711
  tps: 600159.96154
  dtps: 55069.70616
  hps: 23316.37966
 }
}
dps_results: {
 key: "TestGuardian-AllItems-SigilofFidelity-83737"
 value: {
  dps: 86721.68078
  tps: 600893.7232
  dtps: 55862.19596
  hps: 23707.02568
 }
}
dps_results: {
 key: "TestGuardian-AllItems-SigilofGrace-83738"
 value: {
  dps: 85345.88199
  tps: 591798.31375
  dtps: 55494.9408
  hps: 22697.65422
 }
}
dps_results: {
 key: "TestGuardian-AllItems-SigilofKypariZar-84076"
 value: {
  dps: 86658.21875
  tps: 600573.94699
  dtps: 54972.52165
  hps: 23410.42104
 }
}
dps_results: {
 key: "TestGuardian-AllItems-SigilofPatience-83739"
 value: {
  dps: 85257.6611
  tps: 590959.79432
  dtps: 55352.946
  hps: 22618.36217
 }
}
dps_results: {
 key: "TestGuardian-AllItems-SigiloftheCatacombs-83732"
 value: {
  dps: 86459.92355
  tps: 599147.96336
  dtps: 55711.19997
  hps: 23756.27237
 }
}
dps_results: {
 key: "TestGuardian-AllItems-SinisterPrimalDiamond"
 value: {
  dps: 89928.61884
  tps: 622441.83919
  dtps: 54909.50359
  hps: 24525.79471
 }
}
dps_results: {
 key: "TestGuardian-AllItems-SkullrenderMedallion-93256"
 value: {
  dps: 87827.63122
  tps: 607730.33789
  dtps: 55768.15529
  hps: 24153.55517
 }
}
dps_results: {
 key: "TestGuardian-AllItems-SpiritsoftheSun-87163"
 value: {
  dps: 85232.43232
  tps: 590594.61845
  dtps: 55953.71775
  hps: 22571.53866
 }
}
dps_results: {
 key: "TestGuardian-AllItems-SpringrainIdolofDestruction-101023"
 value: {
  dps: 85485.9967
  tps: 592558.32609
  dtps: 54957.86502
  hps: 22979.6745
 }
}
dps_results: {
 key: "TestGuardian-AllItems-SpringrainIdolofRage-101009"
 value: {
  dps: 87516.78182
  tps: 606547.02811
  dtps: 54026.55075
  hps: 23345.47027
 }
}
dps_results: {
 key: "TestGuardian-AllItems-SpringrainStoneofDestruction-101026"
 value: {
  dps: 85232.43232
  tps: 590594.61845
  dtps: 55036.24717
  hps: 22570.79283
 }
}
dps_results: {
 key: "TestGuardian-AllItems-SpringrainStoneofRage-101012"
 value: {
  dps: 87214.70181
  tps: 604536.45547
  dtps: 53723.23944
  hps: 23429.63627
 }
}
dps_results: {
 key: "TestGuardian-AllItems-SpringrainStoneofWisdom-101041"
 value: {
  dps: 85240.95508
  tps: 590654.27783
  dtps: 55953.71775
  hps: 22571.53866
 }
}
dps_results: {
 key: "TestGuardian-AllItems-Static-Caster'sMedallion-93254"
 value: {
  dps: 85730.08249
  tps: 594218.17838
  dtps: 55152.67762
  hps: 23200.55355
 }
}
dps_results: {
 key: "TestGuardian-AllItems-SteadfastFootman'sMedallion-92782"
 value: {
  dps: 85197.17684
  tps: 590536.24852
  dtps: 55953.71775
  hps: 22571.53866
 }
}
dps_results: {
 key: "TestGuardian-AllItems-SteadfastTalismanoftheShado-PanAssault-94507"
 value: {
  dps: 85231.17759
  tps: 590774.24656
  dtps: 54250.8625
  hps: 22581.53932
 }
}
dps_results: {
 key: "TestGuardian-AllItems-StreamtalkerIdolofDestruction-101222"
 value: {
  dps: 85535.07733
  tps: 592901.89844
  dtps: 54792.72583
  hps: 23536.58196
 }
}
dps_results: {
 key: "TestGuardian-AllItems-StreamtalkerIdolofRage-101217"
 value: {
  dps: 87457.74234
  tps: 606152.75581
  dtps: 54374.97745
  hps: 23367.4058
 }
}
dps_results: {
 key: "TestGuardian-AllItems-StreamtalkerStoneofDestruction-101225"
 value: {
  dps: 85232.43232
  tps: 590594.61845
  dtps: 55039.05708
  hps: 22571.18659
 }
}
dps_results: {
 key: "TestGuardian-AllItems-StreamtalkerStoneofRage-101220"
 value: {
  dps: 87214.70181
  tps: 604536.45547
  dtps: 53786.7569
  hps: 23429.37309
 }
}
dps_results: {
 key: "TestGuardian-AllItems-StreamtalkerStoneofWisdom-101250"
 value: {
  dps: 85232.43232
  tps: 590594.61845
  dtps: 55953.71775
  hps: 22571.53866
 }
}
dps_results: {
 key: "TestGuardian-AllItems-StuffofNightmares-87160"
 value: {
  dps: 85310.09831
  tps: 591326.78195
  dtps: 53628.96376
  hps: 22658.09434
 }
}
dps_results: {
 key: "TestGuardian-AllItems-SunsoulDefenderIdol-101160"
 value: {
  dps: 85258.01786
  tps: 590962.04227
  dtps: 55445.77825
  hps: 23138.27622
 }
}
dps_results: {
 key: "TestGuardian-AllItems-SunsoulDefenderStone-101163"
 value: {
  dps: 85160.60737
  tps: 590280.25677
  dtps: 55180.0575
  hps: 23291.43709
 }
}
dps_results: {
 key: "TestGuardian-AllItems-SunsoulIdolofBattle-101152"
 value: {
  dps: 87929.13431
  tps: 609166.65496
  dtps: 55734.67421
  hps: 23870.56692
 }
}
dps_results: {
 key: "TestGuardian-AllItems-SunsoulStoneofBattle-101151"
 value: {
  dps: 85796.71274
  tps: 594668.93441
  dtps: 54951.8666
  hps: 22699.68536
 }
}
dps_results: {
 key: "TestGuardian-AllItems-SunsoulStoneofWisdom-101138"
 value: {
  dps: 85248.93001
  tps: 590710.10232
  dtps: 55953.71775
  hps: 22571.53866
 }
}
dps_results: {
 key: "TestGuardian-AllItems-SwordguardEmbroidery(Rank3)-4894"
 value: {
  dps: 89855.08409
  tps: 622017.77221
  dtps: 54570.87021
  hps: 24279.64811
 }
}
dps_results: {
 key: "TestGuardian-AllItems-SymboloftheCatacombs-83735"
 value: {
  dps: 86631.20488
  tps: 600191.40768
  dtps: 55180.43788
  hps: 23505.00794
 }
}
dps_results: {
 key: "TestGuardian-AllItems-SynapseSprings(MarkII)-4898"
 value: {
  dps: 89727.66646
  tps: 621644.68992
  dtps: 54698.13113
  hps: 24458.18
 }
}
dps_results: {
 key: "TestGuardian-AllItems-TalismanofBloodlust-96864"
 value: {
  dps: 89572.64986
  tps: 621342.56963
  dtps: 54521.64923
  hps: 25020.99502
 }
}
dps_results: {
 key: "TestGuardian-AllItems-TerrorintheMists-87167"
 value: {
  dps: 90682.35027
  tps: 627426.76302
  dtps: 55052.12243
  hps: 24549.17264
 }
}
dps_results: {
 key: "TestGuardian-AllItems-TheGloamingBlade-88149"
 value: {
  dps: 89287.69965
  tps: 618190.0285
  dtps: 54539.2615
  hps: 24228.77394
 }
}
dps_results: {
 key: "TestGuardian-AllItems-Thousand-YearPickledEgg-87573"
 value: {
  dps: 85942.74046
  tps: 595611.23862
  dtps: 56052.25596
  hps: 23304.20772
 }
}
dps_results: {
 key: "TestGuardian-AllItems-TrailseekerIdolofRage-101054"
 value: {
  dps: 87296.61452
  tps: 604953.11925
  dtps: 54585.37553
  hps: 22955.80435
 }
}
dps_results: {
 key: "TestGuardian-AllItems-TrailseekerStoneofRage-101057"
 value: {
  dps: 87214.70181
  tps: 604536.45547
  dtps: 53715.73911
  hps: 23429.15439
 }
}
dps_results: {
 key: "TestGuardian-AllItems-TyrannicalGladiator'sBadgeofConquest-100043"
 value: {
  dps: 87335.1408
  tps: 605046.90737
  dtps: 55177.00079
  hps: 22909.01814
 }
}
dps_results: {
 key: "TestGuardian-AllItems-TyrannicalGladiator'sBadgeofConquest-91099"
 value: {
  dps: 87335.1408
  tps: 605046.90737
  dtps: 55177.00079
  hps: 22909.01814
 }
}
dps_results: {
 key: "TestGuardian-AllItems-TyrannicalGladiator'sBadgeofConquest-94373"
 value: {
  dps: 87335.1408
  tps: 605046.90737
  dtps: 55177.00079
  hps: 22909.01814
 }
}
dps_results: {
 key: "TestGuardian-AllItems-TyrannicalGladiator'sBadgeofConquest-99772"
 value: {
  dps: 87335.1408
  tps: 605046.90737
  dtps: 55177.00079
  hps: 22909.01814
 }
}
dps_results: {
 key: "TestGuardian-AllItems-TyrannicalGladiator'sBadgeofDominance-100016"
 value: {
  dps: 85224.09375
  tps: 590536.24852
  dtps: 55953.71775
  hps: 22571.53866
 }
}
dps_results: {
 key: "TestGuardian-AllItems-TyrannicalGladiator'sBadgeofDominance-91400"
 value: {
  dps: 85224.09375
  tps: 590536.24852
  dtps: 55953.71775
  hps: 22571.53866
 }
}
dps_results: {
 key: "TestGuardian-AllItems-TyrannicalGladiator'sBadgeofDominance-94346"
 value: {
  dps: 85224.09375
  tps: 590536.24852
  dtps: 55953.71775
  hps: 22571.53866
 }
}
dps_results: {
 key: "TestGuardian-AllItems-TyrannicalGladiator'sBadgeofDominance-99937"
 value: {
  dps: 85224.09375
  tps: 590536.24852
  dtps: 55953.71775
  hps: 22571.53866
 }
}
dps_results: {
 key: "TestGuardian-AllItems-TyrannicalGladiator'sBadgeofVictory-100019"
 value: {
  dps: 85861.74655
  tps: 595007.61633
  dtps: 55900.73842
  hps: 22721.66903
 }
}
dps_results: {
 key: "TestGuardian-AllItems-TyrannicalGladiator'sBadgeofVictory-91410"
 value: {
  dps: 85861.74655
  tps: 595007.61633
  dtps: 55900.73842
  hps: 22721.66903
 }
}
dps_results: {
 key: "TestGuardian-AllItems-TyrannicalGladiator'sBadgeofVictory-94349"
 value: {
  dps: 85861.74655
  tps: 595007.61633
  dtps: 55900.73842
  hps: 22721.66903
 }
}
dps_results: {
 key: "TestGuardian-AllItems-TyrannicalGladiator'sBadgeofVictory-99943"
 value: {
  dps: 85861.74655
  tps: 595007.61633
  dtps: 55900.73842
  hps: 22721.66903
 }
}
dps_results: {
 key: "TestGuardian-AllItems-TyrannicalGladiator'sEmblemofCruelty-100066"
 value: {
  dps: 87519.87624
  tps: 606290.89361
  dtps: 55555.4068
  hps: 24180.80568
 }
}
dps_results: {
 key: "TestGuardian-AllItems-TyrannicalGladiator'sEmblemofCruelty-91209"
 value: {
  dps: 87519.87624
  tps: 606290.89361
  dtps: 55555.4068
  hps: 24180.80568
 }
}
dps_results: {
 key: "TestGuardian-AllItems-TyrannicalGladiator'sEmblemofCruelty-94396"
 value: {
  dps: 87519.87624
  tps: 606290.89361
  dtps: 55555.4068
  hps: 24180.80568
 }
}
dps_results: {
 key: "TestGuardian-AllItems-TyrannicalGladiator'sEmblemofCruelty-99838"
 value: {
  dps: 87519.87624
  tps: 606290.89361
  dtps: 55555.4068
  hps: 24180.80568
 }
}
dps_results: {
 key: "TestGuardian-AllItems-TyrannicalGladiator'sEmblemofMeditation-91211"
 value: {
  dps: 85197.17684
  tps: 590536.24852
  dtps: 55953.71775
  hps: 22623.18178
 }
}
dps_results: {
 key: "TestGuardian-AllItems-TyrannicalGladiator'sEmblemofMeditation-94329"
 value: {
  dps: 85197.17684
  tps: 590536.24852
  dtps: 55953.71775
  hps: 22623.18178
 }
}
dps_results: {
 key: "TestGuardian-AllItems-TyrannicalGladiator'sEmblemofMeditation-99840"
 value: {
  dps: 85197.17684
  tps: 590536.24852
  dtps: 55953.71775
  hps: 22623.18178
 }
}
dps_results: {
 key: "TestGuardian-AllItems-TyrannicalGladiator'sEmblemofMeditation-99990"
 value: {
  dps: 85197.17684
  tps: 590536.24852
  dtps: 55953.71775
  hps: 22623.18178
 }
}
dps_results: {
 key: "TestGuardian-AllItems-TyrannicalGladiator'sEmblemofTenacity-100092"
 value: {
  dps: 85197.19402
  tps: 590536.36878
  dtps: 55993.34292
  hps: 23098.96228
 }
}
dps_results: {
 key: "TestGuardian-AllItems-TyrannicalGladiator'sEmblemofTenacity-91210"
 value: {
  dps: 85197.19402
  tps: 590536.36878
  dtps: 55993.34292
  hps: 23098.96228
 }
}
dps_results: {
 key: "TestGuardian-AllItems-TyrannicalGladiator'sEmblemofTenacity-94422"
 value: {
  dps: 85197.19402
  tps: 590536.36878
  dtps: 55993.34292
  hps: 23098.96228
 }
}
dps_results: {
 key: "TestGuardian-AllItems-TyrannicalGladiator'sEmblemofTenacity-99839"
 value: {
  dps: 85197.19402
  tps: 590536.36878
  dtps: 55993.34292
  hps: 23098.96228
 }
}
dps_results: {
 key: "TestGuardian-AllItems-TyrannicalGladiator'sInsigniaofConquest-100026"
 value: {
  dps: 88033.83148
  tps: 610089.34333
  dtps: 54666.13347
  hps: 23184.59424
 }
}
dps_results: {
 key: "TestGuardian-AllItems-TyrannicalGladiator'sInsigniaofDominance-100152"
 value: {
  dps: 85228.8813
  tps: 590536.24852
  dtps: 55953.71775
  hps: 22571.53866
 }
}
dps_results: {
 key: "TestGuardian-AllItems-TyrannicalGladiator'sInsigniaofVictory-100085"
 value: {
  dps: 86104.0055
  tps: 596713.53463
  dtps: 55836.36978
  hps: 22765.91626
 }
}
dps_results: {
 key: "TestGuardian-AllItems-TyrannicalPrimalDiamond"
 value: {
  dps: 89289.96395
  tps: 618205.71538
  dtps: 55264.61416
  hps: 24136.85777
 }
}
dps_results: {
 key: "TestGuardian-AllItems-UnerringVisionofLeiShen-96930"
 value: {
  dps: 87576.45384
  tps: 606804.05078
  dtps: 55684.50888
  hps: 23977.06207
 }
}
dps_results: {
 key: "TestGuardian-AllItems-VaporshieldMedallion-93262"
 value: {
  dps: 85228.37696
  tps: 590754.57769
  dtps: 54132.37229
  hps: 22427.16072
 }
}
dps_results: {
 key: "TestGuardian-AllItems-VialofDragon'sBlood-87063"
 value: {
  dps: 85231.17759
  tps: 590774.24656
  dtps: 53811.92545
  hps: 22581.98843
 }
}
dps_results: {
 key: "TestGuardian-AllItems-VialofIchorousBlood-100963"
 value: {
  dps: 85217.12739
  tps: 590594.61845
  dtps: 55953.71775
  hps: 22571.53866
 }
}
dps_results: {
 key: "TestGuardian-AllItems-VialofIchorousBlood-81264"
 value: {
  dps: 85217.12739
  tps: 590594.61845
  dtps: 55953.71775
  hps: 22571.53866
 }
}
dps_results: {
 key: "TestGuardian-AllItems-ViciousTalismanoftheShado-PanAssault-94511"
 value: {
  dps: 89842.21355
  tps: 621795.88966
  dtps: 54350.61843
  hps: 23475.46027
 }
}
dps_results: {
 key: "TestGuardian-AllItems-VisionofthePredator-81192"
 value: {
  dps: 85578.39063
  tps: 593123.8745
  dtps: 55945.01496
  hps: 22997.58632
 }
}
dps_results: {
 key: "TestGuardian-AllItems-VolatileTalismanoftheShado-PanAssault-94510"
 value: {
  dps: 85454.67702
  tps: 592150.41619
  dtps: 55887.16378
  hps: 23129.32195
 }
}
dps_results: {
 key: "TestGuardian-AllItems-WindsweptPages-81125"
 value: {
  dps: 87398.94158
  tps: 605869.48001
  dtps: 55393.41331
  hps: 24033.2837
 }
}
dps_results: {
 key: "TestGuardian-AllItems-WoundripperMedallion-93253"
 value: {
  dps: 89184.225
  tps: 617029.9859
  dtps: 54576.48688
  hps: 24676.64888
 }
}
dps_results: {
 key: "TestGuardian-AllItems-Wushoolay'sFinalChoice-96785"
 value: {
  dps: 86464.6946
  tps: 599074.49452
  dtps: 55247.70337
  hps: 22663.85129
 }
}
dps_results: {
 key: "TestGuardian-AllItems-Xing-Ho,BreathofYu'lon-102246"
 value: {
  dps: 89063.2451
  tps: 616657.39918
  dtps: 53443.53009
  hps: 25252.67105
 }
}
dps_results: {
 key: "TestGuardian-AllItems-YaungolFireCarrier-86518"
 value: {
  dps: 89918.81324
  tps: 622573.79825
  dtps: 54559.37514
  hps: 24419.70737
 }
}
dps_results: {
 key: "TestGuardian-AllItems-Yu'lon'sBite-103987"
 value: {
  dps: 86438.12537
  tps: 599035.49587
  dtps: 56032.51189
  hps: 23733.90889
 }
}
dps_results: {
 key: "TestGuardian-AllItems-ZenAlchemistStone-75274"
 value: {
  dps: 87750.69719
  tps: 608007.76454
  dtps: 53700.13507
  hps: 23283.62046
 }
}
dps_results: {
 key: "TestGuardian-Average-Default"
 value: {
  dps: 89349.44289
  tps: 618217.97853
  dtps: 54561.10352
  hps: 24432.74708
 }
}
dps_results: {
 key: "TestGuardian-Settings-Worgen-preraid-DefaultTalents-Default-default-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 1.73979648583e+06
  tps: 1.211602553236e+07
  dtps: 1.10570498303e+06
  hps: 110440.73669
 }
}
dps_results: {
 key: "TestGuardian-Settings-Worgen-preraid-DefaultTalents-Default-default-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 96205.0234
  tps: 666014.82135
  dtps: 54598.84323
  hps: 27454.00229
 }
}
dps_results: {
 key: "TestGuardian-Settings-Worgen-preraid-DefaultTalents-Default-default-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 96183.49512
  tps: 636061.84519
  dtps: 48157.63982
  hps: 21684.88318
 }
}
dps_results: {
 key: "TestGuardian-Settings-Worgen-preraid-DefaultTalents-Default-default-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 1.3035787667e+06
  tps: 9.12556158448e+06
  dtps: 1.18346926385e+06
  hps: 73013.81227
 }
}
dps_results: {
 key: "TestGuardian-Settings-Worgen-preraid-DefaultTalents-Default-default-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 78997.04398
  tps: 553016.12696
  dtps: 59115.80743
  hps: 19624.70649
 }
}
dps_results: {
 key: "TestGuardian-Settings-Worgen-preraid-DefaultTalents-Default-default-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 70781.04329
  tps: 495512.68825
  dtps: 57549.44316
  hps: 16040.07477
 }
}
dps_results: {
 key: "TestGuardian-Settings-Worgen-preraid-FoN-Default-default-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 1.73116917752e+06
  tps: 1.1991996782e+07
  dtps: 1.11067028252e+06
  hps: 82839.62463
 }
}
dps_results: {
 key: "TestGuardian-Settings-Worgen-preraid-FoN-Default-default-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 95171.40499
  tps: 640169.58066
  dtps: 55945.7345
  hps: 22123.35765
 }
}
dps_results: {
 key: "TestGuardian-Settings-Worgen-preraid-FoN-Default-default-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 95110.91073
  tps: 601470.2621
  dtps: 51731.14687
  hps: 18302.45064
 }
}
dps_results: {
 key: "TestGuardian-Settings-Worgen-preraid-FoN-Default-default-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 1.29691512029e+06
  tps: 9.03217965544e+06
  dtps: 1.18601493415e+06
  hps: 51222.41425
 }
}
dps_results: {
 key: "TestGuardian-Settings-Worgen-preraid-FoN-Default-default-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 78105.80815
  tps: 533250.32168
  dtps: 60382.93427
  hps: 15888.62522
 }
}
dps_results: {
 key: "TestGuardian-Settings-Worgen-preraid-FoN-Default-default-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 69519.90124
  tps: 469720.22073
  dtps: 59180.45241
  hps: 12938.75987
 }
}
dps_results: {
 key: "TestGuardian-SwitchInFrontOfTarget-Default"
 value: {
  dps: 96205.0234
  tps: 666014.82135
  dtps: 54598.84323
  hps: 27454.00229
 }
}
