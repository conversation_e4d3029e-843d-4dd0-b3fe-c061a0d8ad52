character_stats_results: {
 key: "TestGuardian-CharacterStats-Default"
 value: {
  final_stats: 112.35
  final_stats: 9376.5
  final_stats: 27815.634
  final_stats: 173.25
  final_stats: 187
  final_stats: 2553
  final_stats: 6255
  final_stats: 3451.5
  final_stats: 2567
  final_stats: 9399.05696
  final_stats: 0
  final_stats: 4348
  final_stats: 21015.885
  final_stats: 0
  final_stats: 179.575
  final_stats: 0
  final_stats: 0
  final_stats: 91128.57347
  final_stats: 0
  final_stats: 535821.876
  final_stats: 60000
  final_stats: 3000
  final_stats: 7.50882
  final_stats: 15.05882
  final_stats: 31.34947
  final_stats: 18.34338
  final_stats: 0
 }
}
dps_results: {
 key: "TestGuardian-AllItems-AgilePrimalDiamond"
 value: {
  dps: 90692.67888
  tps: 627473.76643
  dtps: 56069.87287
  hps: 23951.28453
 }
}
dps_results: {
 key: "TestGuardian-AllItems-AlacrityofXuen-103989"
 value: {
  dps: 87397.2992
  tps: 605363.04263
  dtps: 56014.71973
  hps: 24206.86299
 }
}
dps_results: {
 key: "TestGuardian-AllItems-ArcaneBadgeoftheShieldwall-93347"
 value: {
  dps: 85720.34194
  tps: 595605.64464
  dtps: 56094.39821
  hps: 22580.14229
 }
}
dps_results: {
 key: "TestGuardian-AllItems-ArrowflightMedallion-93258"
 value: {
  dps: 89510.18782
  tps: 618622.67662
  dtps: 54771.40565
  hps: 24748.16208
 }
}
dps_results: {
 key: "TestGuardian-AllItems-AssuranceofConsequence-105472"
 value: {
  dps: 86300.23781
  tps: 597344.60374
  dtps: 49749.78763
  hps: 23809.49855
 }
}
dps_results: {
 key: "TestGuardian-AllItems-AusterePrimalDiamond"
 value: {
  dps: 89061.25826
  tps: 616151.99043
  dtps: 55471.16926
  hps: 24001.72148
 }
}
dps_results: {
 key: "TestGuardian-AllItems-BadJuju-96781"
 value: {
  dps: 88983.94169
  tps: 615702.19774
  dtps: 53248.621
  hps: 23127.20869
 }
}
dps_results: {
 key: "TestGuardian-AllItems-BadgeofKypariZar-84079"
 value: {
  dps: 87048.00142
  tps: 602492.97207
  dtps: 55568.95624
  hps: 24283.9785
 }
}
dps_results: {
 key: "TestGuardian-AllItems-BlossomofPureSnow-89081"
 value: {
  dps: 87471.79913
  tps: 604682.05093
  dtps: 55801.70342
  hps: 24352.84852
 }
}
dps_results: {
 key: "TestGuardian-AllItems-BottleofInfiniteStars-87057"
 value: {
  dps: 87877.04708
  tps: 608626.5074
  dtps: 54270.19468
  hps: 23198.78979
 }
}
dps_results: {
 key: "TestGuardian-AllItems-BraidofTenSongs-84072"
 value: {
  dps: 85670.55647
  tps: 594099.22237
  dtps: 55873.17741
  hps: 23626.11966
 }
}
dps_results: {
 key: "TestGuardian-AllItems-Brawler'sStatue-87571"
 value: {
  dps: 85536.87367
  tps: 592478.32004
  dtps: 54664.65277
  hps: 22877.08901
 }
}
dps_results: {
 key: "TestGuardian-AllItems-BreathoftheHydra-96827"
 value: {
  dps: 86379.43996
  tps: 598973.97443
  dtps: 55620.56169
  hps: 24682.48841
 }
}
dps_results: {
 key: "TestGuardian-AllItems-BroochofMunificentDeeds-87500"
 value: {
  dps: 85460.2828
  tps: 591942.1236
  dtps: 55827.21846
  hps: 23000.25506
 }
}
dps_results: {
 key: "TestGuardian-AllItems-BrutalTalismanoftheShado-PanAssault-94508"
 value: {
  dps: 89765.04087
  tps: 621205.15768
  dtps: 55953.04416
  hps: 24563.53061
 }
}
dps_results: {
 key: "TestGuardian-AllItems-BurningPrimalDiamond"
 value: {
  dps: 90302.42261
  tps: 624737.31364
  dtps: 56193.48995
  hps: 23897.94418
 }
}
dps_results: {
 key: "TestGuardian-AllItems-CapacitivePrimalDiamond"
 value: {
  dps: 89453.67137
  tps: 618623.34395
  dtps: 55695.54351
  hps: 24162.41642
 }
}
dps_results: {
 key: "TestGuardian-AllItems-CarbonicCarbuncle-81138"
 value: {
  dps: 87649.43422
  tps: 606824.191
  dtps: 55856.05252
  hps: 23925.55141
 }
}
dps_results: {
 key: "TestGuardian-AllItems-Cha-Ye'sEssenceofBrilliance-96888"
 value: {
  dps: 88904.90695
  tps: 615328.31339
  dtps: 55838.83803
  hps: 26074.42077
 }
}
dps_results: {
 key: "TestGuardian-AllItems-CharmofTenSongs-84071"
 value: {
  dps: 86795.14531
  tps: 601574.38557
  dtps: 55150.76
  hps: 23489.31833
 }
}
dps_results: {
 key: "TestGuardian-AllItems-CommunalIdolofDestruction-101168"
 value: {
  dps: 85519.16509
  tps: 592354.60146
  dtps: 54954.83937
  hps: 23039.47907
 }
}
dps_results: {
 key: "TestGuardian-AllItems-CommunalStoneofDestruction-101171"
 value: {
  dps: 85434.08168
  tps: 591620.36931
  dtps: 54922.4847
  hps: 22877.91067
 }
}
dps_results: {
 key: "TestGuardian-AllItems-CommunalStoneofWisdom-101183"
 value: {
  dps: 85434.08168
  tps: 591620.37062
  dtps: 55901.04854
  hps: 22878.85019
 }
}
dps_results: {
 key: "TestGuardian-AllItems-ContemplationofChi-Ji-103688"
 value: {
  dps: 85434.08168
  tps: 591620.37062
  dtps: 55901.04854
  hps: 22878.85019
 }
}
dps_results: {
 key: "TestGuardian-AllItems-ContemplationofChi-Ji-103988"
 value: {
  dps: 85442.46864
  tps: 591679.07937
  dtps: 55901.04854
  hps: 22878.85019
 }
}
dps_results: {
 key: "TestGuardian-AllItems-Coren'sColdChromiumCoaster-87574"
 value: {
  dps: 88159.54345
  tps: 610439.76985
  dtps: 55566.91756
  hps: 24298.24968
 }
}
dps_results: {
 key: "TestGuardian-AllItems-CoreofDecency-87497"
 value: {
  dps: 85406.12726
  tps: 591563.08723
  dtps: 55901.04854
  hps: 22878.85019
 }
}
dps_results: {
 key: "TestGuardian-AllItems-CourageousPrimalDiamond"
 value: {
  dps: 89066.23142
  tps: 616153.18896
  dtps: 56193.48995
  hps: 23897.94418
 }
}
dps_results: {
 key: "TestGuardian-AllItems-CraftedDreadfulGladiator'sBadgeofConquest-93419"
 value: {
  dps: 86818.17759
  tps: 601198.01909
  dtps: 55597.80994
  hps: 23283.63237
 }
}
dps_results: {
 key: "TestGuardian-AllItems-CraftedDreadfulGladiator'sBadgeofDominance-93600"
 value: {
  dps: 85450.56408
  tps: 591735.74746
  dtps: 55901.04854
  hps: 22878.85019
 }
}
dps_results: {
 key: "TestGuardian-AllItems-CraftedDreadfulGladiator'sBadgeofVictory-93606"
 value: {
  dps: 85846.28827
  tps: 594509.77887
  dtps: 55916.17248
  hps: 22989.85092
 }
}
dps_results: {
 key: "TestGuardian-AllItems-CraftedDreadfulGladiator'sEmblemofCruelty-93485"
 value: {
  dps: 87057.26478
  tps: 602942.08398
  dtps: 55692.81242
  hps: 23919.69123
 }
}
dps_results: {
 key: "TestGuardian-AllItems-CraftedDreadfulGladiator'sEmblemofMeditation-93487"
 value: {
  dps: 85406.12726
  tps: 591563.08723
  dtps: 55931.91062
  hps: 22930.46315
 }
}
dps_results: {
 key: "TestGuardian-AllItems-CraftedDreadfulGladiator'sEmblemofTenacity-93486"
 value: {
  dps: 85460.2828
  tps: 591942.1236
  dtps: 55796.35638
  hps: 23015.35159
 }
}
dps_results: {
 key: "TestGuardian-AllItems-CraftedDreadfulGladiator'sInsigniaofConquest-93424"
 value: {
  dps: 87423.37414
  tps: 605457.20981
  dtps: 55026.06372
  hps: 23260.72152
 }
}
dps_results: {
 key: "TestGuardian-AllItems-CraftedDreadfulGladiator'sInsigniaofDominance-93601"
 value: {
  dps: 85433.99378
  tps: 591619.75531
  dtps: 55901.04854
  hps: 22878.85019
 }
}
dps_results: {
 key: "TestGuardian-AllItems-CraftedDreadfulGladiator'sInsigniaofVictory-93611"
 value: {
  dps: 86004.83762
  tps: 595656.23101
  dtps: 55948.68996
  hps: 23023.9304
 }
}
dps_results: {
 key: "TestGuardian-AllItems-CraftedMalevolentGladiator'sBadgeofConquest-98755"
 value: {
  dps: 87141.122
  tps: 603409.97983
  dtps: 55391.28079
  hps: 23309.87491
 }
}
dps_results: {
 key: "TestGuardian-AllItems-CraftedMalevolentGladiator'sBadgeofDominance-98910"
 value: {
  dps: 85450.56408
  tps: 591735.74746
  dtps: 55901.04854
  hps: 22878.85019
 }
}
dps_results: {
 key: "TestGuardian-AllItems-CraftedMalevolentGladiator'sBadgeofVictory-98912"
 value: {
  dps: 85923.86111
  tps: 595028.24315
  dtps: 55950.29137
  hps: 23002.69714
 }
}
dps_results: {
 key: "TestGuardian-AllItems-CraftedMalevolentGladiator'sEmblemofCruelty-98811"
 value: {
  dps: 87509.79891
  tps: 605908.39342
  dtps: 55795.6041
  hps: 24216.75702
 }
}
dps_results: {
 key: "TestGuardian-AllItems-CraftedMalevolentGladiator'sEmblemofMeditation-98813"
 value: {
  dps: 85406.12726
  tps: 591563.08723
  dtps: 55931.91062
  hps: 22930.46315
 }
}
dps_results: {
 key: "TestGuardian-AllItems-CraftedMalevolentGladiator'sEmblemofTenacity-98812"
 value: {
  dps: 85460.2828
  tps: 591942.1236
  dtps: 55774.40673
  hps: 23075.86082
 }
}
dps_results: {
 key: "TestGuardian-AllItems-CraftedMalevolentGladiator'sInsigniaofConquest-98760"
 value: {
  dps: 87802.31367
  tps: 608061.30372
  dtps: 54966.17296
  hps: 23514.22645
 }
}
dps_results: {
 key: "TestGuardian-AllItems-CraftedMalevolentGladiator'sInsigniaofDominance-98911"
 value: {
  dps: 85446.64659
  tps: 591677.0387
  dtps: 55901.04854
  hps: 22878.85019
 }
}
dps_results: {
 key: "TestGuardian-AllItems-CraftedMalevolentGladiator'sInsigniaofVictory-98917"
 value: {
  dps: 86152.23779
  tps: 596604.36051
  dtps: 55945.67204
  hps: 23047.64952
 }
}
dps_results: {
 key: "TestGuardian-AllItems-CurseofHubris-102307"
 value: {
  dps: 89909.79441
  tps: 620307.66468
  dtps: 55171.57422
  hps: 26038.58244
 }
}
dps_results: {
 key: "TestGuardian-AllItems-CurseofHubris-104649"
 value: {
  dps: 90617.25585
  tps: 625262.27382
  dtps: 55195.23963
  hps: 26350.13894
 }
}
dps_results: {
 key: "TestGuardian-AllItems-CurseofHubris-104898"
 value: {
  dps: 89537.12541
  tps: 617758.46603
  dtps: 55480.32635
  hps: 25905.31961
 }
}
dps_results: {
 key: "TestGuardian-AllItems-CurseofHubris-105147"
 value: {
  dps: 88932.47478
  tps: 613854.17906
  dtps: 55812.24897
  hps: 25348.4872
 }
}
dps_results: {
 key: "TestGuardian-AllItems-CurseofHubris-105396"
 value: {
  dps: 90207.89844
  tps: 622393.42111
  dtps: 55207.18253
  hps: 26358.06034
 }
}
dps_results: {
 key: "TestGuardian-AllItems-CurseofHubris-105645"
 value: {
  dps: 90874.38285
  tps: 627118.70273
  dtps: 55088.0238
  hps: 26679.21567
 }
}
dps_results: {
 key: "TestGuardian-AllItems-CutstitcherMedallion-93255"
 value: {
  dps: 85434.08168
  tps: 591620.37062
  dtps: 55901.04854
  hps: 22878.85019
 }
}
dps_results: {
 key: "TestGuardian-AllItems-Daelo'sFinalWords-87496"
 value: {
  dps: 87165.64793
  tps: 603238.91186
  dtps: 56104.67143
  hps: 22578.7431
 }
}
dps_results: {
 key: "TestGuardian-AllItems-DarkglowEmbroidery(Rank3)-4893"
 value: {
  dps: 89061.25826
  tps: 616151.99043
  dtps: 55471.16926
  hps: 23932.4512
 }
}
dps_results: {
 key: "TestGuardian-AllItems-DarkmistVortex-87172"
 value: {
  dps: 87067.54782
  tps: 603353.4795
  dtps: 56390.16583
  hps: 24094.06514
 }
}
dps_results: {
 key: "TestGuardian-AllItems-DeadeyeBadgeoftheShieldwall-93346"
 value: {
  dps: 87104.58754
  tps: 603345.5443
  dtps: 54963.52977
  hps: 23374.07588
 }
}
dps_results: {
 key: "TestGuardian-AllItems-DelicateVialoftheSanguinaire-96895"
 value: {
  dps: 85490.17827
  tps: 592151.61572
  dtps: 53554.67996
  hps: 22818.42899
 }
}
dps_results: {
 key: "TestGuardian-AllItems-DestructivePrimalDiamond"
 value: {
  dps: 89582.82456
  tps: 619527.48128
  dtps: 55693.46204
  hps: 24213.10459
 }
}
dps_results: {
 key: "TestGuardian-AllItems-DisciplineofXuen-103986"
 value: {
  dps: 88660.1696
  tps: 614122.14016
  dtps: 53435.71172
  hps: 23503.5188
 }
}
dps_results: {
 key: "TestGuardian-AllItems-Dominator'sArcaneBadge-93342"
 value: {
  dps: 85720.34194
  tps: 595605.64464
  dtps: 56094.39821
  hps: 22580.14229
 }
}
dps_results: {
 key: "TestGuardian-AllItems-Dominator'sDeadeyeBadge-93341"
 value: {
  dps: 87104.58754
  tps: 603345.5443
  dtps: 54963.52977
  hps: 23374.07588
 }
}
dps_results: {
 key: "TestGuardian-AllItems-Dominator'sDurableBadge-93345"
 value: {
  dps: 85414.55521
  tps: 591622.11537
  dtps: 54798.9342
  hps: 22915.56902
 }
}
dps_results: {
 key: "TestGuardian-AllItems-Dominator'sKnightlyBadge-93344"
 value: {
  dps: 85877.198
  tps: 594806.76835
  dtps: 55308.07077
  hps: 22982.26482
 }
}
dps_results: {
 key: "TestGuardian-AllItems-Dominator'sMendingBadge-93343"
 value: {
  dps: 85418.77675
  tps: 591620.37062
  dtps: 55901.04854
  hps: 22878.85019
 }
}
dps_results: {
 key: "TestGuardian-AllItems-DreadfulGladiator'sBadgeofConquest-84344"
 value: {
  dps: 86818.17759
  tps: 601198.01909
  dtps: 55597.80994
  hps: 23283.63237
 }
}
dps_results: {
 key: "TestGuardian-AllItems-DreadfulGladiator'sBadgeofDominance-84488"
 value: {
  dps: 85450.56408
  tps: 591735.74746
  dtps: 55901.04854
  hps: 22878.85019
 }
}
dps_results: {
 key: "TestGuardian-AllItems-DreadfulGladiator'sBadgeofVictory-84490"
 value: {
  dps: 85846.28827
  tps: 594509.77887
  dtps: 55916.17248
  hps: 22989.85092
 }
}
dps_results: {
 key: "TestGuardian-AllItems-DreadfulGladiator'sEmblemofCruelty-84399"
 value: {
  dps: 87057.26478
  tps: 602942.08398
  dtps: 55692.81242
  hps: 23919.69123
 }
}
dps_results: {
 key: "TestGuardian-AllItems-DreadfulGladiator'sEmblemofMeditation-84401"
 value: {
  dps: 85406.12726
  tps: 591563.08723
  dtps: 55931.91062
  hps: 22930.46315
 }
}
dps_results: {
 key: "TestGuardian-AllItems-DreadfulGladiator'sEmblemofTenacity-84400"
 value: {
  dps: 85460.2828
  tps: 591942.1236
  dtps: 55796.35638
  hps: 23015.35159
 }
}
dps_results: {
 key: "TestGuardian-AllItems-DreadfulGladiator'sInsigniaofConquest-84349"
 value: {
  dps: 87586.93065
  tps: 606556.90084
  dtps: 55069.49932
  hps: 23318.29323
 }
}
dps_results: {
 key: "TestGuardian-AllItems-DreadfulGladiator'sInsigniaofDominance-84489"
 value: {
  dps: 85422.06967
  tps: 591619.75531
  dtps: 55901.04854
  hps: 22878.85019
 }
}
dps_results: {
 key: "TestGuardian-AllItems-DreadfulGladiator'sInsigniaofVictory-84495"
 value: {
  dps: 86016.67778
  tps: 595701.33963
  dtps: 55912.26682
  hps: 23025.44469
 }
}
dps_results: {
 key: "TestGuardian-AllItems-DurableBadgeoftheShieldwall-93350"
 value: {
  dps: 85414.55521
  tps: 591622.11537
  dtps: 54798.9342
  hps: 22915.56902
 }
}
dps_results: {
 key: "TestGuardian-AllItems-EffulgentPrimalDiamond"
 value: {
  dps: 89061.25826
  tps: 616151.99043
  dtps: 56193.48995
  hps: 24010.6332
 }
}
dps_results: {
 key: "TestGuardian-AllItems-EmberPrimalDiamond"
 value: {
  dps: 89066.23142
  tps: 616153.18896
  dtps: 56193.48995
  hps: 23897.94418
 }
}
dps_results: {
 key: "TestGuardian-AllItems-EmblemofKypariZar-84077"
 value: {
  dps: 86925.49945
  tps: 602012.58452
  dtps: 55703.5422
  hps: 24496.06286
 }
}
dps_results: {
 key: "TestGuardian-AllItems-EmblemoftheCatacombs-83733"
 value: {
  dps: 85638.49071
  tps: 593201.49883
  dtps: 55879.00399
  hps: 23598.40515
 }
}
dps_results: {
 key: "TestGuardian-AllItems-EmptyFruitBarrel-81133"
 value: {
  dps: 85406.12726
  tps: 591563.08723
  dtps: 55901.04854
  hps: 22878.85019
 }
}
dps_results: {
 key: "TestGuardian-AllItems-EnchantWeapon-BloodyDancingSteel-5125"
 value: {
  dps: 90343.95315
  tps: 624770.47081
  dtps: 54442.6467
  hps: 24457.16571
 }
}
dps_results: {
 key: "TestGuardian-AllItems-EnchantWeapon-Colossus-4445"
 value: {
  dps: 89061.28065
  tps: 616152.14712
  dtps: 54631.78463
  hps: 24001.32041
 }
}
dps_results: {
 key: "TestGuardian-AllItems-EnchantWeapon-DancingSteel-4444"
 value: {
  dps: 90170.31538
  tps: 623554.38461
  dtps: 54580.01885
  hps: 23912.01643
 }
}
dps_results: {
 key: "TestGuardian-AllItems-EnchantWeapon-JadeSpirit-4442"
 value: {
  dps: 89099.14408
  tps: 616266.71754
  dtps: 55471.16926
  hps: 24001.72148
 }
}
dps_results: {
 key: "TestGuardian-AllItems-EnchantWeapon-River'sSong-4446"
 value: {
  dps: 89061.25826
  tps: 616151.99043
  dtps: 55149.29649
  hps: 24002.18205
 }
}
dps_results: {
 key: "TestGuardian-AllItems-EnchantWeapon-SpiritofConquest-5124"
 value: {
  dps: 89061.25826
  tps: 616151.99043
  dtps: 55471.16926
  hps: 24001.72148
 }
}
dps_results: {
 key: "TestGuardian-AllItems-EnchantWeapon-Windsong-4441"
 value: {
  dps: 89553.70137
  tps: 619746.2758
  dtps: 54561.51691
  hps: 24581.52942
 }
}
dps_results: {
 key: "TestGuardian-AllItems-EnigmaticPrimalDiamond"
 value: {
  dps: 89582.82456
  tps: 619527.48128
  dtps: 55693.46204
  hps: 24213.10459
 }
}
dps_results: {
 key: "TestGuardian-AllItems-EssenceofTerror-87175"
 value: {
  dps: 85817.06835
  tps: 594301.18538
  dtps: 55680.4911
  hps: 23221.95791
 }
}
dps_results: {
 key: "TestGuardian-AllItems-EternalPrimalDiamond"
 value: {
  dps: 88995.30197
  tps: 615690.42292
  dtps: 56046.38429
  hps: 23794.47223
 }
}
dps_results: {
 key: "TestGuardian-AllItems-EvilEyeofGalakras-105491"
 value: {
  dps: 85877.75888
  tps: 594664.46562
  dtps: 55950.19004
  hps: 23000.00435
 }
}
dps_results: {
 key: "TestGuardian-AllItems-FabledFeatherofJi-Kun-96842"
 value: {
  dps: 88213.17131
  tps: 610962.24916
  dtps: 56211.41669
  hps: 22984.05252
 }
}
dps_results: {
 key: "TestGuardian-AllItems-FearwurmBadge-84074"
 value: {
  dps: 86803.43177
  tps: 601554.33422
  dtps: 56106.11326
  hps: 24157.39428
 }
}
dps_results: {
 key: "TestGuardian-AllItems-FearwurmRelic-84070"
 value: {
  dps: 86741.68945
  tps: 600738.03656
  dtps: 55900.31956
  hps: 23966.02089
 }
}
dps_results: {
 key: "TestGuardian-AllItems-FelsoulIdolofDestruction-101263"
 value: {
  dps: 85616.19483
  tps: 593034.05071
  dtps: 55072.69227
  hps: 23124.65687
 }
}
dps_results: {
 key: "TestGuardian-AllItems-FelsoulStoneofDestruction-101266"
 value: {
  dps: 85488.23722
  tps: 591999.40699
  dtps: 54775.1883
  hps: 22626.92271
 }
}
dps_results: {
 key: "TestGuardian-AllItems-Fen-Yu,FuryofXuen-102248"
 value: {
  dps: 99121.44273
  tps: 686391.105
  dtps: 51716.90713
  hps: 26237.93553
 }
}
dps_results: {
 key: "TestGuardian-AllItems-FlashfrozenResinGlobule-100951"
 value: {
  dps: 86691.74901
  tps: 600116.58258
  dtps: 56124.71043
  hps: 22479.96645
 }
}
dps_results: {
 key: "TestGuardian-AllItems-FlashfrozenResinGlobule-81263"
 value: {
  dps: 86691.74901
  tps: 600116.58258
  dtps: 56124.71043
  hps: 22479.96645
 }
}
dps_results: {
 key: "TestGuardian-AllItems-FleetPrimalDiamond"
 value: {
  dps: 89061.42948
  tps: 616153.18896
  dtps: 55813.64556
  hps: 23898.09519
 }
}
dps_results: {
 key: "TestGuardian-AllItems-ForlornPrimalDiamond"
 value: {
  dps: 89066.23142
  tps: 616153.18896
  dtps: 56193.48995
  hps: 23897.94418
 }
}
dps_results: {
 key: "TestGuardian-AllItems-FortitudeoftheZandalari-94516"
 value: {
  dps: 85460.2828
  tps: 591942.1236
  dtps: 54576.50472
  hps: 22678.55125
 }
}
dps_results: {
 key: "TestGuardian-AllItems-FortitudeoftheZandalari-95677"
 value: {
  dps: 85401.36445
  tps: 591529.74758
  dtps: 54920.48047
  hps: 22930.02072
 }
}
dps_results: {
 key: "TestGuardian-AllItems-FortitudeoftheZandalari-96049"
 value: {
  dps: 85460.2828
  tps: 591942.1236
  dtps: 54504.96806
  hps: 22678.55125
 }
}
dps_results: {
 key: "TestGuardian-AllItems-FortitudeoftheZandalari-96421"
 value: {
  dps: 85460.2828
  tps: 591942.1236
  dtps: 54416.85411
  hps: 22678.55125
 }
}
dps_results: {
 key: "TestGuardian-AllItems-FortitudeoftheZandalari-96793"
 value: {
  dps: 85460.2828
  tps: 591942.1236
  dtps: 54337.37364
  hps: 22678.55125
 }
}
dps_results: {
 key: "TestGuardian-AllItems-GazeoftheTwins-96915"
 value: {
  dps: 89494.46274
  tps: 619721.26564
  dtps: 55883.91616
  hps: 25357.9108
 }
}
dps_results: {
 key: "TestGuardian-AllItems-Gerp'sPerfectArrow-87495"
 value: {
  dps: 88293.48451
  tps: 610907.27066
  dtps: 55288.69262
  hps: 24454.75553
 }
}
dps_results: {
 key: "TestGuardian-AllItems-Gong-Lu,StrengthofXuen-102249"
 value: {
  dps: 96741.15273
  tps: 670168.93747
  dtps: 53942.02905
  hps: 25530.13853
 }
}
dps_results: {
 key: "TestGuardian-AllItems-GrievousGladiator'sBadgeofConquest-100195"
 value: {
  dps: 88024.56652
  tps: 609209.8035
  dtps: 55019.96947
  hps: 23337.68034
 }
}
dps_results: {
 key: "TestGuardian-AllItems-GrievousGladiator'sBadgeofConquest-100603"
 value: {
  dps: 88024.56652
  tps: 609209.8035
  dtps: 55019.96947
  hps: 23337.68034
 }
}
dps_results: {
 key: "TestGuardian-AllItems-GrievousGladiator'sBadgeofConquest-102856"
 value: {
  dps: 88024.56652
  tps: 609209.8035
  dtps: 55019.96947
  hps: 23337.68034
 }
}
dps_results: {
 key: "TestGuardian-AllItems-GrievousGladiator'sBadgeofConquest-103145"
 value: {
  dps: 88024.56652
  tps: 609209.8035
  dtps: 55019.96947
  hps: 23337.68034
 }
}
dps_results: {
 key: "TestGuardian-AllItems-GrievousGladiator'sBadgeofDominance-100490"
 value: {
  dps: 85466.78492
  tps: 591792.11482
  dtps: 55901.04854
  hps: 22878.85019
 }
}
dps_results: {
 key: "TestGuardian-AllItems-GrievousGladiator'sBadgeofDominance-100576"
 value: {
  dps: 85466.78492
  tps: 591792.11482
  dtps: 55901.04854
  hps: 22878.85019
 }
}
dps_results: {
 key: "TestGuardian-AllItems-GrievousGladiator'sBadgeofDominance-102830"
 value: {
  dps: 85466.78492
  tps: 591792.11482
  dtps: 55901.04854
  hps: 22878.85019
 }
}
dps_results: {
 key: "TestGuardian-AllItems-GrievousGladiator'sBadgeofDominance-103308"
 value: {
  dps: 85466.78492
  tps: 591792.11482
  dtps: 55901.04854
  hps: 22878.85019
 }
}
dps_results: {
 key: "TestGuardian-AllItems-GrievousGladiator'sBadgeofVictory-100500"
 value: {
  dps: 86192.94847
  tps: 596826.71002
  dtps: 55942.05187
  hps: 23047.54387
 }
}
dps_results: {
 key: "TestGuardian-AllItems-GrievousGladiator'sBadgeofVictory-100579"
 value: {
  dps: 86192.94847
  tps: 596826.71002
  dtps: 55942.05187
  hps: 23047.54387
 }
}
dps_results: {
 key: "TestGuardian-AllItems-GrievousGladiator'sBadgeofVictory-102833"
 value: {
  dps: 86192.94847
  tps: 596826.71002
  dtps: 55942.05187
  hps: 23047.54387
 }
}
dps_results: {
 key: "TestGuardian-AllItems-GrievousGladiator'sBadgeofVictory-103314"
 value: {
  dps: 86192.94847
  tps: 596826.71002
  dtps: 55942.05187
  hps: 23047.54387
 }
}
dps_results: {
 key: "TestGuardian-AllItems-GrievousGladiator'sEmblemofCruelty-100305"
 value: {
  dps: 88252.90554
  tps: 610951.92217
  dtps: 55759.98995
  hps: 25086.23537
 }
}
dps_results: {
 key: "TestGuardian-AllItems-GrievousGladiator'sEmblemofCruelty-100626"
 value: {
  dps: 88252.90554
  tps: 610951.92217
  dtps: 55759.98995
  hps: 25086.23537
 }
}
dps_results: {
 key: "TestGuardian-AllItems-GrievousGladiator'sEmblemofCruelty-102877"
 value: {
  dps: 88252.90554
  tps: 610951.92217
  dtps: 55759.98995
  hps: 25086.23537
 }
}
dps_results: {
 key: "TestGuardian-AllItems-GrievousGladiator'sEmblemofCruelty-103210"
 value: {
  dps: 88252.90554
  tps: 610951.92217
  dtps: 55759.98995
  hps: 25086.23537
 }
}
dps_results: {
 key: "TestGuardian-AllItems-GrievousGladiator'sEmblemofMeditation-100307"
 value: {
  dps: 85406.12726
  tps: 591563.08723
  dtps: 55931.91062
  hps: 22930.46315
 }
}
dps_results: {
 key: "TestGuardian-AllItems-GrievousGladiator'sEmblemofMeditation-100559"
 value: {
  dps: 85406.12726
  tps: 591563.08723
  dtps: 55931.91062
  hps: 22930.46315
 }
}
dps_results: {
 key: "TestGuardian-AllItems-GrievousGladiator'sEmblemofMeditation-102813"
 value: {
  dps: 85406.12726
  tps: 591563.08723
  dtps: 55931.91062
  hps: 22930.46315
 }
}
dps_results: {
 key: "TestGuardian-AllItems-GrievousGladiator'sEmblemofMeditation-103212"
 value: {
  dps: 85406.12726
  tps: 591563.08723
  dtps: 55931.91062
  hps: 22930.46315
 }
}
dps_results: {
 key: "TestGuardian-AllItems-GrievousGladiator'sEmblemofTenacity-100306"
 value: {
  dps: 85459.96232
  tps: 591939.66559
  dtps: 55977.07571
  hps: 23241.07645
 }
}
dps_results: {
 key: "TestGuardian-AllItems-GrievousGladiator'sEmblemofTenacity-100652"
 value: {
  dps: 85459.96232
  tps: 591939.66559
  dtps: 55977.07571
  hps: 23241.07645
 }
}
dps_results: {
 key: "TestGuardian-AllItems-GrievousGladiator'sEmblemofTenacity-102903"
 value: {
  dps: 85459.96232
  tps: 591939.66559
  dtps: 55977.07571
  hps: 23241.07645
 }
}
dps_results: {
 key: "TestGuardian-AllItems-GrievousGladiator'sEmblemofTenacity-103211"
 value: {
  dps: 85459.96232
  tps: 591939.66559
  dtps: 55977.07571
  hps: 23241.07645
 }
}
dps_results: {
 key: "TestGuardian-AllItems-GrievousGladiator'sInsigniaofConquest-103150"
 value: {
  dps: 89031.66146
  tps: 616426.54683
  dtps: 54799.07612
  hps: 24194.05984
 }
}
dps_results: {
 key: "TestGuardian-AllItems-GrievousGladiator'sInsigniaofDominance-103309"
 value: {
  dps: 85446.96466
  tps: 591677.0387
  dtps: 55901.04854
  hps: 22878.85019
 }
}
dps_results: {
 key: "TestGuardian-AllItems-GrievousGladiator'sInsigniaofVictory-103319"
 value: {
  dps: 86537.41567
  tps: 599292.57901
  dtps: 55895.84415
  hps: 23130.43067
 }
}
dps_results: {
 key: "TestGuardian-AllItems-Hawkmaster'sTalon-89082"
 value: {
  dps: 87751.50273
  tps: 607825.06656
  dtps: 55093.17995
  hps: 24521.9018
 }
}
dps_results: {
 key: "TestGuardian-AllItems-Heart-LesionDefenderIdol-100999"
 value: {
  dps: 85440.85835
  tps: 591806.2011
  dtps: 55121.62687
  hps: 23471.547
 }
}
dps_results: {
 key: "TestGuardian-AllItems-Heart-LesionDefenderStone-101002"
 value: {
  dps: 85482.94649
  tps: 592100.89104
  dtps: 54877.27785
  hps: 23247.27523
 }
}
dps_results: {
 key: "TestGuardian-AllItems-Heart-LesionIdolofBattle-100991"
 value: {
  dps: 88239.10392
  tps: 610903.26611
  dtps: 55721.30417
  hps: 24350.19867
 }
}
dps_results: {
 key: "TestGuardian-AllItems-Heart-LesionStoneofBattle-100990"
 value: {
  dps: 86061.23517
  tps: 596080.874
  dtps: 54791.51777
  hps: 22755.39355
 }
}
dps_results: {
 key: "TestGuardian-AllItems-HeartofFire-81181"
 value: {
  dps: 85414.60365
  tps: 591622.45446
  dtps: 54548.79004
  hps: 22915.13139
 }
}
dps_results: {
 key: "TestGuardian-AllItems-HeartwarmerMedallion-93260"
 value: {
  dps: 85434.08168
  tps: 591620.37062
  dtps: 55901.04854
  hps: 22878.85019
 }
}
dps_results: {
 key: "TestGuardian-AllItems-HelmbreakerMedallion-93261"
 value: {
  dps: 88068.96396
  tps: 608675.69684
  dtps: 55376.72
  hps: 24253.48847
 }
}
dps_results: {
 key: "TestGuardian-AllItems-Horridon'sLastGasp-96757"
 value: {
  dps: 85442.46864
  tps: 591679.07937
  dtps: 55901.04854
  hps: 22878.85019
 }
}
dps_results: {
 key: "TestGuardian-AllItems-ImpassivePrimalDiamond"
 value: {
  dps: 89582.82456
  tps: 619527.48128
  dtps: 55693.46204
  hps: 24213.10459
 }
}
dps_results: {
 key: "TestGuardian-AllItems-IndomitablePrimalDiamond"
 value: {
  dps: 89061.25826
  tps: 616151.99043
  dtps: 56193.48995
  hps: 24010.6332
 }
}
dps_results: {
 key: "TestGuardian-AllItems-InscribedBagofHydra-Spawn-96828"
 value: {
  dps: 85401.36445
  tps: 591529.74628
  dtps: 55061.15783
  hps: 22878.09346
 }
}
dps_results: {
 key: "TestGuardian-AllItems-InsigniaofKypariZar-84078"
 value: {
  dps: 85480.87086
  tps: 592086.33889
  dtps: 55554.43024
  hps: 22885.64424
 }
}
dps_results: {
 key: "TestGuardian-AllItems-IronBellyWok-89083"
 value: {
  dps: 86677.27452
  tps: 600377.43737
  dtps: 55277.09616
  hps: 24306.88875
 }
}
dps_results: {
 key: "TestGuardian-AllItems-IronProtectorTalisman-85181"
 value: {
  dps: 85539.78921
  tps: 592498.71058
  dtps: 55187.30892
  hps: 23024.91611
 }
}
dps_results: {
 key: "TestGuardian-AllItems-JadeBanditFigurine-86043"
 value: {
  dps: 87751.50273
  tps: 607825.06656
  dtps: 55093.17995
  hps: 24521.9018
 }
}
dps_results: {
 key: "TestGuardian-AllItems-JadeBanditFigurine-86772"
 value: {
  dps: 87647.91638
  tps: 607095.90168
  dtps: 54825.93619
  hps: 24001.01468
 }
}
dps_results: {
 key: "TestGuardian-AllItems-JadeCharioteerFigurine-86042"
 value: {
  dps: 86677.27452
  tps: 600377.43737
  dtps: 55277.09616
  hps: 24306.88875
 }
}
dps_results: {
 key: "TestGuardian-AllItems-JadeCharioteerFigurine-86771"
 value: {
  dps: 86517.17225
  tps: 599243.2353
  dtps: 55761.66697
  hps: 23421.3163
 }
}
dps_results: {
 key: "TestGuardian-AllItems-JadeCourtesanFigurine-86045"
 value: {
  dps: 85434.08168
  tps: 591620.37062
  dtps: 55901.04854
  hps: 22878.85019
 }
}
dps_results: {
 key: "TestGuardian-AllItems-JadeCourtesanFigurine-86774"
 value: {
  dps: 85434.08168
  tps: 591620.37062
  dtps: 55901.04854
  hps: 22878.85019
 }
}
dps_results: {
 key: "TestGuardian-AllItems-JadeMagistrateFigurine-86044"
 value: {
  dps: 87471.79913
  tps: 604682.05093
  dtps: 55801.70342
  hps: 24352.84852
 }
}
dps_results: {
 key: "TestGuardian-AllItems-JadeMagistrateFigurine-86773"
 value: {
  dps: 87422.10125
  tps: 604564.9427
  dtps: 56086.58264
  hps: 24648.24404
 }
}
dps_results: {
 key: "TestGuardian-AllItems-JadeWarlordFigurine-86046"
 value: {
  dps: 85459.96232
  tps: 591939.66559
  dtps: 55194.08225
  hps: 23149.16674
 }
}
dps_results: {
 key: "TestGuardian-AllItems-JadeWarlordFigurine-86775"
 value: {
  dps: 85401.38163
  tps: 591529.86654
  dtps: 55215.06411
  hps: 23379.74037
 }
}
dps_results: {
 key: "TestGuardian-AllItems-Ji-Kun'sRisingWinds-96843"
 value: {
  dps: 89239.94017
  tps: 617879.93342
  dtps: 55683.55857
  hps: 26247.81156
 }
}
dps_results: {
 key: "TestGuardian-AllItems-KnightlyBadgeoftheShieldwall-93349"
 value: {
  dps: 85877.198
  tps: 594806.76835
  dtps: 55308.07077
  hps: 22982.26482
 }
}
dps_results: {
 key: "TestGuardian-AllItems-KnotofTenSongs-84073"
 value: {
  dps: 85498.1039
  tps: 592207.03343
  dtps: 55505.51462
  hps: 22951.455
 }
}
dps_results: {
 key: "TestGuardian-AllItems-Kor'kronBookofHurting-92785"
 value: {
  dps: 85406.12726
  tps: 591563.08723
  dtps: 55901.04854
  hps: 22878.85019
 }
}
dps_results: {
 key: "TestGuardian-AllItems-Lao-Chin'sLiquidCourage-89079"
 value: {
  dps: 85459.96232
  tps: 591939.66559
  dtps: 55194.08225
  hps: 23149.16674
 }
}
dps_results: {
 key: "TestGuardian-AllItems-LeiShen'sFinalOrders-87072"
 value: {
  dps: 86521.69601
  tps: 599694.4993
  dtps: 55910.04629
  hps: 23376.67519
 }
}
dps_results: {
 key: "TestGuardian-AllItems-LessonsoftheDarkmaster-81268"
 value: {
  dps: 87992.60835
  tps: 608975.10331
  dtps: 56371.46066
  hps: 23441.7938
 }
}
dps_results: {
 key: "TestGuardian-AllItems-LightdrinkerIdolofRage-101200"
 value: {
  dps: 87494.81446
  tps: 605925.33724
  dtps: 54278.86467
  hps: 23368.83915
 }
}
dps_results: {
 key: "TestGuardian-AllItems-LightdrinkerStoneofRage-101203"
 value: {
  dps: 87468.17078
  tps: 605862.56161
  dtps: 54404.62612
  hps: 23343.51779
 }
}
dps_results: {
 key: "TestGuardian-AllItems-LightoftheCosmos-87065"
 value: {
  dps: 85787.78151
  tps: 594719.92864
  dtps: 55929.38944
  hps: 23240.77481
 }
}
dps_results: {
 key: "TestGuardian-AllItems-LightweaveEmbroidery(Rank3)-4892"
 value: {
  dps: 89066.0602
  tps: 616151.99043
  dtps: 55471.16926
  hps: 23932.4512
 }
}
dps_results: {
 key: "TestGuardian-AllItems-LordBlastington'sScopeofDoom-4699"
 value: {
  dps: 89061.25826
  tps: 616151.99043
  dtps: 55471.16926
  hps: 24001.72148
 }
}
dps_results: {
 key: "TestGuardian-AllItems-MalevolentGladiator'sBadgeofConquest-84934"
 value: {
  dps: 87242.37034
  tps: 604097.19627
  dtps: 55356.63489
  hps: 23362.42722
 }
}
dps_results: {
 key: "TestGuardian-AllItems-MalevolentGladiator'sBadgeofConquest-91452"
 value: {
  dps: 87141.122
  tps: 603409.97983
  dtps: 55391.28079
  hps: 23309.87491
 }
}
dps_results: {
 key: "TestGuardian-AllItems-MalevolentGladiator'sBadgeofDominance-84940"
 value: {
  dps: 85450.56408
  tps: 591735.74746
  dtps: 55901.04854
  hps: 22878.85019
 }
}
dps_results: {
 key: "TestGuardian-AllItems-MalevolentGladiator'sBadgeofDominance-91753"
 value: {
  dps: 85450.56408
  tps: 591735.74746
  dtps: 55901.04854
  hps: 22878.85019
 }
}
dps_results: {
 key: "TestGuardian-AllItems-MalevolentGladiator'sBadgeofVictory-84942"
 value: {
  dps: 85958.1277
  tps: 595257.26666
  dtps: 55949.24237
  hps: 23008.50081
 }
}
dps_results: {
 key: "TestGuardian-AllItems-MalevolentGladiator'sBadgeofVictory-91763"
 value: {
  dps: 85923.86111
  tps: 595028.24315
  dtps: 55950.29137
  hps: 23002.69714
 }
}
dps_results: {
 key: "TestGuardian-AllItems-MalevolentGladiator'sEmblemofCruelty-84936"
 value: {
  dps: 87622.36672
  tps: 606696.41683
  dtps: 55795.6041
  hps: 24271.57167
 }
}
dps_results: {
 key: "TestGuardian-AllItems-MalevolentGladiator'sEmblemofCruelty-91562"
 value: {
  dps: 87509.79891
  tps: 605908.39342
  dtps: 55795.6041
  hps: 24216.75702
 }
}
dps_results: {
 key: "TestGuardian-AllItems-MalevolentGladiator'sEmblemofMeditation-84939"
 value: {
  dps: 85406.12726
  tps: 591563.08723
  dtps: 55931.91062
  hps: 22930.46315
 }
}
dps_results: {
 key: "TestGuardian-AllItems-MalevolentGladiator'sEmblemofMeditation-91564"
 value: {
  dps: 85406.12726
  tps: 591563.08723
  dtps: 55931.91062
  hps: 22930.46315
 }
}
dps_results: {
 key: "TestGuardian-AllItems-MalevolentGladiator'sEmblemofTenacity-84938"
 value: {
  dps: 85401.36445
  tps: 591529.74628
  dtps: 55909.93108
  hps: 23353.1986
 }
}
dps_results: {
 key: "TestGuardian-AllItems-MalevolentGladiator'sEmblemofTenacity-91563"
 value: {
  dps: 85460.2828
  tps: 591942.1236
  dtps: 55774.40673
  hps: 23075.86082
 }
}
dps_results: {
 key: "TestGuardian-AllItems-MalevolentGladiator'sInsigniaofConquest-91457"
 value: {
  dps: 87803.21743
  tps: 608024.35745
  dtps: 55421.14316
  hps: 23475.90453
 }
}
dps_results: {
 key: "TestGuardian-AllItems-MalevolentGladiator'sInsigniaofDominance-91754"
 value: {
  dps: 85438.46325
  tps: 591619.75531
  dtps: 55901.04854
  hps: 22878.85019
 }
}
dps_results: {
 key: "TestGuardian-AllItems-MalevolentGladiator'sInsigniaofVictory-91768"
 value: {
  dps: 86153.06393
  tps: 596624.11564
  dtps: 55945.67221
  hps: 23039.70974
 }
}
dps_results: {
 key: "TestGuardian-AllItems-MarkoftheCatacombs-83731"
 value: {
  dps: 86757.88544
  tps: 600813.28468
  dtps: 55615.76909
  hps: 23699.77516
 }
}
dps_results: {
 key: "TestGuardian-AllItems-MarkoftheHardenedGrunt-92783"
 value: {
  dps: 85406.12726
  tps: 591563.08723
  dtps: 55901.04854
  hps: 22878.85019
 }
}
dps_results: {
 key: "TestGuardian-AllItems-MedallionofMystifyingVapors-93257"
 value: {
  dps: 85414.55521
  tps: 591622.11537
  dtps: 54442.79818
  hps: 22915.76062
 }
}
dps_results: {
 key: "TestGuardian-AllItems-MedallionoftheCatacombs-83734"
 value: {
  dps: 85401.36445
  tps: 591529.74758
  dtps: 55367.46331
  hps: 22878.93558
 }
}
dps_results: {
 key: "TestGuardian-AllItems-MendingBadgeoftheShieldwall-93348"
 value: {
  dps: 85418.77675
  tps: 591620.37062
  dtps: 55901.04854
  hps: 22878.85019
 }
}
dps_results: {
 key: "TestGuardian-AllItems-MirrorScope-4700"
 value: {
  dps: 89061.25826
  tps: 616151.99043
  dtps: 55471.16926
  hps: 24001.72148
 }
}
dps_results: {
 key: "TestGuardian-AllItems-MistdancerDefenderIdol-101089"
 value: {
  dps: 85440.79424
  tps: 591805.75226
  dtps: 54958.41644
  hps: 23472.85582
 }
}
dps_results: {
 key: "TestGuardian-AllItems-MistdancerDefenderStone-101087"
 value: {
  dps: 85482.94649
  tps: 592100.89104
  dtps: 54802.85414
  hps: 23247.12297
 }
}
dps_results: {
 key: "TestGuardian-AllItems-MistdancerIdolofRage-101113"
 value: {
  dps: 87424.98047
  tps: 605432.35604
  dtps: 54616.18773
  hps: 22977.40569
 }
}
dps_results: {
 key: "TestGuardian-AllItems-MistdancerStoneofRage-101117"
 value: {
  dps: 87412.80946
  tps: 605475.08348
  dtps: 54486.97838
  hps: 23607.36755
 }
}
dps_results: {
 key: "TestGuardian-AllItems-MistdancerStoneofWisdom-101107"
 value: {
  dps: 85434.08168
  tps: 591620.37062
  dtps: 55901.04854
  hps: 22878.85019
 }
}
dps_results: {
 key: "TestGuardian-AllItems-MithrilWristwatch-87572"
 value: {
  dps: 87294.9116
  tps: 604605.88242
  dtps: 55647.92295
  hps: 24107.08956
 }
}
dps_results: {
 key: "TestGuardian-AllItems-MountainsageIdolofDestruction-101069"
 value: {
  dps: 85593.73268
  tps: 592875.98339
  dtps: 54901.24043
  hps: 22877.62502
 }
}
dps_results: {
 key: "TestGuardian-AllItems-MountainsageStoneofDestruction-101072"
 value: {
  dps: 85488.23722
  tps: 591999.40699
  dtps: 54767.18332
  hps: 22627.31647
 }
}
dps_results: {
 key: "TestGuardian-AllItems-OathswornDefenderIdol-101303"
 value: {
  dps: 85427.61916
  tps: 591713.49422
  dtps: 55149.43224
  hps: 23435.13675
 }
}
dps_results: {
 key: "TestGuardian-AllItems-OathswornDefenderStone-101306"
 value: {
  dps: 85482.94649
  tps: 592100.89104
  dtps: 54844.32077
  hps: 23247.27523
 }
}
dps_results: {
 key: "TestGuardian-AllItems-OathswornIdolofBattle-101295"
 value: {
  dps: 88262.44908
  tps: 611069.09362
  dtps: 55674.76454
  hps: 24353.68905
 }
}
dps_results: {
 key: "TestGuardian-AllItems-OathswornStoneofBattle-101294"
 value: {
  dps: 86061.23517
  tps: 596080.874
  dtps: 54807.80504
  hps: 22755.78731
 }
}
dps_results: {
 key: "TestGuardian-AllItems-PhaseFingers-4697"
 value: {
  dps: 89061.25826
  tps: 616151.99043
  dtps: 55059.98742
  hps: 23949.69821
 }
}
dps_results: {
 key: "TestGuardian-AllItems-PouchofWhiteAsh-103639"
 value: {
  dps: 85406.12726
  tps: 591563.08723
  dtps: 55901.04854
  hps: 22878.85019
 }
}
dps_results: {
 key: "TestGuardian-AllItems-PowerfulPrimalDiamond"
 value: {
  dps: 89061.25826
  tps: 616151.99043
  dtps: 56193.48995
  hps: 24010.6332
 }
}
dps_results: {
 key: "TestGuardian-AllItems-PriceofProgress-81266"
 value: {
  dps: 85418.77675
  tps: 591620.37062
  dtps: 55901.04854
  hps: 22878.85019
 }
}
dps_results: {
 key: "TestGuardian-AllItems-PridefulGladiator'sBadgeofConquest-102659"
 value: {
  dps: 88685.02081
  tps: 613684.50195
  dtps: 54503.82242
  hps: 23954.31223
 }
}
dps_results: {
 key: "TestGuardian-AllItems-PridefulGladiator'sBadgeofConquest-103342"
 value: {
  dps: 88685.02081
  tps: 613684.50195
  dtps: 54503.82242
  hps: 23954.31223
 }
}
dps_results: {
 key: "TestGuardian-AllItems-PridefulGladiator'sBadgeofDominance-102633"
 value: {
  dps: 85466.78492
  tps: 591792.11482
  dtps: 55901.04854
  hps: 22878.85019
 }
}
dps_results: {
 key: "TestGuardian-AllItems-PridefulGladiator'sBadgeofDominance-103505"
 value: {
  dps: 85466.78492
  tps: 591792.11482
  dtps: 55901.04854
  hps: 22878.85019
 }
}
dps_results: {
 key: "TestGuardian-AllItems-PridefulGladiator'sBadgeofVictory-102636"
 value: {
  dps: 86423.1443
  tps: 598365.24223
  dtps: 55935.00321
  hps: 23086.29645
 }
}
dps_results: {
 key: "TestGuardian-AllItems-PridefulGladiator'sBadgeofVictory-103511"
 value: {
  dps: 86423.1443
  tps: 598365.24223
  dtps: 55935.00321
  hps: 23086.29645
 }
}
dps_results: {
 key: "TestGuardian-AllItems-PridefulGladiator'sEmblemofCruelty-102680"
 value: {
  dps: 89050.43279
  tps: 616346.7899
  dtps: 55931.14622
  hps: 25894.94303
 }
}
dps_results: {
 key: "TestGuardian-AllItems-PridefulGladiator'sEmblemofCruelty-103407"
 value: {
  dps: 89050.43279
  tps: 616346.7899
  dtps: 55931.14622
  hps: 25894.94303
 }
}
dps_results: {
 key: "TestGuardian-AllItems-PridefulGladiator'sEmblemofMeditation-102616"
 value: {
  dps: 85406.12726
  tps: 591563.08723
  dtps: 55931.91062
  hps: 22930.46315
 }
}
dps_results: {
 key: "TestGuardian-AllItems-PridefulGladiator'sEmblemofMeditation-103409"
 value: {
  dps: 85406.12726
  tps: 591563.08723
  dtps: 55931.91062
  hps: 22930.46315
 }
}
dps_results: {
 key: "TestGuardian-AllItems-PridefulGladiator'sEmblemofTenacity-102706"
 value: {
  dps: 85515.24121
  tps: 592326.72331
  dtps: 55763.78909
  hps: 23234.16898
 }
}
dps_results: {
 key: "TestGuardian-AllItems-PridefulGladiator'sEmblemofTenacity-103408"
 value: {
  dps: 85515.24121
  tps: 592326.72331
  dtps: 55763.78909
  hps: 23234.16898
 }
}
dps_results: {
 key: "TestGuardian-AllItems-PridefulGladiator'sInsigniaofConquest-103347"
 value: {
  dps: 90029.59711
  tps: 623253.11817
  dtps: 54519.72851
  hps: 24246.03541
 }
}
dps_results: {
 key: "TestGuardian-AllItems-PridefulGladiator'sInsigniaofDominance-103506"
 value: {
  dps: 85438.6197
  tps: 591619.75531
  dtps: 55901.04854
  hps: 22878.85019
 }
}
dps_results: {
 key: "TestGuardian-AllItems-PridefulGladiator'sInsigniaofVictory-103516"
 value: {
  dps: 86860.78588
  tps: 601434.65105
  dtps: 55927.10658
  hps: 23181.22091
 }
}
dps_results: {
 key: "TestGuardian-AllItems-Primordius'TalismanofRage-96873"
 value: {
  dps: 89954.97956
  tps: 622531.4425
  dtps: 55903.87185
  hps: 26267.0933
 }
}
dps_results: {
 key: "TestGuardian-AllItems-Qian-Le,CourageofNiuzao-102245"
 value: {
  dps: 94356.33531
  tps: 652399.39762
  dtps: 50614.73118
  hps: 26773.85987
 }
}
dps_results: {
 key: "TestGuardian-AllItems-Qian-Ying,FortitudeofNiuzao-102250"
 value: {
  dps: 90036.02367
  tps: 622753.84724
  dtps: 51409.26093
  hps: 25290.45647
 }
}
dps_results: {
 key: "TestGuardian-AllItems-Qin-xi'sPolarizingSeal-87075"
 value: {
  dps: 85414.17974
  tps: 591619.45458
  dtps: 55901.04854
  hps: 22878.85019
 }
}
dps_results: {
 key: "TestGuardian-AllItems-RegaliaoftheEternalBlossom"
 value: {
  dps: 78212.35739
  tps: 541685.25044
  dtps: 53477.68853
  hps: 19898.78968
 }
}
dps_results: {
 key: "TestGuardian-AllItems-RegaliaoftheHauntedForest"
 value: {
  dps: 79913.05751
  tps: 553585.06351
  dtps: 52043.51639
  hps: 23229.20338
 }
}
dps_results: {
 key: "TestGuardian-AllItems-RegaliaoftheShatteredVale"
 value: {
  dps: 82388.06576
  tps: 571269.1306
  dtps: 51012.20582
  hps: 26635.40669
 }
}
dps_results: {
 key: "TestGuardian-AllItems-RelicofChi-Ji-79330"
 value: {
  dps: 85434.08168
  tps: 591620.37062
  dtps: 55901.04854
  hps: 22878.85019
 }
}
dps_results: {
 key: "TestGuardian-AllItems-RelicofKypariZar-84075"
 value: {
  dps: 86469.32483
  tps: 598955.26283
  dtps: 56041.03512
  hps: 24478.57354
 }
}
dps_results: {
 key: "TestGuardian-AllItems-RelicofNiuzao-79329"
 value: {
  dps: 85390.56543
  tps: 591454.23687
  dtps: 54686.99421
  hps: 23421.17362
 }
}
dps_results: {
 key: "TestGuardian-AllItems-RelicofXuen-79327"
 value: {
  dps: 86447.60086
  tps: 598631.62501
  dtps: 55943.43041
  hps: 23121.66024
 }
}
dps_results: {
 key: "TestGuardian-AllItems-RelicofXuen-79328"
 value: {
  dps: 88457.01151
  tps: 612473.24839
  dtps: 55018.91656
  hps: 23702.97804
 }
}
dps_results: {
 key: "TestGuardian-AllItems-RelicofYu'lon-79331"
 value: {
  dps: 85434.08168
  tps: 591620.37062
  dtps: 55901.04854
  hps: 22878.85019
 }
}
dps_results: {
 key: "TestGuardian-AllItems-Renataki'sSoulCharm-96741"
 value: {
  dps: 101814.33398
  tps: 704631.37344
  dtps: 50787.9613
  hps: 27443.64066
 }
}
dps_results: {
 key: "TestGuardian-AllItems-ResolveofNiuzao-103690"
 value: {
  dps: 85408.44346
  tps: 591579.09663
  dtps: 53951.08148
  hps: 22703.41658
 }
}
dps_results: {
 key: "TestGuardian-AllItems-ResolveofNiuzao-103990"
 value: {
  dps: 85400.56628
  tps: 591524.02063
  dtps: 52848.05073
  hps: 22572.55247
 }
}
dps_results: {
 key: "TestGuardian-AllItems-ReverberatingPrimalDiamond"
 value: {
  dps: 90429.97195
  tps: 625650.01956
  dtps: 56190.96478
  hps: 23928.04901
 }
}
dps_results: {
 key: "TestGuardian-AllItems-RevitalizingPrimalDiamond"
 value: {
  dps: 90297.37372
  tps: 624737.31364
  dtps: 56193.48995
  hps: 23897.94418
 }
}
dps_results: {
 key: "TestGuardian-AllItems-SI:7Operative'sManual-92784"
 value: {
  dps: 85406.12726
  tps: 591563.08723
  dtps: 55901.04854
  hps: 22878.85019
 }
}
dps_results: {
 key: "TestGuardian-AllItems-ScrollofReveredAncestors-89080"
 value: {
  dps: 85434.08168
  tps: 591620.37062
  dtps: 55901.04854
  hps: 22878.85019
 }
}
dps_results: {
 key: "TestGuardian-AllItems-Shock-ChargerMedallion-93259"
 value: {
  dps: 85713.88453
  tps: 593966.84561
  dtps: 56297.16792
  hps: 23184.86545
 }
}
dps_results: {
 key: "TestGuardian-AllItems-SigilofCompassion-83736"
 value: {
  dps: 85401.36445
  tps: 591529.74758
  dtps: 55367.46331
  hps: 22878.93558
 }
}
dps_results: {
 key: "TestGuardian-AllItems-SigilofDevotion-83740"
 value: {
  dps: 86742.09913
  tps: 600702.74803
  dtps: 55469.3796
  hps: 23667.45574
 }
}
dps_results: {
 key: "TestGuardian-AllItems-SigilofFidelity-83737"
 value: {
  dps: 86899.06311
  tps: 601771.25142
  dtps: 56367.45889
  hps: 24187.21121
 }
}
dps_results: {
 key: "TestGuardian-AllItems-SigilofGrace-83738"
 value: {
  dps: 85606.9951
  tps: 593262.60788
  dtps: 55508.31849
  hps: 23130.81253
 }
}
dps_results: {
 key: "TestGuardian-AllItems-SigilofKypariZar-84076"
 value: {
  dps: 86804.46293
  tps: 601172.17302
  dtps: 55330.34757
  hps: 23762.60339
 }
}
dps_results: {
 key: "TestGuardian-AllItems-SigilofPatience-83739"
 value: {
  dps: 85401.36445
  tps: 591529.74758
  dtps: 55349.25994
  hps: 22878.40777
 }
}
dps_results: {
 key: "TestGuardian-AllItems-SigiloftheCatacombs-83732"
 value: {
  dps: 86600.34341
  tps: 600295.30305
  dtps: 55515.3113
  hps: 23449.17154
 }
}
dps_results: {
 key: "TestGuardian-AllItems-SinisterPrimalDiamond"
 value: {
  dps: 89453.67137
  tps: 618623.34395
  dtps: 55695.54351
  hps: 24162.41642
 }
}
dps_results: {
 key: "TestGuardian-AllItems-SkullrenderMedallion-93256"
 value: {
  dps: 88068.96396
  tps: 608675.69684
  dtps: 55376.72
  hps: 24253.48847
 }
}
dps_results: {
 key: "TestGuardian-AllItems-SpiritsoftheSun-87163"
 value: {
  dps: 85434.08168
  tps: 591620.37062
  dtps: 55901.04854
  hps: 22878.85019
 }
}
dps_results: {
 key: "TestGuardian-AllItems-SpringrainIdolofDestruction-101023"
 value: {
  dps: 85523.15362
  tps: 592382.35738
  dtps: 55004.00124
  hps: 23232.1841
 }
}
dps_results: {
 key: "TestGuardian-AllItems-SpringrainIdolofRage-101009"
 value: {
  dps: 87512.83236
  tps: 605985.53192
  dtps: 54092.37286
  hps: 23320.56402
 }
}
dps_results: {
 key: "TestGuardian-AllItems-SpringrainStoneofDestruction-101026"
 value: {
  dps: 85488.23722
  tps: 591999.40699
  dtps: 54796.39655
  hps: 22617.5588
 }
}
dps_results: {
 key: "TestGuardian-AllItems-SpringrainStoneofRage-101012"
 value: {
  dps: 87468.17078
  tps: 605862.56161
  dtps: 54402.10113
  hps: 23352.89603
 }
}
dps_results: {
 key: "TestGuardian-AllItems-SpringrainStoneofWisdom-101041"
 value: {
  dps: 85434.08168
  tps: 591620.37062
  dtps: 55901.04854
  hps: 22878.85019
 }
}
dps_results: {
 key: "TestGuardian-AllItems-Static-Caster'sMedallion-93254"
 value: {
  dps: 85713.88453
  tps: 593966.84561
  dtps: 56297.16792
  hps: 23184.86545
 }
}
dps_results: {
 key: "TestGuardian-AllItems-SteadfastFootman'sMedallion-92782"
 value: {
  dps: 85406.12726
  tps: 591563.08723
  dtps: 55901.04854
  hps: 22878.85019
 }
}
dps_results: {
 key: "TestGuardian-AllItems-SteadfastTalismanoftheShado-PanAssault-94507"
 value: {
  dps: 85414.60365
  tps: 591622.45446
  dtps: 54463.55678
  hps: 22914.76603
 }
}
dps_results: {
 key: "TestGuardian-AllItems-StreamtalkerIdolofDestruction-101222"
 value: {
  dps: 85647.08623
  tps: 593250.24578
  dtps: 54952.50592
  hps: 23457.64699
 }
}
dps_results: {
 key: "TestGuardian-AllItems-StreamtalkerIdolofRage-101217"
 value: {
  dps: 87558.13766
  tps: 606393.7273
  dtps: 54495.60483
  hps: 23569.42135
 }
}
dps_results: {
 key: "TestGuardian-AllItems-StreamtalkerStoneofDestruction-101225"
 value: {
  dps: 85488.23722
  tps: 591999.40699
  dtps: 54783.40216
  hps: 22617.95256
 }
}
dps_results: {
 key: "TestGuardian-AllItems-StreamtalkerStoneofRage-101220"
 value: {
  dps: 87468.17078
  tps: 605862.56161
  dtps: 54426.22906
  hps: 23352.98492
 }
}
dps_results: {
 key: "TestGuardian-AllItems-StreamtalkerStoneofWisdom-101250"
 value: {
  dps: 85434.08168
  tps: 591620.37062
  dtps: 55901.04854
  hps: 22878.85019
 }
}
dps_results: {
 key: "TestGuardian-AllItems-StuffofNightmares-87160"
 value: {
  dps: 85421.45074
  tps: 591670.24756
  dtps: 53489.20441
  hps: 22760.51366
 }
}
dps_results: {
 key: "TestGuardian-AllItems-SunsoulDefenderIdol-101160"
 value: {
  dps: 85427.61916
  tps: 591713.49422
  dtps: 55116.35908
  hps: 23434.52974
 }
}
dps_results: {
 key: "TestGuardian-AllItems-SunsoulDefenderStone-101163"
 value: {
  dps: 85482.94649
  tps: 592100.89104
  dtps: 54815.15459
  hps: 23247.27523
 }
}
dps_results: {
 key: "TestGuardian-AllItems-SunsoulIdolofBattle-101152"
 value: {
  dps: 88267.15068
  tps: 611034.85221
  dtps: 55657.69773
  hps: 24348.11372
 }
}
dps_results: {
 key: "TestGuardian-AllItems-SunsoulStoneofBattle-101151"
 value: {
  dps: 86061.23517
  tps: 596080.874
  dtps: 54763.71057
  hps: 22755.78731
 }
}
dps_results: {
 key: "TestGuardian-AllItems-SunsoulStoneofWisdom-101138"
 value: {
  dps: 85442.13416
  tps: 591676.73797
  dtps: 55901.04854
  hps: 22878.85019
 }
}
dps_results: {
 key: "TestGuardian-AllItems-SwordguardEmbroidery(Rank3)-4894"
 value: {
  dps: 89630.13255
  tps: 619981.25926
  dtps: 55447.46306
  hps: 24044.73387
 }
}
dps_results: {
 key: "TestGuardian-AllItems-SymboloftheCatacombs-83735"
 value: {
  dps: 86759.42967
  tps: 600746.3308
  dtps: 55554.63602
  hps: 24112.51798
 }
}
dps_results: {
 key: "TestGuardian-AllItems-SynapseSprings(MarkII)-4898"
 value: {
  dps: 89900.16837
  tps: 622343.70246
  dtps: 55329.43302
  hps: 24311.83062
 }
}
dps_results: {
 key: "TestGuardian-AllItems-TalismanofBloodlust-96864"
 value: {
  dps: 89000.51903
  tps: 617236.85005
  dtps: 54584.50733
  hps: 25024.78326
 }
}
dps_results: {
 key: "TestGuardian-AllItems-TerrorintheMists-87167"
 value: {
  dps: 90965.47486
  tps: 628789.53683
  dtps: 55279.75764
  hps: 25186.5882
 }
}
dps_results: {
 key: "TestGuardian-AllItems-TheGloamingBlade-88149"
 value: {
  dps: 89061.25826
  tps: 616151.99043
  dtps: 55471.16926
  hps: 24001.72148
 }
}
dps_results: {
 key: "TestGuardian-AllItems-Thousand-YearPickledEgg-87573"
 value: {
  dps: 86051.45428
  tps: 595917.61485
  dtps: 55767.29586
  hps: 23309.74465
 }
}
dps_results: {
 key: "TestGuardian-AllItems-TrailseekerIdolofRage-101054"
 value: {
  dps: 87486.4829
  tps: 605820.58952
  dtps: 54661.256
  hps: 23119.63116
 }
}
dps_results: {
 key: "TestGuardian-AllItems-TrailseekerStoneofRage-101057"
 value: {
  dps: 87468.17078
  tps: 605862.56161
  dtps: 54390.76071
  hps: 23352.89603
 }
}
dps_results: {
 key: "TestGuardian-AllItems-TyrannicalGladiator'sBadgeofConquest-100043"
 value: {
  dps: 87548.57022
  tps: 605984.98333
  dtps: 55206.24215
  hps: 23289.09061
 }
}
dps_results: {
 key: "TestGuardian-AllItems-TyrannicalGladiator'sBadgeofConquest-91099"
 value: {
  dps: 87548.57022
  tps: 605984.98333
  dtps: 55206.24215
  hps: 23289.09061
 }
}
dps_results: {
 key: "TestGuardian-AllItems-TyrannicalGladiator'sBadgeofConquest-94373"
 value: {
  dps: 87548.57022
  tps: 605984.98333
  dtps: 55206.24215
  hps: 23289.09061
 }
}
dps_results: {
 key: "TestGuardian-AllItems-TyrannicalGladiator'sBadgeofConquest-99772"
 value: {
  dps: 87548.57022
  tps: 605984.98333
  dtps: 55206.24215
  hps: 23289.09061
 }
}
dps_results: {
 key: "TestGuardian-AllItems-TyrannicalGladiator'sBadgeofDominance-100016"
 value: {
  dps: 85450.56408
  tps: 591735.74746
  dtps: 55901.04854
  hps: 22878.85019
 }
}
dps_results: {
 key: "TestGuardian-AllItems-TyrannicalGladiator'sBadgeofDominance-91400"
 value: {
  dps: 85450.56408
  tps: 591735.74746
  dtps: 55901.04854
  hps: 22878.85019
 }
}
dps_results: {
 key: "TestGuardian-AllItems-TyrannicalGladiator'sBadgeofDominance-94346"
 value: {
  dps: 85450.56408
  tps: 591735.74746
  dtps: 55901.04854
  hps: 22878.85019
 }
}
dps_results: {
 key: "TestGuardian-AllItems-TyrannicalGladiator'sBadgeofDominance-99937"
 value: {
  dps: 85450.56408
  tps: 591735.74746
  dtps: 55901.04854
  hps: 22878.85019
 }
}
dps_results: {
 key: "TestGuardian-AllItems-TyrannicalGladiator'sBadgeofVictory-100019"
 value: {
  dps: 86027.08134
  tps: 595718.1238
  dtps: 55947.13087
  hps: 23020.17935
 }
}
dps_results: {
 key: "TestGuardian-AllItems-TyrannicalGladiator'sBadgeofVictory-91410"
 value: {
  dps: 86027.08134
  tps: 595718.1238
  dtps: 55947.13087
  hps: 23020.17935
 }
}
dps_results: {
 key: "TestGuardian-AllItems-TyrannicalGladiator'sBadgeofVictory-94349"
 value: {
  dps: 86027.08134
  tps: 595718.1238
  dtps: 55947.13087
  hps: 23020.17935
 }
}
dps_results: {
 key: "TestGuardian-AllItems-TyrannicalGladiator'sBadgeofVictory-99943"
 value: {
  dps: 86027.08134
  tps: 595718.1238
  dtps: 55947.13087
  hps: 23020.17935
 }
}
dps_results: {
 key: "TestGuardian-AllItems-TyrannicalGladiator'sEmblemofCruelty-100066"
 value: {
  dps: 87824.13273
  tps: 607976.62639
  dtps: 55602.32656
  hps: 24522.01659
 }
}
dps_results: {
 key: "TestGuardian-AllItems-TyrannicalGladiator'sEmblemofCruelty-91209"
 value: {
  dps: 87824.13273
  tps: 607976.62639
  dtps: 55602.32656
  hps: 24522.01659
 }
}
dps_results: {
 key: "TestGuardian-AllItems-TyrannicalGladiator'sEmblemofCruelty-94396"
 value: {
  dps: 87824.13273
  tps: 607976.62639
  dtps: 55602.32656
  hps: 24522.01659
 }
}
dps_results: {
 key: "TestGuardian-AllItems-TyrannicalGladiator'sEmblemofCruelty-99838"
 value: {
  dps: 87824.13273
  tps: 607976.62639
  dtps: 55602.32656
  hps: 24522.01659
 }
}
dps_results: {
 key: "TestGuardian-AllItems-TyrannicalGladiator'sEmblemofMeditation-91211"
 value: {
  dps: 85406.12726
  tps: 591563.08723
  dtps: 55931.91062
  hps: 22930.46315
 }
}
dps_results: {
 key: "TestGuardian-AllItems-TyrannicalGladiator'sEmblemofMeditation-94329"
 value: {
  dps: 85406.12726
  tps: 591563.08723
  dtps: 55931.91062
  hps: 22930.46315
 }
}
dps_results: {
 key: "TestGuardian-AllItems-TyrannicalGladiator'sEmblemofMeditation-99840"
 value: {
  dps: 85406.12726
  tps: 591563.08723
  dtps: 55931.91062
  hps: 22930.46315
 }
}
dps_results: {
 key: "TestGuardian-AllItems-TyrannicalGladiator'sEmblemofMeditation-99990"
 value: {
  dps: 85406.12726
  tps: 591563.08723
  dtps: 55931.91062
  hps: 22930.46315
 }
}
dps_results: {
 key: "TestGuardian-AllItems-TyrannicalGladiator'sEmblemofTenacity-100092"
 value: {
  dps: 85401.38163
  tps: 591529.86654
  dtps: 55909.93108
  hps: 23406.92519
 }
}
dps_results: {
 key: "TestGuardian-AllItems-TyrannicalGladiator'sEmblemofTenacity-91210"
 value: {
  dps: 85401.38163
  tps: 591529.86654
  dtps: 55909.93108
  hps: 23406.92519
 }
}
dps_results: {
 key: "TestGuardian-AllItems-TyrannicalGladiator'sEmblemofTenacity-94422"
 value: {
  dps: 85401.38163
  tps: 591529.86654
  dtps: 55909.93108
  hps: 23406.92519
 }
}
dps_results: {
 key: "TestGuardian-AllItems-TyrannicalGladiator'sEmblemofTenacity-99839"
 value: {
  dps: 85401.38163
  tps: 591529.86654
  dtps: 55909.93108
  hps: 23406.92519
 }
}
dps_results: {
 key: "TestGuardian-AllItems-TyrannicalGladiator'sInsigniaofConquest-100026"
 value: {
  dps: 88163.94821
  tps: 610478.34197
  dtps: 54844.96192
  hps: 23494.49763
 }
}
dps_results: {
 key: "TestGuardian-AllItems-TyrannicalGladiator'sInsigniaofDominance-100152"
 value: {
  dps: 85438.78132
  tps: 591619.75531
  dtps: 55901.04854
  hps: 22878.85019
 }
}
dps_results: {
 key: "TestGuardian-AllItems-TyrannicalGladiator'sInsigniaofVictory-100085"
 value: {
  dps: 86268.89971
  tps: 597419.01218
  dtps: 55905.07515
  hps: 23071.80409
 }
}
dps_results: {
 key: "TestGuardian-AllItems-TyrannicalPrimalDiamond"
 value: {
  dps: 89061.42948
  tps: 616153.18896
  dtps: 56193.48995
  hps: 23897.94418
 }
}
dps_results: {
 key: "TestGuardian-AllItems-UnerringVisionofLeiShen-96930"
 value: {
  dps: 87880.78466
  tps: 608403.47616
  dtps: 55487.31342
  hps: 24106.02968
 }
}
dps_results: {
 key: "TestGuardian-AllItems-VaporshieldMedallion-93262"
 value: {
  dps: 85414.55521
  tps: 591622.11537
  dtps: 54442.79818
  hps: 22915.76062
 }
}
dps_results: {
 key: "TestGuardian-AllItems-VialofDragon'sBlood-87063"
 value: {
  dps: 85414.55521
  tps: 591622.11537
  dtps: 53844.03726
  hps: 22913.8177
 }
}
dps_results: {
 key: "TestGuardian-AllItems-VialofIchorousBlood-100963"
 value: {
  dps: 85418.77675
  tps: 591620.37062
  dtps: 55901.04854
  hps: 22878.85019
 }
}
dps_results: {
 key: "TestGuardian-AllItems-VialofIchorousBlood-81264"
 value: {
  dps: 85418.77675
  tps: 591620.37062
  dtps: 55901.04854
  hps: 22878.85019
 }
}
dps_results: {
 key: "TestGuardian-AllItems-ViciousTalismanoftheShado-PanAssault-94511"
 value: {
  dps: 89804.15375
  tps: 621242.48942
  dtps: 54725.65703
  hps: 23522.28947
 }
}
dps_results: {
 key: "TestGuardian-AllItems-VisionofthePredator-81192"
 value: {
  dps: 85718.07093
  tps: 593715.48921
  dtps: 55813.47681
  hps: 23159.81112
 }
}
dps_results: {
 key: "TestGuardian-AllItems-VolatileTalismanoftheShado-PanAssault-94510"
 value: {
  dps: 85625.18658
  tps: 592958.65771
  dtps: 56010.62714
  hps: 23717.33489
 }
}
dps_results: {
 key: "TestGuardian-AllItems-WindsweptPages-81125"
 value: {
  dps: 87558.84495
  tps: 607093.22276
  dtps: 54987.26781
  hps: 23977.75839
 }
}
dps_results: {
 key: "TestGuardian-AllItems-WoundripperMedallion-93253"
 value: {
  dps: 89510.18782
  tps: 618622.67662
  dtps: 54771.40565
  hps: 24748.16208
 }
}
dps_results: {
 key: "TestGuardian-AllItems-Wushoolay'sFinalChoice-96785"
 value: {
  dps: 86671.86206
  tps: 600172.94993
  dtps: 56124.71043
  hps: 22479.96645
 }
}
dps_results: {
 key: "TestGuardian-AllItems-Xing-Ho,BreathofYu'lon-102246"
 value: {
  dps: 89072.7685
  tps: 616395.53014
  dtps: 53868.88074
  hps: 24827.71638
 }
}
dps_results: {
 key: "TestGuardian-AllItems-YaungolFireCarrier-86518"
 value: {
  dps: 89693.03063
  tps: 620538.01643
  dtps: 55465.35709
  hps: 24190.96776
 }
}
dps_results: {
 key: "TestGuardian-AllItems-Yu'lon'sBite-103987"
 value: {
  dps: 86665.36001
  tps: 600240.39377
  dtps: 55934.39355
  hps: 23963.93052
 }
}
dps_results: {
 key: "TestGuardian-AllItems-ZenAlchemistStone-75274"
 value: {
  dps: 87540.60518
  tps: 605979.51324
  dtps: 53885.24807
  hps: 23497.91003
 }
}
dps_results: {
 key: "TestGuardian-Average-Default"
 value: {
  dps: 89412.14542
  tps: 618359.47311
  dtps: 54362.20405
  hps: 24614.02865
 }
}
dps_results: {
 key: "TestGuardian-Settings-Worgen-preraid-DefaultTalents-Default-default-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 1.74094420258e+06
  tps: 1.212553488805e+07
  dtps: 1.10396696862e+06
  hps: 111879.94403
 }
}
dps_results: {
 key: "TestGuardian-Settings-Worgen-preraid-DefaultTalents-Default-default-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 96482.94309
  tps: 666938.63816
  dtps: 53935.51565
  hps: 27121.38465
 }
}
dps_results: {
 key: "TestGuardian-Settings-Worgen-preraid-DefaultTalents-Default-default-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 96631.02928
  tps: 634086.0183
  dtps: 48660.0641
  hps: 21799.53681
 }
}
dps_results: {
 key: "TestGuardian-Settings-Worgen-preraid-DefaultTalents-Default-default-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 1.3041963134e+06
  tps: 9.12989511345e+06
  dtps: 1.18074586895e+06
  hps: 73081.96866
 }
}
dps_results: {
 key: "TestGuardian-Settings-Worgen-preraid-DefaultTalents-Default-default-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 79065.02454
  tps: 553493.24535
  dtps: 58924.74923
  hps: 19829.80753
 }
}
dps_results: {
 key: "TestGuardian-Settings-Worgen-preraid-DefaultTalents-Default-default-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 71264.62971
  tps: 498902.40175
  dtps: 57196.61245
  hps: 15920.60659
 }
}
dps_results: {
 key: "TestGuardian-Settings-Worgen-preraid-FoN-Default-default-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 1.7340682016e+06
  tps: 1.200846951087e+07
  dtps: 1.10962378939e+06
  hps: 83379.83684
 }
}
dps_results: {
 key: "TestGuardian-Settings-Worgen-preraid-FoN-Default-default-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 95226.2483
  tps: 641196.58493
  dtps: 55546.95818
  hps: 22585.8481
 }
}
dps_results: {
 key: "TestGuardian-Settings-Worgen-preraid-FoN-Default-default-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 94940.66466
  tps: 603281.12669
  dtps: 50928.39693
  hps: 19498.39328
 }
}
dps_results: {
 key: "TestGuardian-Settings-Worgen-preraid-FoN-Default-default-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 1.29685801643e+06
  tps: 9.03237225434e+06
  dtps: 1.18369815872e+06
  hps: 52235.32175
 }
}
dps_results: {
 key: "TestGuardian-Settings-Worgen-preraid-FoN-Default-default-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 77905.0152
  tps: 531898.69233
  dtps: 59686.57598
  hps: 15283.66103
 }
}
dps_results: {
 key: "TestGuardian-Settings-Worgen-preraid-FoN-Default-default-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 69732.51073
  tps: 471490.05227
  dtps: 59168.70178
  hps: 12522.73803
 }
}
dps_results: {
 key: "TestGuardian-SwitchInFrontOfTarget-Default"
 value: {
  dps: 96482.94309
  tps: 666938.63816
  dtps: 53935.51565
  hps: 27121.38465
 }
}
