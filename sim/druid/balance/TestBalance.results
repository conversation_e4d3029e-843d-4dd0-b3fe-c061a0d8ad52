character_stats_results: {
 key: "TestBalance-CharacterStats-Default"
 value: {
  final_stats: 189
  final_stats: 199.227
  final_stats: 16901.17
  final_stats: 16238.67
  final_stats: 4679
  final_stats: 5553
  final_stats: 950
  final_stats: 4539
  final_stats: 0
  final_stats: 93.25563
  final_stats: 0
  final_stats: 5246
  final_stats: 493.9
  final_stats: 0
  final_stats: 24244.737
  final_stats: 0
  final_stats: 0
  final_stats: 9733.2
  final_stats: 0
  final_stats: 383019.38
  final_stats: 300000
  final_stats: 3000
  final_stats: 16.33235
  final_stats: 16.33235
  final_stats: 14.22151
  final_stats: 14.84257
  final_stats: 0
 }
}
dps_results: {
 key: "TestBalance-AllItems-AgilePrimalDiamond"
 value: {
  dps: 78521.19257
  tps: 79606.19656
  hps: 5358.30565
 }
}
dps_results: {
 key: "TestBalance-AllItems-AssuranceofConsequence-105472"
 value: {
  dps: 76150.56288
  tps: 77281.00611
  hps: 5350.72907
 }
}
dps_results: {
 key: "TestBalance-AllItems-AusterePrimalDiamond"
 value: {
  dps: 78026.30846
  tps: 79120.4591
  hps: 5418.91676
 }
}
dps_results: {
 key: "TestBalance-AllItems-BurningPrimalDiamond"
 value: {
  dps: 79268.44779
  tps: 80345.45278
  hps: 5361.88257
 }
}
dps_results: {
 key: "TestBalance-AllItems-CapacitivePrimalDiamond"
 value: {
  dps: 78484.94786
  tps: 79585.7535
  hps: 5357.48653
 }
}
dps_results: {
 key: "TestBalance-AllItems-CourageousPrimalDiamond"
 value: {
  dps: 79048.82693
  tps: 80131.14307
  hps: 5358.01103
 }
}
dps_results: {
 key: "TestBalance-AllItems-DelicateVialoftheSanguinaire-96895"
 value: {
  dps: 76150.56288
  tps: 77281.00611
  hps: 5350.72907
 }
}
dps_results: {
 key: "TestBalance-AllItems-DestructivePrimalDiamond"
 value: {
  dps: 78883.36046
  tps: 79981.0661
  hps: 5357.48653
 }
}
dps_results: {
 key: "TestBalance-AllItems-EffulgentPrimalDiamond"
 value: {
  dps: 78026.30846
  tps: 79120.4591
  hps: 5418.91676
 }
}
dps_results: {
 key: "TestBalance-AllItems-EmberPrimalDiamond"
 value: {
  dps: 78770.46536
  tps: 79856.69627
  hps: 5356.24028
 }
}
dps_results: {
 key: "TestBalance-AllItems-EnchantWeapon-BloodyDancingSteel-5125"
 value: {
  dps: 76715.63507
  tps: 77890.53434
  hps: 5335.51647
 }
}
dps_results: {
 key: "TestBalance-AllItems-EnchantWeapon-Colossus-4445"
 value: {
  dps: 76715.63507
  tps: 77890.53434
  hps: 5335.51647
 }
}
dps_results: {
 key: "TestBalance-AllItems-EnchantWeapon-DancingSteel-4444"
 value: {
  dps: 76715.63507
  tps: 77890.53434
  hps: 5335.51647
 }
}
dps_results: {
 key: "TestBalance-AllItems-EnchantWeapon-River'sSong-4446"
 value: {
  dps: 76715.63507
  tps: 77890.53434
  hps: 5335.51647
 }
}
dps_results: {
 key: "TestBalance-AllItems-EnchantWeapon-SpiritofConquest-5124"
 value: {
  dps: 76715.63507
  tps: 77890.53434
  hps: 5335.51647
 }
}
dps_results: {
 key: "TestBalance-AllItems-EnchantWeapon-Windsong-4441"
 value: {
  dps: 79196.44861
  tps: 80361.24429
  hps: 5336.47402
 }
}
dps_results: {
 key: "TestBalance-AllItems-EnigmaticPrimalDiamond"
 value: {
  dps: 78883.36046
  tps: 79981.0661
  hps: 5357.48653
 }
}
dps_results: {
 key: "TestBalance-AllItems-EternalPrimalDiamond"
 value: {
  dps: 78026.30846
  tps: 79120.4591
  hps: 5352.69878
 }
}
dps_results: {
 key: "TestBalance-AllItems-EvilEyeofGalakras-105491"
 value: {
  dps: 76150.56288
  tps: 77281.00611
  hps: 5350.72907
 }
}
dps_results: {
 key: "TestBalance-AllItems-FabledFeatherofJi-Kun-96842"
 value: {
  dps: 76150.56288
  tps: 77281.00611
  hps: 5350.72907
 }
}
dps_results: {
 key: "TestBalance-AllItems-Fen-Yu,FuryofXuen-102248"
 value: {
  dps: 80362.38741
  tps: 81473.48378
  hps: 5750.61488
 }
}
dps_results: {
 key: "TestBalance-AllItems-FleetPrimalDiamond"
 value: {
  dps: 78598.60092
  tps: 79692.75156
  hps: 5352.69878
 }
}
dps_results: {
 key: "TestBalance-AllItems-ForlornPrimalDiamond"
 value: {
  dps: 78770.46536
  tps: 79856.69627
  hps: 5356.24028
 }
}
dps_results: {
 key: "TestBalance-AllItems-GazeoftheTwins-96915"
 value: {
  dps: 79153.83863
  tps: 80247.77471
  hps: 5350.72907
 }
}
dps_results: {
 key: "TestBalance-AllItems-Gong-Lu,StrengthofXuen-102249"
 value: {
  dps: 80362.38741
  tps: 81473.48378
  hps: 5750.61488
 }
}
dps_results: {
 key: "TestBalance-AllItems-Horridon'sLastGasp-96757"
 value: {
  dps: 82898.80984
  tps: 83962.40729
  hps: 5382.61446
 }
}
dps_results: {
 key: "TestBalance-AllItems-ImpassivePrimalDiamond"
 value: {
  dps: 78883.36046
  tps: 79981.0661
  hps: 5357.48653
 }
}
dps_results: {
 key: "TestBalance-AllItems-IndomitablePrimalDiamond"
 value: {
  dps: 78026.30846
  tps: 79120.4591
  hps: 5418.91676
 }
}
dps_results: {
 key: "TestBalance-AllItems-InscribedBagofHydra-Spawn-96828"
 value: {
  dps: 76150.56288
  tps: 77278.13613
  hps: 5350.72907
 }
}
dps_results: {
 key: "TestBalance-AllItems-Ji-Kun'sRisingWinds-96843"
 value: {
  dps: 76150.56288
  tps: 77281.00611
  hps: 5350.72907
 }
}
dps_results: {
 key: "TestBalance-AllItems-PhaseFingers-4697"
 value: {
  dps: 78942.06504
  tps: 80015.25811
  hps: 5367.62786
 }
}
dps_results: {
 key: "TestBalance-AllItems-PowerfulPrimalDiamond"
 value: {
  dps: 78026.30846
  tps: 79120.4591
  hps: 5418.91676
 }
}
dps_results: {
 key: "TestBalance-AllItems-PriceofProgress-81266"
 value: {
  dps: 79268.44779
  tps: 80345.45278
  hps: 5361.88257
 }
}
dps_results: {
 key: "TestBalance-AllItems-Primordius'TalismanofRage-96873"
 value: {
  dps: 79347.75401
  tps: 80448.39904
  hps: 5355.51682
 }
}
dps_results: {
 key: "TestBalance-AllItems-Qian-Le,CourageofNiuzao-102245"
 value: {
  dps: 78249.69502
  tps: 79410.54161
  hps: 5747.50448
 }
}
dps_results: {
 key: "TestBalance-AllItems-Qian-Ying,FortitudeofNiuzao-102250"
 value: {
  dps: 76898.22359
  tps: 78076.83337
  hps: 5754.76209
 }
}
dps_results: {
 key: "TestBalance-AllItems-RegaliaoftheEternalBlossom"
 value: {
  dps: 83985.10779
  tps: 85158.47932
  hps: 5910.49203
 }
}
dps_results: {
 key: "TestBalance-AllItems-RegaliaoftheHauntedForest"
 value: {
  dps: 92964.54772
  tps: 93979.31235
  hps: 6455.2321
 }
}
dps_results: {
 key: "TestBalance-AllItems-RegaliaoftheShatteredVale"
 value: {
  dps: 91586.33508
  tps: 92862.75471
  hps: 6577.02573
 }
}
dps_results: {
 key: "TestBalance-AllItems-Renataki'sSoulCharm-96741"
 value: {
  dps: 76150.56288
  tps: 77281.00611
  hps: 5350.72907
 }
}
dps_results: {
 key: "TestBalance-AllItems-ReverberatingPrimalDiamond"
 value: {
  dps: 78521.19257
  tps: 79606.19656
  hps: 5358.30565
 }
}
dps_results: {
 key: "TestBalance-AllItems-RevitalizingPrimalDiamond"
 value: {
  dps: 78521.19257
  tps: 79605.47748
  hps: 5358.30565
 }
}
dps_results: {
 key: "TestBalance-AllItems-SinisterPrimalDiamond"
 value: {
  dps: 78484.94786
  tps: 79585.7535
  hps: 5357.48653
 }
}
dps_results: {
 key: "TestBalance-AllItems-SynapseSprings(MarkII)-4898"
 value: {
  dps: 80062.20017
  tps: 81030.51801
  hps: 5366.67031
 }
}
dps_results: {
 key: "TestBalance-AllItems-TalismanofBloodlust-96864"
 value: {
  dps: 79301.91749
  tps: 80500.03351
  hps: 5348.81398
 }
}
dps_results: {
 key: "TestBalance-AllItems-TheGloamingBlade-88149"
 value: {
  dps: 79268.44779
  tps: 80345.45278
  hps: 5361.88257
 }
}
dps_results: {
 key: "TestBalance-AllItems-TyrannicalPrimalDiamond"
 value: {
  dps: 78026.30846
  tps: 79120.4591
  hps: 5352.69878
 }
}
dps_results: {
 key: "TestBalance-AllItems-UnerringVisionofLeiShen-96930"
 value: {
  dps: 85711.97639
  tps: 86743.00901
  hps: 5383.57201
 }
}
dps_results: {
 key: "TestBalance-AllItems-Wushoolay'sFinalChoice-96785"
 value: {
  dps: 105711.20937
  tps: 106354.46717
  hps: 5385.98873
 }
}
dps_results: {
 key: "TestBalance-AllItems-Xing-Ho,BreathofYu'lon-102246"
 value: {
  dps: 106702.71017
  tps: 107749.58465
  hps: 5785.25228
 }
}
dps_results: {
 key: "TestBalance-AllItems-YaungolFireCarrier-86518"
 value: {
  dps: 79268.44779
  tps: 80345.45278
  hps: 5361.88257
 }
}
dps_results: {
 key: "TestBalance-AllItems-ZenAlchemistStone-75274"
 value: {
  dps: 81536.56774
  tps: 82474.55084
  hps: 5384.65405
 }
}
dps_results: {
 key: "TestBalance-Average-Default"
 value: {
  dps: 80257.45225
  tps: 81341.66222
  hps: 5320.65423
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-preraid-Default-standard-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 88299.15983
  tps: 129255.40837
  hps: 5364.75521
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-preraid-Default-standard-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 79268.44779
  tps: 80345.45278
  hps: 5361.88257
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-preraid-Default-standard-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 117253.07808
  tps: 114222.52803
  hps: 7907.40642
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-preraid-Default-standard-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 56719.61963
  tps: 96557.91309
  hps: 4927.50701
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-preraid-Default-standard-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 50457.3274
  tps: 52478.66707
  hps: 4926.60324
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-preraid-Default-standard-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 58035.14845
  tps: 60175.79681
  hps: 6779.00035
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-t14-Default-standard-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 138520.09724
  tps: 184708.39964
  hps: 6702.79104
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-t14-Default-standard-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 123947.75911
  tps: 124752.94047
  hps: 6703.99835
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-t14-Default-standard-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 176357.3778
  tps: 170639.3096
  hps: 9699.85126
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-t14-Default-standard-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 90032.92232
  tps: 135658.57955
  hps: 6201.78279
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-t14-Default-standard-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 80305.11015
  tps: 82617.23801
  hps: 6206.30609
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-t14-Default-standard-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 91000.36954
  tps: 93248.98385
  hps: 8709.06469
 }
}
dps_results: {
 key: "TestBalance-SwitchInFrontOfTarget-Default"
 value: {
  dps: 79268.44779
  tps: 80345.45278
  hps: 5361.88257
 }
}
