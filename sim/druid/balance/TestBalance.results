character_stats_results: {
 key: "TestBalance-CharacterStats-Default"
 value: {
  final_stats: 194.25
  final_stats: 185.85
  final_stats: 15944.5
  final_stats: 16081.065
  final_stats: 4680
  final_stats: 5553
  final_stats: 950
  final_stats: 4539
  final_stats: 0
  final_stats: 82.66997
  final_stats: 0
  final_stats: 5246
  final_stats: 499.675
  final_stats: 0
  final_stats: 24071.3715
  final_stats: 0
  final_stats: 0
  final_stats: 9733.2
  final_stats: 0
  final_stats: 369626
  final_stats: 300000
  final_stats: 3000
  final_stats: 16.33235
  final_stats: 16.33235
  final_stats: 14.21089
  final_stats: 14.78037
  final_stats: 0
 }
}
dps_results: {
 key: "TestBalance-AllItems-AgilePrimalDiamond"
 value: {
  dps: 92485.17019
  tps: 93689.82833
  hps: 14870.2269
 }
}
dps_results: {
 key: "TestBalance-AllItems-ArmoroftheEternalBlossom"
 value: {
  dps: 69821.89881
  tps: 71128.64656
  hps: 13997.84781
 }
}
dps_results: {
 key: "TestBalance-AllItems-AssuranceofConsequence-105472"
 value: {
  dps: 89280.12611
  tps: 90503.23782
  hps: 14676.43731
 }
}
dps_results: {
 key: "TestBalance-AllItems-AusterePrimalDiamond"
 value: {
  dps: 91871.61032
  tps: 93083.02516
  hps: 14903.50576
 }
}
dps_results: {
 key: "TestBalance-AllItems-BattlegearoftheEternalBlossom"
 value: {
  dps: 70881.7939
  tps: 72226.81665
  hps: 14004.4912
 }
}
dps_results: {
 key: "TestBalance-AllItems-BurningPrimalDiamond"
 value: {
  dps: 92996.95216
  tps: 94179.05362
  hps: 14975.68876
 }
}
dps_results: {
 key: "TestBalance-AllItems-CapacitivePrimalDiamond"
 value: {
  dps: 91474.24548
  tps: 92680.15281
  hps: 14867.44135
 }
}
dps_results: {
 key: "TestBalance-AllItems-CourageousPrimalDiamond"
 value: {
  dps: 92778.15992
  tps: 93965.28628
  hps: 14948.40258
 }
}
dps_results: {
 key: "TestBalance-AllItems-DelicateVialoftheSanguinaire-96895"
 value: {
  dps: 89280.12611
  tps: 90503.23782
  hps: 14676.43731
 }
}
dps_results: {
 key: "TestBalance-AllItems-DestructivePrimalDiamond"
 value: {
  dps: 92070.94327
  tps: 93274.59059
  hps: 14837.11244
 }
}
dps_results: {
 key: "TestBalance-AllItems-EffulgentPrimalDiamond"
 value: {
  dps: 91871.61032
  tps: 93083.02516
  hps: 14903.50576
 }
}
dps_results: {
 key: "TestBalance-AllItems-EmberPrimalDiamond"
 value: {
  dps: 92388.84347
  tps: 93578.10182
  hps: 14915.53593
 }
}
dps_results: {
 key: "TestBalance-AllItems-EnchantWeapon-BloodyDancingSteel-5125"
 value: {
  dps: 90227.95634
  tps: 91480.34769
  hps: 14698.94682
 }
}
dps_results: {
 key: "TestBalance-AllItems-EnchantWeapon-Colossus-4445"
 value: {
  dps: 90227.95634
  tps: 91480.34769
  hps: 14698.94682
 }
}
dps_results: {
 key: "TestBalance-AllItems-EnchantWeapon-DancingSteel-4444"
 value: {
  dps: 90227.95634
  tps: 91480.34769
  hps: 14698.94682
 }
}
dps_results: {
 key: "TestBalance-AllItems-EnchantWeapon-ElementalForce-4443"
 value: {
  dps: 90980.17255
  tps: 92262.6494
  hps: 14740.56647
 }
}
dps_results: {
 key: "TestBalance-AllItems-EnchantWeapon-River'sSong-4446"
 value: {
  dps: 90227.95634
  tps: 91480.34769
  hps: 14698.94682
 }
}
dps_results: {
 key: "TestBalance-AllItems-EnchantWeapon-SpiritofConquest-5124"
 value: {
  dps: 90227.95634
  tps: 91480.34769
  hps: 14698.94682
 }
}
dps_results: {
 key: "TestBalance-AllItems-EnchantWeapon-Windsong-4441"
 value: {
  dps: 91382.91062
  tps: 92709.37065
  hps: 14762.852
 }
}
dps_results: {
 key: "TestBalance-AllItems-EnigmaticPrimalDiamond"
 value: {
  dps: 92070.94327
  tps: 93274.59059
  hps: 14837.11244
 }
}
dps_results: {
 key: "TestBalance-AllItems-EternalPrimalDiamond"
 value: {
  dps: 91871.61032
  tps: 93083.02516
  hps: 14812.61823
 }
}
dps_results: {
 key: "TestBalance-AllItems-EvilEyeofGalakras-105491"
 value: {
  dps: 89280.12611
  tps: 90503.23782
  hps: 14676.43731
 }
}
dps_results: {
 key: "TestBalance-AllItems-FabledFeatherofJi-Kun-96842"
 value: {
  dps: 89279.23237
  tps: 90502.34408
  hps: 14676.43731
 }
}
dps_results: {
 key: "TestBalance-AllItems-Fen-Yu,FuryofXuen-102248"
 value: {
  dps: 94141.18156
  tps: 95421.08409
  hps: 15404.15789
 }
}
dps_results: {
 key: "TestBalance-AllItems-FleetPrimalDiamond"
 value: {
  dps: 92496.33808
  tps: 93707.75292
  hps: 14869.49525
 }
}
dps_results: {
 key: "TestBalance-AllItems-ForlornPrimalDiamond"
 value: {
  dps: 92388.84347
  tps: 93578.10182
  hps: 14915.53593
 }
}
dps_results: {
 key: "TestBalance-AllItems-GazeoftheTwins-96915"
 value: {
  dps: 91853.4087
  tps: 93065.66981
  hps: 14881.96516
 }
}
dps_results: {
 key: "TestBalance-AllItems-Gong-Lu,StrengthofXuen-102249"
 value: {
  dps: 94141.18156
  tps: 95421.08409
  hps: 15404.15789
 }
}
dps_results: {
 key: "TestBalance-AllItems-Horridon'sLastGasp-96757"
 value: {
  dps: 96280.26072
  tps: 97438.1734
  hps: 15241.28473
 }
}
dps_results: {
 key: "TestBalance-AllItems-ImpassivePrimalDiamond"
 value: {
  dps: 92070.94327
  tps: 93274.59059
  hps: 14837.11244
 }
}
dps_results: {
 key: "TestBalance-AllItems-IndomitablePrimalDiamond"
 value: {
  dps: 91871.61032
  tps: 93083.02516
  hps: 14903.50576
 }
}
dps_results: {
 key: "TestBalance-AllItems-InscribedBagofHydra-Spawn-96828"
 value: {
  dps: 89280.12611
  tps: 90503.23782
  hps: 14676.43731
 }
}
dps_results: {
 key: "TestBalance-AllItems-Ji-Kun'sRisingWinds-96843"
 value: {
  dps: 89280.12611
  tps: 90503.23782
  hps: 14676.43731
 }
}
dps_results: {
 key: "TestBalance-AllItems-NitroBoosts-4223"
 value: {
  dps: 92996.95216
  tps: 94179.05362
  hps: 14975.68876
 }
}
dps_results: {
 key: "TestBalance-AllItems-PhaseFingers-4697"
 value: {
  dps: 92772.60128
  tps: 93947.83274
  hps: 14890.73305
 }
}
dps_results: {
 key: "TestBalance-AllItems-PowerfulPrimalDiamond"
 value: {
  dps: 91871.61032
  tps: 93083.02516
  hps: 14903.50576
 }
}
dps_results: {
 key: "TestBalance-AllItems-PriceofProgress-81266"
 value: {
  dps: 92996.95216
  tps: 94179.05362
  hps: 14975.68876
 }
}
dps_results: {
 key: "TestBalance-AllItems-Primordius'TalismanofRage-96873"
 value: {
  dps: 92719.50732
  tps: 93928.95937
  hps: 14820.78153
 }
}
dps_results: {
 key: "TestBalance-AllItems-Qian-Le,CourageofNiuzao-102245"
 value: {
  dps: 91664.22588
  tps: 92871.02588
  hps: 15346.58453
 }
}
dps_results: {
 key: "TestBalance-AllItems-Qian-Ying,FortitudeofNiuzao-102250"
 value: {
  dps: 90240.25575
  tps: 91439.65015
  hps: 15359.40314
 }
}
dps_results: {
 key: "TestBalance-AllItems-RegaliaoftheEternalBlossom"
 value: {
  dps: 99094.37854
  tps: 100390.70386
  hps: 16111.65103
 }
}
dps_results: {
 key: "TestBalance-AllItems-RegaliaoftheHauntedForest"
 value: {
  dps: 108077.22837
  tps: 109271.56068
  hps: 17301.31961
 }
}
dps_results: {
 key: "TestBalance-AllItems-RegaliaoftheShatteredVale"
 value: {
  dps: 105730.95474
  tps: 107111.06164
  hps: 17460.98332
 }
}
dps_results: {
 key: "TestBalance-AllItems-Renataki'sSoulCharm-96741"
 value: {
  dps: 89280.12611
  tps: 90503.23782
  hps: 14676.43731
 }
}
dps_results: {
 key: "TestBalance-AllItems-ReverberatingPrimalDiamond"
 value: {
  dps: 92485.17019
  tps: 93689.82833
  hps: 14870.2269
 }
}
dps_results: {
 key: "TestBalance-AllItems-RevitalizingPrimalDiamond"
 value: {
  dps: 92485.17019
  tps: 93689.82833
  hps: 14870.2269
 }
}
dps_results: {
 key: "TestBalance-AllItems-SinisterPrimalDiamond"
 value: {
  dps: 91474.24548
  tps: 92680.15281
  hps: 14867.44135
 }
}
dps_results: {
 key: "TestBalance-AllItems-SynapseSprings(MarkII)-4898"
 value: {
  dps: 95070.85822
  tps: 96258.48968
  hps: 14948.4783
 }
}
dps_results: {
 key: "TestBalance-AllItems-TalismanofBloodlust-96864"
 value: {
  dps: 93545.73467
  tps: 94867.50723
  hps: 14632.95845
 }
}
dps_results: {
 key: "TestBalance-AllItems-TheGloamingBlade-88149"
 value: {
  dps: 92996.95216
  tps: 94179.05362
  hps: 14975.68876
 }
}
dps_results: {
 key: "TestBalance-AllItems-TyrannicalPrimalDiamond"
 value: {
  dps: 91871.61032
  tps: 93083.02516
  hps: 14812.61823
 }
}
dps_results: {
 key: "TestBalance-AllItems-UnerringVisionofLeiShen-96930"
 value: {
  dps: 100455.11
  tps: 101619.18444
  hps: 15554.45762
 }
}
dps_results: {
 key: "TestBalance-AllItems-Wushoolay'sFinalChoice-96785"
 value: {
  dps: 99938.96084
  tps: 100825.93571
  hps: 15455.45023
 }
}
dps_results: {
 key: "TestBalance-AllItems-Xing-Ho,BreathofYu'lon-102246"
 value: {
  dps: 121292.9282
  tps: 122490.1089
  hps: 16003.68713
 }
}
dps_results: {
 key: "TestBalance-AllItems-YaungolFireCarrier-86518"
 value: {
  dps: 92996.95216
  tps: 94179.05362
  hps: 14975.68876
 }
}
dps_results: {
 key: "TestBalance-AllItems-ZenAlchemistStone-75274"
 value: {
  dps: 95520.80306
  tps: 96574.88797
  hps: 15269.77845
 }
}
dps_results: {
 key: "TestBalance-Average-Default"
 value: {
  dps: 93664.37497
  tps: 94807.36145
  hps: 14929.96867
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-preraid-DefaultTalents-Default-standard-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 125974.31337
  tps: 151877.87457
  hps: 8313.60099
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-preraid-DefaultTalents-Default-standard-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 90986.24312
  tps: 92162.31764
  hps: 14828.12475
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-preraid-DefaultTalents-Default-standard-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 133579.1731
  tps: 131104.84571
  hps: 18808.95301
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-preraid-DefaultTalents-Default-standard-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 70446.03808
  tps: 90684.73808
  hps: 6870.00861
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-preraid-DefaultTalents-Default-standard-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 56892.92486
  tps: 58871.85986
  hps: 12255.19534
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-preraid-DefaultTalents-Default-standard-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 67491.06548
  tps: 69191.79048
  hps: 14437.21316
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-preraid-FoN + HotW-Default-standard-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 16872.61284
  tps: 21320.30323
  hps: 17814.3433
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-preraid-FoN + HotW-Default-standard-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 90544.52762
  tps: 84528.73946
  hps: 6504.3633
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-preraid-FoN + HotW-Default-standard-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 127854.18074
  tps: 112469.19994
  hps: 7990.51818
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-preraid-FoN + HotW-Default-standard-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 9439.60976
  tps: 14439.60976
  hps: 14824.04418
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-preraid-FoN + HotW-Default-standard-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 57767.15782
  tps: 55004.29008
  hps: 5868.96148
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-preraid-FoN + HotW-Default-standard-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 62632.38898
  tps: 58275.65249
  hps: 7143.46577
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-preraid-Incarnation + NV-Default-standard-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 39037.60976
  tps: 40625.86886
  hps: 12351.87474
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-preraid-Incarnation + NV-Default-standard-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 89698.42482
  tps: 90678.15877
  hps: 7110.68721
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-preraid-Incarnation + NV-Default-standard-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 135251.10488
  tps: 132212.8496
  hps: 9451.29627
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-preraid-Incarnation + NV-Default-standard-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 35848.30086
  tps: 42062.30086
  hps: 9155.47455
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-preraid-Incarnation + NV-Default-standard-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 56888.67132
  tps: 58824.49132
  hps: 6334.95951
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-preraid-Incarnation + NV-Default-standard-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 67106.58415
  tps: 69206.63415
  hps: 8394.17955
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-t14-DefaultTalents-Default-standard-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 216623.82708
  tps: 256040.3107
  hps: 10199.60442
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-t14-DefaultTalents-Default-standard-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 143221.35838
  tps: 144327.81853
  hps: 20001.37823
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-t14-DefaultTalents-Default-standard-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 206033.5063
  tps: 201927.55706
  hps: 24743.01901
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-t14-DefaultTalents-Default-standard-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 139235.20388
  tps: 174380.50388
  hps: 9005.98756
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-t14-DefaultTalents-Default-standard-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 92123.02571
  tps: 94452.94071
  hps: 16539.14447
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-t14-DefaultTalents-Default-standard-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 108603.68117
  tps: 110952.43117
  hps: 19262.16675
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-t14-FoN + HotW-Default-standard-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 27721.44546
  tps: 31992.98534
  hps: 22846.60083
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-t14-FoN + HotW-Default-standard-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 139486.38569
  tps: 130441.65635
  hps: 8074.45457
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-t14-FoN + HotW-Default-standard-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 192414.4194
  tps: 169974.03076
  hps: 9759.98499
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-t14-FoN + HotW-Default-standard-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 15536.9866
  tps: 20536.9866
  hps: 19072.73664
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-t14-FoN + HotW-Default-standard-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 91778.16527
  tps: 86887.76924
  hps: 7370.24179
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-t14-FoN + HotW-Default-standard-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 104100.43649
  tps: 96201.52804
  hps: 8906.59678
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-t14-Incarnation + NV-Default-standard-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 85260.26326
  tps: 98678.22591
  hps: 17736.95187
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-t14-Incarnation + NV-Default-standard-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 139264.59474
  tps: 139971.80185
  hps: 8910.64127
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-t14-Incarnation + NV-Default-standard-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 205888.16071
  tps: 200152.84629
  hps: 11324.38969
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-t14-Incarnation + NV-Default-standard-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 102591.93064
  tps: 136334.73064
  hps: 17355.07166
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-t14-Incarnation + NV-Default-standard-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 90627.87156
  tps: 92872.45156
  hps: 7981.58945
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-t14-Incarnation + NV-Default-standard-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 108686.34684
  tps: 110851.92184
  hps: 10497.11403
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-preraid-DefaultTalents-Default-standard-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 125984.26819
  tps: 151868.02666
  hps: 8445.41461
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-preraid-DefaultTalents-Default-standard-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 90970.55334
  tps: 92146.7848
  hps: 14960.57391
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-preraid-DefaultTalents-Default-standard-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 133556.95546
  tps: 131083.41276
  hps: 18955.88598
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-preraid-DefaultTalents-Default-standard-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 70428.44569
  tps: 90667.14569
  hps: 6990.63055
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-preraid-DefaultTalents-Default-standard-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 56882.24578
  tps: 58861.18078
  hps: 12385.39257
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-preraid-DefaultTalents-Default-standard-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 67478.46979
  tps: 69179.19479
  hps: 14581.77542
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-preraid-FoN + HotW-Default-standard-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 16869.50032
  tps: 21317.27837
  hps: 17911.88693
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-preraid-FoN + HotW-Default-standard-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 90528.70905
  tps: 84514.08423
  hps: 6622.1332
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-preraid-FoN + HotW-Default-standard-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 127832.86045
  tps: 112450.36459
  hps: 8108.65481
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-preraid-FoN + HotW-Default-standard-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 9437.75486
  tps: 14437.75486
  hps: 14921.85969
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-preraid-FoN + HotW-Default-standard-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 57756.30962
  tps: 54994.14252
  hps: 5982.59462
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-preraid-FoN + HotW-Default-standard-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 62620.73913
  tps: 58264.94826
  hps: 7260.36932
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-preraid-Incarnation + NV-Default-standard-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 39030.9447
  tps: 40619.41958
  hps: 12457.1168
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-preraid-Incarnation + NV-Default-standard-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 89682.94273
  tps: 90662.84331
  hps: 7239.4646
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-preraid-Incarnation + NV-Default-standard-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 135228.6236
  tps: 132191.2015
  hps: 9593.28599
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-preraid-Incarnation + NV-Default-standard-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 35837.03506
  tps: 42051.03506
  hps: 9263.22709
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-preraid-Incarnation + NV-Default-standard-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 56877.99054
  tps: 58813.81054
  hps: 6458.74548
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-preraid-Incarnation + NV-Default-standard-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 67094.04755
  tps: 69194.09755
  hps: 8534.67708
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-t14-DefaultTalents-Default-standard-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 216255.6328
  tps: 255652.59672
  hps: 10330.1563
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-t14-DefaultTalents-Default-standard-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 143201.29186
  tps: 144307.92916
  hps: 20132.46782
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-t14-DefaultTalents-Default-standard-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 206005.51244
  tps: 201900.44894
  hps: 24885.18945
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-t14-DefaultTalents-Default-standard-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 139214.17765
  tps: 174359.47765
  hps: 9127.46876
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-t14-DefaultTalents-Default-standard-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 92109.21397
  tps: 94439.12897
  hps: 16667.81424
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-t14-DefaultTalents-Default-standard-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 108587.52216
  tps: 110936.27216
  hps: 19403.32209
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-t14-FoN + HotW-Default-standard-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 27714.41278
  tps: 31986.05
  hps: 22943.95545
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-t14-FoN + HotW-Default-standard-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 139196.20755
  tps: 130097.32256
  hps: 8190.80679
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-t14-FoN + HotW-Default-standard-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 192122.98895
  tps: 169683.78443
  hps: 9875.86532
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-t14-FoN + HotW-Default-standard-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 15534.51834
  tps: 20534.51834
  hps: 19170.39119
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-t14-FoN + HotW-Default-standard-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 91764.29913
  tps: 86874.79256
  hps: 7483.87163
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-t14-FoN + HotW-Default-standard-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 104084.85204
  tps: 96187.19582
  hps: 9020.79221
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-t14-Incarnation + NV-Default-standard-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 85248.04001
  tps: 98666.20081
  hps: 17845.50247
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-t14-Incarnation + NV-Default-standard-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 139245.03449
  tps: 139952.45709
  hps: 9038.60627
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-t14-Incarnation + NV-Default-standard-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 205860.29045
  tps: 200126.05344
  hps: 11463.56509
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-t14-Incarnation + NV-Default-standard-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 102576.10221
  tps: 136318.90221
  hps: 17470.79933
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-t14-Incarnation + NV-Default-standard-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 90614.28275
  tps: 92858.86275
  hps: 8105.61391
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-t14-Incarnation + NV-Default-standard-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 108670.16833
  tps: 110835.74333
  hps: 10634.41983
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-preraid-DefaultTalents-Default-standard-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 130426.52017
  tps: 156670.87863
  hps: 8284.52032
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-preraid-DefaultTalents-Default-standard-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 92996.95216
  tps: 94179.05362
  hps: 14975.68876
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-preraid-DefaultTalents-Default-standard-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 141540.94479
  tps: 139174.70208
  hps: 18587.99554
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-preraid-DefaultTalents-Default-standard-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 75241.35425
  tps: 97170.65425
  hps: 7050.63701
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-preraid-DefaultTalents-Default-standard-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 58351.69465
  tps: 60370.39465
  hps: 12320.7411
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-preraid-DefaultTalents-Default-standard-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 71403.71179
  tps: 73236.31179
  hps: 14710.58756
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-preraid-FoN + HotW-Default-standard-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 16869.50032
  tps: 21317.27837
  hps: 17812.66903
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-preraid-FoN + HotW-Default-standard-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 91950.67123
  tps: 85838.04714
  hps: 6490.3175
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-preraid-FoN + HotW-Default-standard-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 133057.71606
  tps: 117402.60796
  hps: 7980.2619
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-preraid-FoN + HotW-Default-standard-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 9437.75486
  tps: 14437.75486
  hps: 14822.66182
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-preraid-FoN + HotW-Default-standard-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 58631.5552
  tps: 55944.65282
  hps: 5840.31692
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-preraid-FoN + HotW-Default-standard-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 65847.77944
  tps: 61628.32181
  hps: 7135.74186
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-preraid-Incarnation + NV-Default-standard-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 39030.9447
  tps: 40619.41958
  hps: 12350.96991
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-preraid-Incarnation + NV-Default-standard-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 91424.69327
  tps: 92444.06385
  hps: 7072.05835
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-preraid-Incarnation + NV-Default-standard-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 140142.5244
  tps: 137184.47729
  hps: 9395.21703
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-preraid-Incarnation + NV-Default-standard-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 35837.03506
  tps: 42051.03506
  hps: 9154.93307
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-preraid-Incarnation + NV-Default-standard-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 57760.54682
  tps: 59728.93682
  hps: 6365.44197
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-preraid-Incarnation + NV-Default-standard-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 69683.25428
  tps: 71694.07928
  hps: 8449.31957
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-t14-DefaultTalents-Default-standard-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 218410.56771
  tps: 257213.83163
  hps: 10283.7463
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-t14-DefaultTalents-Default-standard-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 146510.88431
  tps: 147635.75161
  hps: 20089.78565
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-t14-DefaultTalents-Default-standard-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 216543.08579
  tps: 212526.54729
  hps: 24604.44628
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-t14-DefaultTalents-Default-standard-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 144001.23816
  tps: 179864.43816
  hps: 9048.68152
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-t14-DefaultTalents-Default-standard-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 93989.91347
  tps: 96344.98847
  hps: 16552.13369
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-t14-DefaultTalents-Default-standard-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 113294.34469
  tps: 115768.01969
  hps: 19387.16321
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-t14-FoN + HotW-Default-standard-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 27714.41278
  tps: 31986.05
  hps: 22844.73755
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-t14-FoN + HotW-Default-standard-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 142863.9174
  tps: 133645.94188
  hps: 8052.16242
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-t14-FoN + HotW-Default-standard-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 203024.24094
  tps: 180038.54241
  hps: 9784.43527
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-t14-FoN + HotW-Default-standard-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 15534.51834
  tps: 20534.51834
  hps: 19071.19333
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-t14-FoN + HotW-Default-standard-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 93575.3513
  tps: 88806.72601
  hps: 7355.49662
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-t14-FoN + HotW-Default-standard-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 108254.31587
  tps: 100600.54266
  hps: 8897.18346
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-t14-Incarnation + NV-Default-standard-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 85248.04001
  tps: 98666.20081
  hps: 17735.75097
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-t14-Incarnation + NV-Default-standard-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 143243.99023
  tps: 143971.82783
  hps: 8823.27493
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-t14-Incarnation + NV-Default-standard-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 214847.33243
  tps: 209145.94542
  hps: 11552.72473
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-t14-Incarnation + NV-Default-standard-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 102687.65438
  tps: 136478.75438
  hps: 17355.55249
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-t14-Incarnation + NV-Default-standard-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 92792.91293
  tps: 95059.45793
  hps: 7988.42749
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-t14-Incarnation + NV-Default-standard-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 113501.42545
  tps: 115744.55045
  hps: 10447.83029
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-preraid-DefaultTalents-Default-standard-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 128364.84888
  tps: 155420.53699
  hps: 8331.41892
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-preraid-DefaultTalents-Default-standard-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 91948.1764
  tps: 93107.24286
  hps: 15019.34745
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-preraid-DefaultTalents-Default-standard-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 134589.04735
  tps: 132090.90464
  hps: 18995.81759
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-preraid-DefaultTalents-Default-standard-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 74005.35694
  tps: 96470.95694
  hps: 7153.69492
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-preraid-DefaultTalents-Default-standard-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 57424.42166
  tps: 59400.87166
  hps: 12305.1556
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-preraid-DefaultTalents-Default-standard-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 68203.9779
  tps: 69904.7029
  hps: 14424.02206
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-preraid-FoN + HotW-Default-standard-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 17037.11325
  tps: 21477.44664
  hps: 17977.59108
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-preraid-FoN + HotW-Default-standard-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 91235.56896
  tps: 85147.10174
  hps: 6482.32113
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-preraid-FoN + HotW-Default-standard-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 130097.62801
  tps: 114505.88807
  hps: 7990.25736
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-preraid-FoN + HotW-Default-standard-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 9534.06207
  tps: 14534.06207
  hps: 14878.05524
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-preraid-FoN + HotW-Default-standard-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 58322.59777
  tps: 55619.3095
  hps: 5873.43887
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-preraid-FoN + HotW-Default-standard-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 63352.56777
  tps: 58952.28487
  hps: 7150.79729
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-preraid-Incarnation + NV-Default-standard-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 33368.70271
  tps: 32949.37079
  hps: 11911.56237
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-preraid-Incarnation + NV-Default-standard-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 90189.18002
  tps: 91198.9656
  hps: 7081.95232
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-preraid-Incarnation + NV-Default-standard-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 135521.91922
  tps: 132488.77211
  hps: 9436.08084
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-preraid-Incarnation + NV-Default-standard-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 35771.93977
  tps: 41735.93977
  hps: 9027.11439
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-preraid-Incarnation + NV-Default-standard-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 57310.05346
  tps: 59265.75346
  hps: 6336.16822
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-preraid-Incarnation + NV-Default-standard-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 67627.27765
  tps: 69726.82765
  hps: 8393.92866
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-t14-DefaultTalents-Default-standard-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 218301.59096
  tps: 257677.36701
  hps: 10204.11197
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-t14-DefaultTalents-Default-standard-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 144501.7442
  tps: 145623.71457
  hps: 20182.13514
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-t14-DefaultTalents-Default-standard-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 210287.96764
  tps: 206203.74448
  hps: 24703.62167
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-t14-DefaultTalents-Default-standard-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 142190.92348
  tps: 178482.22348
  hps: 9036.57035
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-t14-DefaultTalents-Default-standard-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 92812.81395
  tps: 95141.97395
  hps: 16613.8699
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-t14-DefaultTalents-Default-standard-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 109142.25657
  tps: 111479.60657
  hps: 19321.0455
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-t14-FoN + HotW-Default-standard-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 27951.89378
  tps: 32223.53101
  hps: 23044.44989
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-t14-FoN + HotW-Default-standard-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 141820.58797
  tps: 132795.48056
  hps: 8076.87351
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-t14-FoN + HotW-Default-standard-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 195903.98638
  tps: 173392.68344
  hps: 9759.72418
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-t14-FoN + HotW-Default-standard-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 15673.22371
  tps: 20673.22371
  hps: 19156.35197
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-t14-FoN + HotW-Default-standard-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 92940.98768
  tps: 88098.22777
  hps: 7362.84662
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-t14-FoN + HotW-Default-standard-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 106195.1203
  tps: 98222.67177
  hps: 8897.18346
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-t14-Incarnation + NV-Default-standard-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 81322.25976
  tps: 93110.66214
  hps: 17306.7359
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-t14-Incarnation + NV-Default-standard-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 141001.14182
  tps: 141703.06722
  hps: 8792.61311
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-t14-Incarnation + NV-Default-standard-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 209994.57351
  tps: 204221.12553
  hps: 11330.84613
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-t14-Incarnation + NV-Default-standard-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 108662.75172
  tps: 145757.15172
  hps: 17781.68986
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-t14-Incarnation + NV-Default-standard-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 91859.79841
  tps: 94109.80341
  hps: 8029.42751
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-t14-Incarnation + NV-Default-standard-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 110352.26087
  tps: 112519.56087
  hps: 10483.90382
 }
}
dps_results: {
 key: "TestBalance-SwitchInFrontOfTarget-Default"
 value: {
  dps: 92996.95216
  tps: 94179.05362
  hps: 14975.68876
 }
}
