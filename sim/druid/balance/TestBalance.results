character_stats_results: {
 key: "TestBalance-CharacterStats-Default"
 value: {
  final_stats: 194.25
  final_stats: 185.85
  final_stats: 15944.5
  final_stats: 15315.3
  final_stats: 4680
  final_stats: 5553
  final_stats: 950
  final_stats: 4539
  final_stats: 0
  final_stats: 82.66997
  final_stats: 0
  final_stats: 5246
  final_stats: 499.675
  final_stats: 0
  final_stats: 23229.03
  final_stats: 0
  final_stats: 0
  final_stats: 9733.2
  final_stats: 0
  final_stats: 369626
  final_stats: 300000
  final_stats: 3000
  final_stats: 16.33235
  final_stats: 16.33235
  final_stats: 14.21089
  final_stats: 14.47813
  final_stats: 0
 }
}
dps_results: {
 key: "TestBalance-AllItems-AgilePrimalDiamond"
 value: {
  dps: 89713.82595
  tps: 91062.60752
  hps: 13985.61384
 }
}
dps_results: {
 key: "TestBalance-AllItems-AssuranceofConsequence-105472"
 value: {
  dps: 87113.45226
  tps: 88479.39662
  hps: 13801.39341
 }
}
dps_results: {
 key: "TestBalance-AllItems-AusterePrimalDiamond"
 value: {
  dps: 89159.16229
  tps: 90513.53153
  hps: 14020.56865
 }
}
dps_results: {
 key: "TestBalance-AllItems-BurningPrimalDiamond"
 value: {
  dps: 90551.0285
  tps: 91891.84148
  hps: 14043.8021
 }
}
dps_results: {
 key: "TestBalance-AllItems-CapacitivePrimalDiamond"
 value: {
  dps: 89782.04096
  tps: 91136.80324
  hps: 14042.00434
 }
}
dps_results: {
 key: "TestBalance-AllItems-CourageousPrimalDiamond"
 value: {
  dps: 90666.33706
  tps: 92007.14688
  hps: 14057.81052
 }
}
dps_results: {
 key: "TestBalance-AllItems-DelicateVialoftheSanguinaire-96895"
 value: {
  dps: 87113.45226
  tps: 88479.39662
  hps: 13801.39341
 }
}
dps_results: {
 key: "TestBalance-AllItems-DestructivePrimalDiamond"
 value: {
  dps: 90093.2851
  tps: 91444.63238
  hps: 14037.82867
 }
}
dps_results: {
 key: "TestBalance-AllItems-EffulgentPrimalDiamond"
 value: {
  dps: 89159.16229
  tps: 90513.53153
  hps: 14020.56865
 }
}
dps_results: {
 key: "TestBalance-AllItems-EmberPrimalDiamond"
 value: {
  dps: 89989.54105
  tps: 91335.98895
  hps: 13991.28706
 }
}
dps_results: {
 key: "TestBalance-AllItems-EnchantWeapon-BloodyDancingSteel-5125"
 value: {
  dps: 87893.39149
  tps: 89291.95953
  hps: 13772.02306
 }
}
dps_results: {
 key: "TestBalance-AllItems-EnchantWeapon-Colossus-4445"
 value: {
  dps: 87893.39149
  tps: 89291.95953
  hps: 13772.02306
 }
}
dps_results: {
 key: "TestBalance-AllItems-EnchantWeapon-DancingSteel-4444"
 value: {
  dps: 87893.39149
  tps: 89291.95953
  hps: 13772.02306
 }
}
dps_results: {
 key: "TestBalance-AllItems-EnchantWeapon-River'sSong-4446"
 value: {
  dps: 87893.39149
  tps: 89291.95953
  hps: 13772.02306
 }
}
dps_results: {
 key: "TestBalance-AllItems-EnchantWeapon-SpiritofConquest-5124"
 value: {
  dps: 87893.39149
  tps: 89291.95953
  hps: 13772.02306
 }
}
dps_results: {
 key: "TestBalance-AllItems-EnchantWeapon-Windsong-4441"
 value: {
  dps: 91349.36974
  tps: 92758.08621
  hps: 13920.15336
 }
}
dps_results: {
 key: "TestBalance-AllItems-EnigmaticPrimalDiamond"
 value: {
  dps: 90093.2851
  tps: 91444.63238
  hps: 14037.82867
 }
}
dps_results: {
 key: "TestBalance-AllItems-EternalPrimalDiamond"
 value: {
  dps: 89159.16229
  tps: 90513.53153
  hps: 13933.50468
 }
}
dps_results: {
 key: "TestBalance-AllItems-EvilEyeofGalakras-105491"
 value: {
  dps: 87113.45226
  tps: 88479.39662
  hps: 13801.39341
 }
}
dps_results: {
 key: "TestBalance-AllItems-FabledFeatherofJi-Kun-96842"
 value: {
  dps: 87113.45226
  tps: 88479.39662
  hps: 13801.39341
 }
}
dps_results: {
 key: "TestBalance-AllItems-Fen-Yu,FuryofXuen-102248"
 value: {
  dps: 91084.98523
  tps: 92487.10118
  hps: 14478.7036
 }
}
dps_results: {
 key: "TestBalance-AllItems-FleetPrimalDiamond"
 value: {
  dps: 89762.13187
  tps: 91116.50111
  hps: 13986.70192
 }
}
dps_results: {
 key: "TestBalance-AllItems-ForlornPrimalDiamond"
 value: {
  dps: 89989.54105
  tps: 91335.98895
  hps: 13991.28706
 }
}
dps_results: {
 key: "TestBalance-AllItems-GazeoftheTwins-96915"
 value: {
  dps: 89599.19256
  tps: 90972.15805
  hps: 13992.51738
 }
}
dps_results: {
 key: "TestBalance-AllItems-Gong-Lu,StrengthofXuen-102249"
 value: {
  dps: 91084.98523
  tps: 92487.10118
  hps: 14478.7036
 }
}
dps_results: {
 key: "TestBalance-AllItems-Horridon'sLastGasp-96757"
 value: {
  dps: 94763.69991
  tps: 96116.48218
  hps: 14288.39533
 }
}
dps_results: {
 key: "TestBalance-AllItems-ImpassivePrimalDiamond"
 value: {
  dps: 90093.2851
  tps: 91444.63238
  hps: 14037.82867
 }
}
dps_results: {
 key: "TestBalance-AllItems-IndomitablePrimalDiamond"
 value: {
  dps: 89159.16229
  tps: 90513.53153
  hps: 14020.56865
 }
}
dps_results: {
 key: "TestBalance-AllItems-InscribedBagofHydra-Spawn-96828"
 value: {
  dps: 87113.45226
  tps: 88476.60934
  hps: 13801.39341
 }
}
dps_results: {
 key: "TestBalance-AllItems-Ji-Kun'sRisingWinds-96843"
 value: {
  dps: 87113.45226
  tps: 88479.39662
  hps: 13801.39341
 }
}
dps_results: {
 key: "TestBalance-AllItems-PhaseFingers-4697"
 value: {
  dps: 90472.82797
  tps: 91885.02619
  hps: 13942.22105
 }
}
dps_results: {
 key: "TestBalance-AllItems-PowerfulPrimalDiamond"
 value: {
  dps: 89159.16229
  tps: 90513.53153
  hps: 14020.56865
 }
}
dps_results: {
 key: "TestBalance-AllItems-PriceofProgress-81266"
 value: {
  dps: 90551.0285
  tps: 91891.84148
  hps: 14043.8021
 }
}
dps_results: {
 key: "TestBalance-AllItems-Primordius'TalismanofRage-96873"
 value: {
  dps: 90466.52564
  tps: 91856.94406
  hps: 14094.87043
 }
}
dps_results: {
 key: "TestBalance-AllItems-Qian-Le,CourageofNiuzao-102245"
 value: {
  dps: 89638.89395
  tps: 91081.90538
  hps: 14533.3981
 }
}
dps_results: {
 key: "TestBalance-AllItems-Qian-Ying,FortitudeofNiuzao-102250"
 value: {
  dps: 87708.44284
  tps: 89136.75008
  hps: 14532.65889
 }
}
dps_results: {
 key: "TestBalance-AllItems-RegaliaoftheEternalBlossom"
 value: {
  dps: 96269.60362
  tps: 97796.49717
  hps: 15503.20616
 }
}
dps_results: {
 key: "TestBalance-AllItems-RegaliaoftheHauntedForest"
 value: {
  dps: 105443.862
  tps: 106861.50792
  hps: 16610.40327
 }
}
dps_results: {
 key: "TestBalance-AllItems-RegaliaoftheShatteredVale"
 value: {
  dps: 103939.40236
  tps: 105494.51204
  hps: 17367.35282
 }
}
dps_results: {
 key: "TestBalance-AllItems-Renataki'sSoulCharm-96741"
 value: {
  dps: 87113.45226
  tps: 88479.39662
  hps: 13801.39341
 }
}
dps_results: {
 key: "TestBalance-AllItems-ReverberatingPrimalDiamond"
 value: {
  dps: 89713.82595
  tps: 91062.60752
  hps: 13985.61384
 }
}
dps_results: {
 key: "TestBalance-AllItems-RevitalizingPrimalDiamond"
 value: {
  dps: 89713.82595
  tps: 91061.90915
  hps: 13985.61384
 }
}
dps_results: {
 key: "TestBalance-AllItems-SinisterPrimalDiamond"
 value: {
  dps: 89782.04096
  tps: 91136.80324
  hps: 14042.00434
 }
}
dps_results: {
 key: "TestBalance-AllItems-SynapseSprings(MarkII)-4898"
 value: {
  dps: 92260.49766
  tps: 93669.13589
  hps: 14091.71004
 }
}
dps_results: {
 key: "TestBalance-AllItems-TalismanofBloodlust-96864"
 value: {
  dps: 90979.25464
  tps: 92467.84
  hps: 14172.68116
 }
}
dps_results: {
 key: "TestBalance-AllItems-TheGloamingBlade-88149"
 value: {
  dps: 90551.0285
  tps: 91891.84148
  hps: 14043.8021
 }
}
dps_results: {
 key: "TestBalance-AllItems-TyrannicalPrimalDiamond"
 value: {
  dps: 89159.16229
  tps: 90513.53153
  hps: 13933.50468
 }
}
dps_results: {
 key: "TestBalance-AllItems-UnerringVisionofLeiShen-96930"
 value: {
  dps: 97659.14159
  tps: 99015.64337
  hps: 14504.77437
 }
}
dps_results: {
 key: "TestBalance-AllItems-Wushoolay'sFinalChoice-96785"
 value: {
  dps: 119680.38866
  tps: 120769.20165
  hps: 15557.63854
 }
}
dps_results: {
 key: "TestBalance-AllItems-Xing-Ho,BreathofYu'lon-102246"
 value: {
  dps: 118809.67637
  tps: 120174.11596
  hps: 15146.28009
 }
}
dps_results: {
 key: "TestBalance-AllItems-YaungolFireCarrier-86518"
 value: {
  dps: 90551.0285
  tps: 91891.84148
  hps: 14043.8021
 }
}
dps_results: {
 key: "TestBalance-AllItems-ZenAlchemistStone-75274"
 value: {
  dps: 93706.27434
  tps: 94910.99396
  hps: 14306.64312
 }
}
dps_results: {
 key: "TestBalance-Average-Default"
 value: {
  dps: 91536.70442
  tps: 92875.26317
  hps: 14156.52991
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-preraid-DefaultTalents-Default-standard-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 120886.64806
  tps: 148820.11605
  hps: 7933.53251
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-preraid-DefaultTalents-Default-standard-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 88753.90935
  tps: 90063.41297
  hps: 14006.53319
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-preraid-DefaultTalents-Default-standard-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 126758.27888
  tps: 124828.99697
  hps: 18132.9308
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-preraid-DefaultTalents-Default-standard-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 69578.44348
  tps: 91133.10729
  hps: 6671.88672
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-preraid-DefaultTalents-Default-standard-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 55810.29681
  tps: 57946.945
  hps: 11886.97728
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-preraid-DefaultTalents-Default-standard-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 66267.71548
  tps: 68025.33143
  hps: 13896.42945
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-preraid-FoN + HotW-Default-standard-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 11980.51342
  tps: 16468.99742
  hps: 17168.78699
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-preraid-FoN + HotW-Default-standard-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 87983.16709
  tps: 82205.18088
  hps: 6225.86048
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-preraid-FoN + HotW-Default-standard-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 122903.93477
  tps: 108113.82816
  hps: 7650.94953
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-preraid-FoN + HotW-Default-standard-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 8823.47383
  tps: 13823.47383
  hps: 14041.38319
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-preraid-FoN + HotW-Default-standard-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 56901.21193
  tps: 53768.53604
  hps: 5668.81093
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-preraid-FoN + HotW-Default-standard-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 61189.16561
  tps: 56329.10214
  hps: 6849.00238
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-preraid-Incarnation + NV-Default-standard-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 31999.22221
  tps: 32810.63087
  hps: 10645.34147
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-preraid-Incarnation + NV-Default-standard-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 87186.17881
  tps: 88312.91883
  hps: 6268.87059
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-preraid-Incarnation + NV-Default-standard-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 128856.73108
  tps: 125952.73117
  hps: 8061.52446
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-preraid-Incarnation + NV-Default-standard-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 29855.42716
  tps: 34479.99097
  hps: 8256.4358
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-preraid-Incarnation + NV-Default-standard-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 54657.60754
  tps: 56713.52073
  hps: 5757.03079
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-preraid-Incarnation + NV-Default-standard-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 64838.53541
  tps: 66961.32637
  hps: 7308.05574
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-t14-DefaultTalents-Default-standard-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 219895.72592
  tps: 261762.01725
  hps: 9905.55493
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-t14-DefaultTalents-Default-standard-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 139746.64312
  tps: 141028.59812
  hps: 19431.89189
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-t14-DefaultTalents-Default-standard-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 202716.02146
  tps: 198791.32148
  hps: 23277.48723
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-t14-DefaultTalents-Default-standard-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 140694.50926
  tps: 178926.91632
  hps: 8633.72961
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-t14-DefaultTalents-Default-standard-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 91134.61939
  tps: 93578.77474
  hps: 16110.20825
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-t14-DefaultTalents-Default-standard-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 105982.56301
  tps: 108407.58978
  hps: 19363.5765
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-t14-FoN + HotW-Default-standard-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 22002.5319
  tps: 26326.40486
  hps: 22054.30822
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-t14-FoN + HotW-Default-standard-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 136204.38641
  tps: 128354.15092
  hps: 7758.05481
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-t14-FoN + HotW-Default-standard-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 184960.83772
  tps: 164187.50196
  hps: 9478.3457
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-t14-FoN + HotW-Default-standard-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 14935.78158
  tps: 19935.78158
  hps: 18085.24113
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-t14-FoN + HotW-Default-standard-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 89889.48611
  tps: 85729.99301
  hps: 7055.48614
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-t14-FoN + HotW-Default-standard-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 102400.09526
  tps: 94644.58189
  hps: 8580.13757
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-t14-Incarnation + NV-Default-standard-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 41580.73262
  tps: 40570.87719
  hps: 13608.99379
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-t14-Incarnation + NV-Default-standard-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 137072.07204
  tps: 137941.43422
  hps: 7777.00442
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-t14-Incarnation + NV-Default-standard-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 200087.95887
  tps: 194933.59478
  hps: 9913.70652
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-t14-Incarnation + NV-Default-standard-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 105840.60965
  tps: 143035.51671
  hps: 15112.55287
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-t14-Incarnation + NV-Default-standard-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 88106.10374
  tps: 90424.01409
  hps: 7078.47087
 }
}
dps_results: {
 key: "TestBalance-Settings-NightElf-t14-Incarnation + NV-Default-standard-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 107251.51314
  tps: 109401.01491
  hps: 9003.67902
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-preraid-DefaultTalents-Default-standard-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 120562.69397
  tps: 148204.34846
  hps: 8056.41884
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-preraid-DefaultTalents-Default-standard-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 88711.20746
  tps: 90020.84882
  hps: 14116.61915
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-preraid-DefaultTalents-Default-standard-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 126737.53674
  tps: 124808.94354
  hps: 18273.78076
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-preraid-DefaultTalents-Default-standard-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 69960.15071
  tps: 91994.37709
  hps: 6853.96149
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-preraid-DefaultTalents-Default-standard-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 55800.00247
  tps: 57936.64878
  hps: 12011.95902
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-preraid-DefaultTalents-Default-standard-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 66255.56283
  tps: 68013.16943
  hps: 14036.51577
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-preraid-FoN + HotW-Default-standard-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 11978.34504
  tps: 16466.90944
  hps: 17262.75333
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-preraid-FoN + HotW-Default-standard-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 87968.08874
  tps: 82191.2257
  hps: 6338.71684
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-preraid-FoN + HotW-Default-standard-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 122883.99473
  tps: 108096.20805
  hps: 7764.72269
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-preraid-FoN + HotW-Default-standard-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 8821.78169
  tps: 13821.78169
  hps: 14135.6411
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-preraid-FoN + HotW-Default-standard-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 56890.72382
  tps: 53758.80089
  hps: 5778.70873
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-preraid-FoN + HotW-Default-standard-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 61177.99515
  tps: 56318.93105
  hps: 6961.58518
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-preraid-Incarnation + NV-Default-standard-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 31992.33838
  tps: 32803.84617
  hps: 10743.32762
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-preraid-Incarnation + NV-Default-standard-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 87173.9018
  tps: 88300.90039
  hps: 6386.85748
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-preraid-Incarnation + NV-Default-standard-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 128835.6324
  tps: 125932.42537
  hps: 8188.57201
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-preraid-Incarnation + NV-Default-standard-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 29849.84656
  tps: 34474.37293
  hps: 8356.36587
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-preraid-Incarnation + NV-Default-standard-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 54647.52651
  tps: 56703.43783
  hps: 5872.74707
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-preraid-Incarnation + NV-Default-standard-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 64826.63263
  tps: 66949.41422
  hps: 7434.93213
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-t14-DefaultTalents-Default-standard-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 219865.22387
  tps: 261731.66995
  hps: 10026.34826
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-t14-DefaultTalents-Default-standard-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 139625.33069
  tps: 140907.3523
  hps: 19556.046
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-t14-DefaultTalents-Default-standard-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 202581.83266
  tps: 198656.29072
  hps: 23409.16532
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-t14-DefaultTalents-Default-standard-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 140673.57678
  tps: 178905.94067
  hps: 8750.81737
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-t14-DefaultTalents-Default-standard-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 91121.22428
  tps: 93565.37747
  hps: 16233.42831
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-t14-DefaultTalents-Default-standard-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 105967.08139
  tps: 108392.09736
  hps: 19499.92332
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-t14-FoN + HotW-Default-standard-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 21999.24965
  tps: 26323.21188
  hps: 22148.09471
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-t14-FoN + HotW-Default-standard-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 136185.2224
  tps: 128336.21642
  hps: 7869.99144
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-t14-FoN + HotW-Default-standard-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 184936.00456
  tps: 164165.39777
  hps: 9591.93791
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-t14-FoN + HotW-Default-standard-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 14933.44915
  tps: 19933.44915
  hps: 18179.35143
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-t14-FoN + HotW-Default-standard-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 89796.92574
  tps: 85641.43156
  hps: 7164.3634
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-t14-FoN + HotW-Default-standard-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 102365.41077
  tps: 94611.08901
  hps: 8690.8325
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-t14-Incarnation + NV-Default-standard-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 41575.04639
  tps: 40565.36694
  hps: 13706.09976
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-t14-Incarnation + NV-Default-standard-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 137024.8226
  tps: 137894.38644
  hps: 7894.01082
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-t14-Incarnation + NV-Default-standard-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 200061.32195
  tps: 194907.96614
  hps: 10039.7838
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-t14-Incarnation + NV-Default-standard-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 105824.60788
  tps: 143019.47177
  hps: 15219.94554
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-t14-Incarnation + NV-Default-standard-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 88093.13217
  tps: 90411.04037
  hps: 7192.21757
 }
}
dps_results: {
 key: "TestBalance-Settings-Tauren-t14-Incarnation + NV-Default-standard-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 107235.83247
  tps: 109385.32344
  hps: 9126.7211
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-preraid-DefaultTalents-Default-standard-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 123981.24582
  tps: 151559.43281
  hps: 7894.01758
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-preraid-DefaultTalents-Default-standard-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 90551.0285
  tps: 91891.84148
  hps: 14043.8021
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-preraid-DefaultTalents-Default-standard-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 132527.81511
  tps: 130626.03003
  hps: 17751.88735
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-preraid-DefaultTalents-Default-standard-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 72033.93106
  tps: 95159.78469
  hps: 6695.54982
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-preraid-DefaultTalents-Default-standard-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 56930.96312
  tps: 59095.0408
  hps: 11973.64381
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-preraid-DefaultTalents-Default-standard-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 69545.96817
  tps: 71329.53158
  hps: 13891.42985
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-preraid-FoN + HotW-Default-standard-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 11978.34504
  tps: 16466.90944
  hps: 17167.21017
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-preraid-FoN + HotW-Default-standard-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 89990.70005
  tps: 84244.8257
  hps: 6204.63708
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-preraid-FoN + HotW-Default-standard-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 128535.80862
  tps: 113972.08124
  hps: 7641.08508
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-preraid-FoN + HotW-Default-standard-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 8821.78169
  tps: 13821.78169
  hps: 14040.11723
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-preraid-FoN + HotW-Default-standard-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 57489.8924
  tps: 54339.31353
  hps: 5661.52604
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-preraid-FoN + HotW-Default-standard-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 64548.744
  tps: 59474.26138
  hps: 6798.08008
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-preraid-Incarnation + NV-Default-standard-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 31992.33838
  tps: 32803.87868
  hps: 10644.62397
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-preraid-Incarnation + NV-Default-standard-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 88950.5435
  tps: 90110.75872
  hps: 6290.11722
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-preraid-Incarnation + NV-Default-standard-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 135805.57196
  tps: 132986.89806
  hps: 8061.2988
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-preraid-Incarnation + NV-Default-standard-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 29910.80453
  tps: 34562.55816
  hps: 8257.38058
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-preraid-Incarnation + NV-Default-standard-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 55954.33088
  tps: 58023.72356
  hps: 5725.58001
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-preraid-Incarnation + NV-Default-standard-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 67801.65842
  tps: 69981.97182
  hps: 7272.86218
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-t14-DefaultTalents-Default-standard-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 221270.18783
  tps: 263366.56836
  hps: 9940.44754
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-t14-DefaultTalents-Default-standard-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 143973.3081
  tps: 145265.47143
  hps: 19508.15205
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-t14-DefaultTalents-Default-standard-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 209967.93303
  tps: 206132.97471
  hps: 22890.87336
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-t14-DefaultTalents-Default-standard-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 144553.44319
  tps: 183820.1365
  hps: 8661.72845
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-t14-DefaultTalents-Default-standard-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 93030.69928
  tps: 95514.30895
  hps: 16028.40369
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-t14-DefaultTalents-Default-standard-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 111443.69418
  tps: 113953.64251
  hps: 19170.74441
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-t14-FoN + HotW-Default-standard-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 21999.24965
  tps: 26323.21188
  hps: 22052.55155
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-t14-FoN + HotW-Default-standard-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 137787.65472
  tps: 129995.34626
  hps: 7724.69275
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-t14-FoN + HotW-Default-standard-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 193812.44664
  tps: 173215.67162
  hps: 9430.51477
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-t14-FoN + HotW-Default-standard-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 14933.44915
  tps: 19933.44915
  hps: 18083.82756
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-t14-FoN + HotW-Default-standard-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 90857.19886
  tps: 86714.48488
  hps: 7051.90583
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-t14-FoN + HotW-Default-standard-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 105588.63823
  tps: 97759.97164
  hps: 8562.23602
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-t14-Incarnation + NV-Default-standard-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 41575.04639
  tps: 40565.40137
  hps: 13608.18033
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-t14-Incarnation + NV-Default-standard-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 139197.73715
  tps: 140144.90271
  hps: 7784.84588
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-t14-Incarnation + NV-Default-standard-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 205947.4515
  tps: 200771.4543
  hps: 9867.76632
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-t14-Incarnation + NV-Default-standard-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 105781.63103
  tps: 143016.32434
  hps: 15123.81114
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-t14-Incarnation + NV-Default-standard-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 89573.6947
  tps: 91926.42936
  hps: 7098.59012
 }
}
dps_results: {
 key: "TestBalance-Settings-Troll-t14-Incarnation + NV-Default-standard-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 112138.30615
  tps: 114362.10448
  hps: 9003.48365
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-preraid-DefaultTalents-Default-standard-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 121863.77079
  tps: 150005.51392
  hps: 7902.81244
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-preraid-DefaultTalents-Default-standard-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 89554.89573
  tps: 90873.8469
  hps: 14065.9689
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-preraid-DefaultTalents-Default-standard-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 127169.282
  tps: 125167.28787
  hps: 18432.83405
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-preraid-DefaultTalents-Default-standard-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 71149.85951
  tps: 93380.66765
  hps: 6697.59114
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-preraid-DefaultTalents-Default-standard-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 56425.72833
  tps: 58570.32374
  hps: 11984.78735
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-preraid-DefaultTalents-Default-standard-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 67086.92978
  tps: 68848.10681
  hps: 13972.95944
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-preraid-FoN + HotW-Default-standard-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 12073.28036
  tps: 16561.84475
  hps: 17322.61066
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-preraid-FoN + HotW-Default-standard-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 88706.74161
  tps: 82803.18967
  hps: 6229.66275
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-preraid-FoN + HotW-Default-standard-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 124041.59932
  tps: 109065.44731
  hps: 7650.71033
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-preraid-FoN + HotW-Default-standard-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 8929.51997
  tps: 13929.51997
  hps: 14092.20385
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-preraid-FoN + HotW-Default-standard-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 57418.16185
  tps: 54231.37454
  hps: 5673.12429
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-preraid-FoN + HotW-Default-standard-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 62206.16048
  tps: 57305.19381
  hps: 6848.82246
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-preraid-Incarnation + NV-Default-standard-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 32842.13087
  tps: 34265.85825
  hps: 10709.64593
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-preraid-Incarnation + NV-Default-standard-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 87790.75763
  tps: 88905.01663
  hps: 6262.64341
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-preraid-Incarnation + NV-Default-standard-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 130001.32855
  tps: 127003.39856
  hps: 8052.01012
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-preraid-Incarnation + NV-Default-standard-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 30285.31183
  tps: 34831.81997
  hps: 8359.63978
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-preraid-Incarnation + NV-Default-standard-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 55260.9271
  tps: 57312.60751
  hps: 5746.24406
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-preraid-Incarnation + NV-Default-standard-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 65816.94709
  tps: 67942.84913
  hps: 7307.886
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-t14-DefaultTalents-Default-standard-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 220659.06756
  tps: 262767.36514
  hps: 9839.89196
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-t14-DefaultTalents-Default-standard-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 140843.06951
  tps: 142131.89786
  hps: 19590.06751
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-t14-DefaultTalents-Default-standard-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 204846.14758
  tps: 200874.11431
  hps: 23406.56025
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-t14-DefaultTalents-Default-standard-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 142362.7601
  tps: 181956.01227
  hps: 8597.96822
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-t14-DefaultTalents-Default-standard-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 92650.09667
  tps: 95091.19928
  hps: 16074.28586
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-t14-DefaultTalents-Default-standard-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 107683.57716
  tps: 110109.4902
  hps: 19035.25198
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-t14-FoN + HotW-Default-standard-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 22224.18609
  tps: 26548.14832
  hps: 22254.53709
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-t14-FoN + HotW-Default-standard-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 137817.65829
  tps: 130072.6757
  hps: 7769.9049
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-t14-FoN + HotW-Default-standard-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 187311.3026
  tps: 166331.96375
  hps: 9501.90237
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-t14-FoN + HotW-Default-standard-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 15077.86802
  tps: 20077.86802
  hps: 18154.98463
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-t14-FoN + HotW-Default-standard-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 90543.04398
  tps: 86377.396
  hps: 7066.06139
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-t14-FoN + HotW-Default-standard-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 103967.53728
  tps: 96080.21327
  hps: 8553.38879
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-t14-Incarnation + NV-Default-standard-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 42565.34594
  tps: 41544.39976
  hps: 13650.66688
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-t14-Incarnation + NV-Default-standard-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 138127.44184
  tps: 138989.81628
  hps: 7758.21963
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-t14-Incarnation + NV-Default-standard-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 202774.1859
  tps: 197437.1581
  hps: 9879.19496
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-t14-Incarnation + NV-Default-standard-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 112900.47889
  tps: 152657.63106
  hps: 15059.11028
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-t14-Incarnation + NV-Default-standard-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 88983.29512
  tps: 91309.30272
  hps: 7068.0905
 }
}
dps_results: {
 key: "TestBalance-Settings-Worgen-t14-Incarnation + NV-Default-standard-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 108597.1698
  tps: 110748.38284
  hps: 9006.10137
 }
}
dps_results: {
 key: "TestBalance-SwitchInFrontOfTarget-Default"
 value: {
  dps: 90551.0285
  tps: 91891.84148
  hps: 14043.8021
 }
}
