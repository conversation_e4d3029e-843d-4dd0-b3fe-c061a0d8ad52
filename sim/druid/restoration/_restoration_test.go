package restoration

import (
	_ "github.com/wowsims/mop/sim/common" // imported to get caster sets included. (we use spellfire here)
)

func init() {
	RegisterRestorationDruid()
}

// func TestRestoration(t *testing.T) {
// 	core.RunTestSuite(t, t.Name(), core.FullCharacterTestSuiteGenerator([]core.CharacterSuiteConfig{
// 		Class: proto.Class_ClassDruid,
// 		Race:  proto.Race_RaceTauren,

// 		GearSet:     core.GetGearSet("../../../ui/restoration_druid/gear_sets", "p1"),
// 		Talents:     StandardTalents,
// 		Glyphs:      StandardGlyphs,
// 		Consumes:    FullConsumes,
// 		SpecOptions: core.SpecOptionsCombo{Label: "Standard", SpecOptions: PlayerOptionsStandard},
// 		Rotation:    core.RotationCombo{Label: "Default", Rotation: DefaultRotation},

// 		ItemFilter: core.ItemFilter{
// 			WeaponTypes: []proto.WeaponType{
// 				proto.WeaponType_WeaponTypeDagger,
// 				proto.WeaponType_WeaponTypeMace,
// 				proto.WeaponType_WeaponTypeOffHand,
// 				proto.WeaponType_WeaponTypeStaff,
// 				proto.WeaponType_WeaponTypePolearm,
// 			},
// 			ArmorType: proto.ArmorType_ArmorTypeLeather,
// 			RangedWeaponTypes: []proto.RangedWeaponType{},
// 		},
// 	}))
// }

// var StandardTalents = "05320031103--230023312131502331050313051"
// var StandardGlyphs = &proto.Glyphs{
// 	Major1: int32(proto.DruidMajorGlyph_GlyphOfWildGrowth),
// 	Major2: int32(proto.DruidMajorGlyph_GlyphOfSwiftmend),
// 	Major3: int32(proto.DruidMajorGlyph_GlyphOfNourish),
// }

// var FullConsumes = &proto.Consumes{
// 	Flask:           proto.Flask_FlaskOfBlindingLight,
// 	Food:            proto.Food_FoodBlackenedBasilisk,
// 	DefaultPotion:   proto.Potions_SuperManaPotion,
// 	PrepopPotion:    proto.Potions_DestructionPotion,
// 	DefaultConjured: proto.Conjured_ConjuredDarkRune,
// }

// var PlayerOptionsStandard = &proto.Player_RestorationDruid{
// 	RestorationDruid: &proto.RestorationDruid{
// 		Options: &proto.RestorationDruid_Options{
// 			InnervateTarget: &proto.UnitReference{Type: proto.UnitReference_Player, Index: 0}, // self innervate
// 		},
// 	},
// }

// var DefaultRotation = core.APLRotationFromJsonString(`{
// 	"type": "TypeAPL",
// 	"priorityList": [
// 		{"action":{"autocastOtherCooldowns":{}}}
// 	]
// }`)
