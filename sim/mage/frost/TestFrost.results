character_stats_results: {
 key: "TestFrost-CharacterStats-Default"
 value: {
  final_stats: 126
  final_stats: 136.5
  final_stats: 22510.4
  final_stats: 20879.145
  final_stats: 576
  final_stats: 4759
  final_stats: 4228
  final_stats: 6415
  final_stats: 341
  final_stats: 0
  final_stats: 0
  final_stats: 5930
  final_stats: 0
  final_stats: 0
  final_stats: 32241.1595
  final_stats: 0
  final_stats: 0
  final_stats: 14882
  final_stats: 0
  final_stats: 461548.6
  final_stats: 300000
  final_stats: 15000
  final_stats: 13.99706
  final_stats: 15
  final_stats: 15.49667
  final_stats: 21.19746
  final_stats: 0
 }
}
dps_results: {
 key: "TestFrost-AllItems-AgilePrimalDiamond"
 value: {
  dps: 145579.11603
  tps: 100752.59374
 }
}
dps_results: {
 key: "TestFrost-AllItems-AssuranceofConsequence-105472"
 value: {
  dps: 139613.63138
  tps: 96607.68385
 }
}
dps_results: {
 key: "TestFrost-AllItems-AusterePrimalDiamond"
 value: {
  dps: 143364.23434
  tps: 98831.07796
 }
}
dps_results: {
 key: "TestFrost-AllItems-BurningPrimalDiamond"
 value: {
  dps: 146748.99648
  tps: 101567.23065
 }
}
dps_results: {
 key: "TestFrost-AllItems-CapacitivePrimalDiamond"
 value: {
  dps: 144125.44514
  tps: 99368.71902
 }
}
dps_results: {
 key: "TestFrost-AllItems-ChronomancerRegalia"
 value: {
  dps: 141658.5001
  tps: 99511.91799
 }
}
dps_results: {
 key: "TestFrost-AllItems-CourageousPrimalDiamond"
 value: {
  dps: 145152.4152
  tps: 100080.29286
 }
}
dps_results: {
 key: "TestFrost-AllItems-DelicateVialoftheSanguinaire-96895"
 value: {
  dps: 139613.63138
  tps: 96607.68385
 }
}
dps_results: {
 key: "TestFrost-AllItems-DestructivePrimalDiamond"
 value: {
  dps: 144308.65703
  tps: 99493.2244
 }
}
dps_results: {
 key: "TestFrost-AllItems-EffulgentPrimalDiamond"
 value: {
  dps: 143364.23434
  tps: 98831.07796
 }
}
dps_results: {
 key: "TestFrost-AllItems-EmberPrimalDiamond"
 value: {
  dps: 144513.34806
  tps: 99627.75381
 }
}
dps_results: {
 key: "TestFrost-AllItems-EnchantWeapon-BloodyDancingSteel-5125"
 value: {
  dps: 142779.82899
  tps: 98883.45215
 }
}
dps_results: {
 key: "TestFrost-AllItems-EnchantWeapon-Colossus-4445"
 value: {
  dps: 142779.82899
  tps: 98883.45215
 }
}
dps_results: {
 key: "TestFrost-AllItems-EnchantWeapon-DancingSteel-4444"
 value: {
  dps: 142779.82899
  tps: 98883.45215
 }
}
dps_results: {
 key: "TestFrost-AllItems-EnchantWeapon-River'sSong-4446"
 value: {
  dps: 142779.82899
  tps: 98883.45215
 }
}
dps_results: {
 key: "TestFrost-AllItems-EnchantWeapon-SpiritofConquest-5124"
 value: {
  dps: 142779.82899
  tps: 98883.45215
 }
}
dps_results: {
 key: "TestFrost-AllItems-EnchantWeapon-Windsong-4441"
 value: {
  dps: 144237.36413
  tps: 99456.66778
 }
}
dps_results: {
 key: "TestFrost-AllItems-EnigmaticPrimalDiamond"
 value: {
  dps: 144308.65703
  tps: 99493.2244
 }
}
dps_results: {
 key: "TestFrost-AllItems-EternalPrimalDiamond"
 value: {
  dps: 143364.23434
  tps: 98831.07796
 }
}
dps_results: {
 key: "TestFrost-AllItems-EvilEyeofGalakras-105491"
 value: {
  dps: 139613.63138
  tps: 96607.68385
 }
}
dps_results: {
 key: "TestFrost-AllItems-FabledFeatherofJi-Kun-96842"
 value: {
  dps: 139610.86524
  tps: 96607.68385
 }
}
dps_results: {
 key: "TestFrost-AllItems-Fen-Yu,FuryofXuen-102248"
 value: {
  dps: 145406.73034
  tps: 99569.30013
 }
}
dps_results: {
 key: "TestFrost-AllItems-FleetPrimalDiamond"
 value: {
  dps: 144245.602
  tps: 98831.07796
 }
}
dps_results: {
 key: "TestFrost-AllItems-ForlornPrimalDiamond"
 value: {
  dps: 144513.34806
  tps: 99627.75381
 }
}
dps_results: {
 key: "TestFrost-AllItems-GazeoftheTwins-96915"
 value: {
  dps: 142322.21035
  tps: 98496.14172
 }
}
dps_results: {
 key: "TestFrost-AllItems-Gong-Lu,StrengthofXuen-102249"
 value: {
  dps: 145406.73034
  tps: 99569.30013
 }
}
dps_results: {
 key: "TestFrost-AllItems-Horridon'sLastGasp-96757"
 value: {
  dps: 149314.46255
  tps: 103350.96358
 }
}
dps_results: {
 key: "TestFrost-AllItems-ImpassivePrimalDiamond"
 value: {
  dps: 144308.65703
  tps: 99493.2244
 }
}
dps_results: {
 key: "TestFrost-AllItems-IndomitablePrimalDiamond"
 value: {
  dps: 143364.23434
  tps: 98831.07796
 }
}
dps_results: {
 key: "TestFrost-AllItems-InscribedBagofHydra-Spawn-96828"
 value: {
  dps: 139613.63138
  tps: 96607.68385
 }
}
dps_results: {
 key: "TestFrost-AllItems-Ji-Kun'sRisingWinds-96843"
 value: {
  dps: 139613.63138
  tps: 96607.68385
 }
}
dps_results: {
 key: "TestFrost-AllItems-PhaseFingers-4697"
 value: {
  dps: 146565.78184
  tps: 101453.27079
 }
}
dps_results: {
 key: "TestFrost-AllItems-PowerfulPrimalDiamond"
 value: {
  dps: 143364.23434
  tps: 98831.07796
 }
}
dps_results: {
 key: "TestFrost-AllItems-PriceofProgress-81266"
 value: {
  dps: 144264.8639
  tps: 99853.4781
 }
}
dps_results: {
 key: "TestFrost-AllItems-Primordius'TalismanofRage-96873"
 value: {
  dps: 143423.4907
  tps: 99316.98105
 }
}
dps_results: {
 key: "TestFrost-AllItems-Qian-Le,CourageofNiuzao-102245"
 value: {
  dps: 143469.43182
  tps: 98373.08674
 }
}
dps_results: {
 key: "TestFrost-AllItems-Qian-Ying,FortitudeofNiuzao-102250"
 value: {
  dps: 141490.40794
  tps: 96985.22277
 }
}
dps_results: {
 key: "TestFrost-AllItems-RegaliaoftheBurningScroll"
 value: {
  dps: 130307.10269
  tps: 89423.05002
 }
}
dps_results: {
 key: "TestFrost-AllItems-RegaliaoftheChromaticHydra"
 value: {
  dps: 139109.41957
  tps: 93406.08119
 }
}
dps_results: {
 key: "TestFrost-AllItems-Renataki'sSoulCharm-96741"
 value: {
  dps: 139588.64642
  tps: 96591.29473
 }
}
dps_results: {
 key: "TestFrost-AllItems-ReverberatingPrimalDiamond"
 value: {
  dps: 145579.11603
  tps: 100752.59374
 }
}
dps_results: {
 key: "TestFrost-AllItems-RevitalizingPrimalDiamond"
 value: {
  dps: 145579.11603
  tps: 100752.59374
 }
}
dps_results: {
 key: "TestFrost-AllItems-SinisterPrimalDiamond"
 value: {
  dps: 144125.44514
  tps: 99368.71902
 }
}
dps_results: {
 key: "TestFrost-AllItems-SynapseSprings(MarkII)-4898"
 value: {
  dps: 149325.97637
  tps: 103247.16342
 }
}
dps_results: {
 key: "TestFrost-AllItems-TalismanofBloodlust-96864"
 value: {
  dps: 143460.55925
  tps: 99059.74757
 }
}
dps_results: {
 key: "TestFrost-AllItems-TheGloamingBlade-88149"
 value: {
  dps: 146748.99648
  tps: 101567.23065
 }
}
dps_results: {
 key: "TestFrost-AllItems-TyrannicalPrimalDiamond"
 value: {
  dps: 143364.23434
  tps: 98831.07796
 }
}
dps_results: {
 key: "TestFrost-AllItems-UnerringVisionofLeiShen-96930"
 value: {
  dps: 152382.74167
  tps: 105235.26827
 }
}
dps_results: {
 key: "TestFrost-AllItems-Wushoolay'sFinalChoice-96785"
 value: {
  dps: 175270.54243
  tps: 121301.56393
 }
}
dps_results: {
 key: "TestFrost-AllItems-Xing-Ho,BreathofYu'lon-102246"
 value: {
  dps: 184456.92996
  tps: 135632.79551
 }
}
dps_results: {
 key: "TestFrost-AllItems-ZenAlchemistStone-75274"
 value: {
  dps: 149434.54212
  tps: 102120.05516
 }
}
dps_results: {
 key: "TestFrost-Average-Default"
 value: {
  dps: 149528.25936
  tps: 102998.44301
 }
}
dps_results: {
 key: "TestFrost-Settings-Orc-p1_bis-Basic-frost-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 305689.16537
  tps: 161855.78769
 }
}
dps_results: {
 key: "TestFrost-Settings-Orc-p1_bis-Basic-frost-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 149731.88297
  tps: 102964.34533
 }
}
dps_results: {
 key: "TestFrost-Settings-Orc-p1_bis-Basic-frost-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 205486.87136
  tps: 140032.09067
 }
}
dps_results: {
 key: "TestFrost-Settings-Orc-p1_bis-Basic-frost-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 241616.7341
  tps: 127294.87276
 }
}
dps_results: {
 key: "TestFrost-Settings-Orc-p1_bis-Basic-frost-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 111561.86853
  tps: 80492.23766
 }
}
dps_results: {
 key: "TestFrost-Settings-Orc-p1_bis-Basic-frost-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 119342.41153
  tps: 86037.74732
 }
}
dps_results: {
 key: "TestFrost-Settings-Orc-p1_prebis_rich-Basic-frost-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 200184.0534
  tps: 101789.25661
 }
}
dps_results: {
 key: "TestFrost-Settings-Orc-p1_prebis_rich-Basic-frost-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 95256.02799
  tps: 64684.28358
 }
}
dps_results: {
 key: "TestFrost-Settings-Orc-p1_prebis_rich-Basic-frost-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 135704.93308
  tps: 91356.83481
 }
}
dps_results: {
 key: "TestFrost-Settings-Orc-p1_prebis_rich-Basic-frost-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 153455.55083
  tps: 77140.3251
 }
}
dps_results: {
 key: "TestFrost-Settings-Orc-p1_prebis_rich-Basic-frost-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 68306.59306
  tps: 48576.30057
 }
}
dps_results: {
 key: "TestFrost-Settings-Orc-p1_prebis_rich-Basic-frost-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 75629.55711
  tps: 53651.89281
 }
}
dps_results: {
 key: "TestFrost-Settings-Troll-p1_bis-Basic-frost-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 300935.5491
  tps: 162727.07103
 }
}
dps_results: {
 key: "TestFrost-Settings-Troll-p1_bis-Basic-frost-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 149759.74867
  tps: 103557.23069
 }
}
dps_results: {
 key: "TestFrost-Settings-Troll-p1_bis-Basic-frost-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 205781.15549
  tps: 140850.16455
 }
}
dps_results: {
 key: "TestFrost-Settings-Troll-p1_bis-Basic-frost-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 235968.98986
  tps: 127601.16954
 }
}
dps_results: {
 key: "TestFrost-Settings-Troll-p1_bis-Basic-frost-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 111561.07996
  tps: 80897.58938
 }
}
dps_results: {
 key: "TestFrost-Settings-Troll-p1_bis-Basic-frost-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 121636.8448
  tps: 88009.98104
 }
}
dps_results: {
 key: "TestFrost-Settings-Troll-p1_prebis_rich-Basic-frost-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 195443.36236
  tps: 101680.90937
 }
}
dps_results: {
 key: "TestFrost-Settings-Troll-p1_prebis_rich-Basic-frost-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 94765.43599
  tps: 64647.9833
 }
}
dps_results: {
 key: "TestFrost-Settings-Troll-p1_prebis_rich-Basic-frost-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 136468.61453
  tps: 92505.5851
 }
}
dps_results: {
 key: "TestFrost-Settings-Troll-p1_prebis_rich-Basic-frost-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 148300.44017
  tps: 77246.6996
 }
}
dps_results: {
 key: "TestFrost-Settings-Troll-p1_prebis_rich-Basic-frost-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 67701.64629
  tps: 48486.78751
 }
}
dps_results: {
 key: "TestFrost-Settings-Troll-p1_prebis_rich-Basic-frost-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 75927.18902
  tps: 54631.73162
 }
}
dps_results: {
 key: "TestFrost-SwitchInFrontOfTarget-Default"
 value: {
  dps: 149759.74867
  tps: 103557.23069
 }
}
