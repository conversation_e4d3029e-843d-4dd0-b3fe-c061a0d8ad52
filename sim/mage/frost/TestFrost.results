character_stats_results: {
 key: "TestFrost-CharacterStats-Default"
 value: {
  final_stats: 126
  final_stats: 136.5
  final_stats: 22280.5
  final_stats: 20505.3975
  final_stats: 288
  final_stats: 5103
  final_stats: 5457
  final_stats: 6572
  final_stats: 0
  final_stats: 0
  final_stats: 0
  final_stats: 5009
  final_stats: 0
  final_stats: 0
  final_stats: 31830.03725
  final_stats: 0
  final_stats: 0
  final_stats: 14882
  final_stats: 0
  final_stats: 458330
  final_stats: 300000
  final_stats: 15000
  final_stats: 15.00882
  final_stats: 15.00882
  final_stats: 17.545
  final_stats: 23.09828
  final_stats: 0
 }
}
dps_results: {
 key: "TestFrost-AllItems-AgilePrimalDiamond"
 value: {
  dps: 134902.63281
  tps: 93499.28916
 }
}
dps_results: {
 key: "TestFrost-AllItems-AssuranceofConsequence-105472"
 value: {
  dps: 129294.58936
  tps: 89572.47365
 }
}
dps_results: {
 key: "TestFrost-AllItems-AusterePrimalDiamond"
 value: {
  dps: 132872.71641
  tps: 91741.16315
 }
}
dps_results: {
 key: "TestFrost-AllItems-BurningPrimalDiamond"
 value: {
  dps: 136028.08229
  tps: 94283.73915
 }
}
dps_results: {
 key: "TestFrost-AllItems-CapacitivePrimalDiamond"
 value: {
  dps: 133632.52615
  tps: 92312.48644
 }
}
dps_results: {
 key: "TestFrost-AllItems-ChronomancerRegalia"
 value: {
  dps: 133268.73382
  tps: 92232.31091
 }
}
dps_results: {
 key: "TestFrost-AllItems-CourageousPrimalDiamond"
 value: {
  dps: 134515.98461
  tps: 92885.54534
 }
}
dps_results: {
 key: "TestFrost-AllItems-DelicateVialoftheSanguinaire-96895"
 value: {
  dps: 129294.58936
  tps: 89572.47365
 }
}
dps_results: {
 key: "TestFrost-AllItems-DestructivePrimalDiamond"
 value: {
  dps: 133805.33009
  tps: 92436.58674
 }
}
dps_results: {
 key: "TestFrost-AllItems-EffulgentPrimalDiamond"
 value: {
  dps: 132872.71641
  tps: 91741.16315
 }
}
dps_results: {
 key: "TestFrost-AllItems-EmberPrimalDiamond"
 value: {
  dps: 133977.52132
  tps: 92508.10032
 }
}
dps_results: {
 key: "TestFrost-AllItems-EnchantWeapon-BloodyDancingSteel-5125"
 value: {
  dps: 132752.42062
  tps: 92016.11343
 }
}
dps_results: {
 key: "TestFrost-AllItems-EnchantWeapon-Colossus-4445"
 value: {
  dps: 132752.42062
  tps: 92016.11343
 }
}
dps_results: {
 key: "TestFrost-AllItems-EnchantWeapon-DancingSteel-4444"
 value: {
  dps: 132752.42062
  tps: 92016.11343
 }
}
dps_results: {
 key: "TestFrost-AllItems-EnchantWeapon-River'sSong-4446"
 value: {
  dps: 132752.42062
  tps: 92016.11343
 }
}
dps_results: {
 key: "TestFrost-AllItems-EnchantWeapon-SpiritofConquest-5124"
 value: {
  dps: 132752.42062
  tps: 92016.11343
 }
}
dps_results: {
 key: "TestFrost-AllItems-EnchantWeapon-Windsong-4441"
 value: {
  dps: 134322.84664
  tps: 92942.83891
 }
}
dps_results: {
 key: "TestFrost-AllItems-EnigmaticPrimalDiamond"
 value: {
  dps: 133805.33009
  tps: 92436.58674
 }
}
dps_results: {
 key: "TestFrost-AllItems-EternalPrimalDiamond"
 value: {
  dps: 132872.71641
  tps: 91741.16315
 }
}
dps_results: {
 key: "TestFrost-AllItems-EvilEyeofGalakras-105491"
 value: {
  dps: 129294.58936
  tps: 89572.47365
 }
}
dps_results: {
 key: "TestFrost-AllItems-FabledFeatherofJi-Kun-96842"
 value: {
  dps: 129294.58936
  tps: 89572.47365
 }
}
dps_results: {
 key: "TestFrost-AllItems-FleetPrimalDiamond"
 value: {
  dps: 133735.22072
  tps: 91741.16315
 }
}
dps_results: {
 key: "TestFrost-AllItems-ForlornPrimalDiamond"
 value: {
  dps: 133977.52132
  tps: 92508.10032
 }
}
dps_results: {
 key: "TestFrost-AllItems-GazeoftheTwins-96915"
 value: {
  dps: 131246.61875
  tps: 90870.74974
 }
}
dps_results: {
 key: "TestFrost-AllItems-Horridon'sLastGasp-96757"
 value: {
  dps: 138436.11858
  tps: 95986.66835
 }
}
dps_results: {
 key: "TestFrost-AllItems-ImpassivePrimalDiamond"
 value: {
  dps: 133805.33009
  tps: 92436.58674
 }
}
dps_results: {
 key: "TestFrost-AllItems-IndomitablePrimalDiamond"
 value: {
  dps: 132872.71641
  tps: 91741.16315
 }
}
dps_results: {
 key: "TestFrost-AllItems-InscribedBagofHydra-Spawn-96828"
 value: {
  dps: 129294.58936
  tps: 89572.47365
 }
}
dps_results: {
 key: "TestFrost-AllItems-Ji-Kun'sRisingWinds-96843"
 value: {
  dps: 129294.58936
  tps: 89572.47365
 }
}
dps_results: {
 key: "TestFrost-AllItems-PhaseFingers-4697"
 value: {
  dps: 135849.59699
  tps: 94091.74453
 }
}
dps_results: {
 key: "TestFrost-AllItems-PowerfulPrimalDiamond"
 value: {
  dps: 132872.71641
  tps: 91741.16315
 }
}
dps_results: {
 key: "TestFrost-AllItems-PriceofProgress-81266"
 value: {
  dps: 133616.81309
  tps: 92578.90633
 }
}
dps_results: {
 key: "TestFrost-AllItems-Primordius'TalismanofRage-96873"
 value: {
  dps: 132601.8065
  tps: 91892.25306
 }
}
dps_results: {
 key: "TestFrost-AllItems-RegaliaoftheBurningScroll"
 value: {
  dps: 120803.66015
  tps: 82120.32184
 }
}
dps_results: {
 key: "TestFrost-AllItems-RegaliaoftheChromaticHydra"
 value: {
  dps: 129583.62811
  tps: 85717.52553
 }
}
dps_results: {
 key: "TestFrost-AllItems-Renataki'sSoulCharm-96741"
 value: {
  dps: 129294.58936
  tps: 89572.47365
 }
}
dps_results: {
 key: "TestFrost-AllItems-ReverberatingPrimalDiamond"
 value: {
  dps: 134902.63281
  tps: 93499.28916
 }
}
dps_results: {
 key: "TestFrost-AllItems-RevitalizingPrimalDiamond"
 value: {
  dps: 134902.63281
  tps: 93499.28916
 }
}
dps_results: {
 key: "TestFrost-AllItems-SinisterPrimalDiamond"
 value: {
  dps: 133632.52615
  tps: 92312.48644
 }
}
dps_results: {
 key: "TestFrost-AllItems-SynapseSprings(MarkII)-4898"
 value: {
  dps: 138142.01399
  tps: 95592.68447
 }
}
dps_results: {
 key: "TestFrost-AllItems-TalismanofBloodlust-96864"
 value: {
  dps: 132837.46531
  tps: 92153.88176
 }
}
dps_results: {
 key: "TestFrost-AllItems-TheGloamingBlade-88149"
 value: {
  dps: 136028.08229
  tps: 94283.73915
 }
}
dps_results: {
 key: "TestFrost-AllItems-TyrannicalPrimalDiamond"
 value: {
  dps: 132872.71641
  tps: 91741.16315
 }
}
dps_results: {
 key: "TestFrost-AllItems-UnerringVisionofLeiShen-96930"
 value: {
  dps: 141217.2831
  tps: 97621.49106
 }
}
dps_results: {
 key: "TestFrost-AllItems-Wushoolay'sFinalChoice-96785"
 value: {
  dps: 160084.58005
  tps: 111005.18682
 }
}
dps_results: {
 key: "TestFrost-AllItems-ZenAlchemistStone-75274"
 value: {
  dps: 136436.37769
  tps: 93246.76454
 }
}
dps_results: {
 key: "TestFrost-Average-Default"
 value: {
  dps: 138622.7413
  tps: 95824.70873
 }
}
dps_results: {
 key: "TestFrost-Settings-Orc-p1_bis-Basic-frost-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 294016.68957
  tps: 152843.60707
 }
}
dps_results: {
 key: "TestFrost-Settings-Orc-p1_bis-Basic-frost-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 137897.3675
  tps: 95089.43096
 }
}
dps_results: {
 key: "TestFrost-Settings-Orc-p1_bis-Basic-frost-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 176920.88485
  tps: 118792.10718
 }
}
dps_results: {
 key: "TestFrost-Settings-Orc-p1_bis-Basic-frost-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 233151.40885
  tps: 122178.41872
 }
}
dps_results: {
 key: "TestFrost-Settings-Orc-p1_bis-Basic-frost-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 104807.33406
  tps: 76344.00609
 }
}
dps_results: {
 key: "TestFrost-Settings-Orc-p1_bis-Basic-frost-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 105157.29351
  tps: 74798.62636
 }
}
dps_results: {
 key: "TestFrost-Settings-Orc-p1_prebis_rich-Basic-frost-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 194571.36346
  tps: 96328.25827
 }
}
dps_results: {
 key: "TestFrost-Settings-Orc-p1_prebis_rich-Basic-frost-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 89166.54455
  tps: 60152.26197
 }
}
dps_results: {
 key: "TestFrost-Settings-Orc-p1_prebis_rich-Basic-frost-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 119364.26645
  tps: 79193.83959
 }
}
dps_results: {
 key: "TestFrost-Settings-Orc-p1_prebis_rich-Basic-frost-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 148794.6926
  tps: 75013.93899
 }
}
dps_results: {
 key: "TestFrost-Settings-Orc-p1_prebis_rich-Basic-frost-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 65366.14035
  tps: 46407.23006
 }
}
dps_results: {
 key: "TestFrost-Settings-Orc-p1_prebis_rich-Basic-frost-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 67160.75247
  tps: 47045.96671
 }
}
dps_results: {
 key: "TestFrost-Settings-Troll-p1_bis-Basic-frost-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 288162.83608
  tps: 152449.66272
 }
}
dps_results: {
 key: "TestFrost-Settings-Troll-p1_bis-Basic-frost-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 138309.55972
  tps: 95758.08434
 }
}
dps_results: {
 key: "TestFrost-Settings-Troll-p1_bis-Basic-frost-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 179493.20401
  tps: 121127.95686
 }
}
dps_results: {
 key: "TestFrost-Settings-Troll-p1_bis-Basic-frost-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 227164.91565
  tps: 122077.58983
 }
}
dps_results: {
 key: "TestFrost-Settings-Troll-p1_bis-Basic-frost-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 104673.52842
  tps: 76578.61875
 }
}
dps_results: {
 key: "TestFrost-Settings-Troll-p1_bis-Basic-frost-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 108263.37108
  tps: 77629.48593
 }
}
dps_results: {
 key: "TestFrost-Settings-Troll-p1_prebis_rich-Basic-frost-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 188897.96006
  tps: 95803.69645
 }
}
dps_results: {
 key: "TestFrost-Settings-Troll-p1_prebis_rich-Basic-frost-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 88596.72243
  tps: 59984.91548
 }
}
dps_results: {
 key: "TestFrost-Settings-Troll-p1_prebis_rich-Basic-frost-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 119589.18349
  tps: 79661.61908
 }
}
dps_results: {
 key: "TestFrost-Settings-Troll-p1_prebis_rich-Basic-frost-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 144443.36427
  tps: 74915.06655
 }
}
dps_results: {
 key: "TestFrost-Settings-Troll-p1_prebis_rich-Basic-frost-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 65031.46973
  tps: 46644.50459
 }
}
dps_results: {
 key: "TestFrost-Settings-Troll-p1_prebis_rich-Basic-frost-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 68709.74696
  tps: 49092.06003
 }
}
dps_results: {
 key: "TestFrost-SwitchInFrontOfTarget-Default"
 value: {
  dps: 138309.55972
  tps: 95758.08434
 }
}
