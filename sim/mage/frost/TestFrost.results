character_stats_results: {
 key: "TestFrost-CharacterStats-Default"
 value: {
  final_stats: 126
  final_stats: 136.5
  final_stats: 22510.4
  final_stats: 20879.145
  final_stats: 576
  final_stats: 4759
  final_stats: 4228
  final_stats: 6415
  final_stats: 341
  final_stats: 0
  final_stats: 0
  final_stats: 5930
  final_stats: 0
  final_stats: 0
  final_stats: 32241.1595
  final_stats: 0
  final_stats: 0
  final_stats: 14882
  final_stats: 0
  final_stats: 461548.6
  final_stats: 300000
  final_stats: 15000
  final_stats: 13.99706
  final_stats: 15
  final_stats: 15.49667
  final_stats: 21.19746
  final_stats: 0
 }
}
dps_results: {
 key: "TestFrost-AllItems-AgilePrimalDiamond"
 value: {
  dps: 143959.96598
  tps: 100359.52475
 }
}
dps_results: {
 key: "TestFrost-AllItems-AssuranceofConsequence-105472"
 value: {
  dps: 138026.84726
  tps: 96184.35097
 }
}
dps_results: {
 key: "TestFrost-AllItems-AusterePrimalDiamond"
 value: {
  dps: 141775.29301
  tps: 98461.62744
 }
}
dps_results: {
 key: "TestFrost-AllItems-BurningPrimalDiamond"
 value: {
  dps: 145097.46672
  tps: 101150.50448
 }
}
dps_results: {
 key: "TestFrost-AllItems-CapacitivePrimalDiamond"
 value: {
  dps: 142384.08877
  tps: 98899.97573
 }
}
dps_results: {
 key: "TestFrost-AllItems-ChronomancerRegalia"
 value: {
  dps: 139921.15715
  tps: 98440.21995
 }
}
dps_results: {
 key: "TestFrost-AllItems-CourageousPrimalDiamond"
 value: {
  dps: 143473.04786
  tps: 99650.8965
 }
}
dps_results: {
 key: "TestFrost-AllItems-DelicateVialoftheSanguinaire-96895"
 value: {
  dps: 138026.84726
  tps: 96184.35097
 }
}
dps_results: {
 key: "TestFrost-AllItems-DestructivePrimalDiamond"
 value: {
  dps: 142634.03974
  tps: 99081.05885
 }
}
dps_results: {
 key: "TestFrost-AllItems-EffulgentPrimalDiamond"
 value: {
  dps: 141775.29301
  tps: 98461.62744
 }
}
dps_results: {
 key: "TestFrost-AllItems-EmberPrimalDiamond"
 value: {
  dps: 142893.54661
  tps: 99235.94009
 }
}
dps_results: {
 key: "TestFrost-AllItems-EnchantWeapon-BloodyDancingSteel-5125"
 value: {
  dps: 141118.91303
  tps: 98403.17804
 }
}
dps_results: {
 key: "TestFrost-AllItems-EnchantWeapon-Colossus-4445"
 value: {
  dps: 141118.91303
  tps: 98403.17804
 }
}
dps_results: {
 key: "TestFrost-AllItems-EnchantWeapon-DancingSteel-4444"
 value: {
  dps: 141118.91303
  tps: 98403.17804
 }
}
dps_results: {
 key: "TestFrost-AllItems-EnchantWeapon-River'sSong-4446"
 value: {
  dps: 141118.91303
  tps: 98403.17804
 }
}
dps_results: {
 key: "TestFrost-AllItems-EnchantWeapon-SpiritofConquest-5124"
 value: {
  dps: 141118.91303
  tps: 98403.17804
 }
}
dps_results: {
 key: "TestFrost-AllItems-EnchantWeapon-Windsong-4441"
 value: {
  dps: 142623.61914
  tps: 99066.82979
 }
}
dps_results: {
 key: "TestFrost-AllItems-EnigmaticPrimalDiamond"
 value: {
  dps: 142634.03974
  tps: 99081.05885
 }
}
dps_results: {
 key: "TestFrost-AllItems-EternalPrimalDiamond"
 value: {
  dps: 141775.29301
  tps: 98461.62744
 }
}
dps_results: {
 key: "TestFrost-AllItems-EvilEyeofGalakras-105491"
 value: {
  dps: 138026.84726
  tps: 96184.35097
 }
}
dps_results: {
 key: "TestFrost-AllItems-FabledFeatherofJi-Kun-96842"
 value: {
  dps: 138023.38958
  tps: 96184.35097
 }
}
dps_results: {
 key: "TestFrost-AllItems-Fen-Yu,FuryofXuen-102248"
 value: {
  dps: 143921.15382
  tps: 99297.67571
 }
}
dps_results: {
 key: "TestFrost-AllItems-FleetPrimalDiamond"
 value: {
  dps: 142643.08056
  tps: 98461.62744
 }
}
dps_results: {
 key: "TestFrost-AllItems-ForlornPrimalDiamond"
 value: {
  dps: 142893.54661
  tps: 99235.94009
 }
}
dps_results: {
 key: "TestFrost-AllItems-GazeoftheTwins-96915"
 value: {
  dps: 140507.52375
  tps: 97908.48949
 }
}
dps_results: {
 key: "TestFrost-AllItems-Gong-Lu,StrengthofXuen-102249"
 value: {
  dps: 143921.15382
  tps: 99297.67571
 }
}
dps_results: {
 key: "TestFrost-AllItems-Horridon'sLastGasp-96757"
 value: {
  dps: 147535.93506
  tps: 102881.99576
 }
}
dps_results: {
 key: "TestFrost-AllItems-ImpassivePrimalDiamond"
 value: {
  dps: 142634.03974
  tps: 99081.05885
 }
}
dps_results: {
 key: "TestFrost-AllItems-IndomitablePrimalDiamond"
 value: {
  dps: 141775.29301
  tps: 98461.62744
 }
}
dps_results: {
 key: "TestFrost-AllItems-InscribedBagofHydra-Spawn-96828"
 value: {
  dps: 138026.84726
  tps: 96184.35097
 }
}
dps_results: {
 key: "TestFrost-AllItems-Ji-Kun'sRisingWinds-96843"
 value: {
  dps: 138026.84726
  tps: 96184.35097
 }
}
dps_results: {
 key: "TestFrost-AllItems-PhaseFingers-4697"
 value: {
  dps: 144649.07685
  tps: 100760.08284
 }
}
dps_results: {
 key: "TestFrost-AllItems-PowerfulPrimalDiamond"
 value: {
  dps: 141775.29301
  tps: 98461.62744
 }
}
dps_results: {
 key: "TestFrost-AllItems-PriceofProgress-81266"
 value: {
  dps: 142655.22793
  tps: 99429.59502
 }
}
dps_results: {
 key: "TestFrost-AllItems-Primordius'TalismanofRage-96873"
 value: {
  dps: 141892.91885
  tps: 98995.52756
 }
}
dps_results: {
 key: "TestFrost-AllItems-Qian-Le,CourageofNiuzao-102245"
 value: {
  dps: 142373.95528
  tps: 98392.10853
 }
}
dps_results: {
 key: "TestFrost-AllItems-Qian-Ying,FortitudeofNiuzao-102250"
 value: {
  dps: 140465.54864
  tps: 97058.68245
 }
}
dps_results: {
 key: "TestFrost-AllItems-RegaliaoftheBurningScroll"
 value: {
  dps: 128960.86738
  tps: 89263.15725
 }
}
dps_results: {
 key: "TestFrost-AllItems-RegaliaoftheChromaticHydra"
 value: {
  dps: 138387.17064
  tps: 93581.51605
 }
}
dps_results: {
 key: "TestFrost-AllItems-Renataki'sSoulCharm-96741"
 value: {
  dps: 138023.28987
  tps: 96187.67366
 }
}
dps_results: {
 key: "TestFrost-AllItems-ReverberatingPrimalDiamond"
 value: {
  dps: 143959.96598
  tps: 100359.52475
 }
}
dps_results: {
 key: "TestFrost-AllItems-RevitalizingPrimalDiamond"
 value: {
  dps: 143959.96598
  tps: 100359.52475
 }
}
dps_results: {
 key: "TestFrost-AllItems-SinisterPrimalDiamond"
 value: {
  dps: 142384.08877
  tps: 98899.97573
 }
}
dps_results: {
 key: "TestFrost-AllItems-SynapseSprings(MarkII)-4898"
 value: {
  dps: 147337.87986
  tps: 102472.42661
 }
}
dps_results: {
 key: "TestFrost-AllItems-TalismanofBloodlust-96864"
 value: {
  dps: 142665.47112
  tps: 99327.13623
 }
}
dps_results: {
 key: "TestFrost-AllItems-TheGloamingBlade-88149"
 value: {
  dps: 145097.46672
  tps: 101150.50448
 }
}
dps_results: {
 key: "TestFrost-AllItems-TyrannicalPrimalDiamond"
 value: {
  dps: 141775.29301
  tps: 98461.62744
 }
}
dps_results: {
 key: "TestFrost-AllItems-UnerringVisionofLeiShen-96930"
 value: {
  dps: 150558.98798
  tps: 104786.67649
 }
}
dps_results: {
 key: "TestFrost-AllItems-Wushoolay'sFinalChoice-96785"
 value: {
  dps: 173447.84571
  tps: 120917.29523
 }
}
dps_results: {
 key: "TestFrost-AllItems-Xing-Ho,BreathofYu'lon-102246"
 value: {
  dps: 182832.02355
  tps: 135296.43587
 }
}
dps_results: {
 key: "TestFrost-AllItems-ZenAlchemistStone-75274"
 value: {
  dps: 147545.25317
  tps: 101564.42278
 }
}
dps_results: {
 key: "TestFrost-Average-Default"
 value: {
  dps: 148195.12624
  tps: 102949.40787
 }
}
dps_results: {
 key: "TestFrost-Settings-Orc-p1_bis-Basic-frost-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 304390.37964
  tps: 162013.11289
 }
}
dps_results: {
 key: "TestFrost-Settings-Orc-p1_bis-Basic-frost-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 148021.41206
  tps: 102643.71562
 }
}
dps_results: {
 key: "TestFrost-Settings-Orc-p1_bis-Basic-frost-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 201454.58726
  tps: 138499.85309
 }
}
dps_results: {
 key: "TestFrost-Settings-Orc-p1_bis-Basic-frost-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 240546.07272
  tps: 127485.88379
 }
}
dps_results: {
 key: "TestFrost-Settings-Orc-p1_bis-Basic-frost-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 110956.47992
  tps: 80777.32366
 }
}
dps_results: {
 key: "TestFrost-Settings-Orc-p1_bis-Basic-frost-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 118788.44277
  tps: 87035.89923
 }
}
dps_results: {
 key: "TestFrost-Settings-Orc-p1_prebis_rich-Basic-frost-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 200061.45667
  tps: 101949.71164
 }
}
dps_results: {
 key: "TestFrost-Settings-Orc-p1_prebis_rich-Basic-frost-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 95120.07592
  tps: 64954.82789
 }
}
dps_results: {
 key: "TestFrost-Settings-Orc-p1_prebis_rich-Basic-frost-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 134838.44464
  tps: 92020.60103
 }
}
dps_results: {
 key: "TestFrost-Settings-Orc-p1_prebis_rich-Basic-frost-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 153560.88169
  tps: 77630.27303
 }
}
dps_results: {
 key: "TestFrost-Settings-Orc-p1_prebis_rich-Basic-frost-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 68082.37579
  tps: 48758.41817
 }
}
dps_results: {
 key: "TestFrost-Settings-Orc-p1_prebis_rich-Basic-frost-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 75321.43775
  tps: 54226.47376
 }
}
dps_results: {
 key: "TestFrost-Settings-Troll-p1_bis-Basic-frost-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 299023.53563
  tps: 162503.53519
 }
}
dps_results: {
 key: "TestFrost-Settings-Troll-p1_bis-Basic-frost-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 147862.18278
  tps: 102860.28247
 }
}
dps_results: {
 key: "TestFrost-Settings-Troll-p1_bis-Basic-frost-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 200892.67586
  tps: 138780.62988
 }
}
dps_results: {
 key: "TestFrost-Settings-Troll-p1_bis-Basic-frost-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 235102.16694
  tps: 127653.12691
 }
}
dps_results: {
 key: "TestFrost-Settings-Troll-p1_bis-Basic-frost-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 110838.26582
  tps: 81026.93926
 }
}
dps_results: {
 key: "TestFrost-Settings-Troll-p1_bis-Basic-frost-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 119604.23259
  tps: 87516.70511
 }
}
dps_results: {
 key: "TestFrost-Settings-Troll-p1_prebis_rich-Basic-frost-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 194734.63134
  tps: 101337.13936
 }
}
dps_results: {
 key: "TestFrost-Settings-Troll-p1_prebis_rich-Basic-frost-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 94023.56453
  tps: 64520.92306
 }
}
dps_results: {
 key: "TestFrost-Settings-Troll-p1_prebis_rich-Basic-frost-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 134807.4632
  tps: 92530.05301
 }
}
dps_results: {
 key: "TestFrost-Settings-Troll-p1_prebis_rich-Basic-frost-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 147902.79939
  tps: 77229.75814
 }
}
dps_results: {
 key: "TestFrost-Settings-Troll-p1_prebis_rich-Basic-frost-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 67217.51438
  tps: 48334.26113
 }
}
dps_results: {
 key: "TestFrost-Settings-Troll-p1_prebis_rich-Basic-frost-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 74673.01389
  tps: 54148.13236
 }
}
dps_results: {
 key: "TestFrost-SwitchInFrontOfTarget-Default"
 value: {
  dps: 147862.18278
  tps: 102860.28247
 }
}
