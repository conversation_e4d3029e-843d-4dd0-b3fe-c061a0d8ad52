character_stats_results: {
 key: "TestArcane-CharacterStats-Default"
 value: {
  final_stats: 126
  final_stats: 136.5
  final_stats: 22510.4
  final_stats: 18943.155
  final_stats: 576
  final_stats: 4333
  final_stats: 1911
  final_stats: 4926
  final_stats: 768
  final_stats: 0
  final_stats: 0
  final_stats: 12562
  final_stats: 0
  final_stats: 0
  final_stats: 30111.5705
  final_stats: 0
  final_stats: 0
  final_stats: 14882
  final_stats: 0
  final_stats: 461548.6
  final_stats: 300000
  final_stats: 15000
  final_stats: 12.74412
  final_stats: 15.00294
  final_stats: 11.635
  final_stats: 16.57167
  final_stats: 0
 }
}
dps_results: {
 key: "TestArcane-AllItems-AgilePrimalDiamond"
 value: {
  dps: 151344.07816
  tps: 149120.76935
 }
}
dps_results: {
 key: "TestArcane-AllItems-AssuranceofConsequence-105472"
 value: {
  dps: 144436.72205
  tps: 142289.56229
 }
}
dps_results: {
 key: "TestArcane-AllItems-AusterePrimalDiamond"
 value: {
  dps: 150646.7281
  tps: 148428.05053
 }
}
dps_results: {
 key: "TestArcane-AllItems-BurningPrimalDiamond"
 value: {
  dps: 152693.36639
  tps: 150456.38389
 }
}
dps_results: {
 key: "TestArcane-AllItems-CapacitivePrimalDiamond"
 value: {
  dps: 151280.46783
  tps: 149055.59914
 }
}
dps_results: {
 key: "TestArcane-AllItems-ChronomancerRegalia"
 value: {
  dps: 138088.77839
  tps: 135847.56562
 }
}
dps_results: {
 key: "TestArcane-AllItems-CourageousPrimalDiamond"
 value: {
  dps: 152639.27002
  tps: 150400.13149
 }
}
dps_results: {
 key: "TestArcane-AllItems-DelicateVialoftheSanguinaire-96895"
 value: {
  dps: 144436.72205
  tps: 142289.56229
 }
}
dps_results: {
 key: "TestArcane-AllItems-DestructivePrimalDiamond"
 value: {
  dps: 151552.14553
  tps: 149320.52852
 }
}
dps_results: {
 key: "TestArcane-AllItems-EffulgentPrimalDiamond"
 value: {
  dps: 150646.7281
  tps: 148428.05053
 }
}
dps_results: {
 key: "TestArcane-AllItems-EmberPrimalDiamond"
 value: {
  dps: 151985.64801
  tps: 149753.3298
 }
}
dps_results: {
 key: "TestArcane-AllItems-EnchantWeapon-BloodyDancingSteel-5125"
 value: {
  dps: 146087.62694
  tps: 143928.5302
 }
}
dps_results: {
 key: "TestArcane-AllItems-EnchantWeapon-Colossus-4445"
 value: {
  dps: 146087.62694
  tps: 143928.5302
 }
}
dps_results: {
 key: "TestArcane-AllItems-EnchantWeapon-DancingSteel-4444"
 value: {
  dps: 146087.62694
  tps: 143928.5302
 }
}
dps_results: {
 key: "TestArcane-AllItems-EnchantWeapon-River'sSong-4446"
 value: {
  dps: 146087.62694
  tps: 143928.5302
 }
}
dps_results: {
 key: "TestArcane-AllItems-EnchantWeapon-SpiritofConquest-5124"
 value: {
  dps: 146087.62694
  tps: 143928.5302
 }
}
dps_results: {
 key: "TestArcane-AllItems-EnchantWeapon-Windsong-4441"
 value: {
  dps: 147840.54904
  tps: 145646.94576
 }
}
dps_results: {
 key: "TestArcane-AllItems-EnigmaticPrimalDiamond"
 value: {
  dps: 151552.14553
  tps: 149320.52852
 }
}
dps_results: {
 key: "TestArcane-AllItems-EternalPrimalDiamond"
 value: {
  dps: 150646.7281
  tps: 148428.05053
 }
}
dps_results: {
 key: "TestArcane-AllItems-EvilEyeofGalakras-105491"
 value: {
  dps: 144436.72205
  tps: 142289.56229
 }
}
dps_results: {
 key: "TestArcane-AllItems-FabledFeatherofJi-Kun-96842"
 value: {
  dps: 144391.08331
  tps: 142243.94134
 }
}
dps_results: {
 key: "TestArcane-AllItems-Fen-Yu,FuryofXuen-102248"
 value: {
  dps: 150522.08729
  tps: 148319.96588
 }
}
dps_results: {
 key: "TestArcane-AllItems-FleetPrimalDiamond"
 value: {
  dps: 151875.11921
  tps: 149649.98825
 }
}
dps_results: {
 key: "TestArcane-AllItems-ForlornPrimalDiamond"
 value: {
  dps: 151985.64801
  tps: 149753.3298
 }
}
dps_results: {
 key: "TestArcane-AllItems-GazeoftheTwins-96915"
 value: {
  dps: 147178.07946
  tps: 144953.61364
 }
}
dps_results: {
 key: "TestArcane-AllItems-Gong-Lu,StrengthofXuen-102249"
 value: {
  dps: 150522.08729
  tps: 148319.96588
 }
}
dps_results: {
 key: "TestArcane-AllItems-Horridon'sLastGasp-96757"
 value: {
  dps: 155404.37555
  tps: 153134.1867
 }
}
dps_results: {
 key: "TestArcane-AllItems-ImpassivePrimalDiamond"
 value: {
  dps: 151552.14553
  tps: 149320.52852
 }
}
dps_results: {
 key: "TestArcane-AllItems-IndomitablePrimalDiamond"
 value: {
  dps: 150646.7281
  tps: 148428.05053
 }
}
dps_results: {
 key: "TestArcane-AllItems-InscribedBagofHydra-Spawn-96828"
 value: {
  dps: 144436.72205
  tps: 142289.56229
 }
}
dps_results: {
 key: "TestArcane-AllItems-Ji-Kun'sRisingWinds-96843"
 value: {
  dps: 144436.72205
  tps: 142289.56229
 }
}
dps_results: {
 key: "TestArcane-AllItems-PhaseFingers-4697"
 value: {
  dps: 152162.42164
  tps: 149924.71547
 }
}
dps_results: {
 key: "TestArcane-AllItems-PowerfulPrimalDiamond"
 value: {
  dps: 150646.7281
  tps: 148428.05053
 }
}
dps_results: {
 key: "TestArcane-AllItems-PriceofProgress-81266"
 value: {
  dps: 149906.31802
  tps: 147698.01229
 }
}
dps_results: {
 key: "TestArcane-AllItems-Primordius'TalismanofRage-96873"
 value: {
  dps: 148317.0176
  tps: 146097.01957
 }
}
dps_results: {
 key: "TestArcane-AllItems-Qian-Le,CourageofNiuzao-102245"
 value: {
  dps: 149787.90332
  tps: 147551.45514
 }
}
dps_results: {
 key: "TestArcane-AllItems-Qian-Ying,FortitudeofNiuzao-102250"
 value: {
  dps: 147602.20368
  tps: 145386.42469
 }
}
dps_results: {
 key: "TestArcane-AllItems-RegaliaoftheBurningScroll"
 value: {
  dps: 133060.90567
  tps: 130986.51356
 }
}
dps_results: {
 key: "TestArcane-AllItems-RegaliaoftheChromaticHydra"
 value: {
  dps: 148096.77381
  tps: 145795.71298
 }
}
dps_results: {
 key: "TestArcane-AllItems-Renataki'sSoulCharm-96741"
 value: {
  dps: 144435.28845
  tps: 142288.12868
 }
}
dps_results: {
 key: "TestArcane-AllItems-ReverberatingPrimalDiamond"
 value: {
  dps: 151344.07816
  tps: 149120.76935
 }
}
dps_results: {
 key: "TestArcane-AllItems-RevitalizingPrimalDiamond"
 value: {
  dps: 151344.07816
  tps: 149120.76935
 }
}
dps_results: {
 key: "TestArcane-AllItems-SinisterPrimalDiamond"
 value: {
  dps: 151280.46783
  tps: 149055.59914
 }
}
dps_results: {
 key: "TestArcane-AllItems-SynapseSprings(MarkII)-4898"
 value: {
  dps: 154992.0309
  tps: 152646.8245
 }
}
dps_results: {
 key: "TestArcane-AllItems-TalismanofBloodlust-96864"
 value: {
  dps: 148559.42714
  tps: 146318.2057
 }
}
dps_results: {
 key: "TestArcane-AllItems-TheGloamingBlade-88149"
 value: {
  dps: 152693.36639
  tps: 150456.38389
 }
}
dps_results: {
 key: "TestArcane-AllItems-TyrannicalPrimalDiamond"
 value: {
  dps: 150646.7281
  tps: 148428.05053
 }
}
dps_results: {
 key: "TestArcane-AllItems-UnerringVisionofLeiShen-96930"
 value: {
  dps: 162139.99058
  tps: 159772.50882
 }
}
dps_results: {
 key: "TestArcane-AllItems-Wushoolay'sFinalChoice-96785"
 value: {
  dps: 183256.14057
  tps: 180497.48602
 }
}
dps_results: {
 key: "TestArcane-AllItems-Xing-Ho,BreathofYu'lon-102246"
 value: {
  dps: 186322.92319
  tps: 183972.81341
 }
}
dps_results: {
 key: "TestArcane-AllItems-ZenAlchemistStone-75274"
 value: {
  dps: 154610.84871
  tps: 152264.08056
 }
}
dps_results: {
 key: "TestArcane-Average-Default"
 value: {
  dps: 156952.80831
  tps: 154540.10174
 }
}
dps_results: {
 key: "TestArcane-Settings-Orc-p1_bis-Arcane-default-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 242795.92441
  tps: 244773.65238
 }
}
dps_results: {
 key: "TestArcane-Settings-Orc-p1_bis-Arcane-default-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 152977.27206
  tps: 150541.03405
 }
}
dps_results: {
 key: "TestArcane-Settings-Orc-p1_bis-Arcane-default-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 210947.10633
  tps: 203376.19221
 }
}
dps_results: {
 key: "TestArcane-Settings-Orc-p1_bis-Arcane-default-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 180419.2357
  tps: 182221.15365
 }
}
dps_results: {
 key: "TestArcane-Settings-Orc-p1_bis-Arcane-default-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 113312.48303
  tps: 111919.30468
 }
}
dps_results: {
 key: "TestArcane-Settings-Orc-p1_bis-Arcane-default-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 129356.35096
  tps: 126339.79473
 }
}
dps_results: {
 key: "TestArcane-Settings-Orc-prebis-Arcane-default-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 144430.87767
  tps: 146354.81886
 }
}
dps_results: {
 key: "TestArcane-Settings-Orc-prebis-Arcane-default-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 90797.20011
  tps: 89053.16357
 }
}
dps_results: {
 key: "TestArcane-Settings-Orc-prebis-Arcane-default-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 132138.45257
  tps: 126537.7006
 }
}
dps_results: {
 key: "TestArcane-Settings-Orc-prebis-Arcane-default-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 103380.85422
  tps: 105579.76372
 }
}
dps_results: {
 key: "TestArcane-Settings-Orc-prebis-Arcane-default-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 64625.14445
  tps: 63607.15172
 }
}
dps_results: {
 key: "TestArcane-Settings-Orc-prebis-Arcane-default-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 76098.22228
  tps: 73718.65132
 }
}
dps_results: {
 key: "TestArcane-Settings-Troll-p1_bis-Arcane-default-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 246467.98922
  tps: 248355.45972
 }
}
dps_results: {
 key: "TestArcane-Settings-Troll-p1_bis-Arcane-default-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 155492.59886
  tps: 153144.73064
 }
}
dps_results: {
 key: "TestArcane-Settings-Troll-p1_bis-Arcane-default-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 216305.56202
  tps: 208897.98782
 }
}
dps_results: {
 key: "TestArcane-Settings-Troll-p1_bis-Arcane-default-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 181794.32196
  tps: 183878.98438
 }
}
dps_results: {
 key: "TestArcane-Settings-Troll-p1_bis-Arcane-default-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 115960.30943
  tps: 114662.17318
 }
}
dps_results: {
 key: "TestArcane-Settings-Troll-p1_bis-Arcane-default-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 134235.49186
  tps: 131336.76125
 }
}
dps_results: {
 key: "TestArcane-Settings-Troll-prebis-Arcane-default-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 145890.00175
  tps: 147822.42199
 }
}
dps_results: {
 key: "TestArcane-Settings-Troll-prebis-Arcane-default-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 92069.20839
  tps: 90408.6188
 }
}
dps_results: {
 key: "TestArcane-Settings-Troll-prebis-Arcane-default-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 135304.15401
  tps: 129921.75155
 }
}
dps_results: {
 key: "TestArcane-Settings-Troll-prebis-Arcane-default-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 103635.78752
  tps: 105914.69638
 }
}
dps_results: {
 key: "TestArcane-Settings-Troll-prebis-Arcane-default-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 65646.05772
  tps: 64717.61856
 }
}
dps_results: {
 key: "TestArcane-Settings-Troll-prebis-Arcane-default-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 78826.99132
  tps: 76555.23887
 }
}
dps_results: {
 key: "TestArcane-SwitchInFrontOfTarget-Default"
 value: {
  dps: 155492.59886
  tps: 153144.73064
 }
}
