character_stats_results: {
 key: "TestFire-CharacterStats-Default"
 value: {
  final_stats: 126
  final_stats: 136.5
  final_stats: 22510.4
  final_stats: 19670.805
  final_stats: 576
  final_stats: 4762
  final_stats: 7828
  final_stats: 5011
  final_stats: 341
  final_stats: 0
  final_stats: 0
  final_stats: 5284
  final_stats: 0
  final_stats: 0
  final_stats: 30911.9855
  final_stats: 0
  final_stats: 0
  final_stats: 14882
  final_stats: 0
  final_stats: 461548.6
  final_stats: 300000
  final_stats: 15000
  final_stats: 14.00588
  final_stats: 15.00882
  final_stats: 21.49667
  final_stats: 26.72054
  final_stats: 0
 }
}
dps_results: {
 key: "TestFire-AllItems-AgilePrimalDiamond"
 value: {
  dps: 129150.6345
  tps: 125849.64938
 }
}
dps_results: {
 key: "TestFire-AllItems-AlacrityofXuen-103989"
 value: {
  dps: 120226.50355
  tps: 117097.57236
 }
}
dps_results: {
 key: "TestFire-AllItems-ArcaneBadgeoftheShieldwall-93347"
 value: {
  dps: 127380.82859
  tps: 124093.34143
 }
}
dps_results: {
 key: "TestFire-AllItems-ArrowflightMedallion-93258"
 value: {
  dps: 126130.09459
  tps: 122753.83209
 }
}
dps_results: {
 key: "TestFire-AllItems-AssuranceofConsequence-105472"
 value: {
  dps: 120478.84435
  tps: 117317.31226
 }
}
dps_results: {
 key: "TestFire-AllItems-AusterePrimalDiamond"
 value: {
  dps: 126490.29829
  tps: 123212.07869
 }
}
dps_results: {
 key: "TestFire-AllItems-BadJuju-96781"
 value: {
  dps: 125774.85626
  tps: 122634.91745
 }
}
dps_results: {
 key: "TestFire-AllItems-BadgeofKypariZar-84079"
 value: {
  dps: 121877.82007
  tps: 118717.44852
 }
}
dps_results: {
 key: "TestFire-AllItems-BlossomofPureSnow-89081"
 value: {
  dps: 131952.20912
  tps: 128485.37926
 }
}
dps_results: {
 key: "TestFire-AllItems-BottleofInfiniteStars-87057"
 value: {
  dps: 123103.36097
  tps: 119984.34293
 }
}
dps_results: {
 key: "TestFire-AllItems-BraidofTenSongs-84072"
 value: {
  dps: 121877.82007
  tps: 118717.44852
 }
}
dps_results: {
 key: "TestFire-AllItems-Brawler'sStatue-87571"
 value: {
  dps: 120715.62739
  tps: 117587.95812
 }
}
dps_results: {
 key: "TestFire-AllItems-BreathoftheHydra-96827"
 value: {
  dps: 140005.17456
  tps: 136359.80789
 }
}
dps_results: {
 key: "TestFire-AllItems-BroochofMunificentDeeds-87500"
 value: {
  dps: 120715.62739
  tps: 117587.95812
 }
}
dps_results: {
 key: "TestFire-AllItems-BrutalTalismanoftheShado-PanAssault-94508"
 value: {
  dps: 118833.65106
  tps: 115724.50851
 }
}
dps_results: {
 key: "TestFire-AllItems-BurningPrimalDiamond"
 value: {
  dps: 130153.45199
  tps: 126848.45391
 }
}
dps_results: {
 key: "TestFire-AllItems-CapacitivePrimalDiamond"
 value: {
  dps: 128284.41943
  tps: 124957.99854
 }
}
dps_results: {
 key: "TestFire-AllItems-CarbonicCarbuncle-81138"
 value: {
  dps: 124717.54573
  tps: 121433.35006
 }
}
dps_results: {
 key: "TestFire-AllItems-Cha-Ye'sEssenceofBrilliance-96888"
 value: {
  dps: 140883.87061
  tps: 137222.23681
 }
}
dps_results: {
 key: "TestFire-AllItems-CharmofTenSongs-84071"
 value: {
  dps: 123993.49824
  tps: 120803.89597
 }
}
dps_results: {
 key: "TestFire-AllItems-ChronomancerRegalia"
 value: {
  dps: 137241.45575
  tps: 134195.02705
 }
}
dps_results: {
 key: "TestFire-AllItems-CommunalIdolofDestruction-101168"
 value: {
  dps: 123808.279
  tps: 120679.28529
 }
}
dps_results: {
 key: "TestFire-AllItems-CommunalStoneofDestruction-101171"
 value: {
  dps: 127766.38939
  tps: 124617.24171
 }
}
dps_results: {
 key: "TestFire-AllItems-CommunalStoneofWisdom-101183"
 value: {
  dps: 125659.55223
  tps: 122442.53869
 }
}
dps_results: {
 key: "TestFire-AllItems-ContemplationofChi-Ji-103688"
 value: {
  dps: 126068.58439
  tps: 122853.43601
 }
}
dps_results: {
 key: "TestFire-AllItems-ContemplationofChi-Ji-103988"
 value: {
  dps: 129109.91841
  tps: 125815.88937
 }
}
dps_results: {
 key: "TestFire-AllItems-Coren'sColdChromiumCoaster-87574"
 value: {
  dps: 123028.1915
  tps: 119826.40245
 }
}
dps_results: {
 key: "TestFire-AllItems-CoreofDecency-87497"
 value: {
  dps: 120226.50355
  tps: 117097.57236
 }
}
dps_results: {
 key: "TestFire-AllItems-CourageousPrimalDiamond"
 value: {
  dps: 128339.41767
  tps: 124993.76174
 }
}
dps_results: {
 key: "TestFire-AllItems-CraftedDreadfulGladiator'sBadgeofConquest-93419"
 value: {
  dps: 120548.54457
  tps: 117420.8753
 }
}
dps_results: {
 key: "TestFire-AllItems-CraftedDreadfulGladiator'sBadgeofDominance-93600"
 value: {
  dps: 124343.56834
  tps: 121034.828
 }
}
dps_results: {
 key: "TestFire-AllItems-CraftedDreadfulGladiator'sBadgeofVictory-93606"
 value: {
  dps: 120548.54457
  tps: 117420.8753
 }
}
dps_results: {
 key: "TestFire-AllItems-CraftedDreadfulGladiator'sEmblemofCruelty-93485"
 value: {
  dps: 124032.79267
  tps: 120849.34187
 }
}
dps_results: {
 key: "TestFire-AllItems-CraftedDreadfulGladiator'sEmblemofMeditation-93487"
 value: {
  dps: 119483.3036
  tps: 116370.73991
 }
}
dps_results: {
 key: "TestFire-AllItems-CraftedDreadfulGladiator'sEmblemofTenacity-93486"
 value: {
  dps: 119483.3036
  tps: 116370.73991
 }
}
dps_results: {
 key: "TestFire-AllItems-CraftedDreadfulGladiator'sInsigniaofConquest-93424"
 value: {
  dps: 120610.72367
  tps: 117425.79169
 }
}
dps_results: {
 key: "TestFire-AllItems-CraftedDreadfulGladiator'sInsigniaofDominance-93601"
 value: {
  dps: 125745.63433
  tps: 122386.58727
 }
}
dps_results: {
 key: "TestFire-AllItems-CraftedDreadfulGladiator'sInsigniaofVictory-93611"
 value: {
  dps: 120499.30383
  tps: 117401.58912
 }
}
dps_results: {
 key: "TestFire-AllItems-CraftedMalevolentGladiator'sBadgeofConquest-98755"
 value: {
  dps: 120548.54457
  tps: 117420.8753
 }
}
dps_results: {
 key: "TestFire-AllItems-CraftedMalevolentGladiator'sBadgeofDominance-98910"
 value: {
  dps: 125382.76763
  tps: 122042.60174
 }
}
dps_results: {
 key: "TestFire-AllItems-CraftedMalevolentGladiator'sBadgeofVictory-98912"
 value: {
  dps: 120548.54457
  tps: 117420.8753
 }
}
dps_results: {
 key: "TestFire-AllItems-CraftedMalevolentGladiator'sEmblemofCruelty-98811"
 value: {
  dps: 124129.83223
  tps: 120937.4945
 }
}
dps_results: {
 key: "TestFire-AllItems-CraftedMalevolentGladiator'sEmblemofMeditation-98813"
 value: {
  dps: 119483.3036
  tps: 116370.73991
 }
}
dps_results: {
 key: "TestFire-AllItems-CraftedMalevolentGladiator'sEmblemofTenacity-98812"
 value: {
  dps: 119483.3036
  tps: 116370.73991
 }
}
dps_results: {
 key: "TestFire-AllItems-CraftedMalevolentGladiator'sInsigniaofConquest-98760"
 value: {
  dps: 120018.5357
  tps: 116996.97658
 }
}
dps_results: {
 key: "TestFire-AllItems-CraftedMalevolentGladiator'sInsigniaofDominance-98911"
 value: {
  dps: 126720.35654
  tps: 123309.58082
 }
}
dps_results: {
 key: "TestFire-AllItems-CraftedMalevolentGladiator'sInsigniaofVictory-98917"
 value: {
  dps: 120263.90942
  tps: 117095.45622
 }
}
dps_results: {
 key: "TestFire-AllItems-CurseofHubris-102307"
 value: {
  dps: 131213.71814
  tps: 127511.36363
 }
}
dps_results: {
 key: "TestFire-AllItems-CurseofHubris-104649"
 value: {
  dps: 133158.82718
  tps: 129396.15533
 }
}
dps_results: {
 key: "TestFire-AllItems-CurseofHubris-104898"
 value: {
  dps: 130522.90904
  tps: 126859.09767
 }
}
dps_results: {
 key: "TestFire-AllItems-CurseofHubris-105147"
 value: {
  dps: 130197.67664
  tps: 126564.49887
 }
}
dps_results: {
 key: "TestFire-AllItems-CurseofHubris-105396"
 value: {
  dps: 131042.23561
  tps: 127309.26497
 }
}
dps_results: {
 key: "TestFire-AllItems-CurseofHubris-105645"
 value: {
  dps: 134819.34887
  tps: 131008.12923
 }
}
dps_results: {
 key: "TestFire-AllItems-CutstitcherMedallion-93255"
 value: {
  dps: 126617.46359
  tps: 123407.13265
 }
}
dps_results: {
 key: "TestFire-AllItems-Daelo'sFinalWords-87496"
 value: {
  dps: 122078.96426
  tps: 118980.48576
 }
}
dps_results: {
 key: "TestFire-AllItems-DarkglowEmbroidery(Rank3)-4893"
 value: {
  dps: 126042.30986
  tps: 122812.03596
 }
}
dps_results: {
 key: "TestFire-AllItems-DarkmistVortex-87172"
 value: {
  dps: 123987.07672
  tps: 120786.06922
 }
}
dps_results: {
 key: "TestFire-AllItems-DeadeyeBadgeoftheShieldwall-93346"
 value: {
  dps: 121578.21798
  tps: 118581.15223
 }
}
dps_results: {
 key: "TestFire-AllItems-DelicateVialoftheSanguinaire-96895"
 value: {
  dps: 120226.50355
  tps: 117097.57236
 }
}
dps_results: {
 key: "TestFire-AllItems-DestructivePrimalDiamond"
 value: {
  dps: 128798.57146
  tps: 125474.47652
 }
}
dps_results: {
 key: "TestFire-AllItems-DisciplineofXuen-103986"
 value: {
  dps: 124905.19865
  tps: 121846.89325
 }
}
dps_results: {
 key: "TestFire-AllItems-Dominator'sArcaneBadge-93342"
 value: {
  dps: 127380.82859
  tps: 124093.34143
 }
}
dps_results: {
 key: "TestFire-AllItems-Dominator'sDeadeyeBadge-93341"
 value: {
  dps: 121578.21798
  tps: 118581.15223
 }
}
dps_results: {
 key: "TestFire-AllItems-Dominator'sDurableBadge-93345"
 value: {
  dps: 121578.21798
  tps: 118581.15223
 }
}
dps_results: {
 key: "TestFire-AllItems-Dominator'sKnightlyBadge-93344"
 value: {
  dps: 121578.21798
  tps: 118581.15223
 }
}
dps_results: {
 key: "TestFire-AllItems-Dominator'sMendingBadge-93343"
 value: {
  dps: 124411.28311
  tps: 121232.81211
 }
}
dps_results: {
 key: "TestFire-AllItems-DreadfulGladiator'sBadgeofConquest-84344"
 value: {
  dps: 120548.54457
  tps: 117420.8753
 }
}
dps_results: {
 key: "TestFire-AllItems-DreadfulGladiator'sBadgeofDominance-84488"
 value: {
  dps: 124343.56834
  tps: 121034.828
 }
}
dps_results: {
 key: "TestFire-AllItems-DreadfulGladiator'sBadgeofVictory-84490"
 value: {
  dps: 120548.54457
  tps: 117420.8753
 }
}
dps_results: {
 key: "TestFire-AllItems-DreadfulGladiator'sEmblemofCruelty-84399"
 value: {
  dps: 124032.79267
  tps: 120849.34187
 }
}
dps_results: {
 key: "TestFire-AllItems-DreadfulGladiator'sEmblemofMeditation-84401"
 value: {
  dps: 119483.3036
  tps: 116370.73991
 }
}
dps_results: {
 key: "TestFire-AllItems-DreadfulGladiator'sEmblemofTenacity-84400"
 value: {
  dps: 122578.84215
  tps: 119430.63191
 }
}
dps_results: {
 key: "TestFire-AllItems-DreadfulGladiator'sInsigniaofConquest-84349"
 value: {
  dps: 120592.39608
  tps: 117475.5883
 }
}
dps_results: {
 key: "TestFire-AllItems-DreadfulGladiator'sInsigniaofDominance-84489"
 value: {
  dps: 124703.9744
  tps: 121396.36973
 }
}
dps_results: {
 key: "TestFire-AllItems-DreadfulGladiator'sInsigniaofVictory-84495"
 value: {
  dps: 120061.55794
  tps: 116892.25027
 }
}
dps_results: {
 key: "TestFire-AllItems-DurableBadgeoftheShieldwall-93350"
 value: {
  dps: 121578.21798
  tps: 118581.15223
 }
}
dps_results: {
 key: "TestFire-AllItems-EffulgentPrimalDiamond"
 value: {
  dps: 126490.29829
  tps: 123212.07869
 }
}
dps_results: {
 key: "TestFire-AllItems-EmberPrimalDiamond"
 value: {
  dps: 127843.21634
  tps: 124507.8469
 }
}
dps_results: {
 key: "TestFire-AllItems-EmblemofKypariZar-84077"
 value: {
  dps: 123280.41873
  tps: 120081.37353
 }
}
dps_results: {
 key: "TestFire-AllItems-EmblemoftheCatacombs-83733"
 value: {
  dps: 122823.27902
  tps: 119689.35227
 }
}
dps_results: {
 key: "TestFire-AllItems-EmptyFruitBarrel-81133"
 value: {
  dps: 120226.50355
  tps: 117097.57236
 }
}
dps_results: {
 key: "TestFire-AllItems-EnchantWeapon-BloodyDancingSteel-5125"
 value: {
  dps: 124319.15768
  tps: 121074.46486
 }
}
dps_results: {
 key: "TestFire-AllItems-EnchantWeapon-Colossus-4445"
 value: {
  dps: 124317.80064
  tps: 121073.10781
 }
}
dps_results: {
 key: "TestFire-AllItems-EnchantWeapon-DancingSteel-4444"
 value: {
  dps: 124319.15768
  tps: 121074.46486
 }
}
dps_results: {
 key: "TestFire-AllItems-EnchantWeapon-River'sSong-4446"
 value: {
  dps: 126286.34596
  tps: 123087.54301
 }
}
dps_results: {
 key: "TestFire-AllItems-EnchantWeapon-SpiritofConquest-5124"
 value: {
  dps: 124319.15768
  tps: 121074.46486
 }
}
dps_results: {
 key: "TestFire-AllItems-EnchantWeapon-Windsong-4441"
 value: {
  dps: 129362.37888
  tps: 126117.1201
 }
}
dps_results: {
 key: "TestFire-AllItems-EnigmaticPrimalDiamond"
 value: {
  dps: 128798.57146
  tps: 125474.47652
 }
}
dps_results: {
 key: "TestFire-AllItems-EternalPrimalDiamond"
 value: {
  dps: 126490.29829
  tps: 123212.07869
 }
}
dps_results: {
 key: "TestFire-AllItems-EvilEyeofGalakras-105491"
 value: {
  dps: 120104.79378
  tps: 116955.74963
 }
}
dps_results: {
 key: "TestFire-AllItems-FabledFeatherofJi-Kun-96842"
 value: {
  dps: 120226.50355
  tps: 117097.57236
 }
}
dps_results: {
 key: "TestFire-AllItems-FearwurmBadge-84074"
 value: {
  dps: 123280.41873
  tps: 120081.37353
 }
}
dps_results: {
 key: "TestFire-AllItems-FearwurmRelic-84070"
 value: {
  dps: 124331.41773
  tps: 121104.40181
 }
}
dps_results: {
 key: "TestFire-AllItems-FelsoulIdolofDestruction-101263"
 value: {
  dps: 124275.93686
  tps: 121100.88924
 }
}
dps_results: {
 key: "TestFire-AllItems-FelsoulStoneofDestruction-101266"
 value: {
  dps: 127760.02866
  tps: 124625.5184
 }
}
dps_results: {
 key: "TestFire-AllItems-Fen-Yu,FuryofXuen-102248"
 value: {
  dps: 127784.05984
  tps: 124587.58307
 }
}
dps_results: {
 key: "TestFire-AllItems-FlashfrozenResinGlobule-100951"
 value: {
  dps: 124274.26566
  tps: 120902.32509
 }
}
dps_results: {
 key: "TestFire-AllItems-FlashfrozenResinGlobule-81263"
 value: {
  dps: 124859.031
  tps: 121440.65409
 }
}
dps_results: {
 key: "TestFire-AllItems-FlashingSteelTalisman-81265"
 value: {
  dps: 119484.87658
  tps: 116364.28934
 }
}
dps_results: {
 key: "TestFire-AllItems-FleetPrimalDiamond"
 value: {
  dps: 127958.99222
  tps: 124698.18688
 }
}
dps_results: {
 key: "TestFire-AllItems-ForlornPrimalDiamond"
 value: {
  dps: 127843.21634
  tps: 124507.8469
 }
}
dps_results: {
 key: "TestFire-AllItems-FortitudeoftheZandalari-94516"
 value: {
  dps: 124376.54653
  tps: 121231.44784
 }
}
dps_results: {
 key: "TestFire-AllItems-FortitudeoftheZandalari-95677"
 value: {
  dps: 123550.81075
  tps: 120402.75627
 }
}
dps_results: {
 key: "TestFire-AllItems-FortitudeoftheZandalari-96049"
 value: {
  dps: 124403.37158
  tps: 121266.25349
 }
}
dps_results: {
 key: "TestFire-AllItems-FortitudeoftheZandalari-96421"
 value: {
  dps: 124959.99233
  tps: 121823.84728
 }
}
dps_results: {
 key: "TestFire-AllItems-FortitudeoftheZandalari-96793"
 value: {
  dps: 125187.65799
  tps: 122051.51294
 }
}
dps_results: {
 key: "TestFire-AllItems-GazeoftheTwins-96915"
 value: {
  dps: 124394.21455
  tps: 121100.71916
 }
}
dps_results: {
 key: "TestFire-AllItems-Gerp'sPerfectArrow-87495"
 value: {
  dps: 123560.18721
  tps: 120363.02873
 }
}
dps_results: {
 key: "TestFire-AllItems-Gong-Lu,StrengthofXuen-102249"
 value: {
  dps: 127784.05984
  tps: 124587.58307
 }
}
dps_results: {
 key: "TestFire-AllItems-GrievousGladiator'sBadgeofConquest-100195"
 value: {
  dps: 120548.54457
  tps: 117420.8753
 }
}
dps_results: {
 key: "TestFire-AllItems-GrievousGladiator'sBadgeofConquest-100603"
 value: {
  dps: 120548.54457
  tps: 117420.8753
 }
}
dps_results: {
 key: "TestFire-AllItems-GrievousGladiator'sBadgeofConquest-102856"
 value: {
  dps: 120548.54457
  tps: 117420.8753
 }
}
dps_results: {
 key: "TestFire-AllItems-GrievousGladiator'sBadgeofConquest-103145"
 value: {
  dps: 120548.54457
  tps: 117420.8753
 }
}
dps_results: {
 key: "TestFire-AllItems-GrievousGladiator'sBadgeofDominance-100490"
 value: {
  dps: 128006.06712
  tps: 124576.06189
 }
}
dps_results: {
 key: "TestFire-AllItems-GrievousGladiator'sBadgeofDominance-100576"
 value: {
  dps: 128006.06712
  tps: 124576.06189
 }
}
dps_results: {
 key: "TestFire-AllItems-GrievousGladiator'sBadgeofDominance-102830"
 value: {
  dps: 128006.06712
  tps: 124576.06189
 }
}
dps_results: {
 key: "TestFire-AllItems-GrievousGladiator'sBadgeofDominance-103308"
 value: {
  dps: 128006.06712
  tps: 124576.06189
 }
}
dps_results: {
 key: "TestFire-AllItems-GrievousGladiator'sBadgeofVictory-100500"
 value: {
  dps: 120548.54457
  tps: 117420.8753
 }
}
dps_results: {
 key: "TestFire-AllItems-GrievousGladiator'sBadgeofVictory-100579"
 value: {
  dps: 120548.54457
  tps: 117420.8753
 }
}
dps_results: {
 key: "TestFire-AllItems-GrievousGladiator'sBadgeofVictory-102833"
 value: {
  dps: 120548.54457
  tps: 117420.8753
 }
}
dps_results: {
 key: "TestFire-AllItems-GrievousGladiator'sBadgeofVictory-103314"
 value: {
  dps: 120548.54457
  tps: 117420.8753
 }
}
dps_results: {
 key: "TestFire-AllItems-GrievousGladiator'sEmblemofCruelty-100305"
 value: {
  dps: 125881.04363
  tps: 122638.4633
 }
}
dps_results: {
 key: "TestFire-AllItems-GrievousGladiator'sEmblemofCruelty-100626"
 value: {
  dps: 125881.04363
  tps: 122638.4633
 }
}
dps_results: {
 key: "TestFire-AllItems-GrievousGladiator'sEmblemofCruelty-102877"
 value: {
  dps: 125881.04363
  tps: 122638.4633
 }
}
dps_results: {
 key: "TestFire-AllItems-GrievousGladiator'sEmblemofCruelty-103210"
 value: {
  dps: 125881.04363
  tps: 122638.4633
 }
}
dps_results: {
 key: "TestFire-AllItems-GrievousGladiator'sEmblemofMeditation-100307"
 value: {
  dps: 119483.3036
  tps: 116370.73991
 }
}
dps_results: {
 key: "TestFire-AllItems-GrievousGladiator'sEmblemofMeditation-100559"
 value: {
  dps: 119483.3036
  tps: 116370.73991
 }
}
dps_results: {
 key: "TestFire-AllItems-GrievousGladiator'sEmblemofMeditation-102813"
 value: {
  dps: 119483.3036
  tps: 116370.73991
 }
}
dps_results: {
 key: "TestFire-AllItems-GrievousGladiator'sEmblemofMeditation-103212"
 value: {
  dps: 119483.3036
  tps: 116370.73991
 }
}
dps_results: {
 key: "TestFire-AllItems-GrievousGladiator'sEmblemofTenacity-100306"
 value: {
  dps: 119483.3036
  tps: 116370.73991
 }
}
dps_results: {
 key: "TestFire-AllItems-GrievousGladiator'sEmblemofTenacity-100652"
 value: {
  dps: 119483.3036
  tps: 116370.73991
 }
}
dps_results: {
 key: "TestFire-AllItems-GrievousGladiator'sEmblemofTenacity-102903"
 value: {
  dps: 119483.3036
  tps: 116370.73991
 }
}
dps_results: {
 key: "TestFire-AllItems-GrievousGladiator'sEmblemofTenacity-103211"
 value: {
  dps: 119483.3036
  tps: 116370.73991
 }
}
dps_results: {
 key: "TestFire-AllItems-GrievousGladiator'sInsigniaofConquest-103150"
 value: {
  dps: 120328.91397
  tps: 117211.53515
 }
}
dps_results: {
 key: "TestFire-AllItems-GrievousGladiator'sInsigniaofDominance-103309"
 value: {
  dps: 131157.03596
  tps: 127701.09017
 }
}
dps_results: {
 key: "TestFire-AllItems-GrievousGladiator'sInsigniaofVictory-103319"
 value: {
  dps: 120907.49234
  tps: 117843.52981
 }
}
dps_results: {
 key: "TestFire-AllItems-Hawkmaster'sTalon-89082"
 value: {
  dps: 124214.16733
  tps: 120987.05365
 }
}
dps_results: {
 key: "TestFire-AllItems-Heart-LesionDefenderIdol-100999"
 value: {
  dps: 120389.27831
  tps: 117241.17723
 }
}
dps_results: {
 key: "TestFire-AllItems-Heart-LesionDefenderStone-101002"
 value: {
  dps: 123807.79713
  tps: 120740.36508
 }
}
dps_results: {
 key: "TestFire-AllItems-Heart-LesionIdolofBattle-100991"
 value: {
  dps: 124908.90508
  tps: 121732.48132
 }
}
dps_results: {
 key: "TestFire-AllItems-Heart-LesionStoneofBattle-100990"
 value: {
  dps: 124002.76383
  tps: 120904.22478
 }
}
dps_results: {
 key: "TestFire-AllItems-HeartofFire-81181"
 value: {
  dps: 123044.68582
  tps: 119879.71428
 }
}
dps_results: {
 key: "TestFire-AllItems-HeartwarmerMedallion-93260"
 value: {
  dps: 126617.46359
  tps: 123407.13265
 }
}
dps_results: {
 key: "TestFire-AllItems-HelmbreakerMedallion-93261"
 value: {
  dps: 126130.09459
  tps: 122753.83209
 }
}
dps_results: {
 key: "TestFire-AllItems-Horridon'sLastGasp-96757"
 value: {
  dps: 128997.56489
  tps: 125682.88596
 }
}
dps_results: {
 key: "TestFire-AllItems-ImpassivePrimalDiamond"
 value: {
  dps: 128798.57146
  tps: 125474.47652
 }
}
dps_results: {
 key: "TestFire-AllItems-IndomitablePrimalDiamond"
 value: {
  dps: 126490.29829
  tps: 123212.07869
 }
}
dps_results: {
 key: "TestFire-AllItems-InscribedBagofHydra-Spawn-96828"
 value: {
  dps: 120226.50355
  tps: 117097.57236
 }
}
dps_results: {
 key: "TestFire-AllItems-InsigniaofKypariZar-84078"
 value: {
  dps: 120226.50355
  tps: 117097.57236
 }
}
dps_results: {
 key: "TestFire-AllItems-IronBellyWok-89083"
 value: {
  dps: 124214.16733
  tps: 120987.05365
 }
}
dps_results: {
 key: "TestFire-AllItems-IronProtectorTalisman-85181"
 value: {
  dps: 120282.18334
  tps: 117174.45334
 }
}
dps_results: {
 key: "TestFire-AllItems-JadeBanditFigurine-86043"
 value: {
  dps: 124214.16733
  tps: 120987.05365
 }
}
dps_results: {
 key: "TestFire-AllItems-JadeBanditFigurine-86772"
 value: {
  dps: 123657.97469
  tps: 120428.85409
 }
}
dps_results: {
 key: "TestFire-AllItems-JadeCharioteerFigurine-86042"
 value: {
  dps: 124214.16733
  tps: 120987.05365
 }
}
dps_results: {
 key: "TestFire-AllItems-JadeCharioteerFigurine-86771"
 value: {
  dps: 123657.97469
  tps: 120428.85409
 }
}
dps_results: {
 key: "TestFire-AllItems-JadeCourtesanFigurine-86045"
 value: {
  dps: 126574.10748
  tps: 123367.87438
 }
}
dps_results: {
 key: "TestFire-AllItems-JadeCourtesanFigurine-86774"
 value: {
  dps: 125058.10507
  tps: 121868.40422
 }
}
dps_results: {
 key: "TestFire-AllItems-JadeMagistrateFigurine-86044"
 value: {
  dps: 131952.20912
  tps: 128485.37926
 }
}
dps_results: {
 key: "TestFire-AllItems-JadeMagistrateFigurine-86773"
 value: {
  dps: 130222.67814
  tps: 126769.72866
 }
}
dps_results: {
 key: "TestFire-AllItems-JadeWarlordFigurine-86046"
 value: {
  dps: 122299.20356
  tps: 119271.93695
 }
}
dps_results: {
 key: "TestFire-AllItems-JadeWarlordFigurine-86775"
 value: {
  dps: 121884.24109
  tps: 118856.97448
 }
}
dps_results: {
 key: "TestFire-AllItems-Ji-Kun'sRisingWinds-96843"
 value: {
  dps: 120226.50355
  tps: 117097.57236
 }
}
dps_results: {
 key: "TestFire-AllItems-KnightlyBadgeoftheShieldwall-93349"
 value: {
  dps: 121578.21798
  tps: 118581.15223
 }
}
dps_results: {
 key: "TestFire-AllItems-KnotofTenSongs-84073"
 value: {
  dps: 120226.50355
  tps: 117097.57236
 }
}
dps_results: {
 key: "TestFire-AllItems-Kor'kronBookofHurting-92785"
 value: {
  dps: 120455.38981
  tps: 117322.10115
 }
}
dps_results: {
 key: "TestFire-AllItems-Lao-Chin'sLiquidCourage-89079"
 value: {
  dps: 122299.20356
  tps: 119271.93695
 }
}
dps_results: {
 key: "TestFire-AllItems-LeiShen'sFinalOrders-87072"
 value: {
  dps: 125444.83728
  tps: 122199.1313
 }
}
dps_results: {
 key: "TestFire-AllItems-LessonsoftheDarkmaster-81268"
 value: {
  dps: 120715.62739
  tps: 117587.95812
 }
}
dps_results: {
 key: "TestFire-AllItems-LightdrinkerIdolofRage-101200"
 value: {
  dps: 122781.07556
  tps: 119658.35867
 }
}
dps_results: {
 key: "TestFire-AllItems-LightdrinkerStoneofRage-101203"
 value: {
  dps: 125147.50051
  tps: 122044.35513
 }
}
dps_results: {
 key: "TestFire-AllItems-LordBlastington'sScopeofDoom-4699"
 value: {
  dps: 124319.15768
  tps: 121074.46486
 }
}
dps_results: {
 key: "TestFire-AllItems-MalevolentGladiator'sBadgeofConquest-84934"
 value: {
  dps: 120548.54457
  tps: 117420.8753
 }
}
dps_results: {
 key: "TestFire-AllItems-MalevolentGladiator'sBadgeofConquest-91452"
 value: {
  dps: 120548.54457
  tps: 117420.8753
 }
}
dps_results: {
 key: "TestFire-AllItems-MalevolentGladiator'sBadgeofDominance-84940"
 value: {
  dps: 125681.52537
  tps: 122329.94718
 }
}
dps_results: {
 key: "TestFire-AllItems-MalevolentGladiator'sBadgeofDominance-91753"
 value: {
  dps: 125382.76763
  tps: 122042.60174
 }
}
dps_results: {
 key: "TestFire-AllItems-MalevolentGladiator'sBadgeofVictory-84942"
 value: {
  dps: 120548.54457
  tps: 117420.8753
 }
}
dps_results: {
 key: "TestFire-AllItems-MalevolentGladiator'sBadgeofVictory-91763"
 value: {
  dps: 120548.54457
  tps: 117420.8753
 }
}
dps_results: {
 key: "TestFire-AllItems-MalevolentGladiator'sEmblemofCruelty-84936"
 value: {
  dps: 124567.6419
  tps: 121373.42613
 }
}
dps_results: {
 key: "TestFire-AllItems-MalevolentGladiator'sEmblemofCruelty-91562"
 value: {
  dps: 124129.83223
  tps: 120937.4945
 }
}
dps_results: {
 key: "TestFire-AllItems-MalevolentGladiator'sEmblemofMeditation-84939"
 value: {
  dps: 119483.3036
  tps: 116370.73991
 }
}
dps_results: {
 key: "TestFire-AllItems-MalevolentGladiator'sEmblemofMeditation-91564"
 value: {
  dps: 119483.3036
  tps: 116370.73991
 }
}
dps_results: {
 key: "TestFire-AllItems-MalevolentGladiator'sEmblemofTenacity-84938"
 value: {
  dps: 119483.3036
  tps: 116370.73991
 }
}
dps_results: {
 key: "TestFire-AllItems-MalevolentGladiator'sEmblemofTenacity-91563"
 value: {
  dps: 119483.3036
  tps: 116370.73991
 }
}
dps_results: {
 key: "TestFire-AllItems-MalevolentGladiator'sInsigniaofConquest-91457"
 value: {
  dps: 120479.36801
  tps: 117356.03336
 }
}
dps_results: {
 key: "TestFire-AllItems-MalevolentGladiator'sInsigniaofDominance-91754"
 value: {
  dps: 126609.54595
  tps: 123243.95015
 }
}
dps_results: {
 key: "TestFire-AllItems-MalevolentGladiator'sInsigniaofVictory-91768"
 value: {
  dps: 120199.54192
  tps: 117116.28478
 }
}
dps_results: {
 key: "TestFire-AllItems-MarkoftheCatacombs-83731"
 value: {
  dps: 125726.42295
  tps: 122540.75798
 }
}
dps_results: {
 key: "TestFire-AllItems-MarkoftheHardenedGrunt-92783"
 value: {
  dps: 119850.24544
  tps: 116736.21243
 }
}
dps_results: {
 key: "TestFire-AllItems-MedallionofMystifyingVapors-93257"
 value: {
  dps: 122469.86307
  tps: 119454.72424
 }
}
dps_results: {
 key: "TestFire-AllItems-MedallionoftheCatacombs-83734"
 value: {
  dps: 121737.87008
  tps: 118604.64827
 }
}
dps_results: {
 key: "TestFire-AllItems-MendingBadgeoftheShieldwall-93348"
 value: {
  dps: 124411.28311
  tps: 121232.81211
 }
}
dps_results: {
 key: "TestFire-AllItems-MirrorScope-4700"
 value: {
  dps: 124319.15768
  tps: 121074.46486
 }
}
dps_results: {
 key: "TestFire-AllItems-MistdancerDefenderIdol-101089"
 value: {
  dps: 120783.54749
  tps: 117684.69077
 }
}
dps_results: {
 key: "TestFire-AllItems-MistdancerDefenderStone-101087"
 value: {
  dps: 122765.42957
  tps: 119657.51232
 }
}
dps_results: {
 key: "TestFire-AllItems-MistdancerIdolofRage-101113"
 value: {
  dps: 123724.87476
  tps: 120566.58833
 }
}
dps_results: {
 key: "TestFire-AllItems-MistdancerStoneofRage-101117"
 value: {
  dps: 123852.42034
  tps: 120774.74493
 }
}
dps_results: {
 key: "TestFire-AllItems-MistdancerStoneofWisdom-101107"
 value: {
  dps: 125659.55223
  tps: 122442.53869
 }
}
dps_results: {
 key: "TestFire-AllItems-MithrilWristwatch-87572"
 value: {
  dps: 124061.79283
  tps: 120821.05152
 }
}
dps_results: {
 key: "TestFire-AllItems-MountainsageIdolofDestruction-101069"
 value: {
  dps: 124509.13748
  tps: 121329.68131
 }
}
dps_results: {
 key: "TestFire-AllItems-MountainsageStoneofDestruction-101072"
 value: {
  dps: 131053.20716
  tps: 127907.86288
 }
}
dps_results: {
 key: "TestFire-AllItems-OathswornDefenderIdol-101303"
 value: {
  dps: 121170.89331
  tps: 118078.14575
 }
}
dps_results: {
 key: "TestFire-AllItems-OathswornDefenderStone-101306"
 value: {
  dps: 122872.184
  tps: 119695.06583
 }
}
dps_results: {
 key: "TestFire-AllItems-OathswornIdolofBattle-101295"
 value: {
  dps: 124068.09355
  tps: 120893.93542
 }
}
dps_results: {
 key: "TestFire-AllItems-OathswornStoneofBattle-101294"
 value: {
  dps: 122351.63037
  tps: 119327.77335
 }
}
dps_results: {
 key: "TestFire-AllItems-PhaseFingers-4697"
 value: {
  dps: 130051.55615
  tps: 126719.33673
 }
}
dps_results: {
 key: "TestFire-AllItems-PouchofWhiteAsh-103639"
 value: {
  dps: 120455.38981
  tps: 117322.10115
 }
}
dps_results: {
 key: "TestFire-AllItems-PowerfulPrimalDiamond"
 value: {
  dps: 126490.29829
  tps: 123212.07869
 }
}
dps_results: {
 key: "TestFire-AllItems-PriceofProgress-81266"
 value: {
  dps: 125293.8212
  tps: 122094.76627
 }
}
dps_results: {
 key: "TestFire-AllItems-PridefulGladiator'sBadgeofConquest-102659"
 value: {
  dps: 120548.54457
  tps: 117420.8753
 }
}
dps_results: {
 key: "TestFire-AllItems-PridefulGladiator'sBadgeofConquest-103342"
 value: {
  dps: 120548.54457
  tps: 117420.8753
 }
}
dps_results: {
 key: "TestFire-AllItems-PridefulGladiator'sBadgeofDominance-102633"
 value: {
  dps: 130053.8101
  tps: 126501.70678
 }
}
dps_results: {
 key: "TestFire-AllItems-PridefulGladiator'sBadgeofDominance-103505"
 value: {
  dps: 130053.8101
  tps: 126501.70678
 }
}
dps_results: {
 key: "TestFire-AllItems-PridefulGladiator'sBadgeofVictory-102636"
 value: {
  dps: 120548.54457
  tps: 117420.8753
 }
}
dps_results: {
 key: "TestFire-AllItems-PridefulGladiator'sBadgeofVictory-103511"
 value: {
  dps: 120548.54457
  tps: 117420.8753
 }
}
dps_results: {
 key: "TestFire-AllItems-PridefulGladiator'sEmblemofCruelty-102680"
 value: {
  dps: 127081.73321
  tps: 123808.83905
 }
}
dps_results: {
 key: "TestFire-AllItems-PridefulGladiator'sEmblemofCruelty-103407"
 value: {
  dps: 127081.73321
  tps: 123808.83905
 }
}
dps_results: {
 key: "TestFire-AllItems-PridefulGladiator'sEmblemofMeditation-102616"
 value: {
  dps: 119483.3036
  tps: 116370.73991
 }
}
dps_results: {
 key: "TestFire-AllItems-PridefulGladiator'sEmblemofMeditation-103409"
 value: {
  dps: 119483.3036
  tps: 116370.73991
 }
}
dps_results: {
 key: "TestFire-AllItems-PridefulGladiator'sEmblemofTenacity-102706"
 value: {
  dps: 119483.3036
  tps: 116370.73991
 }
}
dps_results: {
 key: "TestFire-AllItems-PridefulGladiator'sEmblemofTenacity-103408"
 value: {
  dps: 119483.3036
  tps: 116370.73991
 }
}
dps_results: {
 key: "TestFire-AllItems-PridefulGladiator'sInsigniaofConquest-103347"
 value: {
  dps: 119481.91663
  tps: 116383.50445
 }
}
dps_results: {
 key: "TestFire-AllItems-PridefulGladiator'sInsigniaofDominance-103506"
 value: {
  dps: 134589.65146
  tps: 130922.54663
 }
}
dps_results: {
 key: "TestFire-AllItems-PridefulGladiator'sInsigniaofVictory-103516"
 value: {
  dps: 121205.24243
  tps: 118040.27006
 }
}
dps_results: {
 key: "TestFire-AllItems-Primordius'TalismanofRage-96873"
 value: {
  dps: 126274.65379
  tps: 122985.64326
 }
}
dps_results: {
 key: "TestFire-AllItems-Qian-Le,CourageofNiuzao-102245"
 value: {
  dps: 124059.97269
  tps: 120872.5077
 }
}
dps_results: {
 key: "TestFire-AllItems-Qian-Ying,FortitudeofNiuzao-102250"
 value: {
  dps: 122592.68653
  tps: 119471.02371
 }
}
dps_results: {
 key: "TestFire-AllItems-Qin-xi'sPolarizingSeal-87075"
 value: {
  dps: 120226.50355
  tps: 117097.57236
 }
}
dps_results: {
 key: "TestFire-AllItems-RegaliaoftheBurningScroll"
 value: {
  dps: 114058.17907
  tps: 111082.01992
 }
}
dps_results: {
 key: "TestFire-AllItems-RegaliaoftheChromaticHydra"
 value: {
  dps: 128253.74285
  tps: 124927.39884
 }
}
dps_results: {
 key: "TestFire-AllItems-RelicofChi-Ji-79330"
 value: {
  dps: 126021.51833
  tps: 122786.40576
 }
}
dps_results: {
 key: "TestFire-AllItems-RelicofKypariZar-84075"
 value: {
  dps: 125638.10658
  tps: 122360.68052
 }
}
dps_results: {
 key: "TestFire-AllItems-RelicofNiuzao-79329"
 value: {
  dps: 121856.6605
  tps: 118755.88827
 }
}
dps_results: {
 key: "TestFire-AllItems-RelicofXuen-79327"
 value: {
  dps: 120226.50355
  tps: 117097.57236
 }
}
dps_results: {
 key: "TestFire-AllItems-RelicofXuen-79328"
 value: {
  dps: 120226.50355
  tps: 117097.57236
 }
}
dps_results: {
 key: "TestFire-AllItems-RelicofYu'lon-79331"
 value: {
  dps: 130776.61441
  tps: 127383.99925
 }
}
dps_results: {
 key: "TestFire-AllItems-Renataki'sSoulCharm-96741"
 value: {
  dps: 120226.50355
  tps: 117097.57236
 }
}
dps_results: {
 key: "TestFire-AllItems-ResolveofNiuzao-103690"
 value: {
  dps: 123729.72315
  tps: 120563.16319
 }
}
dps_results: {
 key: "TestFire-AllItems-ResolveofNiuzao-103990"
 value: {
  dps: 124395.1917
  tps: 121239.74142
 }
}
dps_results: {
 key: "TestFire-AllItems-ReverberatingPrimalDiamond"
 value: {
  dps: 129150.6345
  tps: 125849.64938
 }
}
dps_results: {
 key: "TestFire-AllItems-RevitalizingPrimalDiamond"
 value: {
  dps: 129150.6345
  tps: 125849.64938
 }
}
dps_results: {
 key: "TestFire-AllItems-SI:7Operative'sManual-92784"
 value: {
  dps: 120455.38981
  tps: 117322.10115
 }
}
dps_results: {
 key: "TestFire-AllItems-ScrollofReveredAncestors-89080"
 value: {
  dps: 126574.10748
  tps: 123367.87438
 }
}
dps_results: {
 key: "TestFire-AllItems-SearingWords-81267"
 value: {
  dps: 123768.19687
  tps: 120573.29849
 }
}
dps_results: {
 key: "TestFire-AllItems-Shock-ChargerMedallion-93259"
 value: {
  dps: 131213.62274
  tps: 127741.80697
 }
}
dps_results: {
 key: "TestFire-AllItems-SigilofCompassion-83736"
 value: {
  dps: 121738.58933
  tps: 118545.01726
 }
}
dps_results: {
 key: "TestFire-AllItems-SigilofDevotion-83740"
 value: {
  dps: 123149.06862
  tps: 119942.41809
 }
}
dps_results: {
 key: "TestFire-AllItems-SigilofFidelity-83737"
 value: {
  dps: 124675.59094
  tps: 121439.96869
 }
}
dps_results: {
 key: "TestFire-AllItems-SigilofGrace-83738"
 value: {
  dps: 121737.87008
  tps: 118604.64827
 }
}
dps_results: {
 key: "TestFire-AllItems-SigilofKypariZar-84076"
 value: {
  dps: 125647.7259
  tps: 122440.22176
 }
}
dps_results: {
 key: "TestFire-AllItems-SigilofPatience-83739"
 value: {
  dps: 120226.50355
  tps: 117097.57236
 }
}
dps_results: {
 key: "TestFire-AllItems-SigiloftheCatacombs-83732"
 value: {
  dps: 123552.05748
  tps: 120321.06965
 }
}
dps_results: {
 key: "TestFire-AllItems-SinisterPrimalDiamond"
 value: {
  dps: 128284.41943
  tps: 124957.99854
 }
}
dps_results: {
 key: "TestFire-AllItems-SkullrenderMedallion-93256"
 value: {
  dps: 126130.09459
  tps: 122753.83209
 }
}
dps_results: {
 key: "TestFire-AllItems-SpiritsoftheSun-87163"
 value: {
  dps: 126607.06265
  tps: 123359.99783
 }
}
dps_results: {
 key: "TestFire-AllItems-SpringrainIdolofDestruction-101023"
 value: {
  dps: 125064.07329
  tps: 121957.3076
 }
}
dps_results: {
 key: "TestFire-AllItems-SpringrainIdolofRage-101009"
 value: {
  dps: 123981.52864
  tps: 120811.08899
 }
}
dps_results: {
 key: "TestFire-AllItems-SpringrainStoneofDestruction-101026"
 value: {
  dps: 129760.25679
  tps: 126558.99818
 }
}
dps_results: {
 key: "TestFire-AllItems-SpringrainStoneofRage-101012"
 value: {
  dps: 122466.88734
  tps: 119409.85391
 }
}
dps_results: {
 key: "TestFire-AllItems-SpringrainStoneofWisdom-101041"
 value: {
  dps: 125659.55223
  tps: 122442.53869
 }
}
dps_results: {
 key: "TestFire-AllItems-Static-Caster'sMedallion-93254"
 value: {
  dps: 131213.62274
  tps: 127741.80697
 }
}
dps_results: {
 key: "TestFire-AllItems-SteadfastFootman'sMedallion-92782"
 value: {
  dps: 119850.24544
  tps: 116736.21243
 }
}
dps_results: {
 key: "TestFire-AllItems-SteadfastTalismanoftheShado-PanAssault-94507"
 value: {
  dps: 124568.7524
  tps: 121407.13764
 }
}
dps_results: {
 key: "TestFire-AllItems-StreamtalkerIdolofDestruction-101222"
 value: {
  dps: 125316.60569
  tps: 122148.62274
 }
}
dps_results: {
 key: "TestFire-AllItems-StreamtalkerIdolofRage-101217"
 value: {
  dps: 123473.86911
  tps: 120320.84588
 }
}
dps_results: {
 key: "TestFire-AllItems-StreamtalkerStoneofDestruction-101225"
 value: {
  dps: 129066.95965
  tps: 125903.75537
 }
}
dps_results: {
 key: "TestFire-AllItems-StreamtalkerStoneofRage-101220"
 value: {
  dps: 125200.91933
  tps: 122059.2734
 }
}
dps_results: {
 key: "TestFire-AllItems-StreamtalkerStoneofWisdom-101250"
 value: {
  dps: 125659.55223
  tps: 122442.53869
 }
}
dps_results: {
 key: "TestFire-AllItems-StuffofNightmares-87160"
 value: {
  dps: 123582.39501
  tps: 120513.55426
 }
}
dps_results: {
 key: "TestFire-AllItems-SunsoulDefenderIdol-101160"
 value: {
  dps: 118985.44758
  tps: 115901.96164
 }
}
dps_results: {
 key: "TestFire-AllItems-SunsoulDefenderStone-101163"
 value: {
  dps: 122806.23795
  tps: 119750.30346
 }
}
dps_results: {
 key: "TestFire-AllItems-SunsoulIdolofBattle-101152"
 value: {
  dps: 124886.13056
  tps: 121698.46047
 }
}
dps_results: {
 key: "TestFire-AllItems-SunsoulStoneofBattle-101151"
 value: {
  dps: 124061.48758
  tps: 121020.12909
 }
}
dps_results: {
 key: "TestFire-AllItems-SunsoulStoneofWisdom-101138"
 value: {
  dps: 125659.55223
  tps: 122442.53869
 }
}
dps_results: {
 key: "TestFire-AllItems-SwordguardEmbroidery(Rank3)-4894"
 value: {
  dps: 126721.48289
  tps: 123485.93184
 }
}
dps_results: {
 key: "TestFire-AllItems-SymboloftheCatacombs-83735"
 value: {
  dps: 121737.87008
  tps: 118604.64827
 }
}
dps_results: {
 key: "TestFire-AllItems-SynapseSprings(MarkII)-4898"
 value: {
  dps: 132719.39088
  tps: 129283.94184
 }
}
dps_results: {
 key: "TestFire-AllItems-TalismanofBloodlust-96864"
 value: {
  dps: 123253.04617
  tps: 120079.26671
 }
}
dps_results: {
 key: "TestFire-AllItems-TerrorintheMists-87167"
 value: {
  dps: 125366.76866
  tps: 122001.55898
 }
}
dps_results: {
 key: "TestFire-AllItems-Thousand-YearPickledEgg-87573"
 value: {
  dps: 125502.04241
  tps: 122296.21525
 }
}
dps_results: {
 key: "TestFire-AllItems-TrailseekerIdolofRage-101054"
 value: {
  dps: 123153.99149
  tps: 119944.23874
 }
}
dps_results: {
 key: "TestFire-AllItems-TrailseekerStoneofRage-101057"
 value: {
  dps: 123455.16225
  tps: 120392.09815
 }
}
dps_results: {
 key: "TestFire-AllItems-TyrannicalGladiator'sBadgeofConquest-100043"
 value: {
  dps: 120548.54457
  tps: 117420.8753
 }
}
dps_results: {
 key: "TestFire-AllItems-TyrannicalGladiator'sBadgeofConquest-91099"
 value: {
  dps: 120548.54457
  tps: 117420.8753
 }
}
dps_results: {
 key: "TestFire-AllItems-TyrannicalGladiator'sBadgeofConquest-94373"
 value: {
  dps: 120548.54457
  tps: 117420.8753
 }
}
dps_results: {
 key: "TestFire-AllItems-TyrannicalGladiator'sBadgeofConquest-99772"
 value: {
  dps: 120548.54457
  tps: 117420.8753
 }
}
dps_results: {
 key: "TestFire-AllItems-TyrannicalGladiator'sBadgeofDominance-100016"
 value: {
  dps: 126566.97634
  tps: 123189.05787
 }
}
dps_results: {
 key: "TestFire-AllItems-TyrannicalGladiator'sBadgeofDominance-91400"
 value: {
  dps: 126566.97634
  tps: 123189.05787
 }
}
dps_results: {
 key: "TestFire-AllItems-TyrannicalGladiator'sBadgeofDominance-94346"
 value: {
  dps: 126566.97634
  tps: 123189.05787
 }
}
dps_results: {
 key: "TestFire-AllItems-TyrannicalGladiator'sBadgeofDominance-99937"
 value: {
  dps: 126566.97634
  tps: 123189.05787
 }
}
dps_results: {
 key: "TestFire-AllItems-TyrannicalGladiator'sBadgeofVictory-100019"
 value: {
  dps: 120548.54457
  tps: 117420.8753
 }
}
dps_results: {
 key: "TestFire-AllItems-TyrannicalGladiator'sBadgeofVictory-91410"
 value: {
  dps: 120548.54457
  tps: 117420.8753
 }
}
dps_results: {
 key: "TestFire-AllItems-TyrannicalGladiator'sBadgeofVictory-94349"
 value: {
  dps: 120548.54457
  tps: 117420.8753
 }
}
dps_results: {
 key: "TestFire-AllItems-TyrannicalGladiator'sBadgeofVictory-99943"
 value: {
  dps: 120548.54457
  tps: 117420.8753
 }
}
dps_results: {
 key: "TestFire-AllItems-TyrannicalGladiator'sEmblemofCruelty-100066"
 value: {
  dps: 124950.40017
  tps: 121737.83033
 }
}
dps_results: {
 key: "TestFire-AllItems-TyrannicalGladiator'sEmblemofCruelty-91209"
 value: {
  dps: 124950.40017
  tps: 121737.83033
 }
}
dps_results: {
 key: "TestFire-AllItems-TyrannicalGladiator'sEmblemofCruelty-94396"
 value: {
  dps: 124950.40017
  tps: 121737.83033
 }
}
dps_results: {
 key: "TestFire-AllItems-TyrannicalGladiator'sEmblemofCruelty-99838"
 value: {
  dps: 124950.40017
  tps: 121737.83033
 }
}
dps_results: {
 key: "TestFire-AllItems-TyrannicalGladiator'sEmblemofMeditation-91211"
 value: {
  dps: 119483.3036
  tps: 116370.73991
 }
}
dps_results: {
 key: "TestFire-AllItems-TyrannicalGladiator'sEmblemofMeditation-94329"
 value: {
  dps: 119483.3036
  tps: 116370.73991
 }
}
dps_results: {
 key: "TestFire-AllItems-TyrannicalGladiator'sEmblemofMeditation-99840"
 value: {
  dps: 119483.3036
  tps: 116370.73991
 }
}
dps_results: {
 key: "TestFire-AllItems-TyrannicalGladiator'sEmblemofMeditation-99990"
 value: {
  dps: 119483.3036
  tps: 116370.73991
 }
}
dps_results: {
 key: "TestFire-AllItems-TyrannicalGladiator'sEmblemofTenacity-100092"
 value: {
  dps: 119483.3036
  tps: 116370.73991
 }
}
dps_results: {
 key: "TestFire-AllItems-TyrannicalGladiator'sEmblemofTenacity-91210"
 value: {
  dps: 119483.3036
  tps: 116370.73991
 }
}
dps_results: {
 key: "TestFire-AllItems-TyrannicalGladiator'sEmblemofTenacity-94422"
 value: {
  dps: 119483.3036
  tps: 116370.73991
 }
}
dps_results: {
 key: "TestFire-AllItems-TyrannicalGladiator'sEmblemofTenacity-99839"
 value: {
  dps: 119483.3036
  tps: 116370.73991
 }
}
dps_results: {
 key: "TestFire-AllItems-TyrannicalGladiator'sInsigniaofConquest-100026"
 value: {
  dps: 119582.23829
  tps: 116438.10164
 }
}
dps_results: {
 key: "TestFire-AllItems-TyrannicalGladiator'sInsigniaofDominance-100152"
 value: {
  dps: 128259.26679
  tps: 124864.70815
 }
}
dps_results: {
 key: "TestFire-AllItems-TyrannicalGladiator'sInsigniaofVictory-100085"
 value: {
  dps: 120132.56565
  tps: 117017.16201
 }
}
dps_results: {
 key: "TestFire-AllItems-TyrannicalPrimalDiamond"
 value: {
  dps: 126490.29829
  tps: 123212.07869
 }
}
dps_results: {
 key: "TestFire-AllItems-UnerringVisionofLeiShen-96930"
 value: {
  dps: 134581.12683
  tps: 131187.38764
 }
}
dps_results: {
 key: "TestFire-AllItems-VaporshieldMedallion-93262"
 value: {
  dps: 122469.86307
  tps: 119454.72424
 }
}
dps_results: {
 key: "TestFire-AllItems-VialofDragon'sBlood-87063"
 value: {
  dps: 122390.8687
  tps: 119340.55466
 }
}
dps_results: {
 key: "TestFire-AllItems-VialofIchorousBlood-100963"
 value: {
  dps: 125176.90185
  tps: 121986.51026
 }
}
dps_results: {
 key: "TestFire-AllItems-VialofIchorousBlood-81264"
 value: {
  dps: 125625.87883
  tps: 122428.11548
 }
}
dps_results: {
 key: "TestFire-AllItems-ViciousTalismanoftheShado-PanAssault-94511"
 value: {
  dps: 120077.24414
  tps: 116903.77901
 }
}
dps_results: {
 key: "TestFire-AllItems-VisionofthePredator-81192"
 value: {
  dps: 127998.00688
  tps: 124713.80928
 }
}
dps_results: {
 key: "TestFire-AllItems-VolatileTalismanoftheShado-PanAssault-94510"
 value: {
  dps: 130836.94545
  tps: 127581.68777
 }
}
dps_results: {
 key: "TestFire-AllItems-WindsweptPages-81125"
 value: {
  dps: 122255.88101
  tps: 119082.99822
 }
}
dps_results: {
 key: "TestFire-AllItems-WoundripperMedallion-93253"
 value: {
  dps: 126130.09459
  tps: 122753.83209
 }
}
dps_results: {
 key: "TestFire-AllItems-Wushoolay'sFinalChoice-96785"
 value: {
  dps: 156993.99635
  tps: 152984.7811
 }
}
dps_results: {
 key: "TestFire-AllItems-Xing-Ho,BreathofYu'lon-102246"
 value: {
  dps: 167281.20765
  tps: 163923.05513
 }
}
dps_results: {
 key: "TestFire-AllItems-Yu'lon'sBite-103987"
 value: {
  dps: 136846.525
  tps: 133268.5294
 }
}
dps_results: {
 key: "TestFire-AllItems-ZenAlchemistStone-75274"
 value: {
  dps: 127175.77504
  tps: 123829.26065
 }
}
dps_results: {
 key: "TestFire-Average-Default"
 value: {
  dps: 134435.79215
  tps: 130971.22007
 }
}
dps_results: {
 key: "TestFire-Settings-Troll-p1_bis-Fire-fire-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 257382.417
  tps: 253838.72439
 }
}
dps_results: {
 key: "TestFire-Settings-Troll-p1_bis-Fire-fire-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 131365.7342
  tps: 127981.55661
 }
}
dps_results: {
 key: "TestFire-Settings-Troll-p1_bis-Fire-fire-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 197198.73572
  tps: 185392.78266
 }
}
dps_results: {
 key: "TestFire-Settings-Troll-p1_bis-Fire-fire-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 166070.38243
  tps: 164253.52485
 }
}
dps_results: {
 key: "TestFire-Settings-Troll-p1_bis-Fire-fire-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 93215.46794
  tps: 91409.78014
 }
}
dps_results: {
 key: "TestFire-Settings-Troll-p1_bis-Fire-fire-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 111170.84159
  tps: 106666.05326
 }
}
dps_results: {
 key: "TestFire-Settings-Troll-p1_prebis-Fire-fire-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 147843.17593
  tps: 145050.43781
 }
}
dps_results: {
 key: "TestFire-Settings-Troll-p1_prebis-Fire-fire-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 84611.87067
  tps: 81899.37788
 }
}
dps_results: {
 key: "TestFire-Settings-Troll-p1_prebis-Fire-fire-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 147264.39839
  tps: 137787.23703
 }
}
dps_results: {
 key: "TestFire-Settings-Troll-p1_prebis-Fire-fire-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 90698.09544
  tps: 89315.23092
 }
}
dps_results: {
 key: "TestFire-Settings-Troll-p1_prebis-Fire-fire-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 55904.55767
  tps: 54510.88352
 }
}
dps_results: {
 key: "TestFire-Settings-Troll-p1_prebis-Fire-fire-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 69493.84151
  tps: 66084.5079
 }
}
dps_results: {
 key: "TestFire-Settings-Worgen-p1_bis-Fire-fire-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 258981.50852
  tps: 255472.71797
 }
}
dps_results: {
 key: "TestFire-Settings-Worgen-p1_bis-Fire-fire-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 134335.10816
  tps: 131003.76549
 }
}
dps_results: {
 key: "TestFire-Settings-Worgen-p1_bis-Fire-fire-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 199077.39071
  tps: 187559.26697
 }
}
dps_results: {
 key: "TestFire-Settings-Worgen-p1_bis-Fire-fire-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 157494.79406
  tps: 155655.86154
 }
}
dps_results: {
 key: "TestFire-Settings-Worgen-p1_bis-Fire-fire-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 90773.0837
  tps: 88965.15392
 }
}
dps_results: {
 key: "TestFire-Settings-Worgen-p1_bis-Fire-fire-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 108048.5549
  tps: 103601.37302
 }
}
dps_results: {
 key: "TestFire-Settings-Worgen-p1_prebis-Fire-fire-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 139803.75745
  tps: 137221.30548
 }
}
dps_results: {
 key: "TestFire-Settings-Worgen-p1_prebis-Fire-fire-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 82471.33607
  tps: 79915.78205
 }
}
dps_results: {
 key: "TestFire-Settings-Worgen-p1_prebis-Fire-fire-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 138923.73153
  tps: 130250.77324
 }
}
dps_results: {
 key: "TestFire-Settings-Worgen-p1_prebis-Fire-fire-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 87916.0115
  tps: 86572.95411
 }
}
dps_results: {
 key: "TestFire-Settings-Worgen-p1_prebis-Fire-fire-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 55373.45674
  tps: 53981.97109
 }
}
dps_results: {
 key: "TestFire-Settings-Worgen-p1_prebis-Fire-fire-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 66070.39712
  tps: 62698.33309
 }
}
dps_results: {
 key: "TestFire-SwitchInFrontOfTarget-Default"
 value: {
  dps: 131365.7342
  tps: 127981.55661
 }
}
