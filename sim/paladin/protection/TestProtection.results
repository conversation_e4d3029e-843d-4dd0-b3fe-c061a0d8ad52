character_stats_results: {
 key: "TestProtection-CharacterStats-Default"
 value: {
  final_stats: 15877.05
  final_stats: 196.35
  final_stats: 31844.79375
  final_stats: 206.85
  final_stats: 201
  final_stats: 2674
  final_stats: 2111
  final_stats: 10118
  final_stats: 5014
  final_stats: 468.00002
  final_stats: 15072.88137
  final_stats: 4520
  final_stats: 35204.51
  final_stats: 0
  final_stats: 216.535
  final_stats: 0
  final_stats: 0
  final_stats: 57426.6
  final_stats: 0
  final_stats: 592230.1125
  final_stats: 60000
  final_stats: 3000
  final_stats: 7.86471
  final_stats: 22.61176
  final_stats: 13.53797
  final_stats: 11.93997
  final_stats: 28.70139
 }
}
dps_results: {
 key: "TestProtection-AllItems-AgilePrimalDiamond"
 value: {
  dps: 157050.8069
  tps: 1.08703887607e+06
  dtps: 34857.36805
  hps: 30136.80168
 }
}
dps_results: {
 key: "TestProtection-AllItems-AlacrityofXuen-103989"
 value: {
  dps: 160418.44737
  tps: 1.10955108534e+06
  dtps: 32910.00866
  hps: 31560.72581
 }
}
dps_results: {
 key: "TestProtection-AllItems-ArcaneBadgeoftheShieldwall-93347"
 value: {
  dps: 154809.3015
  tps: 1.07112825096e+06
  dtps: 35823.26002
  hps: 29799.47438
 }
}
dps_results: {
 key: "TestProtection-AllItems-ArrowflightMedallion-93258"
 value: {
  dps: 156151.16143
  tps: 1.08066758878e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-AssuranceofConsequence-105472"
 value: {
  dps: 152591.04602
  tps: 1.05688959256e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-AusterePrimalDiamond"
 value: {
  dps: 156259.87784
  tps: 1.08157385213e+06
  dtps: 34431.98727
  hps: 30130.9003
 }
}
dps_results: {
 key: "TestProtection-AllItems-BadJuju-96781"
 value: {
  dps: 152832.77502
  tps: 1.05854130557e+06
  dtps: 34802.93985
  hps: 29333.60219
 }
}
dps_results: {
 key: "TestProtection-AllItems-BadgeofKypariZar-84079"
 value: {
  dps: 154659.46568
  tps: 1.07052536884e+06
  dtps: 36193.84284
  hps: 29341.44541
 }
}
dps_results: {
 key: "TestProtection-AllItems-BattlegearofWingedTriumph"
 value: {
  dps: 152024.71418
  tps: 1.05261644584e+06
  dtps: 30190.79781
  hps: 26460.75717
 }
}
dps_results: {
 key: "TestProtection-AllItems-BattlegearoftheLightningEmperor"
 value: {
  dps: 150469.10622
  tps: 1.04082824752e+06
  dtps: 33467.53067
  hps: 25996.83014
 }
}
dps_results: {
 key: "TestProtection-AllItems-BlossomofPureSnow-89081"
 value: {
  dps: 155955.15485
  tps: 1.07929554272e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-BottleofInfiniteStars-87057"
 value: {
  dps: 152652.86202
  tps: 1.05728191451e+06
  dtps: 35385.86473
  hps: 29333.6942
 }
}
dps_results: {
 key: "TestProtection-AllItems-BraidofTenSongs-84072"
 value: {
  dps: 154544.28801
  tps: 1.07017084928e+06
  dtps: 35577.49569
  hps: 29778.02658
 }
}
dps_results: {
 key: "TestProtection-AllItems-Brawler'sStatue-87571"
 value: {
  dps: 153585.37139
  tps: 1.06346379452e+06
  dtps: 35303.2389
  hps: 29427.14083
 }
}
dps_results: {
 key: "TestProtection-AllItems-BreathoftheHydra-96827"
 value: {
  dps: 158181.09578
  tps: 1.09557641632e+06
  dtps: 34884.41654
  hps: 30408.0306
 }
}
dps_results: {
 key: "TestProtection-AllItems-BroochofMunificentDeeds-87500"
 value: {
  dps: 152296.86179
  tps: 1.05487825454e+06
  dtps: 36749.38594
  hps: 29336.04807
 }
}
dps_results: {
 key: "TestProtection-AllItems-BrutalTalismanoftheShado-PanAssault-94508"
 value: {
  dps: 158681.45415
  tps: 1.09749523402e+06
  dtps: 34919.33762
  hps: 30342.21485
 }
}
dps_results: {
 key: "TestProtection-AllItems-BurningPrimalDiamond"
 value: {
  dps: 157056.54879
  tps: 1.08707906934e+06
  dtps: 34857.36805
  hps: 30136.80168
 }
}
dps_results: {
 key: "TestProtection-AllItems-CapacitivePrimalDiamond"
 value: {
  dps: 157060.16523
  tps: 1.08706707231e+06
  dtps: 34857.36805
  hps: 30136.80168
 }
}
dps_results: {
 key: "TestProtection-AllItems-CarbonicCarbuncle-81138"
 value: {
  dps: 157818.68521
  tps: 1.09257127612e+06
  dtps: 35932.06558
  hps: 29695.91326
 }
}
dps_results: {
 key: "TestProtection-AllItems-Cha-Ye'sEssenceofBrilliance-96888"
 value: {
  dps: 157439.39033
  tps: 1.09002533139e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-CharmofTenSongs-84071"
 value: {
  dps: 156326.50393
  tps: 1.08230083763e+06
  dtps: 36218.60772
  hps: 29683.5948
 }
}
dps_results: {
 key: "TestProtection-AllItems-CommunalIdolofDestruction-101168"
 value: {
  dps: 154716.63912
  tps: 1.07174394426e+06
  dtps: 35378.43612
  hps: 29734.51449
 }
}
dps_results: {
 key: "TestProtection-AllItems-CommunalStoneofDestruction-101171"
 value: {
  dps: 152589.25143
  tps: 1.0568766304e+06
  dtps: 34795.61165
  hps: 29345.29753
 }
}
dps_results: {
 key: "TestProtection-AllItems-CommunalStoneofWisdom-101183"
 value: {
  dps: 152526.6026
  tps: 1.05643848861e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-ContemplationofChi-Ji-103688"
 value: {
  dps: 152528.6146
  tps: 1.0564525726e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-ContemplationofChi-Ji-103988"
 value: {
  dps: 152662.12011
  tps: 1.05728003364e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-Coren'sColdChromiumCoaster-87574"
 value: {
  dps: 156176.71602
  tps: 1.08153564052e+06
  dtps: 36775.62476
  hps: 29582.14596
 }
}
dps_results: {
 key: "TestProtection-AllItems-CoreofDecency-87497"
 value: {
  dps: 152376.59289
  tps: 1.05538842061e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-CourageousPrimalDiamond"
 value: {
  dps: 156199.66024
  tps: 1.08115250392e+06
  dtps: 34857.36805
  hps: 30136.80168
 }
}
dps_results: {
 key: "TestProtection-AllItems-CraftedDreadfulGladiator'sBadgeofConquest-93419"
 value: {
  dps: 152538.87821
  tps: 1.05652441786e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-CraftedDreadfulGladiator'sBadgeofDominance-93600"
 value: {
  dps: 152655.93963
  tps: 1.05713096424e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-CraftedDreadfulGladiator'sBadgeofVictory-93606"
 value: {
  dps: 155101.38147
  tps: 1.0740184916e+06
  dtps: 35956.34972
  hps: 29701.46262
 }
}
dps_results: {
 key: "TestProtection-AllItems-CraftedDreadfulGladiator'sEmblemofCruelty-93485"
 value: {
  dps: 154616.60342
  tps: 1.0707229639e+06
  dtps: 36927.92905
  hps: 29348.47345
 }
}
dps_results: {
 key: "TestProtection-AllItems-CraftedDreadfulGladiator'sEmblemofMeditation-93487"
 value: {
  dps: 152376.00395
  tps: 1.05538429809e+06
  dtps: 36927.92905
  hps: 29348.47345
 }
}
dps_results: {
 key: "TestProtection-AllItems-CraftedDreadfulGladiator'sEmblemofTenacity-93486"
 value: {
  dps: 152288.17508
  tps: 1.05481744752e+06
  dtps: 36759.77452
  hps: 29335.51803
 }
}
dps_results: {
 key: "TestProtection-AllItems-CraftedDreadfulGladiator'sInsigniaofConquest-93424"
 value: {
  dps: 152523.41624
  tps: 1.05641618406e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-CraftedDreadfulGladiator'sInsigniaofDominance-93601"
 value: {
  dps: 152671.12177
  tps: 1.05710459234e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-CraftedDreadfulGladiator'sInsigniaofVictory-93611"
 value: {
  dps: 156041.34195
  tps: 1.08047334055e+06
  dtps: 35693.57335
  hps: 29855.03149
 }
}
dps_results: {
 key: "TestProtection-AllItems-CraftedMalevolentGladiator'sBadgeofConquest-98755"
 value: {
  dps: 152542.47436
  tps: 1.05654959091e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-CraftedMalevolentGladiator'sBadgeofDominance-98910"
 value: {
  dps: 152659.30073
  tps: 1.05715449197e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-CraftedMalevolentGladiator'sBadgeofVictory-98912"
 value: {
  dps: 155326.1448
  tps: 1.07551098145e+06
  dtps: 35867.0618
  hps: 29745.2106
 }
}
dps_results: {
 key: "TestProtection-AllItems-CraftedMalevolentGladiator'sEmblemofCruelty-98811"
 value: {
  dps: 155130.13067
  tps: 1.07431765465e+06
  dtps: 36927.92905
  hps: 29348.47345
 }
}
dps_results: {
 key: "TestProtection-AllItems-CraftedMalevolentGladiator'sEmblemofMeditation-98813"
 value: {
  dps: 152376.00395
  tps: 1.05538429809e+06
  dtps: 36927.92905
  hps: 29348.47345
 }
}
dps_results: {
 key: "TestProtection-AllItems-CraftedMalevolentGladiator'sEmblemofTenacity-98812"
 value: {
  dps: 152304.4938
  tps: 1.05493167855e+06
  dtps: 36785.78798
  hps: 29336.23374
 }
}
dps_results: {
 key: "TestProtection-AllItems-CraftedMalevolentGladiator'sInsigniaofConquest-98760"
 value: {
  dps: 152456.61317
  tps: 1.05594856258e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-CraftedMalevolentGladiator'sInsigniaofDominance-98911"
 value: {
  dps: 152705.50714
  tps: 1.05745109596e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-CraftedMalevolentGladiator'sInsigniaofVictory-98917"
 value: {
  dps: 156081.59987
  tps: 1.08036717133e+06
  dtps: 35309.81799
  hps: 30002.60203
 }
}
dps_results: {
 key: "TestProtection-AllItems-CurseofHubris-102307"
 value: {
  dps: 158823.19692
  tps: 1.09755374253e+06
  dtps: 36473.83925
  hps: 29338.46793
 }
}
dps_results: {
 key: "TestProtection-AllItems-CurseofHubris-104649"
 value: {
  dps: 159837.1124
  tps: 1.10439408245e+06
  dtps: 36485.83385
  hps: 29343.0623
 }
}
dps_results: {
 key: "TestProtection-AllItems-CurseofHubris-104898"
 value: {
  dps: 158063.90354
  tps: 1.09259018704e+06
  dtps: 36562.09016
  hps: 29335.9105
 }
}
dps_results: {
 key: "TestProtection-AllItems-CurseofHubris-105147"
 value: {
  dps: 157495.85875
  tps: 1.08888887101e+06
  dtps: 36558.68097
  hps: 29333.61268
 }
}
dps_results: {
 key: "TestProtection-AllItems-CurseofHubris-105396"
 value: {
  dps: 159344.32646
  tps: 1.1010737835e+06
  dtps: 36473.66845
  hps: 29338.72412
 }
}
dps_results: {
 key: "TestProtection-AllItems-CurseofHubris-105645"
 value: {
  dps: 160202.38569
  tps: 1.10685611674e+06
  dtps: 36559.241
  hps: 29342.11653
 }
}
dps_results: {
 key: "TestProtection-AllItems-CutstitcherMedallion-93255"
 value: {
  dps: 152528.6146
  tps: 1.0564525726e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-Daelo'sFinalWords-87496"
 value: {
  dps: 155261.12197
  tps: 1.07397362543e+06
  dtps: 35753.96417
  hps: 29796.83197
 }
}
dps_results: {
 key: "TestProtection-AllItems-DarkglowEmbroidery(Rank3)-4893"
 value: {
  dps: 157030.8896
  tps: 1.08685164155e+06
  dtps: 34908.15539
  hps: 30216.29146
 }
}
dps_results: {
 key: "TestProtection-AllItems-DarkmistVortex-87172"
 value: {
  dps: 159012.23175
  tps: 1.09908956674e+06
  dtps: 33620.17809
  hps: 31110.46168
 }
}
dps_results: {
 key: "TestProtection-AllItems-DeadeyeBadgeoftheShieldwall-93346"
 value: {
  dps: 152438.49765
  tps: 1.05582175397e+06
  dtps: 35615.50418
  hps: 29333.36039
 }
}
dps_results: {
 key: "TestProtection-AllItems-DelicateVialoftheSanguinaire-96895"
 value: {
  dps: 152630.72964
  tps: 1.05707986763e+06
  dtps: 35242.66254
  hps: 29360.88359
 }
}
dps_results: {
 key: "TestProtection-AllItems-DestructivePrimalDiamond"
 value: {
  dps: 157352.89579
  tps: 1.08911618623e+06
  dtps: 34857.36805
  hps: 30136.80168
 }
}
dps_results: {
 key: "TestProtection-AllItems-DisciplineofXuen-103986"
 value: {
  dps: 152777.61573
  tps: 1.05819578054e+06
  dtps: 32886.53247
  hps: 29341.04233
 }
}
dps_results: {
 key: "TestProtection-AllItems-Dominator'sArcaneBadge-93342"
 value: {
  dps: 154809.3015
  tps: 1.07112825096e+06
  dtps: 35823.26002
  hps: 29799.47438
 }
}
dps_results: {
 key: "TestProtection-AllItems-Dominator'sDeadeyeBadge-93341"
 value: {
  dps: 152438.49765
  tps: 1.05582175397e+06
  dtps: 35615.50418
  hps: 29333.36039
 }
}
dps_results: {
 key: "TestProtection-AllItems-Dominator'sDurableBadge-93345"
 value: {
  dps: 152475.38569
  tps: 1.0561538894e+06
  dtps: 34585.94177
  hps: 29356.72437
 }
}
dps_results: {
 key: "TestProtection-AllItems-Dominator'sKnightlyBadge-93344"
 value: {
  dps: 154225.12461
  tps: 1.06815212737e+06
  dtps: 34747.35385
  hps: 29673.92017
 }
}
dps_results: {
 key: "TestProtection-AllItems-Dominator'sMendingBadge-93343"
 value: {
  dps: 152501.15254
  tps: 1.05626033817e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-DreadfulGladiator'sBadgeofConquest-84344"
 value: {
  dps: 152538.87821
  tps: 1.05652441786e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-DreadfulGladiator'sBadgeofDominance-84488"
 value: {
  dps: 152655.93963
  tps: 1.05713096424e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-DreadfulGladiator'sBadgeofVictory-84490"
 value: {
  dps: 155101.38147
  tps: 1.0740184916e+06
  dtps: 35956.34972
  hps: 29701.46262
 }
}
dps_results: {
 key: "TestProtection-AllItems-DreadfulGladiator'sEmblemofCruelty-84399"
 value: {
  dps: 154616.60342
  tps: 1.0707229639e+06
  dtps: 36927.92905
  hps: 29348.47345
 }
}
dps_results: {
 key: "TestProtection-AllItems-DreadfulGladiator'sEmblemofMeditation-84401"
 value: {
  dps: 152376.00395
  tps: 1.05538429809e+06
  dtps: 36927.92905
  hps: 29348.47345
 }
}
dps_results: {
 key: "TestProtection-AllItems-DreadfulGladiator'sEmblemofTenacity-84400"
 value: {
  dps: 152442.37415
  tps: 1.05580839943e+06
  dtps: 36016.28622
  hps: 29337.14241
 }
}
dps_results: {
 key: "TestProtection-AllItems-DreadfulGladiator'sInsigniaofConquest-84349"
 value: {
  dps: 152472.22144
  tps: 1.05605782049e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-DreadfulGladiator'sInsigniaofDominance-84489"
 value: {
  dps: 152656.24913
  tps: 1.05710628991e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-DreadfulGladiator'sInsigniaofVictory-84495"
 value: {
  dps: 155821.36872
  tps: 1.07900155812e+06
  dtps: 35809.21817
  hps: 29854.02954
 }
}
dps_results: {
 key: "TestProtection-AllItems-DurableBadgeoftheShieldwall-93350"
 value: {
  dps: 152475.38569
  tps: 1.0561538894e+06
  dtps: 34585.94177
  hps: 29356.72437
 }
}
dps_results: {
 key: "TestProtection-AllItems-EffulgentPrimalDiamond"
 value: {
  dps: 156319.20259
  tps: 1.08199000033e+06
  dtps: 34882.26653
  hps: 30138.23618
 }
}
dps_results: {
 key: "TestProtection-AllItems-EmberPrimalDiamond"
 value: {
  dps: 156199.66024
  tps: 1.08115250392e+06
  dtps: 34857.36805
  hps: 30136.80168
 }
}
dps_results: {
 key: "TestProtection-AllItems-EmblemofKypariZar-84077"
 value: {
  dps: 157222.36953
  tps: 1.08906321476e+06
  dtps: 36237.18635
  hps: 29805.73388
 }
}
dps_results: {
 key: "TestProtection-AllItems-EmblemoftheCatacombs-83733"
 value: {
  dps: 154499.78411
  tps: 1.07016886887e+06
  dtps: 34846.01343
  hps: 29719.17128
 }
}
dps_results: {
 key: "TestProtection-AllItems-EmptyFruitBarrel-81133"
 value: {
  dps: 152376.59289
  tps: 1.05538842061e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-EnchantWeapon-BloodyDancingSteel-5125"
 value: {
  dps: 157583.43504
  tps: 1.09072179962e+06
  dtps: 34817.49357
  hps: 30258.47206
 }
}
dps_results: {
 key: "TestProtection-AllItems-EnchantWeapon-Colossus-4445"
 value: {
  dps: 155759.99928
  tps: 1.07865018965e+06
  dtps: 34647.48502
  hps: 29808.72048
 }
}
dps_results: {
 key: "TestProtection-AllItems-EnchantWeapon-JadeSpirit-4442"
 value: {
  dps: 155966.70799
  tps: 1.0799855325e+06
  dtps: 35557.53904
  hps: 29815.91735
 }
}
dps_results: {
 key: "TestProtection-AllItems-EnchantWeapon-River'sSong-4446"
 value: {
  dps: 155690.3007
  tps: 1.07774609386e+06
  dtps: 34776.73392
  hps: 29819.19528
 }
}
dps_results: {
 key: "TestProtection-AllItems-EnchantWeapon-SpiritofConquest-5124"
 value: {
  dps: 155797.27296
  tps: 1.07891120538e+06
  dtps: 35557.53904
  hps: 29815.91735
 }
}
dps_results: {
 key: "TestProtection-AllItems-EnchantWeapon-Windsong-4441"
 value: {
  dps: 156625.86626
  tps: 1.08436145204e+06
  dtps: 35199.4208
  hps: 29947.56398
 }
}
dps_results: {
 key: "TestProtection-AllItems-EnigmaticPrimalDiamond"
 value: {
  dps: 157352.89579
  tps: 1.08911618623e+06
  dtps: 34857.36805
  hps: 30136.80168
 }
}
dps_results: {
 key: "TestProtection-AllItems-EssenceofTerror-87175"
 value: {
  dps: 154767.39719
  tps: 1.07199264849e+06
  dtps: 36298.00639
  hps: 29998.27038
 }
}
dps_results: {
 key: "TestProtection-AllItems-EternalPrimalDiamond"
 value: {
  dps: 156203.92229
  tps: 1.08118248827e+06
  dtps: 34736.67803
  hps: 30128.6781
 }
}
dps_results: {
 key: "TestProtection-AllItems-EvilEyeofGalakras-105491"
 value: {
  dps: 159115.83616
  tps: 1.10150784694e+06
  dtps: 32512.60907
  hps: 30556.76517
 }
}
dps_results: {
 key: "TestProtection-AllItems-FabledFeatherofJi-Kun-96842"
 value: {
  dps: 166209.69799
  tps: 1.15148062189e+06
  dtps: 31430.69014
  hps: 32200.72674
 }
}
dps_results: {
 key: "TestProtection-AllItems-FearwurmBadge-84074"
 value: {
  dps: 156311.97294
  tps: 1.082433101e+06
  dtps: 36285.61383
  hps: 29793.19928
 }
}
dps_results: {
 key: "TestProtection-AllItems-FearwurmRelic-84070"
 value: {
  dps: 156583.02993
  tps: 1.08458420759e+06
  dtps: 36300.00825
  hps: 29816.95097
 }
}
dps_results: {
 key: "TestProtection-AllItems-FelsoulIdolofDestruction-101263"
 value: {
  dps: 154889.29059
  tps: 1.07283133385e+06
  dtps: 34865.55935
  hps: 29780.52271
 }
}
dps_results: {
 key: "TestProtection-AllItems-FelsoulStoneofDestruction-101266"
 value: {
  dps: 152684.54383
  tps: 1.05754397723e+06
  dtps: 34476.70153
  hps: 29342.25026
 }
}
dps_results: {
 key: "TestProtection-AllItems-Fen-Yu,FuryofXuen-102248"
 value: {
  dps: 165873.08112
  tps: 1.14927797489e+06
  dtps: 33292.57225
  hps: 30069.20573
 }
}
dps_results: {
 key: "TestProtection-AllItems-FlashfrozenResinGlobule-100951"
 value: {
  dps: 152583.12051
  tps: 1.05648858351e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-FlashfrozenResinGlobule-81263"
 value: {
  dps: 152589.6745
  tps: 1.05648858351e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-FlashingSteelTalisman-81265"
 value: {
  dps: 152557.2103
  tps: 1.05665274249e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-FleetPrimalDiamond"
 value: {
  dps: 156210.52173
  tps: 1.08118483734e+06
  dtps: 34344.51307
  hps: 30135.32668
 }
}
dps_results: {
 key: "TestProtection-AllItems-ForlornPrimalDiamond"
 value: {
  dps: 156199.66024
  tps: 1.08115250392e+06
  dtps: 34857.36805
  hps: 30136.80168
 }
}
dps_results: {
 key: "TestProtection-AllItems-FortitudeoftheZandalari-94516"
 value: {
  dps: 152503.01632
  tps: 1.05623299467e+06
  dtps: 35104.02603
  hps: 29333.63126
 }
}
dps_results: {
 key: "TestProtection-AllItems-FortitudeoftheZandalari-95677"
 value: {
  dps: 152482.85711
  tps: 1.05609188016e+06
  dtps: 35385.86473
  hps: 29333.6942
 }
}
dps_results: {
 key: "TestProtection-AllItems-FortitudeoftheZandalari-96049"
 value: {
  dps: 152513.99976
  tps: 1.05630987873e+06
  dtps: 34995.07766
  hps: 29333.60219
 }
}
dps_results: {
 key: "TestProtection-AllItems-FortitudeoftheZandalari-96421"
 value: {
  dps: 152519.49093
  tps: 1.05634831691e+06
  dtps: 34882.14634
  hps: 29333.60219
 }
}
dps_results: {
 key: "TestProtection-AllItems-FortitudeoftheZandalari-96793"
 value: {
  dps: 152519.49093
  tps: 1.05634831691e+06
  dtps: 34802.93985
  hps: 29333.60219
 }
}
dps_results: {
 key: "TestProtection-AllItems-GazeoftheTwins-96915"
 value: {
  dps: 159531.12205
  tps: 1.10493764246e+06
  dtps: 34708.25575
  hps: 30138.94454
 }
}
dps_results: {
 key: "TestProtection-AllItems-Gerp'sPerfectArrow-87495"
 value: {
  dps: 154670.55072
  tps: 1.07110059502e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-Gladiator'sVindication"
 value: {
  dps: 165518.7869
  tps: 1.145816423e+06
  dtps: 29773.4485
  hps: 28405.83676
 }
}
dps_results: {
 key: "TestProtection-AllItems-Gong-Lu,StrengthofXuen-102249"
 value: {
  dps: 170392.7665
  tps: 1.18042244734e+06
  dtps: 31991.84832
  hps: 30870.88937
 }
}
dps_results: {
 key: "TestProtection-AllItems-GrievousGladiator'sBadgeofConquest-100195"
 value: {
  dps: 152588.15308
  tps: 1.056869342e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-GrievousGladiator'sBadgeofConquest-100603"
 value: {
  dps: 152588.15308
  tps: 1.056869342e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-GrievousGladiator'sBadgeofConquest-102856"
 value: {
  dps: 152588.15308
  tps: 1.056869342e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-GrievousGladiator'sBadgeofConquest-103145"
 value: {
  dps: 152588.15308
  tps: 1.056869342e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-GrievousGladiator'sBadgeofDominance-100490"
 value: {
  dps: 152760.55811
  tps: 1.05773064673e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-GrievousGladiator'sBadgeofDominance-100576"
 value: {
  dps: 152760.55811
  tps: 1.05773064673e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-GrievousGladiator'sBadgeofDominance-102830"
 value: {
  dps: 152760.55811
  tps: 1.05773064673e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-GrievousGladiator'sBadgeofDominance-103308"
 value: {
  dps: 152760.55811
  tps: 1.05773064673e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-GrievousGladiator'sBadgeofVictory-100500"
 value: {
  dps: 156600.20483
  tps: 1.08381853894e+06
  dtps: 35150.71506
  hps: 30029.0468
 }
}
dps_results: {
 key: "TestProtection-AllItems-GrievousGladiator'sBadgeofVictory-100579"
 value: {
  dps: 156600.20483
  tps: 1.08381853894e+06
  dtps: 35150.71506
  hps: 30029.0468
 }
}
dps_results: {
 key: "TestProtection-AllItems-GrievousGladiator'sBadgeofVictory-102833"
 value: {
  dps: 156600.20483
  tps: 1.08381853894e+06
  dtps: 35150.71506
  hps: 30029.0468
 }
}
dps_results: {
 key: "TestProtection-AllItems-GrievousGladiator'sBadgeofVictory-103314"
 value: {
  dps: 156600.20483
  tps: 1.08381853894e+06
  dtps: 35150.71506
  hps: 30029.0468
 }
}
dps_results: {
 key: "TestProtection-AllItems-GrievousGladiator'sEmblemofCruelty-100305"
 value: {
  dps: 156619.29119
  tps: 1.08455747447e+06
  dtps: 36927.92905
  hps: 29348.47345
 }
}
dps_results: {
 key: "TestProtection-AllItems-GrievousGladiator'sEmblemofCruelty-100626"
 value: {
  dps: 156619.29119
  tps: 1.08455747447e+06
  dtps: 36927.92905
  hps: 29348.47345
 }
}
dps_results: {
 key: "TestProtection-AllItems-GrievousGladiator'sEmblemofCruelty-102877"
 value: {
  dps: 156619.29119
  tps: 1.08455747447e+06
  dtps: 36927.92905
  hps: 29348.47345
 }
}
dps_results: {
 key: "TestProtection-AllItems-GrievousGladiator'sEmblemofCruelty-103210"
 value: {
  dps: 156619.29119
  tps: 1.08455747447e+06
  dtps: 36927.92905
  hps: 29348.47345
 }
}
dps_results: {
 key: "TestProtection-AllItems-GrievousGladiator'sEmblemofMeditation-100307"
 value: {
  dps: 152376.00395
  tps: 1.05538429809e+06
  dtps: 36927.92905
  hps: 29348.47345
 }
}
dps_results: {
 key: "TestProtection-AllItems-GrievousGladiator'sEmblemofMeditation-100559"
 value: {
  dps: 152376.00395
  tps: 1.05538429809e+06
  dtps: 36927.92905
  hps: 29348.47345
 }
}
dps_results: {
 key: "TestProtection-AllItems-GrievousGladiator'sEmblemofMeditation-102813"
 value: {
  dps: 152376.00395
  tps: 1.05538429809e+06
  dtps: 36927.92905
  hps: 29348.47345
 }
}
dps_results: {
 key: "TestProtection-AllItems-GrievousGladiator'sEmblemofMeditation-103212"
 value: {
  dps: 152376.00395
  tps: 1.05538429809e+06
  dtps: 36927.92905
  hps: 29348.47345
 }
}
dps_results: {
 key: "TestProtection-AllItems-GrievousGladiator'sEmblemofTenacity-100306"
 value: {
  dps: 152256.36034
  tps: 1.05459449438e+06
  dtps: 36693.38134
  hps: 29337.92632
 }
}
dps_results: {
 key: "TestProtection-AllItems-GrievousGladiator'sEmblemofTenacity-100652"
 value: {
  dps: 152256.36034
  tps: 1.05459449438e+06
  dtps: 36693.38134
  hps: 29337.92632
 }
}
dps_results: {
 key: "TestProtection-AllItems-GrievousGladiator'sEmblemofTenacity-102903"
 value: {
  dps: 152256.36034
  tps: 1.05459449438e+06
  dtps: 36693.38134
  hps: 29337.92632
 }
}
dps_results: {
 key: "TestProtection-AllItems-GrievousGladiator'sEmblemofTenacity-103211"
 value: {
  dps: 152256.36034
  tps: 1.05459449438e+06
  dtps: 36693.38134
  hps: 29337.92632
 }
}
dps_results: {
 key: "TestProtection-AllItems-GrievousGladiator'sInsigniaofConquest-103150"
 value: {
  dps: 152647.72693
  tps: 1.05728635894e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-GrievousGladiator'sInsigniaofDominance-103309"
 value: {
  dps: 152843.72962
  tps: 1.05816869896e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-GrievousGladiator'sInsigniaofVictory-103319"
 value: {
  dps: 157943.86611
  tps: 1.0932849238e+06
  dtps: 34589.54783
  hps: 30321.57726
 }
}
dps_results: {
 key: "TestProtection-AllItems-Hawkmaster'sTalon-89082"
 value: {
  dps: 156383.84038
  tps: 1.08127461594e+06
  dtps: 36083.91666
  hps: 29987.72679
 }
}
dps_results: {
 key: "TestProtection-AllItems-Heart-LesionDefenderIdol-100999"
 value: {
  dps: 153192.98274
  tps: 1.06077038131e+06
  dtps: 35610.22825
  hps: 29436.20282
 }
}
dps_results: {
 key: "TestProtection-AllItems-Heart-LesionDefenderStone-101002"
 value: {
  dps: 152382.74234
  tps: 1.05543050178e+06
  dtps: 34418.1659
  hps: 29338.5492
 }
}
dps_results: {
 key: "TestProtection-AllItems-Heart-LesionIdolofBattle-100991"
 value: {
  dps: 158603.28614
  tps: 1.09800853751e+06
  dtps: 35493.42348
  hps: 29897.49943
 }
}
dps_results: {
 key: "TestProtection-AllItems-Heart-LesionStoneofBattle-100990"
 value: {
  dps: 154702.32974
  tps: 1.07144685773e+06
  dtps: 33576.39213
  hps: 29769.76401
 }
}
dps_results: {
 key: "TestProtection-AllItems-HeartofFire-81181"
 value: {
  dps: 153337.67012
  tps: 1.06162563923e+06
  dtps: 34997.03864
  hps: 29400.68776
 }
}
dps_results: {
 key: "TestProtection-AllItems-HeartwarmerMedallion-93260"
 value: {
  dps: 152528.6146
  tps: 1.0564525726e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-HelmbreakerMedallion-93261"
 value: {
  dps: 158470.6627
  tps: 1.09663008959e+06
  dtps: 35720.7091
  hps: 29818.36935
 }
}
dps_results: {
 key: "TestProtection-AllItems-Horridon'sLastGasp-96757"
 value: {
  dps: 152724.55434
  tps: 1.05771707327e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-ImpassivePrimalDiamond"
 value: {
  dps: 157352.89579
  tps: 1.08911618623e+06
  dtps: 34857.36805
  hps: 30136.80168
 }
}
dps_results: {
 key: "TestProtection-AllItems-IndomitablePrimalDiamond"
 value: {
  dps: 156319.20259
  tps: 1.08199000033e+06
  dtps: 34882.26653
  hps: 30138.23618
 }
}
dps_results: {
 key: "TestProtection-AllItems-InscribedBagofHydra-Spawn-96828"
 value: {
  dps: 152376.59289
  tps: 1.05538842061e+06
  dtps: 36916.78038
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-InsigniaofKypariZar-84078"
 value: {
  dps: 153111.71413
  tps: 1.06038841836e+06
  dtps: 35819.96609
  hps: 29422.91256
 }
}
dps_results: {
 key: "TestProtection-AllItems-IronBellyWok-89083"
 value: {
  dps: 158914.87649
  tps: 1.09871200182e+06
  dtps: 34697.94058
  hps: 30448.71704
 }
}
dps_results: {
 key: "TestProtection-AllItems-IronProtectorTalisman-85181"
 value: {
  dps: 153157.22909
  tps: 1.06045279353e+06
  dtps: 35369.18852
  hps: 29445.0489
 }
}
dps_results: {
 key: "TestProtection-AllItems-JadeBanditFigurine-86043"
 value: {
  dps: 156383.84038
  tps: 1.08127461594e+06
  dtps: 36083.91666
  hps: 29987.72679
 }
}
dps_results: {
 key: "TestProtection-AllItems-JadeBanditFigurine-86772"
 value: {
  dps: 155568.16595
  tps: 1.07622688297e+06
  dtps: 35803.79891
  hps: 29843.55468
 }
}
dps_results: {
 key: "TestProtection-AllItems-JadeCharioteerFigurine-86042"
 value: {
  dps: 158914.87649
  tps: 1.09871200182e+06
  dtps: 34697.94058
  hps: 30448.71704
 }
}
dps_results: {
 key: "TestProtection-AllItems-JadeCharioteerFigurine-86771"
 value: {
  dps: 157501.86237
  tps: 1.089527912e+06
  dtps: 34843.43761
  hps: 30294.40877
 }
}
dps_results: {
 key: "TestProtection-AllItems-JadeCourtesanFigurine-86045"
 value: {
  dps: 152528.6146
  tps: 1.0564525726e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-JadeCourtesanFigurine-86774"
 value: {
  dps: 152520.64309
  tps: 1.05639677208e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-JadeMagistrateFigurine-86044"
 value: {
  dps: 155955.15485
  tps: 1.07929554272e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-JadeMagistrateFigurine-86773"
 value: {
  dps: 155461.36858
  tps: 1.07606087764e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-JadeWarlordFigurine-86046"
 value: {
  dps: 152276.76821
  tps: 1.05468878287e+06
  dtps: 35037.87115
  hps: 29325.17686
 }
}
dps_results: {
 key: "TestProtection-AllItems-JadeWarlordFigurine-86775"
 value: {
  dps: 152341.54183
  tps: 1.05514227322e+06
  dtps: 35186.45469
  hps: 29322.43909
 }
}
dps_results: {
 key: "TestProtection-AllItems-Ji-Kun'sRisingWinds-96843"
 value: {
  dps: 152782.48052
  tps: 1.05822967902e+06
  dtps: 37041.65411
  hps: 30182.90718
 }
}
dps_results: {
 key: "TestProtection-AllItems-KnightlyBadgeoftheShieldwall-93349"
 value: {
  dps: 154225.12461
  tps: 1.06815212737e+06
  dtps: 34747.35385
  hps: 29673.92017
 }
}
dps_results: {
 key: "TestProtection-AllItems-KnotofTenSongs-84073"
 value: {
  dps: 153447.47311
  tps: 1.06273980621e+06
  dtps: 35719.41835
  hps: 29400.41882
 }
}
dps_results: {
 key: "TestProtection-AllItems-Kor'kronBookofHurting-92785"
 value: {
  dps: 152376.59289
  tps: 1.05538842061e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-Lao-Chin'sLiquidCourage-89079"
 value: {
  dps: 152276.76821
  tps: 1.05468878287e+06
  dtps: 35037.87115
  hps: 29325.17686
 }
}
dps_results: {
 key: "TestProtection-AllItems-LessonsoftheDarkmaster-81268"
 value: {
  dps: 155770.05002
  tps: 1.07788094851e+06
  dtps: 36022.59099
  hps: 29953.63255
 }
}
dps_results: {
 key: "TestProtection-AllItems-LightdrinkerIdolofRage-101200"
 value: {
  dps: 152587.7088
  tps: 1.05682584202e+06
  dtps: 35630.78871
  hps: 29334.95384
 }
}
dps_results: {
 key: "TestProtection-AllItems-LightdrinkerStoneofRage-101203"
 value: {
  dps: 152545.00054
  tps: 1.05656697418e+06
  dtps: 34534.28571
  hps: 29341.84891
 }
}
dps_results: {
 key: "TestProtection-AllItems-LightoftheCosmos-87065"
 value: {
  dps: 156303.84025
  tps: 1.08284417011e+06
  dtps: 35675.32726
  hps: 30067.71655
 }
}
dps_results: {
 key: "TestProtection-AllItems-LightweaveEmbroidery(Rank3)-4892"
 value: {
  dps: 157088.91436
  tps: 1.08725781484e+06
  dtps: 34908.15539
  hps: 30216.29146
 }
}
dps_results: {
 key: "TestProtection-AllItems-LordBlastington'sScopeofDoom-4699"
 value: {
  dps: 155797.27296
  tps: 1.07891120538e+06
  dtps: 35557.53904
  hps: 29815.91735
 }
}
dps_results: {
 key: "TestProtection-AllItems-MalevolentGladiator'sBadgeofConquest-84934"
 value: {
  dps: 152542.47436
  tps: 1.05654959091e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-MalevolentGladiator'sBadgeofConquest-91452"
 value: {
  dps: 152542.47436
  tps: 1.05654959091e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-MalevolentGladiator'sBadgeofDominance-84940"
 value: {
  dps: 152659.30073
  tps: 1.05715449197e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-MalevolentGladiator'sBadgeofDominance-91753"
 value: {
  dps: 152659.30073
  tps: 1.05715449197e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-MalevolentGladiator'sBadgeofVictory-84942"
 value: {
  dps: 155474.95884
  tps: 1.07651691536e+06
  dtps: 35851.97075
  hps: 29771.0815
 }
}
dps_results: {
 key: "TestProtection-AllItems-MalevolentGladiator'sBadgeofVictory-91763"
 value: {
  dps: 155326.1448
  tps: 1.07551098145e+06
  dtps: 35867.0618
  hps: 29745.2106
 }
}
dps_results: {
 key: "TestProtection-AllItems-MalevolentGladiator'sEmblemofCruelty-84936"
 value: {
  dps: 155327.11959
  tps: 1.07565069911e+06
  dtps: 36927.92905
  hps: 29348.47345
 }
}
dps_results: {
 key: "TestProtection-AllItems-MalevolentGladiator'sEmblemofCruelty-91562"
 value: {
  dps: 155130.13067
  tps: 1.07431765465e+06
  dtps: 36927.92905
  hps: 29348.47345
 }
}
dps_results: {
 key: "TestProtection-AllItems-MalevolentGladiator'sEmblemofMeditation-84939"
 value: {
  dps: 152376.00395
  tps: 1.05538429809e+06
  dtps: 36927.92905
  hps: 29348.47345
 }
}
dps_results: {
 key: "TestProtection-AllItems-MalevolentGladiator'sEmblemofMeditation-91564"
 value: {
  dps: 152376.00395
  tps: 1.05538429809e+06
  dtps: 36927.92905
  hps: 29348.47345
 }
}
dps_results: {
 key: "TestProtection-AllItems-MalevolentGladiator'sEmblemofTenacity-84938"
 value: {
  dps: 152271.47265
  tps: 1.05470015551e+06
  dtps: 36742.1681
  hps: 29327.79911
 }
}
dps_results: {
 key: "TestProtection-AllItems-MalevolentGladiator'sEmblemofTenacity-91563"
 value: {
  dps: 152304.4938
  tps: 1.05493167855e+06
  dtps: 36785.78798
  hps: 29336.23374
 }
}
dps_results: {
 key: "TestProtection-AllItems-MalevolentGladiator'sInsigniaofConquest-91457"
 value: {
  dps: 152434.3766
  tps: 1.05579290663e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-MalevolentGladiator'sInsigniaofDominance-91754"
 value: {
  dps: 152733.05485
  tps: 1.05753812392e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-MalevolentGladiator'sInsigniaofVictory-91768"
 value: {
  dps: 156739.66975
  tps: 1.08498938293e+06
  dtps: 35186.55773
  hps: 30018.60043
 }
}
dps_results: {
 key: "TestProtection-AllItems-MarkoftheCatacombs-83731"
 value: {
  dps: 154192.04439
  tps: 1.06788379757e+06
  dtps: 36096.74992
  hps: 29336.12978
 }
}
dps_results: {
 key: "TestProtection-AllItems-MarkoftheHardenedGrunt-92783"
 value: {
  dps: 152376.00395
  tps: 1.05538429809e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-MedallionofMystifyingVapors-93257"
 value: {
  dps: 152931.16023
  tps: 1.05934508124e+06
  dtps: 34337.41328
  hps: 29374.8928
 }
}
dps_results: {
 key: "TestProtection-AllItems-MedallionoftheCatacombs-83734"
 value: {
  dps: 152947.24172
  tps: 1.05934285743e+06
  dtps: 35494.80946
  hps: 29399.08677
 }
}
dps_results: {
 key: "TestProtection-AllItems-MendingBadgeoftheShieldwall-93348"
 value: {
  dps: 152501.15254
  tps: 1.05626033817e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-MirrorScope-4700"
 value: {
  dps: 155797.27296
  tps: 1.07891120538e+06
  dtps: 35557.53904
  hps: 29815.91735
 }
}
dps_results: {
 key: "TestProtection-AllItems-MistdancerDefenderIdol-101089"
 value: {
  dps: 153094.35157
  tps: 1.06025365773e+06
  dtps: 35702.98556
  hps: 29397.0758
 }
}
dps_results: {
 key: "TestProtection-AllItems-MistdancerDefenderStone-101087"
 value: {
  dps: 152363.36115
  tps: 1.05529483347e+06
  dtps: 34603.70304
  hps: 29324.76542
 }
}
dps_results: {
 key: "TestProtection-AllItems-MistdancerIdolofRage-101113"
 value: {
  dps: 152548.80287
  tps: 1.05655350049e+06
  dtps: 35630.78871
  hps: 29334.95384
 }
}
dps_results: {
 key: "TestProtection-AllItems-MistdancerStoneofRage-101117"
 value: {
  dps: 152533.80253
  tps: 1.05648878814e+06
  dtps: 34695.22026
  hps: 29344.1909
 }
}
dps_results: {
 key: "TestProtection-AllItems-MistdancerStoneofWisdom-101107"
 value: {
  dps: 152526.6026
  tps: 1.05643848861e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-MithrilWristwatch-87572"
 value: {
  dps: 155041.49079
  tps: 1.07369717552e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-MountainsageIdolofDestruction-101069"
 value: {
  dps: 154327.9392
  tps: 1.06890506874e+06
  dtps: 35131.49899
  hps: 29750.55791
 }
}
dps_results: {
 key: "TestProtection-AllItems-MountainsageStoneofDestruction-101072"
 value: {
  dps: 152631.81072
  tps: 1.05717464547e+06
  dtps: 34684.332
  hps: 29347.57106
 }
}
dps_results: {
 key: "TestProtection-AllItems-OathswornDefenderIdol-101303"
 value: {
  dps: 153146.47922
  tps: 1.06044445667e+06
  dtps: 35517.74768
  hps: 29435.17236
 }
}
dps_results: {
 key: "TestProtection-AllItems-OathswornDefenderStone-101306"
 value: {
  dps: 152370.42062
  tps: 1.05534454976e+06
  dtps: 34933.77693
  hps: 29323.54043
 }
}
dps_results: {
 key: "TestProtection-AllItems-OathswornIdolofBattle-101295"
 value: {
  dps: 158902.18731
  tps: 1.10004788227e+06
  dtps: 35782.89996
  hps: 29873.84673
 }
}
dps_results: {
 key: "TestProtection-AllItems-OathswornStoneofBattle-101294"
 value: {
  dps: 154704.37875
  tps: 1.07146102577e+06
  dtps: 33617.99197
  hps: 29769.90278
 }
}
dps_results: {
 key: "TestProtection-AllItems-PhaseFingers-4697"
 value: {
  dps: 157451.09325
  tps: 1.08960903744e+06
  dtps: 34744.80585
  hps: 30193.49844
 }
}
dps_results: {
 key: "TestProtection-AllItems-PlateofWingedTriumph"
 value: {
  dps: 143892.28824
  tps: 995324.8827
  dtps: 30275.05587
  hps: 28222.03831
 }
}
dps_results: {
 key: "TestProtection-AllItems-PlateoftheLightningEmperor"
 value: {
  dps: 143072.69567
  tps: 989227.35582
  dtps: 30491.24617
  hps: 25472.04318
 }
}
dps_results: {
 key: "TestProtection-AllItems-PouchofWhiteAsh-103639"
 value: {
  dps: 152376.59289
  tps: 1.05538842061e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-PowerfulPrimalDiamond"
 value: {
  dps: 156319.20259
  tps: 1.08199000033e+06
  dtps: 34882.26653
  hps: 30138.23618
 }
}
dps_results: {
 key: "TestProtection-AllItems-PriceofProgress-81266"
 value: {
  dps: 152514.50163
  tps: 1.05635378183e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-PridefulGladiator'sBadgeofConquest-102659"
 value: {
  dps: 152671.48096
  tps: 1.0574526371e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-PridefulGladiator'sBadgeofConquest-103342"
 value: {
  dps: 152671.48096
  tps: 1.0574526371e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-PridefulGladiator'sBadgeofDominance-102633"
 value: {
  dps: 152791.42856
  tps: 1.05785516303e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-PridefulGladiator'sBadgeofDominance-103505"
 value: {
  dps: 152791.42856
  tps: 1.05785516303e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-PridefulGladiator'sBadgeofVictory-102636"
 value: {
  dps: 157871.8179
  tps: 1.09247503125e+06
  dtps: 35007.85975
  hps: 30209.53992
 }
}
dps_results: {
 key: "TestProtection-AllItems-PridefulGladiator'sBadgeofVictory-103511"
 value: {
  dps: 157871.8179
  tps: 1.09247503125e+06
  dtps: 35007.85975
  hps: 30209.53992
 }
}
dps_results: {
 key: "TestProtection-AllItems-PridefulGladiator'sEmblemofCruelty-102680"
 value: {
  dps: 157688.56026
  tps: 1.09176952089e+06
  dtps: 36927.92905
  hps: 29348.47345
 }
}
dps_results: {
 key: "TestProtection-AllItems-PridefulGladiator'sEmblemofCruelty-103407"
 value: {
  dps: 157688.56026
  tps: 1.09176952089e+06
  dtps: 36927.92905
  hps: 29348.47345
 }
}
dps_results: {
 key: "TestProtection-AllItems-PridefulGladiator'sEmblemofMeditation-102616"
 value: {
  dps: 152376.00395
  tps: 1.05538429809e+06
  dtps: 36927.92905
  hps: 29348.47345
 }
}
dps_results: {
 key: "TestProtection-AllItems-PridefulGladiator'sEmblemofMeditation-103409"
 value: {
  dps: 152376.00395
  tps: 1.05538429809e+06
  dtps: 36927.92905
  hps: 29348.47345
 }
}
dps_results: {
 key: "TestProtection-AllItems-PridefulGladiator'sEmblemofTenacity-102706"
 value: {
  dps: 152233.52203
  tps: 1.05436106865e+06
  dtps: 36543.36167
  hps: 29335.47896
 }
}
dps_results: {
 key: "TestProtection-AllItems-PridefulGladiator'sEmblemofTenacity-103408"
 value: {
  dps: 152233.52203
  tps: 1.05436106865e+06
  dtps: 36543.36167
  hps: 29335.47896
 }
}
dps_results: {
 key: "TestProtection-AllItems-PridefulGladiator'sInsigniaofConquest-103347"
 value: {
  dps: 152589.67044
  tps: 1.05687996351e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-PridefulGladiator'sInsigniaofDominance-103506"
 value: {
  dps: 153059.24569
  tps: 1.05936431891e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-PridefulGladiator'sInsigniaofVictory-103516"
 value: {
  dps: 159528.07467
  tps: 1.10394706166e+06
  dtps: 34325.9538
  hps: 30617.04561
 }
}
dps_results: {
 key: "TestProtection-AllItems-Primordius'TalismanofRage-96873"
 value: {
  dps: 161169.35069
  tps: 1.11565806161e+06
  dtps: 34724.00054
  hps: 30166.06018
 }
}
dps_results: {
 key: "TestProtection-AllItems-Qian-Le,CourageofNiuzao-102245"
 value: {
  dps: 157397.74855
  tps: 1.08958412831e+06
  dtps: 33148.48671
  hps: 29885.59572
 }
}
dps_results: {
 key: "TestProtection-AllItems-Qian-Ying,FortitudeofNiuzao-102250"
 value: {
  dps: 158363.31843
  tps: 1.09619476972e+06
  dtps: 31352.62714
  hps: 30761.93462
 }
}
dps_results: {
 key: "TestProtection-AllItems-Qin-xi'sPolarizingSeal-87075"
 value: {
  dps: 152376.59289
  tps: 1.05538842061e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-RelicofChi-Ji-79330"
 value: {
  dps: 152529.20353
  tps: 1.05645669511e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-RelicofKypariZar-84075"
 value: {
  dps: 156193.11918
  tps: 1.08186992795e+06
  dtps: 36366.58244
  hps: 29762.10962
 }
}
dps_results: {
 key: "TestProtection-AllItems-RelicofNiuzao-79329"
 value: {
  dps: 154154.14905
  tps: 1.06716592595e+06
  dtps: 35104.26402
  hps: 29442.36817
 }
}
dps_results: {
 key: "TestProtection-AllItems-RelicofXuen-79328"
 value: {
  dps: 152573.98682
  tps: 1.05677017815e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-RelicofYu'lon-79331"
 value: {
  dps: 152701.46813
  tps: 1.05766254731e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-Renataki'sSoulCharm-96741"
 value: {
  dps: 153125.58195
  tps: 1.06063075909e+06
  dtps: 37029.68331
  hps: 29361.17586
 }
}
dps_results: {
 key: "TestProtection-AllItems-ResolveofNiuzao-103690"
 value: {
  dps: 153358.81905
  tps: 1.06183577318e+06
  dtps: 34602.28272
  hps: 29392.05107
 }
}
dps_results: {
 key: "TestProtection-AllItems-ResolveofNiuzao-103990"
 value: {
  dps: 153653.72944
  tps: 1.06364576908e+06
  dtps: 34022.49284
  hps: 29409.37827
 }
}
dps_results: {
 key: "TestProtection-AllItems-ReverberatingPrimalDiamond"
 value: {
  dps: 157419.92319
  tps: 1.08957487663e+06
  dtps: 34908.15539
  hps: 30216.29146
 }
}
dps_results: {
 key: "TestProtection-AllItems-RevitalizingPrimalDiamond"
 value: {
  dps: 157023.90205
  tps: 1.08685054212e+06
  dtps: 34857.36805
  hps: 30136.80168
 }
}
dps_results: {
 key: "TestProtection-AllItems-SI:7Operative'sManual-92784"
 value: {
  dps: 152376.59289
  tps: 1.05538842061e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-ScrollofReveredAncestors-89080"
 value: {
  dps: 152528.6146
  tps: 1.0564525726e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-SearingWords-81267"
 value: {
  dps: 154971.09821
  tps: 1.07320442744e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-Shock-ChargerMedallion-93259"
 value: {
  dps: 156099.84755
  tps: 1.08106290572e+06
  dtps: 35752.10092
  hps: 30067.41647
 }
}
dps_results: {
 key: "TestProtection-AllItems-SigilofCompassion-83736"
 value: {
  dps: 152445.43883
  tps: 1.05582985218e+06
  dtps: 36257.76865
  hps: 29341.44541
 }
}
dps_results: {
 key: "TestProtection-AllItems-SigilofDevotion-83740"
 value: {
  dps: 154228.86089
  tps: 1.06814151309e+06
  dtps: 35707.61212
  hps: 29338.18454
 }
}
dps_results: {
 key: "TestProtection-AllItems-SigilofFidelity-83737"
 value: {
  dps: 156516.58203
  tps: 1.08372705085e+06
  dtps: 35960.31762
  hps: 29732.90073
 }
}
dps_results: {
 key: "TestProtection-AllItems-SigilofGrace-83738"
 value: {
  dps: 154632.62088
  tps: 1.0706756776e+06
  dtps: 35604.59057
  hps: 29851.80588
 }
}
dps_results: {
 key: "TestProtection-AllItems-SigilofKypariZar-84076"
 value: {
  dps: 154467.63927
  tps: 1.06968031486e+06
  dtps: 35836.66149
  hps: 29342.63625
 }
}
dps_results: {
 key: "TestProtection-AllItems-SigilofPatience-83739"
 value: {
  dps: 152570.40027
  tps: 1.05674777732e+06
  dtps: 35047.03015
  hps: 29351.00262
 }
}
dps_results: {
 key: "TestProtection-AllItems-SigiloftheCatacombs-83732"
 value: {
  dps: 156505.05
  tps: 1.08410544439e+06
  dtps: 36287.70613
  hps: 29735.80466
 }
}
dps_results: {
 key: "TestProtection-AllItems-SinisterPrimalDiamond"
 value: {
  dps: 157060.16523
  tps: 1.08706707231e+06
  dtps: 34857.36805
  hps: 30136.80168
 }
}
dps_results: {
 key: "TestProtection-AllItems-SkullrenderMedallion-93256"
 value: {
  dps: 158470.6627
  tps: 1.09663008959e+06
  dtps: 35720.7091
  hps: 29818.36935
 }
}
dps_results: {
 key: "TestProtection-AllItems-SpiritsoftheSun-87163"
 value: {
  dps: 152549.73563
  tps: 1.05649334227e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-SpringrainIdolofDestruction-101023"
 value: {
  dps: 154664.69871
  tps: 1.07110249576e+06
  dtps: 35033.27765
  hps: 29743.49927
 }
}
dps_results: {
 key: "TestProtection-AllItems-SpringrainIdolofRage-101009"
 value: {
  dps: 152634.56292
  tps: 1.05715382087e+06
  dtps: 35630.78871
  hps: 29334.95384
 }
}
dps_results: {
 key: "TestProtection-AllItems-SpringrainStoneofDestruction-101026"
 value: {
  dps: 152614.59031
  tps: 1.05705430257e+06
  dtps: 34898.41689
  hps: 29345.13426
 }
}
dps_results: {
 key: "TestProtection-AllItems-SpringrainStoneofRage-101012"
 value: {
  dps: 152564.51987
  tps: 1.05670360952e+06
  dtps: 34458.11187
  hps: 29346.6277
 }
}
dps_results: {
 key: "TestProtection-AllItems-SpringrainStoneofWisdom-101041"
 value: {
  dps: 152526.6026
  tps: 1.05643848861e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-Static-Caster'sMedallion-93254"
 value: {
  dps: 156099.84755
  tps: 1.08106290572e+06
  dtps: 35752.10092
  hps: 30067.41647
 }
}
dps_results: {
 key: "TestProtection-AllItems-SteadfastFootman'sMedallion-92782"
 value: {
  dps: 152376.00395
  tps: 1.05538429809e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-SteadfastTalismanoftheShado-PanAssault-94507"
 value: {
  dps: 152808.43322
  tps: 1.05828149771e+06
  dtps: 35043.58719
  hps: 29335.08307
 }
}
dps_results: {
 key: "TestProtection-AllItems-StreamtalkerIdolofDestruction-101222"
 value: {
  dps: 154869.43587
  tps: 1.07261689268e+06
  dtps: 35068.99282
  hps: 29805.11739
 }
}
dps_results: {
 key: "TestProtection-AllItems-StreamtalkerIdolofRage-101217"
 value: {
  dps: 152597.51204
  tps: 1.05689446469e+06
  dtps: 35630.78871
  hps: 29334.95384
 }
}
dps_results: {
 key: "TestProtection-AllItems-StreamtalkerStoneofDestruction-101225"
 value: {
  dps: 152619.52115
  tps: 1.05708881844e+06
  dtps: 34629.09647
  hps: 29353.7373
 }
}
dps_results: {
 key: "TestProtection-AllItems-StreamtalkerStoneofRage-101220"
 value: {
  dps: 152525.55776
  tps: 1.05643117474e+06
  dtps: 34882.91967
  hps: 29340.57698
 }
}
dps_results: {
 key: "TestProtection-AllItems-StreamtalkerStoneofWisdom-101250"
 value: {
  dps: 152526.6026
  tps: 1.05643848861e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-StuffofNightmares-87160"
 value: {
  dps: 153716.08276
  tps: 1.06411557168e+06
  dtps: 34276.5569
  hps: 29450.34039
 }
}
dps_results: {
 key: "TestProtection-AllItems-SunsoulDefenderIdol-101160"
 value: {
  dps: 153652.67946
  tps: 1.06415278282e+06
  dtps: 35214.04589
  hps: 29431.15019
 }
}
dps_results: {
 key: "TestProtection-AllItems-SunsoulDefenderStone-101163"
 value: {
  dps: 152334.72161
  tps: 1.05509425669e+06
  dtps: 34589.82546
  hps: 29325.13518
 }
}
dps_results: {
 key: "TestProtection-AllItems-SunsoulIdolofBattle-101152"
 value: {
  dps: 159024.63096
  tps: 1.10076939223e+06
  dtps: 35524.47837
  hps: 29876.56339
 }
}
dps_results: {
 key: "TestProtection-AllItems-SunsoulStoneofBattle-101151"
 value: {
  dps: 154677.21626
  tps: 1.07127088832e+06
  dtps: 33559.08123
  hps: 29770.14079
 }
}
dps_results: {
 key: "TestProtection-AllItems-SunsoulStoneofWisdom-101138"
 value: {
  dps: 152526.6026
  tps: 1.05643848861e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-SwordguardEmbroidery(Rank3)-4894"
 value: {
  dps: 158285.66702
  tps: 1.09534284222e+06
  dtps: 34775.39658
  hps: 30447.92176
 }
}
dps_results: {
 key: "TestProtection-AllItems-SymboloftheCatacombs-83735"
 value: {
  dps: 154459.11932
  tps: 1.06922268343e+06
  dtps: 36257.76865
  hps: 29341.44541
 }
}
dps_results: {
 key: "TestProtection-AllItems-SynapseSprings(MarkII)-4898"
 value: {
  dps: 158313.30102
  tps: 1.09540472418e+06
  dtps: 34723.80309
  hps: 30354.32729
 }
}
dps_results: {
 key: "TestProtection-AllItems-TalismanofBloodlust-96864"
 value: {
  dps: 157219.79602
  tps: 1.08889673601e+06
  dtps: 35657.74905
  hps: 30284.91074
 }
}
dps_results: {
 key: "TestProtection-AllItems-TerrorintheMists-87167"
 value: {
  dps: 157732.31903
  tps: 1.09122459243e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-Thousand-YearPickledEgg-87573"
 value: {
  dps: 152518.61123
  tps: 1.05638254902e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-TrailseekerIdolofRage-101054"
 value: {
  dps: 152594.36606
  tps: 1.0568724428e+06
  dtps: 35630.78871
  hps: 29334.95384
 }
}
dps_results: {
 key: "TestProtection-AllItems-TrailseekerStoneofRage-101057"
 value: {
  dps: 152517.96094
  tps: 1.05637779697e+06
  dtps: 34789.60578
  hps: 29343.9481
 }
}
dps_results: {
 key: "TestProtection-AllItems-TyrannicalGladiator'sBadgeofConquest-100043"
 value: {
  dps: 152548.21812
  tps: 1.05658979729e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-TyrannicalGladiator'sBadgeofConquest-91099"
 value: {
  dps: 152548.21812
  tps: 1.05658979729e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-TyrannicalGladiator'sBadgeofConquest-94373"
 value: {
  dps: 152548.21812
  tps: 1.05658979729e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-TyrannicalGladiator'sBadgeofConquest-99772"
 value: {
  dps: 152548.21812
  tps: 1.05658979729e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-TyrannicalGladiator'sBadgeofDominance-100016"
 value: {
  dps: 152680.79108
  tps: 1.05717227752e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-TyrannicalGladiator'sBadgeofDominance-91400"
 value: {
  dps: 152680.79108
  tps: 1.05717227752e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-TyrannicalGladiator'sBadgeofDominance-94346"
 value: {
  dps: 152680.79108
  tps: 1.05717227752e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-TyrannicalGladiator'sBadgeofDominance-99937"
 value: {
  dps: 152680.79108
  tps: 1.05717227752e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-TyrannicalGladiator'sBadgeofVictory-100019"
 value: {
  dps: 155965.2758
  tps: 1.07972190693e+06
  dtps: 35573.7578
  hps: 29859.47058
 }
}
dps_results: {
 key: "TestProtection-AllItems-TyrannicalGladiator'sBadgeofVictory-91410"
 value: {
  dps: 155965.2758
  tps: 1.07972190693e+06
  dtps: 35573.7578
  hps: 29859.47058
 }
}
dps_results: {
 key: "TestProtection-AllItems-TyrannicalGladiator'sBadgeofVictory-94349"
 value: {
  dps: 155965.2758
  tps: 1.07972190693e+06
  dtps: 35573.7578
  hps: 29859.47058
 }
}
dps_results: {
 key: "TestProtection-AllItems-TyrannicalGladiator'sBadgeofVictory-99943"
 value: {
  dps: 155965.2758
  tps: 1.07972190693e+06
  dtps: 35573.7578
  hps: 29859.47058
 }
}
dps_results: {
 key: "TestProtection-AllItems-TyrannicalGladiator'sEmblemofCruelty-100066"
 value: {
  dps: 155569.90087
  tps: 1.07730446921e+06
  dtps: 36927.92905
  hps: 29348.47345
 }
}
dps_results: {
 key: "TestProtection-AllItems-TyrannicalGladiator'sEmblemofCruelty-91209"
 value: {
  dps: 155569.90087
  tps: 1.07730446921e+06
  dtps: 36927.92905
  hps: 29348.47345
 }
}
dps_results: {
 key: "TestProtection-AllItems-TyrannicalGladiator'sEmblemofCruelty-94396"
 value: {
  dps: 155569.90087
  tps: 1.07730446921e+06
  dtps: 36927.92905
  hps: 29348.47345
 }
}
dps_results: {
 key: "TestProtection-AllItems-TyrannicalGladiator'sEmblemofCruelty-99838"
 value: {
  dps: 155569.90087
  tps: 1.07730446921e+06
  dtps: 36927.92905
  hps: 29348.47345
 }
}
dps_results: {
 key: "TestProtection-AllItems-TyrannicalGladiator'sEmblemofMeditation-91211"
 value: {
  dps: 152376.00395
  tps: 1.05538429809e+06
  dtps: 36927.92905
  hps: 29348.47345
 }
}
dps_results: {
 key: "TestProtection-AllItems-TyrannicalGladiator'sEmblemofMeditation-94329"
 value: {
  dps: 152376.00395
  tps: 1.05538429809e+06
  dtps: 36927.92905
  hps: 29348.47345
 }
}
dps_results: {
 key: "TestProtection-AllItems-TyrannicalGladiator'sEmblemofMeditation-99840"
 value: {
  dps: 152376.00395
  tps: 1.05538429809e+06
  dtps: 36927.92905
  hps: 29348.47345
 }
}
dps_results: {
 key: "TestProtection-AllItems-TyrannicalGladiator'sEmblemofMeditation-99990"
 value: {
  dps: 152376.00395
  tps: 1.05538429809e+06
  dtps: 36927.92905
  hps: 29348.47345
 }
}
dps_results: {
 key: "TestProtection-AllItems-TyrannicalGladiator'sEmblemofTenacity-100092"
 value: {
  dps: 152303.64981
  tps: 1.05492539565e+06
  dtps: 36744.61618
  hps: 29327.80433
 }
}
dps_results: {
 key: "TestProtection-AllItems-TyrannicalGladiator'sEmblemofTenacity-91210"
 value: {
  dps: 152303.64981
  tps: 1.05492539565e+06
  dtps: 36744.61618
  hps: 29327.80433
 }
}
dps_results: {
 key: "TestProtection-AllItems-TyrannicalGladiator'sEmblemofTenacity-94422"
 value: {
  dps: 152303.64981
  tps: 1.05492539565e+06
  dtps: 36744.61618
  hps: 29327.80433
 }
}
dps_results: {
 key: "TestProtection-AllItems-TyrannicalGladiator'sEmblemofTenacity-99839"
 value: {
  dps: 152303.64981
  tps: 1.05492539565e+06
  dtps: 36744.61618
  hps: 29327.80433
 }
}
dps_results: {
 key: "TestProtection-AllItems-TyrannicalGladiator'sInsigniaofConquest-100026"
 value: {
  dps: 152557.89635
  tps: 1.05665754485e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-TyrannicalGladiator'sInsigniaofDominance-100152"
 value: {
  dps: 152731.92921
  tps: 1.05753024443e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-TyrannicalGladiator'sInsigniaofVictory-100085"
 value: {
  dps: 156759.25709
  tps: 1.08521885214e+06
  dtps: 35263.51035
  hps: 30096.62617
 }
}
dps_results: {
 key: "TestProtection-AllItems-TyrannicalPrimalDiamond"
 value: {
  dps: 156168.86143
  tps: 1.08093691221e+06
  dtps: 34857.36805
  hps: 30136.80168
 }
}
dps_results: {
 key: "TestProtection-AllItems-UnerringVisionofLeiShen-96930"
 value: {
  dps: 160400.58397
  tps: 1.11080815319e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-VaporshieldMedallion-93262"
 value: {
  dps: 152931.16023
  tps: 1.05934508124e+06
  dtps: 34337.41328
  hps: 29374.8928
 }
}
dps_results: {
 key: "TestProtection-AllItems-VestmentsofWingedTriumph"
 value: {
  dps: 124221.75911
  tps: 859717.25297
  dtps: 38872.64865
  hps: 21225.47134
 }
}
dps_results: {
 key: "TestProtection-AllItems-VestmentsoftheLightningEmperor"
 value: {
  dps: 129149.25639
  tps: 893625.9387
  dtps: 39935.25885
  hps: 21994.91134
 }
}
dps_results: {
 key: "TestProtection-AllItems-VialofDragon'sBlood-87063"
 value: {
  dps: 152976.15882
  tps: 1.05929195761e+06
  dtps: 33869.78448
  hps: 29420.23521
 }
}
dps_results: {
 key: "TestProtection-AllItems-VialofIchorousBlood-100963"
 value: {
  dps: 152475.75298
  tps: 1.05608254128e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-VialofIchorousBlood-81264"
 value: {
  dps: 152514.50163
  tps: 1.05635378183e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-ViciousTalismanoftheShado-PanAssault-94511"
 value: {
  dps: 152679.50571
  tps: 1.05750881041e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-VisionofthePredator-81192"
 value: {
  dps: 155592.67455
  tps: 1.0777540738e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-VolatileTalismanoftheShado-PanAssault-94510"
 value: {
  dps: 154385.64007
  tps: 1.0694489012e+06
  dtps: 36020.07446
  hps: 29871.41235
 }
}
dps_results: {
 key: "TestProtection-AllItems-WhiteTigerBattlegear"
 value: {
  dps: 146790.14762
  tps: 1.01509028896e+06
  dtps: 36490.33029
  hps: 27700.75925
 }
}
dps_results: {
 key: "TestProtection-AllItems-WhiteTigerPlate"
 value: {
  dps: 137158.31361
  tps: 948485.06222
  dtps: 32021.75811
  hps: 24423.96773
 }
}
dps_results: {
 key: "TestProtection-AllItems-WhiteTigerVestments"
 value: {
  dps: 127330.73545
  tps: 881221.42574
  dtps: 42356.06652
  hps: 21381.46762
 }
}
dps_results: {
 key: "TestProtection-AllItems-WindsweptPages-81125"
 value: {
  dps: 154959.49125
  tps: 1.07267074194e+06
  dtps: 36374.33537
  hps: 29911.83834
 }
}
dps_results: {
 key: "TestProtection-AllItems-WoundripperMedallion-93253"
 value: {
  dps: 156151.16143
  tps: 1.08066758878e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-Wushoolay'sFinalChoice-96785"
 value: {
  dps: 153299.75378
  tps: 1.06169759139e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-Xing-Ho,BreathofYu'lon-102246"
 value: {
  dps: 174963.55237
  tps: 1.21248587192e+06
  dtps: 33382.41355
  hps: 29938.98102
 }
}
dps_results: {
 key: "TestProtection-AllItems-YaungolFireCarrier-86518"
 value: {
  dps: 157419.92319
  tps: 1.08957487663e+06
  dtps: 34908.15539
  hps: 30216.29146
 }
}
dps_results: {
 key: "TestProtection-AllItems-Yu'lon'sBite-103987"
 value: {
  dps: 158524.03042
  tps: 1.09788830129e+06
  dtps: 36927.92905
  hps: 29345.48271
 }
}
dps_results: {
 key: "TestProtection-AllItems-ZenAlchemistStone-75274"
 value: {
  dps: 156155.92593
  tps: 1.08059474594e+06
  dtps: 34383.06458
  hps: 30034.87977
 }
}
dps_results: {
 key: "TestProtection-Average-Default"
 value: {
  dps: 158988.23717
  tps: 1.09909634496e+06
  dtps: 34530.51175
  hps: 30252.5046
 }
}
dps_results: {
 key: "TestProtection-Settings-BloodElf-p1-balanced-Seal of Insight-default-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 904891.16249
  tps: 6.28016441825e+06
  dtps: 941382.18734
  hps: 147184.33646
 }
}
dps_results: {
 key: "TestProtection-Settings-BloodElf-p1-balanced-Seal of Insight-default-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 160572.46729
  tps: 1.11118166825e+06
  dtps: 34687.88049
  hps: 30513.63115
 }
}
dps_results: {
 key: "TestProtection-Settings-BloodElf-p1-balanced-Seal of Insight-default-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 190519.21725
  tps: 1.26517800686e+06
  dtps: 28956.21377
  hps: 34469.8964
 }
}
dps_results: {
 key: "TestProtection-Settings-BloodElf-p1-balanced-Seal of Insight-default-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 615505.60697
  tps: 4.33264994877e+06
  dtps: 1.22569519606e+06
  hps: 101210.38539
 }
}
dps_results: {
 key: "TestProtection-Settings-BloodElf-p1-balanced-Seal of Insight-default-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 123080.32294
  tps: 862650.01055
  dtps: 49258.64584
  hps: 25228.1211
 }
}
dps_results: {
 key: "TestProtection-Settings-BloodElf-p1-balanced-Seal of Insight-default-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 124004.89214
  tps: 869151.77001
  dtps: 42835.87445
  hps: 24341.02043
 }
}
dps_results: {
 key: "TestProtection-Settings-BloodElf-p1-balanced-Seal of Righteousness-default-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 1.29002481446e+06
  tps: 8.97598404996e+06
  dtps: 945627.05263
  hps: 148.05753
 }
}
dps_results: {
 key: "TestProtection-Settings-BloodElf-p1-balanced-Seal of Righteousness-default-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 165570.17388
  tps: 1.14604227671e+06
  dtps: 36108.26681
  hps: 592.23011
 }
}
dps_results: {
 key: "TestProtection-Settings-BloodElf-p1-balanced-Seal of Righteousness-default-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 197851.07958
  tps: 1.31588345497e+06
  dtps: 29046.61817
  hps: 888.34517
 }
}
dps_results: {
 key: "TestProtection-Settings-BloodElf-p1-balanced-Seal of Righteousness-default-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 851697.13423
  tps: 5.98615183963e+06
  dtps: 1.22803432202e+06
  hps: 257.93456
 }
}
dps_results: {
 key: "TestProtection-Settings-BloodElf-p1-balanced-Seal of Righteousness-default-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 127120.00982
  tps: 890931.61371
  dtps: 51156.56268
  hps: 515.86912
 }
}
dps_results: {
 key: "TestProtection-Settings-BloodElf-p1-balanced-Seal of Righteousness-default-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 128023.55178
  tps: 897283.01248
  dtps: 45226.72946
  hps: 1289.67281
 }
}
dps_results: {
 key: "TestProtection-Settings-BloodElf-p1-balanced-Seal of Truth-default-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 919651.72403
  tps: 6.38362284903e+06
  dtps: 948584.45059
  hps: 103.64027
 }
}
dps_results: {
 key: "TestProtection-Settings-BloodElf-p1-balanced-Seal of Truth-default-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 167575.71433
  tps: 1.16020421253e+06
  dtps: 36354.03855
  hps: 592.23011
 }
}
dps_results: {
 key: "TestProtection-Settings-BloodElf-p1-balanced-Seal of Truth-default-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 196615.92237
  tps: 1.30785836771e+06
  dtps: 28913.50664
  hps: 888.34517
 }
}
dps_results: {
 key: "TestProtection-Settings-BloodElf-p1-balanced-Seal of Truth-default-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 614767.28835
  tps: 4.32756741842e+06
  dtps: 1.23213017834e+06
  hps: 257.93456
 }
}
dps_results: {
 key: "TestProtection-Settings-BloodElf-p1-balanced-Seal of Truth-default-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 127499.1354
  tps: 893583.50782
  dtps: 51107.95034
  hps: 515.86912
 }
}
dps_results: {
 key: "TestProtection-Settings-BloodElf-p1-balanced-Seal of Truth-default-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 127577.53431
  tps: 894162.64015
  dtps: 44971.76833
  hps: 1289.67281
 }
}
dps_results: {
 key: "TestProtection-SwitchInFrontOfTarget-Default"
 value: {
  dps: 160572.46729
  tps: 1.11118166825e+06
  dtps: 34687.88049
  hps: 30513.63115
 }
}
