character_stats_results: {
 key: "TestProtection-CharacterStats-Default"
 value: {
  final_stats: 14849.1
  final_stats: 112.35
  final_stats: 36151.5
  final_stats: 122.85
  final_stats: 121
  final_stats: 3405
  final_stats: 0
  final_stats: 7725
  final_stats: 5117
  final_stats: 1971
  final_stats: 15314.43125
  final_stats: 6286
  final_stats: 32943.02
  final_stats: 0
  final_stats: 124.135
  final_stats: 0
  final_stats: 0
  final_stats: 58727.724
  final_stats: 0
  final_stats: 652524
  final_stats: 60000
  final_stats: 3000
  final_stats: 10.01471
  final_stats: 25.06471
  final_stats: 10.01123
  final_stats: 8.38849
  final_stats: 31.31422
 }
}
dps_results: {
 key: "TestProtection-AllItems-AgilePrimalDiamond"
 value: {
  dps: 133350.69413
  tps: 923092.69878
  dtps: 61059.04551
  hps: 74021.10468
 }
}
dps_results: {
 key: "TestProtection-AllItems-AlacrityofXuen-103989"
 value: {
  dps: 133219.56554
  tps: 921772.31035
  dtps: 59716.13992
  hps: 74636.48368
 }
}
dps_results: {
 key: "TestProtection-AllItems-ArcaneBadgeoftheShieldwall-93347"
 value: {
  dps: 129822.67249
  tps: 898296.73054
  dtps: 61184.43078
  hps: 72428.10485
 }
}
dps_results: {
 key: "TestProtection-AllItems-ArrowflightMedallion-93258"
 value: {
  dps: 130722.66965
  tps: 903787.15081
  dtps: 61797.05941
  hps: 71807.20136
 }
}
dps_results: {
 key: "TestProtection-AllItems-AssuranceofConsequence-105472"
 value: {
  dps: 128486.10268
  tps: 889378.35227
  dtps: 61797.09283
  hps: 70532.79804
 }
}
dps_results: {
 key: "TestProtection-AllItems-AusterePrimalDiamond"
 value: {
  dps: 132873.83972
  tps: 919736.16804
  dtps: 60308.28468
  hps: 73681.2667
 }
}
dps_results: {
 key: "TestProtection-AllItems-BadJuju-96781"
 value: {
  dps: 128636.96849
  tps: 890434.32794
  dtps: 59934.65558
  hps: 71318.12054
 }
}
dps_results: {
 key: "TestProtection-AllItems-BadgeofKypariZar-84079"
 value: {
  dps: 129965.37216
  tps: 899181.92231
  dtps: 61042.87943
  hps: 71413.43769
 }
}
dps_results: {
 key: "TestProtection-AllItems-BattlegearofWingedTriumph"
 value: {
  dps: 138293.1799
  tps: 957339.08428
  dtps: 59037.78075
  hps: 73856.84538
 }
}
dps_results: {
 key: "TestProtection-AllItems-BattlegearoftheLightningEmperor"
 value: {
  dps: 138168.80884
  tps: 955230.52496
  dtps: 61580.69762
  hps: 72170.08145
 }
}
dps_results: {
 key: "TestProtection-AllItems-BlossomofPureSnow-89081"
 value: {
  dps: 130673.10796
  tps: 903440.219
  dtps: 61802.38162
  hps: 71910.94358
 }
}
dps_results: {
 key: "TestProtection-AllItems-BottleofInfiniteStars-87057"
 value: {
  dps: 128423.64567
  tps: 888941.09324
  dtps: 60448.81015
  hps: 71022.65532
 }
}
dps_results: {
 key: "TestProtection-AllItems-BraidofTenSongs-84072"
 value: {
  dps: 129944.14623
  tps: 899957.50034
  dtps: 60443.30837
  hps: 71487.06505
 }
}
dps_results: {
 key: "TestProtection-AllItems-Brawler'sStatue-87571"
 value: {
  dps: 128675.42395
  tps: 890845.59528
  dtps: 59649.44317
  hps: 70609.32917
 }
}
dps_results: {
 key: "TestProtection-AllItems-BreathoftheHydra-96827"
 value: {
  dps: 132145.63187
  tps: 915474.02017
  dtps: 60505.54048
  hps: 72857.98627
 }
}
dps_results: {
 key: "TestProtection-AllItems-BroochofMunificentDeeds-87500"
 value: {
  dps: 128358.72717
  tps: 888417.50221
  dtps: 61324.90392
  hps: 70573.31798
 }
}
dps_results: {
 key: "TestProtection-AllItems-BrutalTalismanoftheShado-PanAssault-94508"
 value: {
  dps: 132287.7014
  tps: 914623.54983
  dtps: 59923.71898
  hps: 73268.80077
 }
}
dps_results: {
 key: "TestProtection-AllItems-BurningPrimalDiamond"
 value: {
  dps: 133354.0851
  tps: 923116.43553
  dtps: 61059.04551
  hps: 74059.31426
 }
}
dps_results: {
 key: "TestProtection-AllItems-CapacitivePrimalDiamond"
 value: {
  dps: 133416.36144
  tps: 923506.28291
  dtps: 61058.10961
  hps: 73999.14452
 }
}
dps_results: {
 key: "TestProtection-AllItems-CarbonicCarbuncle-81138"
 value: {
  dps: 132487.01399
  tps: 916491.46261
  dtps: 60658.82389
  hps: 72270.03099
 }
}
dps_results: {
 key: "TestProtection-AllItems-Cha-Ye'sEssenceofBrilliance-96888"
 value: {
  dps: 132392.18809
  tps: 916050.60764
  dtps: 61808.93467
  hps: 71970.92873
 }
}
dps_results: {
 key: "TestProtection-AllItems-CharmofTenSongs-84071"
 value: {
  dps: 130996.83104
  tps: 907033.24551
  dtps: 61228.262
  hps: 71523.7329
 }
}
dps_results: {
 key: "TestProtection-AllItems-CommunalIdolofDestruction-101168"
 value: {
  dps: 130273.82458
  tps: 901907.43061
  dtps: 60121.34168
  hps: 71699.80627
 }
}
dps_results: {
 key: "TestProtection-AllItems-CommunalStoneofDestruction-101171"
 value: {
  dps: 128494.83266
  tps: 889439.83716
  dtps: 60404.91945
  hps: 71386.57915
 }
}
dps_results: {
 key: "TestProtection-AllItems-CommunalStoneofWisdom-101183"
 value: {
  dps: 128517.32933
  tps: 889596.93881
  dtps: 61802.44776
  hps: 70791.96623
 }
}
dps_results: {
 key: "TestProtection-AllItems-ContemplationofChi-Ji-103688"
 value: {
  dps: 128472.08392
  tps: 889193.90598
  dtps: 61802.38162
  hps: 70718.33971
 }
}
dps_results: {
 key: "TestProtection-AllItems-ContemplationofChi-Ji-103988"
 value: {
  dps: 128539.96293
  tps: 889543.08589
  dtps: 61802.38162
  hps: 70759.62921
 }
}
dps_results: {
 key: "TestProtection-AllItems-Coren'sColdChromiumCoaster-87574"
 value: {
  dps: 131025.60266
  tps: 906835.82363
  dtps: 61802.25318
  hps: 71492.06849
 }
}
dps_results: {
 key: "TestProtection-AllItems-CoreofDecency-87497"
 value: {
  dps: 128423.61571
  tps: 888940.94353
  dtps: 61802.08355
  hps: 70649.45226
 }
}
dps_results: {
 key: "TestProtection-AllItems-CourageousPrimalDiamond"
 value: {
  dps: 132854.95301
  tps: 919664.94525
  dtps: 61058.10961
  hps: 73829.42815
 }
}
dps_results: {
 key: "TestProtection-AllItems-CraftedDreadfulGladiator'sBadgeofConquest-93419"
 value: {
  dps: 128373.27856
  tps: 888588.58347
  dtps: 61797.09283
  hps: 70532.5148
 }
}
dps_results: {
 key: "TestProtection-AllItems-CraftedDreadfulGladiator'sBadgeofDominance-93600"
 value: {
  dps: 128458.7327
  tps: 888974.47425
  dtps: 61797.09283
  hps: 70648.12076
 }
}
dps_results: {
 key: "TestProtection-AllItems-CraftedDreadfulGladiator'sBadgeofVictory-93606"
 value: {
  dps: 129551.68025
  tps: 896422.85208
  dtps: 60885.71237
  hps: 71360.18888
 }
}
dps_results: {
 key: "TestProtection-AllItems-CraftedDreadfulGladiator'sEmblemofCruelty-93485"
 value: {
  dps: 129936.05622
  tps: 899218.49614
  dtps: 61739.60476
  hps: 71141.51391
 }
}
dps_results: {
 key: "TestProtection-AllItems-CraftedDreadfulGladiator'sEmblemofMeditation-93487"
 value: {
  dps: 128329.154
  tps: 888279.06155
  dtps: 61728.44327
  hps: 70597.99356
 }
}
dps_results: {
 key: "TestProtection-AllItems-CraftedDreadfulGladiator'sEmblemofTenacity-93486"
 value: {
  dps: 128359.34128
  tps: 888421.801
  dtps: 61325.38699
  hps: 70575.30925
 }
}
dps_results: {
 key: "TestProtection-AllItems-CraftedDreadfulGladiator'sInsigniaofConquest-93424"
 value: {
  dps: 128434.80961
  tps: 889019.30082
  dtps: 61797.05941
  hps: 70532.34367
 }
}
dps_results: {
 key: "TestProtection-AllItems-CraftedDreadfulGladiator'sInsigniaofDominance-93601"
 value: {
  dps: 128537.41432
  tps: 889525.24561
  dtps: 61797.05941
  hps: 70669.20416
 }
}
dps_results: {
 key: "TestProtection-AllItems-CraftedDreadfulGladiator'sInsigniaofVictory-93611"
 value: {
  dps: 130325.61119
  tps: 901928.98804
  dtps: 60558.49432
  hps: 71670.18831
 }
}
dps_results: {
 key: "TestProtection-AllItems-CraftedMalevolentGladiator'sBadgeofConquest-98755"
 value: {
  dps: 128421.56142
  tps: 888926.56347
  dtps: 61797.09283
  hps: 70532.5148
 }
}
dps_results: {
 key: "TestProtection-AllItems-CraftedMalevolentGladiator'sBadgeofDominance-98910"
 value: {
  dps: 128462.88044
  tps: 889003.50842
  dtps: 61797.09283
  hps: 70687.53008
 }
}
dps_results: {
 key: "TestProtection-AllItems-CraftedMalevolentGladiator'sBadgeofVictory-98912"
 value: {
  dps: 129824.99782
  tps: 898260.96003
  dtps: 60619.98941
  hps: 71580.87908
 }
}
dps_results: {
 key: "TestProtection-AllItems-CraftedMalevolentGladiator'sEmblemofCruelty-98811"
 value: {
  dps: 130284.58528
  tps: 901556.09313
  dtps: 61739.60476
  hps: 71245.74821
 }
}
dps_results: {
 key: "TestProtection-AllItems-CraftedMalevolentGladiator'sEmblemofMeditation-98813"
 value: {
  dps: 128329.154
  tps: 888279.06155
  dtps: 61728.44327
  hps: 70597.99356
 }
}
dps_results: {
 key: "TestProtection-AllItems-CraftedMalevolentGladiator'sEmblemofTenacity-98812"
 value: {
  dps: 128272.89099
  tps: 887816.84899
  dtps: 61213.44928
  hps: 70686.55147
 }
}
dps_results: {
 key: "TestProtection-AllItems-CraftedMalevolentGladiator'sInsigniaofConquest-98760"
 value: {
  dps: 128441.44001
  tps: 889065.71364
  dtps: 61797.05941
  hps: 70532.16133
 }
}
dps_results: {
 key: "TestProtection-AllItems-CraftedMalevolentGladiator'sInsigniaofDominance-98911"
 value: {
  dps: 128504.85707
  tps: 889200.75209
  dtps: 61797.05941
  hps: 70761.09114
 }
}
dps_results: {
 key: "TestProtection-AllItems-CraftedMalevolentGladiator'sInsigniaofVictory-98917"
 value: {
  dps: 130782.63403
  tps: 905053.56618
  dtps: 60399.87655
  hps: 71982.90075
 }
}
dps_results: {
 key: "TestProtection-AllItems-CurseofHubris-102307"
 value: {
  dps: 133693.04548
  tps: 923056.25445
  dtps: 61147.68758
  hps: 73427.1804
 }
}
dps_results: {
 key: "TestProtection-AllItems-CurseofHubris-104649"
 value: {
  dps: 134132.93229
  tps: 925934.67425
  dtps: 61221.73072
  hps: 73811.56327
 }
}
dps_results: {
 key: "TestProtection-AllItems-CurseofHubris-104898"
 value: {
  dps: 133106.94229
  tps: 919030.09628
  dtps: 61313.6027
  hps: 73139.88477
 }
}
dps_results: {
 key: "TestProtection-AllItems-CurseofHubris-105147"
 value: {
  dps: 132541.42421
  tps: 915267.04529
  dtps: 61339.74861
  hps: 73075.42648
 }
}
dps_results: {
 key: "TestProtection-AllItems-CurseofHubris-105396"
 value: {
  dps: 134065.94493
  tps: 925566.15688
  dtps: 61058.24764
  hps: 73486.49421
 }
}
dps_results: {
 key: "TestProtection-AllItems-CurseofHubris-105645"
 value: {
  dps: 134555.50234
  tps: 928702.97902
  dtps: 60925.31808
  hps: 74059.77803
 }
}
dps_results: {
 key: "TestProtection-AllItems-CutstitcherMedallion-93255"
 value: {
  dps: 128471.81748
  tps: 889192.0409
  dtps: 61802.38162
  hps: 70718.39253
 }
}
dps_results: {
 key: "TestProtection-AllItems-Daelo'sFinalWords-87496"
 value: {
  dps: 130189.12139
  tps: 900271.55559
  dtps: 60660.47375
  hps: 72237.46727
 }
}
dps_results: {
 key: "TestProtection-AllItems-DarkglowEmbroidery(Rank3)-4893"
 value: {
  dps: 132886.68425
  tps: 919826.44475
  dtps: 60271.65691
  hps: 73611.255
 }
}
dps_results: {
 key: "TestProtection-AllItems-DeadeyeBadgeoftheShieldwall-93346"
 value: {
  dps: 128455.46
  tps: 889164.05352
  dtps: 60900.20075
  hps: 71039.26472
 }
}
dps_results: {
 key: "TestProtection-AllItems-DelicateVialoftheSanguinaire-96895"
 value: {
  dps: 128674.98703
  tps: 890700.19272
  dtps: 59645.20071
  hps: 70637.25443
 }
}
dps_results: {
 key: "TestProtection-AllItems-DestructivePrimalDiamond"
 value: {
  dps: 133646.95946
  tps: 925071.13702
  dtps: 61058.10961
  hps: 74052.6796
 }
}
dps_results: {
 key: "TestProtection-AllItems-DisciplineofXuen-103986"
 value: {
  dps: 128469.65316
  tps: 889262.38064
  dtps: 59440.28136
  hps: 71707.23186
 }
}
dps_results: {
 key: "TestProtection-AllItems-Dominator'sArcaneBadge-93342"
 value: {
  dps: 129822.67249
  tps: 898296.73054
  dtps: 61184.43078
  hps: 72428.10485
 }
}
dps_results: {
 key: "TestProtection-AllItems-Dominator'sDeadeyeBadge-93341"
 value: {
  dps: 128455.46
  tps: 889164.05352
  dtps: 60900.20075
  hps: 71039.26472
 }
}
dps_results: {
 key: "TestProtection-AllItems-Dominator'sDurableBadge-93345"
 value: {
  dps: 128745.73608
  tps: 891196.5861
  dtps: 59790.91577
  hps: 71121.89997
 }
}
dps_results: {
 key: "TestProtection-AllItems-Dominator'sKnightlyBadge-93344"
 value: {
  dps: 130122.95112
  tps: 900671.88129
  dtps: 59904.2911
  hps: 71921.76693
 }
}
dps_results: {
 key: "TestProtection-AllItems-Dominator'sMendingBadge-93343"
 value: {
  dps: 128421.51108
  tps: 888926.21106
  dtps: 61802.38162
  hps: 70670.49027
 }
}
dps_results: {
 key: "TestProtection-AllItems-DreadfulGladiator'sBadgeofConquest-84344"
 value: {
  dps: 128373.27856
  tps: 888588.58347
  dtps: 61797.09283
  hps: 70532.5148
 }
}
dps_results: {
 key: "TestProtection-AllItems-DreadfulGladiator'sBadgeofDominance-84488"
 value: {
  dps: 128458.7327
  tps: 888974.47425
  dtps: 61797.09283
  hps: 70648.12076
 }
}
dps_results: {
 key: "TestProtection-AllItems-DreadfulGladiator'sBadgeofVictory-84490"
 value: {
  dps: 129551.68025
  tps: 896422.85208
  dtps: 60885.71237
  hps: 71360.18888
 }
}
dps_results: {
 key: "TestProtection-AllItems-DreadfulGladiator'sEmblemofCruelty-84399"
 value: {
  dps: 129936.05622
  tps: 899218.49614
  dtps: 61739.60476
  hps: 71141.51391
 }
}
dps_results: {
 key: "TestProtection-AllItems-DreadfulGladiator'sEmblemofMeditation-84401"
 value: {
  dps: 128329.154
  tps: 888279.06155
  dtps: 61728.44327
  hps: 70597.99356
 }
}
dps_results: {
 key: "TestProtection-AllItems-DreadfulGladiator'sEmblemofTenacity-84400"
 value: {
  dps: 128329.42698
  tps: 888281.7124
  dtps: 60850.53706
  hps: 70834.03923
 }
}
dps_results: {
 key: "TestProtection-AllItems-DreadfulGladiator'sInsigniaofConquest-84349"
 value: {
  dps: 128458.02554
  tps: 889181.81232
  dtps: 61797.09283
  hps: 70532.45389
 }
}
dps_results: {
 key: "TestProtection-AllItems-DreadfulGladiator'sInsigniaofDominance-84489"
 value: {
  dps: 128470.80763
  tps: 889184.97191
  dtps: 61797.05941
  hps: 70684.60437
 }
}
dps_results: {
 key: "TestProtection-AllItems-DreadfulGladiator'sInsigniaofVictory-84495"
 value: {
  dps: 130150.73612
  tps: 900567.35766
  dtps: 60478.00877
  hps: 71763.47122
 }
}
dps_results: {
 key: "TestProtection-AllItems-DurableBadgeoftheShieldwall-93350"
 value: {
  dps: 128745.73608
  tps: 891196.5861
  dtps: 59790.91577
  hps: 71121.89997
 }
}
dps_results: {
 key: "TestProtection-AllItems-EffulgentPrimalDiamond"
 value: {
  dps: 132871.08337
  tps: 919717.41361
  dtps: 61021.34061
  hps: 73562.53956
 }
}
dps_results: {
 key: "TestProtection-AllItems-EmberPrimalDiamond"
 value: {
  dps: 132854.95301
  tps: 919664.94525
  dtps: 61058.10961
  hps: 73829.42815
 }
}
dps_results: {
 key: "TestProtection-AllItems-EmblemofKypariZar-84077"
 value: {
  dps: 130822.26988
  tps: 905907.5681
  dtps: 61296.00471
  hps: 71568.70404
 }
}
dps_results: {
 key: "TestProtection-AllItems-EmblemoftheCatacombs-83733"
 value: {
  dps: 130103.04506
  tps: 900867.66918
  dtps: 60238.86485
  hps: 71687.13474
 }
}
dps_results: {
 key: "TestProtection-AllItems-EmptyFruitBarrel-81133"
 value: {
  dps: 128430.03526
  tps: 888985.88037
  dtps: 61797.05941
  hps: 70683.19019
 }
}
dps_results: {
 key: "TestProtection-AllItems-EnchantWeapon-BloodyDancingSteel-5125"
 value: {
  dps: 133348.74354
  tps: 922975.79201
  dtps: 59638.72507
  hps: 74328.10153
 }
}
dps_results: {
 key: "TestProtection-AllItems-EnchantWeapon-Colossus-4445"
 value: {
  dps: 131750.43023
  tps: 912084.40445
  dtps: 59413.41581
  hps: 73207.25048
 }
}
dps_results: {
 key: "TestProtection-AllItems-EnchantWeapon-DancingSteel-4444"
 value: {
  dps: 134008.51412
  tps: 927597.22105
  dtps: 59579.01705
  hps: 74205.8429
 }
}
dps_results: {
 key: "TestProtection-AllItems-EnchantWeapon-JadeSpirit-4442"
 value: {
  dps: 131870.47553
  tps: 912924.7216
  dtps: 60454.81972
  hps: 73326.67173
 }
}
dps_results: {
 key: "TestProtection-AllItems-EnchantWeapon-River'sSong-4446"
 value: {
  dps: 132086.89862
  tps: 914532.20637
  dtps: 59658.62275
  hps: 73382.92611
 }
}
dps_results: {
 key: "TestProtection-AllItems-EnchantWeapon-SpiritofConquest-5124"
 value: {
  dps: 131771.26693
  tps: 912230.2614
  dtps: 60454.81972
  hps: 73207.37036
 }
}
dps_results: {
 key: "TestProtection-AllItems-EnigmaticPrimalDiamond"
 value: {
  dps: 133646.95946
  tps: 925071.13702
  dtps: 61058.10961
  hps: 74052.6796
 }
}
dps_results: {
 key: "TestProtection-AllItems-EssenceofTerror-87175"
 value: {
  dps: 130402.11837
  tps: 902782.96481
  dtps: 61375.95943
  hps: 71738.94051
 }
}
dps_results: {
 key: "TestProtection-AllItems-EternalPrimalDiamond"
 value: {
  dps: 133140.01108
  tps: 921661.48673
  dtps: 60400.95019
  hps: 73898.09949
 }
}
dps_results: {
 key: "TestProtection-AllItems-EvilEyeofGalakras-105491"
 value: {
  dps: 139774.92898
  tps: 967971.57246
  dtps: 51792.49742
  hps: 78590.15323
 }
}
dps_results: {
 key: "TestProtection-AllItems-FabledFeatherofJi-Kun-96842"
 value: {
  dps: 140788.06097
  tps: 975331.09831
  dtps: 57118.85995
  hps: 76626.794
 }
}
dps_results: {
 key: "TestProtection-AllItems-FearwurmBadge-84074"
 value: {
  dps: 130751.31505
  tps: 905498.68784
  dtps: 61173.12667
  hps: 71551.10859
 }
}
dps_results: {
 key: "TestProtection-AllItems-FearwurmRelic-84070"
 value: {
  dps: 131184.91832
  tps: 908069.36857
  dtps: 61442.87902
  hps: 71674.91195
 }
}
dps_results: {
 key: "TestProtection-AllItems-FelsoulIdolofDestruction-101263"
 value: {
  dps: 130223.52238
  tps: 901470.28442
  dtps: 59820.93364
  hps: 71971.34879
 }
}
dps_results: {
 key: "TestProtection-AllItems-FelsoulStoneofDestruction-101266"
 value: {
  dps: 128465.91828
  tps: 889236.83648
  dtps: 60381.181
  hps: 71400.38656
 }
}
dps_results: {
 key: "TestProtection-AllItems-FlashfrozenResinGlobule-100951"
 value: {
  dps: 128457.89474
  tps: 888872.01583
  dtps: 61797.05941
  hps: 70689.74933
 }
}
dps_results: {
 key: "TestProtection-AllItems-FlashfrozenResinGlobule-81263"
 value: {
  dps: 128478.40575
  tps: 888913.48643
  dtps: 61797.05941
  hps: 70689.74933
 }
}
dps_results: {
 key: "TestProtection-AllItems-FlashingSteelTalisman-81265"
 value: {
  dps: 128416.77235
  tps: 888893.03996
  dtps: 61797.05941
  hps: 70532.4579
 }
}
dps_results: {
 key: "TestProtection-AllItems-FleetPrimalDiamond"
 value: {
  dps: 132960.62124
  tps: 920404.67287
  dtps: 60615.42563
  hps: 74029.72692
 }
}
dps_results: {
 key: "TestProtection-AllItems-ForlornPrimalDiamond"
 value: {
  dps: 132854.95301
  tps: 919664.94525
  dtps: 61058.10961
  hps: 73829.42815
 }
}
dps_results: {
 key: "TestProtection-AllItems-FortitudeoftheZandalari-94516"
 value: {
  dps: 128374.27117
  tps: 888594.99674
  dtps: 60162.32165
  hps: 71202.56299
 }
}
dps_results: {
 key: "TestProtection-AllItems-FortitudeoftheZandalari-95677"
 value: {
  dps: 128349.89065
  tps: 888424.43305
  dtps: 60404.27791
  hps: 71048.13273
 }
}
dps_results: {
 key: "TestProtection-AllItems-FortitudeoftheZandalari-96049"
 value: {
  dps: 128398.19078
  tps: 888762.23398
  dtps: 60053.09039
  hps: 71204.54497
 }
}
dps_results: {
 key: "TestProtection-AllItems-FortitudeoftheZandalari-96421"
 value: {
  dps: 128446.10522
  tps: 889097.53509
  dtps: 59974.47574
  hps: 71286.00214
 }
}
dps_results: {
 key: "TestProtection-AllItems-FortitudeoftheZandalari-96793"
 value: {
  dps: 128522.98663
  tps: 889636.07993
  dtps: 59846.10959
  hps: 71332.13228
 }
}
dps_results: {
 key: "TestProtection-AllItems-GazeoftheTwins-96915"
 value: {
  dps: 134035.82699
  tps: 927582.34595
  dtps: 59888.90899
  hps: 72876.60401
 }
}
dps_results: {
 key: "TestProtection-AllItems-Gerp'sPerfectArrow-87495"
 value: {
  dps: 129923.48708
  tps: 899227.75495
  dtps: 61808.25432
  hps: 70965.4579
 }
}
dps_results: {
 key: "TestProtection-AllItems-Gladiator'sVindication"
 value: {
  dps: 153360.18499
  tps: 1.06135219427e+06
  dtps: 56124.85216
  hps: 79050.81418
 }
}
dps_results: {
 key: "TestProtection-AllItems-GrievousGladiator'sBadgeofConquest-100195"
 value: {
  dps: 128475.60054
  tps: 889304.83734
  dtps: 61797.09283
  hps: 70532.5148
 }
}
dps_results: {
 key: "TestProtection-AllItems-GrievousGladiator'sBadgeofConquest-100603"
 value: {
  dps: 128475.60054
  tps: 889304.83734
  dtps: 61797.09283
  hps: 70532.5148
 }
}
dps_results: {
 key: "TestProtection-AllItems-GrievousGladiator'sBadgeofConquest-102856"
 value: {
  dps: 128475.60054
  tps: 889304.83734
  dtps: 61797.09283
  hps: 70532.5148
 }
}
dps_results: {
 key: "TestProtection-AllItems-GrievousGladiator'sBadgeofConquest-103145"
 value: {
  dps: 128475.60054
  tps: 889304.83734
  dtps: 61797.09283
  hps: 70532.5148
 }
}
dps_results: {
 key: "TestProtection-AllItems-GrievousGladiator'sBadgeofDominance-100490"
 value: {
  dps: 128510.02514
  tps: 889236.92861
  dtps: 61797.09283
  hps: 70797.33318
 }
}
dps_results: {
 key: "TestProtection-AllItems-GrievousGladiator'sBadgeofDominance-100576"
 value: {
  dps: 128510.02514
  tps: 889236.92861
  dtps: 61797.09283
  hps: 70797.33318
 }
}
dps_results: {
 key: "TestProtection-AllItems-GrievousGladiator'sBadgeofDominance-102830"
 value: {
  dps: 128510.02514
  tps: 889236.92861
  dtps: 61797.09283
  hps: 70797.33318
 }
}
dps_results: {
 key: "TestProtection-AllItems-GrievousGladiator'sBadgeofDominance-103308"
 value: {
  dps: 128510.02514
  tps: 889236.92861
  dtps: 61797.09283
  hps: 70797.33318
 }
}
dps_results: {
 key: "TestProtection-AllItems-GrievousGladiator'sBadgeofVictory-100500"
 value: {
  dps: 130629.11939
  tps: 903815.87299
  dtps: 60396.66666
  hps: 72046.68897
 }
}
dps_results: {
 key: "TestProtection-AllItems-GrievousGladiator'sBadgeofVictory-100579"
 value: {
  dps: 130629.11939
  tps: 903815.87299
  dtps: 60396.66666
  hps: 72046.68897
 }
}
dps_results: {
 key: "TestProtection-AllItems-GrievousGladiator'sBadgeofVictory-102833"
 value: {
  dps: 130629.11939
  tps: 903815.87299
  dtps: 60396.66666
  hps: 72046.68897
 }
}
dps_results: {
 key: "TestProtection-AllItems-GrievousGladiator'sBadgeofVictory-103314"
 value: {
  dps: 130629.11939
  tps: 903815.87299
  dtps: 60396.66666
  hps: 72046.68897
 }
}
dps_results: {
 key: "TestProtection-AllItems-GrievousGladiator'sEmblemofCruelty-100305"
 value: {
  dps: 131663.46858
  tps: 911112.66502
  dtps: 61739.60476
  hps: 71574.80483
 }
}
dps_results: {
 key: "TestProtection-AllItems-GrievousGladiator'sEmblemofCruelty-100626"
 value: {
  dps: 131663.46858
  tps: 911112.66502
  dtps: 61739.60476
  hps: 71574.80483
 }
}
dps_results: {
 key: "TestProtection-AllItems-GrievousGladiator'sEmblemofCruelty-102877"
 value: {
  dps: 131663.46858
  tps: 911112.66502
  dtps: 61739.60476
  hps: 71574.80483
 }
}
dps_results: {
 key: "TestProtection-AllItems-GrievousGladiator'sEmblemofCruelty-103210"
 value: {
  dps: 131663.46858
  tps: 911112.66502
  dtps: 61739.60476
  hps: 71574.80483
 }
}
dps_results: {
 key: "TestProtection-AllItems-GrievousGladiator'sEmblemofMeditation-100307"
 value: {
  dps: 128329.154
  tps: 888279.06155
  dtps: 61728.44327
  hps: 70597.99356
 }
}
dps_results: {
 key: "TestProtection-AllItems-GrievousGladiator'sEmblemofMeditation-100559"
 value: {
  dps: 128329.154
  tps: 888279.06155
  dtps: 61728.44327
  hps: 70597.99356
 }
}
dps_results: {
 key: "TestProtection-AllItems-GrievousGladiator'sEmblemofMeditation-102813"
 value: {
  dps: 128329.154
  tps: 888279.06155
  dtps: 61728.44327
  hps: 70597.99356
 }
}
dps_results: {
 key: "TestProtection-AllItems-GrievousGladiator'sEmblemofMeditation-103212"
 value: {
  dps: 128329.154
  tps: 888279.06155
  dtps: 61728.44327
  hps: 70597.99356
 }
}
dps_results: {
 key: "TestProtection-AllItems-GrievousGladiator'sEmblemofTenacity-100306"
 value: {
  dps: 128454.2766
  tps: 889134.64015
  dtps: 61289.30053
  hps: 70641.37515
 }
}
dps_results: {
 key: "TestProtection-AllItems-GrievousGladiator'sEmblemofTenacity-100652"
 value: {
  dps: 128454.2766
  tps: 889134.64015
  dtps: 61289.30053
  hps: 70641.37515
 }
}
dps_results: {
 key: "TestProtection-AllItems-GrievousGladiator'sEmblemofTenacity-102903"
 value: {
  dps: 128454.2766
  tps: 889134.64015
  dtps: 61289.30053
  hps: 70641.37515
 }
}
dps_results: {
 key: "TestProtection-AllItems-GrievousGladiator'sEmblemofTenacity-103211"
 value: {
  dps: 128454.2766
  tps: 889134.64015
  dtps: 61289.30053
  hps: 70641.37515
 }
}
dps_results: {
 key: "TestProtection-AllItems-GrievousGladiator'sInsigniaofConquest-103150"
 value: {
  dps: 128526.73786
  tps: 889662.79855
  dtps: 61797.05941
  hps: 70532.21952
 }
}
dps_results: {
 key: "TestProtection-AllItems-GrievousGladiator'sInsigniaofDominance-103309"
 value: {
  dps: 128679.94855
  tps: 890228.67487
  dtps: 61800.98027
  hps: 70838.15321
 }
}
dps_results: {
 key: "TestProtection-AllItems-GrievousGladiator'sInsigniaofVictory-103319"
 value: {
  dps: 131872.90987
  tps: 912605.25924
  dtps: 60098.28943
  hps: 72494.01539
 }
}
dps_results: {
 key: "TestProtection-AllItems-Hawkmaster'sTalon-89082"
 value: {
  dps: 130223.93971
  tps: 901137.96327
  dtps: 61518.46404
  hps: 72368.89337
 }
}
dps_results: {
 key: "TestProtection-AllItems-Heart-LesionDefenderIdol-100999"
 value: {
  dps: 128146.92814
  tps: 887148.46005
  dtps: 60352.69505
  hps: 70581.69944
 }
}
dps_results: {
 key: "TestProtection-AllItems-Heart-LesionDefenderStone-101002"
 value: {
  dps: 128338.14137
  tps: 888273.3916
  dtps: 59804.08208
  hps: 71395.88142
 }
}
dps_results: {
 key: "TestProtection-AllItems-Heart-LesionIdolofBattle-100991"
 value: {
  dps: 132481.0654
  tps: 916663.90655
  dtps: 60272.60189
  hps: 72570.34534
 }
}
dps_results: {
 key: "TestProtection-AllItems-Heart-LesionStoneofBattle-100990"
 value: {
  dps: 130642.83171
  tps: 904266.37508
  dtps: 59228.68795
  hps: 72250.32021
 }
}
dps_results: {
 key: "TestProtection-AllItems-HeartofFire-81181"
 value: {
  dps: 128174.31166
  tps: 887409.43118
  dtps: 60110.88894
  hps: 70706.46139
 }
}
dps_results: {
 key: "TestProtection-AllItems-HeartwarmerMedallion-93260"
 value: {
  dps: 128471.81748
  tps: 889192.0409
  dtps: 61802.38162
  hps: 70718.39253
 }
}
dps_results: {
 key: "TestProtection-AllItems-HelmbreakerMedallion-93261"
 value: {
  dps: 133074.60067
  tps: 919989.18339
  dtps: 60637.96475
  hps: 73049.16924
 }
}
dps_results: {
 key: "TestProtection-AllItems-Horridon'sLastGasp-96757"
 value: {
  dps: 128539.78962
  tps: 889567.50097
  dtps: 61802.38964
  hps: 70774.8937
 }
}
dps_results: {
 key: "TestProtection-AllItems-ImpassivePrimalDiamond"
 value: {
  dps: 133646.95946
  tps: 925071.13702
  dtps: 61058.10961
  hps: 74052.6796
 }
}
dps_results: {
 key: "TestProtection-AllItems-IndomitablePrimalDiamond"
 value: {
  dps: 132871.08337
  tps: 919717.41361
  dtps: 61021.34061
  hps: 73562.53956
 }
}
dps_results: {
 key: "TestProtection-AllItems-InscribedBagofHydra-Spawn-96828"
 value: {
  dps: 128389.25008
  tps: 888700.33408
  dtps: 60513.1193
  hps: 70547.87731
 }
}
dps_results: {
 key: "TestProtection-AllItems-InsigniaofKypariZar-84078"
 value: {
  dps: 128700.4686
  tps: 890930.29135
  dtps: 60090.34908
  hps: 70578.27967
 }
}
dps_results: {
 key: "TestProtection-AllItems-IronBellyWok-89083"
 value: {
  dps: 132691.17637
  tps: 918182.74182
  dtps: 60688.06632
  hps: 73523.04299
 }
}
dps_results: {
 key: "TestProtection-AllItems-IronProtectorTalisman-85181"
 value: {
  dps: 128315.22924
  tps: 888395.89925
  dtps: 60590.6414
  hps: 70526.54124
 }
}
dps_results: {
 key: "TestProtection-AllItems-JadeBanditFigurine-86043"
 value: {
  dps: 130223.93971
  tps: 901137.96327
  dtps: 61518.46404
  hps: 72368.89337
 }
}
dps_results: {
 key: "TestProtection-AllItems-JadeBanditFigurine-86772"
 value: {
  dps: 130073.25948
  tps: 899939.07256
  dtps: 61410.79341
  hps: 71834.46631
 }
}
dps_results: {
 key: "TestProtection-AllItems-JadeCharioteerFigurine-86042"
 value: {
  dps: 132691.17637
  tps: 918182.74182
  dtps: 60688.06632
  hps: 73523.04299
 }
}
dps_results: {
 key: "TestProtection-AllItems-JadeCharioteerFigurine-86771"
 value: {
  dps: 131706.87274
  tps: 911168.92217
  dtps: 60726.37096
  hps: 72970.23262
 }
}
dps_results: {
 key: "TestProtection-AllItems-JadeCourtesanFigurine-86045"
 value: {
  dps: 128459.48677
  tps: 889192.0409
  dtps: 61802.38162
  hps: 70691.85414
 }
}
dps_results: {
 key: "TestProtection-AllItems-JadeCourtesanFigurine-86774"
 value: {
  dps: 128446.81325
  tps: 889103.32626
  dtps: 61802.38162
  hps: 70670.49027
 }
}
dps_results: {
 key: "TestProtection-AllItems-JadeMagistrateFigurine-86044"
 value: {
  dps: 130673.10796
  tps: 903440.219
  dtps: 61802.38162
  hps: 71910.94358
 }
}
dps_results: {
 key: "TestProtection-AllItems-JadeMagistrateFigurine-86773"
 value: {
  dps: 130547.8221
  tps: 902789.18752
  dtps: 61802.38162
  hps: 71802.95201
 }
}
dps_results: {
 key: "TestProtection-AllItems-JadeWarlordFigurine-86046"
 value: {
  dps: 128520.80165
  tps: 889552.37862
  dtps: 60271.35972
  hps: 71458.1827
 }
}
dps_results: {
 key: "TestProtection-AllItems-JadeWarlordFigurine-86775"
 value: {
  dps: 128365.50886
  tps: 888534.42057
  dtps: 60313.29671
  hps: 71362.74284
 }
}
dps_results: {
 key: "TestProtection-AllItems-Ji-Kun'sRisingWinds-96843"
 value: {
  dps: 128363.55353
  tps: 888520.50824
  dtps: 61797.92125
  hps: 71109.45631
 }
}
dps_results: {
 key: "TestProtection-AllItems-KnightlyBadgeoftheShieldwall-93349"
 value: {
  dps: 130122.95112
  tps: 900671.88129
  dtps: 59904.2911
  hps: 71921.76693
 }
}
dps_results: {
 key: "TestProtection-AllItems-KnotofTenSongs-84073"
 value: {
  dps: 128699.17215
  tps: 890921.45115
  dtps: 60262.85094
  hps: 70589.21834
 }
}
dps_results: {
 key: "TestProtection-AllItems-Kor'kronBookofHurting-92785"
 value: {
  dps: 128363.33161
  tps: 888518.95479
  dtps: 61797.10085
  hps: 70532.75106
 }
}
dps_results: {
 key: "TestProtection-AllItems-Lao-Chin'sLiquidCourage-89079"
 value: {
  dps: 128520.80165
  tps: 889552.37862
  dtps: 60271.35972
  hps: 71458.1827
 }
}
dps_results: {
 key: "TestProtection-AllItems-LessonsoftheDarkmaster-81268"
 value: {
  dps: 130056.67284
  tps: 899697.59922
  dtps: 61008.74371
  hps: 71469.27448
 }
}
dps_results: {
 key: "TestProtection-AllItems-LightdrinkerIdolofRage-101200"
 value: {
  dps: 128411.91075
  tps: 888858.94875
  dtps: 60592.4994
  hps: 70938.45654
 }
}
dps_results: {
 key: "TestProtection-AllItems-LightdrinkerStoneofRage-101203"
 value: {
  dps: 128529.07734
  tps: 889679.24993
  dtps: 60309.36861
  hps: 71301.75119
 }
}
dps_results: {
 key: "TestProtection-AllItems-LightoftheCosmos-87065"
 value: {
  dps: 130997.7746
  tps: 906697.24532
  dtps: 60754.82946
  hps: 72316.14998
 }
}
dps_results: {
 key: "TestProtection-AllItems-LightweaveEmbroidery(Rank3)-4892"
 value: {
  dps: 132968.30825
  tps: 920397.81278
  dtps: 60271.65691
  hps: 73660.17355
 }
}
dps_results: {
 key: "TestProtection-AllItems-LordBlastington'sScopeofDoom-4699"
 value: {
  dps: 131771.26693
  tps: 912230.2614
  dtps: 60454.81972
  hps: 73207.37036
 }
}
dps_results: {
 key: "TestProtection-AllItems-MalevolentGladiator'sBadgeofConquest-84934"
 value: {
  dps: 128421.56142
  tps: 888926.56347
  dtps: 61797.09283
  hps: 70532.5148
 }
}
dps_results: {
 key: "TestProtection-AllItems-MalevolentGladiator'sBadgeofConquest-91452"
 value: {
  dps: 128421.56142
  tps: 888926.56347
  dtps: 61797.09283
  hps: 70532.5148
 }
}
dps_results: {
 key: "TestProtection-AllItems-MalevolentGladiator'sBadgeofDominance-84940"
 value: {
  dps: 128462.88044
  tps: 889003.50842
  dtps: 61797.09283
  hps: 70687.53008
 }
}
dps_results: {
 key: "TestProtection-AllItems-MalevolentGladiator'sBadgeofDominance-91753"
 value: {
  dps: 128462.88044
  tps: 889003.50842
  dtps: 61797.09283
  hps: 70687.53008
 }
}
dps_results: {
 key: "TestProtection-AllItems-MalevolentGladiator'sBadgeofVictory-84942"
 value: {
  dps: 129931.47066
  tps: 898972.91231
  dtps: 60620.15851
  hps: 71650.86607
 }
}
dps_results: {
 key: "TestProtection-AllItems-MalevolentGladiator'sBadgeofVictory-91763"
 value: {
  dps: 129824.99782
  tps: 898260.96003
  dtps: 60619.98941
  hps: 71580.87908
 }
}
dps_results: {
 key: "TestProtection-AllItems-MalevolentGladiator'sEmblemofCruelty-84936"
 value: {
  dps: 130446.50995
  tps: 902689.56586
  dtps: 61739.60476
  hps: 71280.21335
 }
}
dps_results: {
 key: "TestProtection-AllItems-MalevolentGladiator'sEmblemofCruelty-91562"
 value: {
  dps: 130284.58528
  tps: 901556.09313
  dtps: 61739.60476
  hps: 71245.74821
 }
}
dps_results: {
 key: "TestProtection-AllItems-MalevolentGladiator'sEmblemofMeditation-84939"
 value: {
  dps: 128329.154
  tps: 888279.06155
  dtps: 61728.44327
  hps: 70597.99356
 }
}
dps_results: {
 key: "TestProtection-AllItems-MalevolentGladiator'sEmblemofMeditation-91564"
 value: {
  dps: 128329.154
  tps: 888279.06155
  dtps: 61728.44327
  hps: 70597.99356
 }
}
dps_results: {
 key: "TestProtection-AllItems-MalevolentGladiator'sEmblemofTenacity-84938"
 value: {
  dps: 128278.40445
  tps: 887855.44321
  dtps: 61219.92084
  hps: 70670.58931
 }
}
dps_results: {
 key: "TestProtection-AllItems-MalevolentGladiator'sEmblemofTenacity-91563"
 value: {
  dps: 128272.89099
  tps: 887816.84899
  dtps: 61213.44928
  hps: 70686.55147
 }
}
dps_results: {
 key: "TestProtection-AllItems-MalevolentGladiator'sInsigniaofConquest-91457"
 value: {
  dps: 128468.3056
  tps: 889253.77272
  dtps: 61797.09283
  hps: 70532.77762
 }
}
dps_results: {
 key: "TestProtection-AllItems-MalevolentGladiator'sInsigniaofDominance-91754"
 value: {
  dps: 128516.97119
  tps: 889382.14368
  dtps: 61797.05941
  hps: 70756.18051
 }
}
dps_results: {
 key: "TestProtection-AllItems-MalevolentGladiator'sInsigniaofVictory-91768"
 value: {
  dps: 130834.55184
  tps: 905432.32156
  dtps: 60375.57045
  hps: 72109.5171
 }
}
dps_results: {
 key: "TestProtection-AllItems-MarkoftheCatacombs-83731"
 value: {
  dps: 129777.05293
  tps: 898202.94085
  dtps: 60961.15198
  hps: 71325.47312
 }
}
dps_results: {
 key: "TestProtection-AllItems-MarkoftheHardenedGrunt-92783"
 value: {
  dps: 128363.43293
  tps: 888519.66405
  dtps: 61797.05941
  hps: 70532.51071
 }
}
dps_results: {
 key: "TestProtection-AllItems-MedallionofMystifyingVapors-93257"
 value: {
  dps: 129055.74464
  tps: 893366.82102
  dtps: 59449.66781
  hps: 71250.83435
 }
}
dps_results: {
 key: "TestProtection-AllItems-MedallionoftheCatacombs-83734"
 value: {
  dps: 128552.21791
  tps: 889842.28394
  dtps: 59910.91503
  hps: 70818.10912
 }
}
dps_results: {
 key: "TestProtection-AllItems-MendingBadgeoftheShieldwall-93348"
 value: {
  dps: 128421.51108
  tps: 888926.21106
  dtps: 61802.38162
  hps: 70670.49027
 }
}
dps_results: {
 key: "TestProtection-AllItems-MirrorScope-4700"
 value: {
  dps: 131771.26693
  tps: 912230.2614
  dtps: 60454.81972
  hps: 73207.37036
 }
}
dps_results: {
 key: "TestProtection-AllItems-MistdancerDefenderIdol-101089"
 value: {
  dps: 127980.36242
  tps: 885931.82137
  dtps: 60297.11193
  hps: 70562.03334
 }
}
dps_results: {
 key: "TestProtection-AllItems-MistdancerDefenderStone-101087"
 value: {
  dps: 128325.89074
  tps: 888187.63719
  dtps: 59827.51261
  hps: 71365.41877
 }
}
dps_results: {
 key: "TestProtection-AllItems-MistdancerIdolofRage-101113"
 value: {
  dps: 128371.14774
  tps: 888573.60772
  dtps: 60592.4747
  hps: 70938.22364
 }
}
dps_results: {
 key: "TestProtection-AllItems-MistdancerStoneofRage-101117"
 value: {
  dps: 128433.04817
  tps: 889006.9457
  dtps: 60537.93283
  hps: 71241.51875
 }
}
dps_results: {
 key: "TestProtection-AllItems-MistdancerStoneofWisdom-101107"
 value: {
  dps: 128619.8884
  tps: 890314.85233
  dtps: 61802.38162
  hps: 70792.29959
 }
}
dps_results: {
 key: "TestProtection-AllItems-MithrilWristwatch-87572"
 value: {
  dps: 130255.60263
  tps: 901455.97106
  dtps: 61808.22089
  hps: 71135.03074
 }
}
dps_results: {
 key: "TestProtection-AllItems-MountainsageIdolofDestruction-101069"
 value: {
  dps: 129925.19952
  tps: 899540.66864
  dtps: 60099.74835
  hps: 71975.28801
 }
}
dps_results: {
 key: "TestProtection-AllItems-MountainsageStoneofDestruction-101072"
 value: {
  dps: 128455.38211
  tps: 889163.28334
  dtps: 60420.46046
  hps: 71404.95614
 }
}
dps_results: {
 key: "TestProtection-AllItems-OathswornDefenderIdol-101303"
 value: {
  dps: 128642.02631
  tps: 890615.64722
  dtps: 60142.2589
  hps: 70756.46297
 }
}
dps_results: {
 key: "TestProtection-AllItems-OathswornDefenderStone-101306"
 value: {
  dps: 128337.61864
  tps: 888269.6575
  dtps: 59971.50862
  hps: 71309.22871
 }
}
dps_results: {
 key: "TestProtection-AllItems-OathswornIdolofBattle-101295"
 value: {
  dps: 132580.02974
  tps: 917319.40289
  dtps: 60572.86403
  hps: 72473.34333
 }
}
dps_results: {
 key: "TestProtection-AllItems-OathswornStoneofBattle-101294"
 value: {
  dps: 130696.67772
  tps: 904643.48718
  dtps: 59198.44678
  hps: 72269.47764
 }
}
dps_results: {
 key: "TestProtection-AllItems-PhaseFingers-4697"
 value: {
  dps: 133026.69273
  tps: 921037.51782
  dtps: 59935.62161
  hps: 73833.2562
 }
}
dps_results: {
 key: "TestProtection-AllItems-PlateofWingedTriumph"
 value: {
  dps: 136839.0625
  tps: 946490.82396
  dtps: 53877.59546
  hps: 82862.42619
 }
}
dps_results: {
 key: "TestProtection-AllItems-PlateoftheLightningEmperor"
 value: {
  dps: 133597.72353
  tps: 924334.12454
  dtps: 52028.53773
  hps: 70183.76147
 }
}
dps_results: {
 key: "TestProtection-AllItems-PouchofWhiteAsh-103639"
 value: {
  dps: 128363.33161
  tps: 888518.95479
  dtps: 61797.10085
  hps: 70532.75106
 }
}
dps_results: {
 key: "TestProtection-AllItems-PowerfulPrimalDiamond"
 value: {
  dps: 132871.08337
  tps: 919717.41361
  dtps: 61021.34061
  hps: 73562.53956
 }
}
dps_results: {
 key: "TestProtection-AllItems-PriceofProgress-81266"
 value: {
  dps: 128421.63167
  tps: 888928.91925
  dtps: 61802.38162
  hps: 70670.52699
 }
}
dps_results: {
 key: "TestProtection-AllItems-PridefulGladiator'sBadgeofConquest-102659"
 value: {
  dps: 128510.20778
  tps: 889547.08797
  dtps: 61797.09283
  hps: 70532.5148
 }
}
dps_results: {
 key: "TestProtection-AllItems-PridefulGladiator'sBadgeofConquest-103342"
 value: {
  dps: 128510.20778
  tps: 889547.08797
  dtps: 61797.09283
  hps: 70532.5148
 }
}
dps_results: {
 key: "TestProtection-AllItems-PridefulGladiator'sBadgeofDominance-102633"
 value: {
  dps: 128615.71487
  tps: 889779.0391
  dtps: 61797.09283
  hps: 70924.00707
 }
}
dps_results: {
 key: "TestProtection-AllItems-PridefulGladiator'sBadgeofDominance-103505"
 value: {
  dps: 128615.71487
  tps: 889779.0391
  dtps: 61797.09283
  hps: 70924.00707
 }
}
dps_results: {
 key: "TestProtection-AllItems-PridefulGladiator'sBadgeofVictory-102636"
 value: {
  dps: 131214.42004
  tps: 907692.61246
  dtps: 60026.91586
  hps: 72553.14281
 }
}
dps_results: {
 key: "TestProtection-AllItems-PridefulGladiator'sBadgeofVictory-103511"
 value: {
  dps: 131214.42004
  tps: 907692.61246
  dtps: 60026.91586
  hps: 72553.14281
 }
}
dps_results: {
 key: "TestProtection-AllItems-PridefulGladiator'sEmblemofCruelty-102680"
 value: {
  dps: 132529.97802
  tps: 917133.57333
  dtps: 61740.31853
  hps: 71994.02518
 }
}
dps_results: {
 key: "TestProtection-AllItems-PridefulGladiator'sEmblemofCruelty-103407"
 value: {
  dps: 132529.97802
  tps: 917133.57333
  dtps: 61740.31853
  hps: 71994.02518
 }
}
dps_results: {
 key: "TestProtection-AllItems-PridefulGladiator'sEmblemofMeditation-102616"
 value: {
  dps: 128329.154
  tps: 888279.06155
  dtps: 61728.44327
  hps: 70597.99356
 }
}
dps_results: {
 key: "TestProtection-AllItems-PridefulGladiator'sEmblemofMeditation-103409"
 value: {
  dps: 128329.154
  tps: 888279.06155
  dtps: 61728.44327
  hps: 70597.99356
 }
}
dps_results: {
 key: "TestProtection-AllItems-PridefulGladiator'sEmblemofTenacity-102706"
 value: {
  dps: 128213.16731
  tps: 887447.24516
  dtps: 61188.38988
  hps: 70638.85186
 }
}
dps_results: {
 key: "TestProtection-AllItems-PridefulGladiator'sEmblemofTenacity-103408"
 value: {
  dps: 128213.16731
  tps: 887447.24516
  dtps: 61188.38988
  hps: 70638.85186
 }
}
dps_results: {
 key: "TestProtection-AllItems-PridefulGladiator'sInsigniaofConquest-103347"
 value: {
  dps: 128574.13963
  tps: 889994.61097
  dtps: 61797.09283
  hps: 70532.90979
 }
}
dps_results: {
 key: "TestProtection-AllItems-PridefulGladiator'sInsigniaofDominance-103506"
 value: {
  dps: 128784.56233
  tps: 890916.31346
  dtps: 61797.05941
  hps: 70996.43168
 }
}
dps_results: {
 key: "TestProtection-AllItems-PridefulGladiator'sInsigniaofVictory-103516"
 value: {
  dps: 133491.74616
  tps: 923533.86222
  dtps: 59672.56998
  hps: 73193.71486
 }
}
dps_results: {
 key: "TestProtection-AllItems-Primordius'TalismanofRage-96873"
 value: {
  dps: 135371.65884
  tps: 936586.59565
  dtps: 60297.5888
  hps: 73473.79446
 }
}
dps_results: {
 key: "TestProtection-AllItems-Qin-xi'sPolarizingSeal-87075"
 value: {
  dps: 128568.40113
  tps: 889954.44147
  dtps: 61802.38162
  hps: 70822.49368
 }
}
dps_results: {
 key: "TestProtection-AllItems-RelicofChi-Ji-79330"
 value: {
  dps: 128471.77554
  tps: 889191.74727
  dtps: 61802.38162
  hps: 70718.44124
 }
}
dps_results: {
 key: "TestProtection-AllItems-RelicofKypariZar-84075"
 value: {
  dps: 131273.18165
  tps: 908955.92384
  dtps: 60813.18442
  hps: 71976.43319
 }
}
dps_results: {
 key: "TestProtection-AllItems-RelicofNiuzao-79329"
 value: {
  dps: 128377.42393
  tps: 888534.83958
  dtps: 59849.77777
  hps: 70564.4866
 }
}
dps_results: {
 key: "TestProtection-AllItems-RelicofXuen-79327"
 value: {
  dps: 131575.38867
  tps: 910463.36585
  dtps: 59570.86312
  hps: 72573.53074
 }
}
dps_results: {
 key: "TestProtection-AllItems-RelicofXuen-79328"
 value: {
  dps: 128422.41272
  tps: 888932.52259
  dtps: 61797.05941
  hps: 70532.6625
 }
}
dps_results: {
 key: "TestProtection-AllItems-RelicofYu'lon-79331"
 value: {
  dps: 128540.46027
  tps: 889758.85543
  dtps: 61802.38162
  hps: 70852.4076
 }
}
dps_results: {
 key: "TestProtection-AllItems-Renataki'sSoulCharm-96741"
 value: {
  dps: 128978.88227
  tps: 892827.8094
  dtps: 61811.22258
  hps: 70532.70091
 }
}
dps_results: {
 key: "TestProtection-AllItems-ResolveofNiuzao-103690"
 value: {
  dps: 128597.0935
  tps: 890298.11219
  dtps: 59403.94848
  hps: 70928.68968
 }
}
dps_results: {
 key: "TestProtection-AllItems-ResolveofNiuzao-103990"
 value: {
  dps: 128541.20052
  tps: 889641.13147
  dtps: 59067.11131
  hps: 71269.94302
 }
}
dps_results: {
 key: "TestProtection-AllItems-ReverberatingPrimalDiamond"
 value: {
  dps: 133812.67671
  tps: 926281.46332
  dtps: 60825.10323
  hps: 74274.09673
 }
}
dps_results: {
 key: "TestProtection-AllItems-RevitalizingPrimalDiamond"
 value: {
  dps: 133335.89519
  tps: 922989.10617
  dtps: 61059.04551
  hps: 74021.10468
 }
}
dps_results: {
 key: "TestProtection-AllItems-SI:7Operative'sManual-92784"
 value: {
  dps: 128363.33161
  tps: 888518.95479
  dtps: 61797.10085
  hps: 70532.75106
 }
}
dps_results: {
 key: "TestProtection-AllItems-ScrollofReveredAncestors-89080"
 value: {
  dps: 128459.48677
  tps: 889192.0409
  dtps: 61802.38162
  hps: 70691.85414
 }
}
dps_results: {
 key: "TestProtection-AllItems-SearingWords-81267"
 value: {
  dps: 130176.6821
  tps: 900903.52734
  dtps: 61808.22892
  hps: 71100.21407
 }
}
dps_results: {
 key: "TestProtection-AllItems-Shock-ChargerMedallion-93259"
 value: {
  dps: 130970.05878
  tps: 906191.52869
  dtps: 60768.70798
  hps: 72202.39803
 }
}
dps_results: {
 key: "TestProtection-AllItems-SigilofCompassion-83736"
 value: {
  dps: 128277.07062
  tps: 887915.0679
  dtps: 61069.0894
  hps: 70763.41441
 }
}
dps_results: {
 key: "TestProtection-AllItems-SigilofDevotion-83740"
 value: {
  dps: 129736.67931
  tps: 897920.00055
  dtps: 61107.42377
  hps: 71322.49858
 }
}
dps_results: {
 key: "TestProtection-AllItems-SigilofFidelity-83737"
 value: {
  dps: 131472.50276
  tps: 910084.08289
  dtps: 61070.28114
  hps: 71890.81546
 }
}
dps_results: {
 key: "TestProtection-AllItems-SigilofGrace-83738"
 value: {
  dps: 129732.09973
  tps: 898431.43877
  dtps: 60675.9048
  hps: 71530.33773
 }
}
dps_results: {
 key: "TestProtection-AllItems-SigilofKypariZar-84076"
 value: {
  dps: 129781.07766
  tps: 898230.989
  dtps: 61100.23914
  hps: 71307.68016
 }
}
dps_results: {
 key: "TestProtection-AllItems-SigilofPatience-83739"
 value: {
  dps: 128849.8608
  tps: 891925.38912
  dtps: 60175.33842
  hps: 70887.31002
 }
}
dps_results: {
 key: "TestProtection-AllItems-SigiloftheCatacombs-83732"
 value: {
  dps: 131065.32611
  tps: 907611.55324
  dtps: 61520.08155
  hps: 71574.40968
 }
}
dps_results: {
 key: "TestProtection-AllItems-SinisterPrimalDiamond"
 value: {
  dps: 133416.36144
  tps: 923506.28291
  dtps: 61058.10961
  hps: 73999.14452
 }
}
dps_results: {
 key: "TestProtection-AllItems-SkullrenderMedallion-93256"
 value: {
  dps: 133074.60067
  tps: 919989.18339
  dtps: 60637.96475
  hps: 73049.16924
 }
}
dps_results: {
 key: "TestProtection-AllItems-SpiritsoftheSun-87163"
 value: {
  dps: 128477.30639
  tps: 889230.46323
  dtps: 61802.38964
  hps: 70728.32296
 }
}
dps_results: {
 key: "TestProtection-AllItems-SpringrainIdolofDestruction-101023"
 value: {
  dps: 129797.69594
  tps: 898651.17855
  dtps: 60255.75613
  hps: 71859.42666
 }
}
dps_results: {
 key: "TestProtection-AllItems-SpringrainIdolofRage-101009"
 value: {
  dps: 128421.21573
  tps: 888924.08368
  dtps: 60592.43326
  hps: 70937.82832
 }
}
dps_results: {
 key: "TestProtection-AllItems-SpringrainStoneofDestruction-101026"
 value: {
  dps: 128462.69556
  tps: 889214.47744
  dtps: 60566.82657
  hps: 71326.84734
 }
}
dps_results: {
 key: "TestProtection-AllItems-SpringrainStoneofRage-101012"
 value: {
  dps: 128457.13929
  tps: 889175.4836
  dtps: 60371.37715
  hps: 71178.58201
 }
}
dps_results: {
 key: "TestProtection-AllItems-SpringrainStoneofWisdom-101041"
 value: {
  dps: 128496.31594
  tps: 889449.84512
  dtps: 61802.41434
  hps: 70821.60512
 }
}
dps_results: {
 key: "TestProtection-AllItems-Static-Caster'sMedallion-93254"
 value: {
  dps: 130970.05878
  tps: 906191.52869
  dtps: 60768.70798
  hps: 72202.39803
 }
}
dps_results: {
 key: "TestProtection-AllItems-SteadfastFootman'sMedallion-92782"
 value: {
  dps: 128363.43293
  tps: 888519.66405
  dtps: 61797.05941
  hps: 70532.51071
 }
}
dps_results: {
 key: "TestProtection-AllItems-SteadfastTalismanoftheShado-PanAssault-94507"
 value: {
  dps: 128552.92517
  tps: 889845.7797
  dtps: 59924.56878
  hps: 71122.53418
 }
}
dps_results: {
 key: "TestProtection-AllItems-StreamtalkerIdolofDestruction-101222"
 value: {
  dps: 130262.16055
  tps: 901742.8716
  dtps: 60536.24815
  hps: 72041.83284
 }
}
dps_results: {
 key: "TestProtection-AllItems-StreamtalkerIdolofRage-101217"
 value: {
  dps: 128413.61626
  tps: 888870.88735
  dtps: 60592.46668
  hps: 70937.79693
 }
}
dps_results: {
 key: "TestProtection-AllItems-StreamtalkerStoneofDestruction-101225"
 value: {
  dps: 128436.57488
  tps: 889031.52269
  dtps: 60368.71484
  hps: 71373.48944
 }
}
dps_results: {
 key: "TestProtection-AllItems-StreamtalkerStoneofRage-101220"
 value: {
  dps: 128437.73759
  tps: 889039.54665
  dtps: 60459.15521
  hps: 71281.00804
 }
}
dps_results: {
 key: "TestProtection-AllItems-StreamtalkerStoneofWisdom-101250"
 value: {
  dps: 128608.14037
  tps: 890232.61612
  dtps: 61807.37233
  hps: 70851.93449
 }
}
dps_results: {
 key: "TestProtection-AllItems-StuffofNightmares-87160"
 value: {
  dps: 128676.78291
  tps: 890754.40504
  dtps: 59356.68573
  hps: 70973.50069
 }
}
dps_results: {
 key: "TestProtection-AllItems-SunsoulDefenderIdol-101160"
 value: {
  dps: 128109.33016
  tps: 886849.3428
  dtps: 60036.27815
  hps: 70605.91306
 }
}
dps_results: {
 key: "TestProtection-AllItems-SunsoulDefenderStone-101163"
 value: {
  dps: 128325.15016
  tps: 888182.45319
  dtps: 59824.6081
  hps: 71320.78877
 }
}
dps_results: {
 key: "TestProtection-AllItems-SunsoulIdolofBattle-101152"
 value: {
  dps: 132462.53631
  tps: 916355.38861
  dtps: 60444.62538
  hps: 72536.6303
 }
}
dps_results: {
 key: "TestProtection-AllItems-SunsoulStoneofBattle-101151"
 value: {
  dps: 130574.42288
  tps: 903788.20327
  dtps: 59146.16847
  hps: 72318.58403
 }
}
dps_results: {
 key: "TestProtection-AllItems-SunsoulStoneofWisdom-101138"
 value: {
  dps: 128569.29119
  tps: 889960.67189
  dtps: 61807.37233
  hps: 70935.15784
 }
}
dps_results: {
 key: "TestProtection-AllItems-SwordguardEmbroidery(Rank3)-4894"
 value: {
  dps: 133796.23982
  tps: 925916.91692
  dtps: 60279.37757
  hps: 74240.77123
 }
}
dps_results: {
 key: "TestProtection-AllItems-SymboloftheCatacombs-83735"
 value: {
  dps: 129921.91352
  tps: 898877.71178
  dtps: 61103.47634
  hps: 71658.50472
 }
}
dps_results: {
 key: "TestProtection-AllItems-SynapseSprings(MarkII)-4898"
 value: {
  dps: 133445.73257
  tps: 923430.58646
  dtps: 59990.19536
  hps: 74382.33252
 }
}
dps_results: {
 key: "TestProtection-AllItems-TalismanofBloodlust-96864"
 value: {
  dps: 131707.01689
  tps: 912293.40558
  dtps: 60668.6263
  hps: 72689.08767
 }
}
dps_results: {
 key: "TestProtection-AllItems-TerrorintheMists-87167"
 value: {
  dps: 131616.64114
  tps: 909605.2394
  dtps: 61767.02891
  hps: 71859.13359
 }
}
dps_results: {
 key: "TestProtection-AllItems-Thousand-YearPickledEgg-87573"
 value: {
  dps: 129955.90351
  tps: 899679.12813
  dtps: 60806.23728
  hps: 71456.54623
 }
}
dps_results: {
 key: "TestProtection-AllItems-TrailseekerIdolofRage-101054"
 value: {
  dps: 128466.77595
  tps: 889243.00522
  dtps: 60592.4994
  hps: 70938.44268
 }
}
dps_results: {
 key: "TestProtection-AllItems-TrailseekerStoneofRage-101057"
 value: {
  dps: 128448.46668
  tps: 889114.85029
  dtps: 60373.86687
  hps: 71213.4733
 }
}
dps_results: {
 key: "TestProtection-AllItems-TyrannicalGladiator'sBadgeofConquest-100043"
 value: {
  dps: 128464.00644
  tps: 889223.6786
  dtps: 61797.09283
  hps: 70532.5148
 }
}
dps_results: {
 key: "TestProtection-AllItems-TyrannicalGladiator'sBadgeofConquest-91099"
 value: {
  dps: 128464.00644
  tps: 889223.6786
  dtps: 61797.09283
  hps: 70532.5148
 }
}
dps_results: {
 key: "TestProtection-AllItems-TyrannicalGladiator'sBadgeofConquest-94373"
 value: {
  dps: 128464.00644
  tps: 889223.6786
  dtps: 61797.09283
  hps: 70532.5148
 }
}
dps_results: {
 key: "TestProtection-AllItems-TyrannicalGladiator'sBadgeofConquest-99772"
 value: {
  dps: 128464.00644
  tps: 889223.6786
  dtps: 61797.09283
  hps: 70532.5148
 }
}
dps_results: {
 key: "TestProtection-AllItems-TyrannicalGladiator'sBadgeofDominance-100016"
 value: {
  dps: 128491.02943
  tps: 889200.55137
  dtps: 61797.09283
  hps: 70710.45252
 }
}
dps_results: {
 key: "TestProtection-AllItems-TyrannicalGladiator'sBadgeofDominance-91400"
 value: {
  dps: 128491.02943
  tps: 889200.55137
  dtps: 61797.09283
  hps: 70710.45252
 }
}
dps_results: {
 key: "TestProtection-AllItems-TyrannicalGladiator'sBadgeofDominance-94346"
 value: {
  dps: 128491.02943
  tps: 889200.55137
  dtps: 61797.09283
  hps: 70710.45252
 }
}
dps_results: {
 key: "TestProtection-AllItems-TyrannicalGladiator'sBadgeofDominance-99937"
 value: {
  dps: 128491.02943
  tps: 889200.55137
  dtps: 61797.09283
  hps: 70710.45252
 }
}
dps_results: {
 key: "TestProtection-AllItems-TyrannicalGladiator'sBadgeofVictory-100019"
 value: {
  dps: 129972.20926
  tps: 899242.11819
  dtps: 60624.18169
  hps: 71704.87452
 }
}
dps_results: {
 key: "TestProtection-AllItems-TyrannicalGladiator'sBadgeofVictory-91410"
 value: {
  dps: 129972.20926
  tps: 899242.11819
  dtps: 60624.18169
  hps: 71704.87452
 }
}
dps_results: {
 key: "TestProtection-AllItems-TyrannicalGladiator'sBadgeofVictory-94349"
 value: {
  dps: 129972.20926
  tps: 899242.11819
  dtps: 60624.18169
  hps: 71704.87452
 }
}
dps_results: {
 key: "TestProtection-AllItems-TyrannicalGladiator'sBadgeofVictory-99943"
 value: {
  dps: 129972.20926
  tps: 899242.11819
  dtps: 60624.18169
  hps: 71704.87452
 }
}
dps_results: {
 key: "TestProtection-AllItems-TyrannicalGladiator'sEmblemofCruelty-100066"
 value: {
  dps: 130847.72639
  tps: 905402.46971
  dtps: 61739.60476
  hps: 71418.91962
 }
}
dps_results: {
 key: "TestProtection-AllItems-TyrannicalGladiator'sEmblemofCruelty-91209"
 value: {
  dps: 130847.72639
  tps: 905402.46971
  dtps: 61739.60476
  hps: 71418.91962
 }
}
dps_results: {
 key: "TestProtection-AllItems-TyrannicalGladiator'sEmblemofCruelty-94396"
 value: {
  dps: 130847.72639
  tps: 905402.46971
  dtps: 61739.60476
  hps: 71418.91962
 }
}
dps_results: {
 key: "TestProtection-AllItems-TyrannicalGladiator'sEmblemofCruelty-99838"
 value: {
  dps: 130847.72639
  tps: 905402.46971
  dtps: 61739.60476
  hps: 71418.91962
 }
}
dps_results: {
 key: "TestProtection-AllItems-TyrannicalGladiator'sEmblemofMeditation-91211"
 value: {
  dps: 128329.154
  tps: 888279.06155
  dtps: 61728.44327
  hps: 70597.99356
 }
}
dps_results: {
 key: "TestProtection-AllItems-TyrannicalGladiator'sEmblemofMeditation-94329"
 value: {
  dps: 128329.154
  tps: 888279.06155
  dtps: 61728.44327
  hps: 70597.99356
 }
}
dps_results: {
 key: "TestProtection-AllItems-TyrannicalGladiator'sEmblemofMeditation-99840"
 value: {
  dps: 128329.154
  tps: 888279.06155
  dtps: 61728.44327
  hps: 70597.99356
 }
}
dps_results: {
 key: "TestProtection-AllItems-TyrannicalGladiator'sEmblemofMeditation-99990"
 value: {
  dps: 128329.154
  tps: 888279.06155
  dtps: 61728.44327
  hps: 70597.99356
 }
}
dps_results: {
 key: "TestProtection-AllItems-TyrannicalGladiator'sEmblemofTenacity-100092"
 value: {
  dps: 128251.21703
  tps: 887665.13128
  dtps: 61190.92913
  hps: 70673.39679
 }
}
dps_results: {
 key: "TestProtection-AllItems-TyrannicalGladiator'sEmblemofTenacity-91210"
 value: {
  dps: 128251.21703
  tps: 887665.13128
  dtps: 61190.92913
  hps: 70673.39679
 }
}
dps_results: {
 key: "TestProtection-AllItems-TyrannicalGladiator'sEmblemofTenacity-94422"
 value: {
  dps: 128251.21703
  tps: 887665.13128
  dtps: 61190.92913
  hps: 70673.39679
 }
}
dps_results: {
 key: "TestProtection-AllItems-TyrannicalGladiator'sEmblemofTenacity-99839"
 value: {
  dps: 128251.21703
  tps: 887665.13128
  dtps: 61190.92913
  hps: 70673.39679
 }
}
dps_results: {
 key: "TestProtection-AllItems-TyrannicalGladiator'sInsigniaofConquest-100026"
 value: {
  dps: 128492.31017
  tps: 889421.80471
  dtps: 61797.09283
  hps: 70532.26142
 }
}
dps_results: {
 key: "TestProtection-AllItems-TyrannicalGladiator'sInsigniaofDominance-100152"
 value: {
  dps: 128582.57974
  tps: 889642.70435
  dtps: 61797.05941
  hps: 70787.74931
 }
}
dps_results: {
 key: "TestProtection-AllItems-TyrannicalGladiator'sInsigniaofVictory-100085"
 value: {
  dps: 131344.88905
  tps: 908904.44189
  dtps: 60116.50667
  hps: 72161.32226
 }
}
dps_results: {
 key: "TestProtection-AllItems-TyrannicalPrimalDiamond"
 value: {
  dps: 132837.79272
  tps: 919544.82321
  dtps: 61058.10961
  hps: 73793.38138
 }
}
dps_results: {
 key: "TestProtection-AllItems-UnerringVisionofLeiShen-96930"
 value: {
  dps: 134147.4468
  tps: 928691.4251
  dtps: 61807.45216
  hps: 72471.34323
 }
}
dps_results: {
 key: "TestProtection-AllItems-VaporshieldMedallion-93262"
 value: {
  dps: 129055.74464
  tps: 893366.82102
  dtps: 59449.66781
  hps: 71250.83435
 }
}
dps_results: {
 key: "TestProtection-AllItems-VestmentsofWingedTriumph"
 value: {
  dps: 114089.51417
  tps: 789352.23105
  dtps: 65587.05584
  hps: 63878.42037
 }
}
dps_results: {
 key: "TestProtection-AllItems-VestmentsoftheLightningEmperor"
 value: {
  dps: 119488.18493
  tps: 827364.29095
  dtps: 66504.81991
  hps: 64918.01387
 }
}
dps_results: {
 key: "TestProtection-AllItems-VialofDragon'sBlood-87063"
 value: {
  dps: 128369.54897
  tps: 888726.93977
  dtps: 59325.39752
  hps: 71035.49469
 }
}
dps_results: {
 key: "TestProtection-AllItems-VialofIchorousBlood-100963"
 value: {
  dps: 128421.36047
  tps: 888925.15686
  dtps: 61802.41505
  hps: 70659.86076
 }
}
dps_results: {
 key: "TestProtection-AllItems-VialofIchorousBlood-81264"
 value: {
  dps: 128421.36047
  tps: 888925.15686
  dtps: 61802.41505
  hps: 70670.70278
 }
}
dps_results: {
 key: "TestProtection-AllItems-ViciousTalismanoftheShado-PanAssault-94511"
 value: {
  dps: 128539.08661
  tps: 889749.23983
  dtps: 61797.12555
  hps: 70532.67484
 }
}
dps_results: {
 key: "TestProtection-AllItems-VisionofthePredator-81192"
 value: {
  dps: 130249.10361
  tps: 901719.35879
  dtps: 61808.50148
  hps: 71262.89809
 }
}
dps_results: {
 key: "TestProtection-AllItems-VolatileTalismanoftheShado-PanAssault-94510"
 value: {
  dps: 129627.11208
  tps: 897161.66989
  dtps: 60762.12771
  hps: 71400.1036
 }
}
dps_results: {
 key: "TestProtection-AllItems-WhiteTigerBattlegear"
 value: {
  dps: 135844.05396
  tps: 939930.20977
  dtps: 64553.48409
  hps: 72501.04364
 }
}
dps_results: {
 key: "TestProtection-AllItems-WhiteTigerPlate"
 value: {
  dps: 126268.06426
  tps: 873627.88151
  dtps: 59358.62197
  hps: 70363.30104
 }
}
dps_results: {
 key: "TestProtection-AllItems-WhiteTigerVestments"
 value: {
  dps: 117994.23629
  tps: 816300.88224
  dtps: 69094.97287
  hps: 63850.90718
 }
}
dps_results: {
 key: "TestProtection-AllItems-WindsweptPages-81125"
 value: {
  dps: 130304.60548
  tps: 901740.68997
  dtps: 60982.42324
  hps: 71676.48959
 }
}
dps_results: {
 key: "TestProtection-AllItems-WoundripperMedallion-93253"
 value: {
  dps: 130722.66965
  tps: 903787.15081
  dtps: 61797.05941
  hps: 71807.20136
 }
}
dps_results: {
 key: "TestProtection-AllItems-Wushoolay'sFinalChoice-96785"
 value: {
  dps: 128990.18104
  tps: 892906.9008
  dtps: 61802.08355
  hps: 71375.4931
 }
}
dps_results: {
 key: "TestProtection-AllItems-YaungolFireCarrier-86518"
 value: {
  dps: 132873.83972
  tps: 919736.16804
  dtps: 60308.28468
  hps: 73681.2667
 }
}
dps_results: {
 key: "TestProtection-AllItems-Yu'lon'sBite-103987"
 value: {
  dps: 132613.49121
  tps: 917754.29301
  dtps: 61755.45128
  hps: 72223.60225
 }
}
dps_results: {
 key: "TestProtection-AllItems-ZenAlchemistStone-75274"
 value: {
  dps: 130919.74725
  tps: 905799.94557
  dtps: 59662.17952
  hps: 72296.8479
 }
}
dps_results: {
 key: "TestProtection-Average-Default"
 value: {
  dps: 133106.95181
  tps: 920519.46472
  dtps: 59850.64336
  hps: 73930.64553
 }
}
dps_results: {
 key: "TestProtection-Settings-BloodElf-p1-Seal of Insight-default-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 1.05655507493e+06
  tps: 7.35437768239e+06
  dtps: 1.04701673986e+06
  hps: 339320.99453
 }
}
dps_results: {
 key: "TestProtection-Settings-BloodElf-p1-Seal of Insight-default-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 134425.35265
  tps: 930596.7586
  dtps: 60308.28468
  hps: 73681.2667
 }
}
dps_results: {
 key: "TestProtection-Settings-BloodElf-p1-Seal of Insight-default-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 164436.74738
  tps: 1.0954782818e+06
  dtps: 49173.05828
  hps: 83566.45837
 }
}
dps_results: {
 key: "TestProtection-Settings-BloodElf-p1-Seal of Insight-default-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 761005.85015
  tps: 5.35481565108e+06
  dtps: 1.28768003987e+06
  hps: 241673.79565
 }
}
dps_results: {
 key: "TestProtection-Settings-BloodElf-p1-Seal of Insight-default-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 103589.9564
  tps: 726366.33479
  dtps: 71116.46068
  hps: 59430.76336
 }
}
dps_results: {
 key: "TestProtection-Settings-BloodElf-p1-Seal of Insight-default-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 107919.40428
  tps: 756803.72998
  dtps: 60467.99267
  hps: 58308.29784
 }
}
dps_results: {
 key: "TestProtection-Settings-BloodElf-p1-Seal of Righteousness-default-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 1.36220777761e+06
  tps: 9.491067502e+06
  dtps: 1.04266422481e+06
  hps: 179668.27723
 }
}
dps_results: {
 key: "TestProtection-Settings-BloodElf-p1-Seal of Righteousness-default-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 138321.47938
  tps: 957587.23291
  dtps: 60273.56715
  hps: 43374.69995
 }
}
dps_results: {
 key: "TestProtection-Settings-BloodElf-p1-Seal of Righteousness-default-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 168619.87601
  tps: 1.12337086838e+06
  dtps: 48926.814
  hps: 46096.04898
 }
}
dps_results: {
 key: "TestProtection-Settings-BloodElf-p1-Seal of Righteousness-default-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 959918.83059
  tps: 6.7471397141e+06
  dtps: 1.28985944701e+06
  hps: 127725.34312
 }
}
dps_results: {
 key: "TestProtection-Settings-BloodElf-p1-Seal of Righteousness-default-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 106642.14397
  tps: 747729.08277
  dtps: 70881.21461
  hps: 34868.07825
 }
}
dps_results: {
 key: "TestProtection-Settings-BloodElf-p1-Seal of Righteousness-default-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 111268.22936
  tps: 780241.1055
  dtps: 59615.85136
  hps: 32473.61509
 }
}
dps_results: {
 key: "TestProtection-Settings-BloodElf-p1-Seal of Truth-default-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 1.07890098328e+06
  tps: 7.51084434086e+06
  dtps: 1.03996985824e+06
  hps: 180920.19063
 }
}
dps_results: {
 key: "TestProtection-Settings-BloodElf-p1-Seal of Truth-default-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 139578.50192
  tps: 966664.92347
  dtps: 60201.14027
  hps: 43066.90848
 }
}
dps_results: {
 key: "TestProtection-Settings-BloodElf-p1-Seal of Truth-default-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 169812.86299
  tps: 1.13311029102e+06
  dtps: 48710.49884
  hps: 46481.87149
 }
}
dps_results: {
 key: "TestProtection-Settings-BloodElf-p1-Seal of Truth-default-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 752765.23805
  tps: 5.29704456633e+06
  dtps: 1.29099191779e+06
  hps: 128730.05198
 }
}
dps_results: {
 key: "TestProtection-Settings-BloodElf-p1-Seal of Truth-default-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 107868.9698
  tps: 756317.39358
  dtps: 70758.36905
  hps: 34802.93823
 }
}
dps_results: {
 key: "TestProtection-Settings-BloodElf-p1-Seal of Truth-default-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 112748.47823
  tps: 790608.74764
  dtps: 59741.53611
  hps: 32426.39538
 }
}
dps_results: {
 key: "TestProtection-SwitchInFrontOfTarget-Default"
 value: {
  dps: 134425.35265
  tps: 930596.7586
  dtps: 60308.28468
  hps: 73681.2667
 }
}
