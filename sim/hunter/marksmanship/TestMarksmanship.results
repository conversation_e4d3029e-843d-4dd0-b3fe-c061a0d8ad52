character_stats_results: {
 key: "TestMarksmanship-CharacterStats-Default"
 value: {
  final_stats: 177.45
  final_stats: 20478.9375
  final_stats: 22600.6
  final_stats: 191.1
  final_stats: 195
  final_stats: 2550
  final_stats: 8556
  final_stats: 3452
  final_stats: 2566
  final_stats: 0
  final_stats: 0
  final_stats: 6296
  final_stats: 45229.6625
  final_stats: 61060.04438
  final_stats: 0
  final_stats: 0
  final_stats: 0
  final_stats: 25425
  final_stats: 0
  final_stats: 462811.4
  final_stats: 0
  final_stats: 0
  final_stats: 7.5
  final_stats: 15.04706
  final_stats: 33.98925
  final_stats: 19.26
  final_stats: 0
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-AgilePrimalDiamond"
 value: {
  dps: 156469.91771
  tps: 129742.54899
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-AlacrityofXuen-103989"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-ArcaneBadgeoftheShieldwall-93347"
 value: {
  dps: 147459.60003
  tps: 122685.12846
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-ArrowflightMedallion-93258"
 value: {
  dps: 154839.32231
  tps: 128237.94149
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-AssuranceofConsequence-105472"
 value: {
  dps: 160659.3459
  tps: 129450.71982
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-AusterePrimalDiamond"
 value: {
  dps: 153052.67339
  tps: 126640.71814
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-BadJuju-96781"
 value: {
  dps: 160021.23315
  tps: 132037.89254
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-BadgeofKypariZar-84079"
 value: {
  dps: 147700.55681
  tps: 122805.53283
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-BattlegearoftheSaurokStalker"
 value: {
  dps: 145153.54065
  tps: 118918.81593
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-BlossomofPureSnow-89081"
 value: {
  dps: 148822.56189
  tps: 123725.07669
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-BraidofTenSongs-84072"
 value: {
  dps: 147804.51947
  tps: 122855.539
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-Brawler'sStatue-87571"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-BreathoftheHydra-96827"
 value: {
  dps: 149758.722
  tps: 124600.11764
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-BroochofMunificentDeeds-87500"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-BrutalTalismanoftheShado-PanAssault-94508"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-BurningPrimalDiamond"
 value: {
  dps: 155381.63245
  tps: 128952.26278
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-CapacitivePrimalDiamond"
 value: {
  dps: 153690.84496
  tps: 127152.39479
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-CarbonicCarbuncle-81138"
 value: {
  dps: 148940.50373
  tps: 123840.25329
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-Cha-Ye'sEssenceofBrilliance-96888"
 value: {
  dps: 149835.00724
  tps: 124726.1749
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-CharmofTenSongs-84071"
 value: {
  dps: 148841.39554
  tps: 123716.68627
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-CommunalIdolofDestruction-101168"
 value: {
  dps: 147782.61943
  tps: 123149.58923
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-CommunalStoneofDestruction-101171"
 value: {
  dps: 148836.01178
  tps: 124415.89427
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-CommunalStoneofWisdom-101183"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-ContemplationofChi-Ji-103688"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-ContemplationofChi-Ji-103988"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-Coren'sColdChromiumCoaster-87574"
 value: {
  dps: 152272.98318
  tps: 126233.52688
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-CoreofDecency-87497"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-CourageousPrimalDiamond"
 value: {
  dps: 153052.67339
  tps: 126640.71814
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-CraftedDreadfulGladiator'sBadgeofConquest-93419"
 value: {
  dps: 150840.54967
  tps: 125003.8492
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-CraftedDreadfulGladiator'sBadgeofDominance-93600"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-CraftedDreadfulGladiator'sBadgeofVictory-93606"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-CraftedDreadfulGladiator'sEmblemofCruelty-93485"
 value: {
  dps: 148165.68883
  tps: 123392.03106
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-CraftedDreadfulGladiator'sEmblemofMeditation-93487"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-CraftedDreadfulGladiator'sEmblemofTenacity-93486"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-CraftedDreadfulGladiator'sInsigniaofConquest-93424"
 value: {
  dps: 152631.67501
  tps: 126369.0365
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-CraftedDreadfulGladiator'sInsigniaofDominance-93601"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-CraftedDreadfulGladiator'sInsigniaofVictory-93611"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-CraftedMalevolentGladiator'sBadgeofConquest-98755"
 value: {
  dps: 151602.70362
  tps: 125511.16973
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-CraftedMalevolentGladiator'sBadgeofDominance-98910"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-CraftedMalevolentGladiator'sBadgeofVictory-98912"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-CraftedMalevolentGladiator'sEmblemofCruelty-98811"
 value: {
  dps: 148330.27846
  tps: 123514.7617
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-CraftedMalevolentGladiator'sEmblemofMeditation-98813"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-CraftedMalevolentGladiator'sEmblemofTenacity-98812"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-CraftedMalevolentGladiator'sInsigniaofConquest-98760"
 value: {
  dps: 153560.1406
  tps: 126987.94145
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-CraftedMalevolentGladiator'sInsigniaofDominance-98911"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-CraftedMalevolentGladiator'sInsigniaofVictory-98917"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-CurseofHubris-102307"
 value: {
  dps: 151459.3444
  tps: 125462.97046
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-CurseofHubris-104649"
 value: {
  dps: 152187.26275
  tps: 126017.0343
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-CurseofHubris-104898"
 value: {
  dps: 150778.32924
  tps: 124962.1568
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-CurseofHubris-105147"
 value: {
  dps: 150402.87337
  tps: 124697.88302
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-CurseofHubris-105396"
 value: {
  dps: 151830.821
  tps: 125742.31551
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-CurseofHubris-105645"
 value: {
  dps: 152543.68644
  tps: 126290.42961
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-CutstitcherMedallion-93255"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-Daelo'sFinalWords-87496"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-DarkglowEmbroidery(Rank3)-4893"
 value: {
  dps: 156229.48739
  tps: 129576.92578
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-DarkmistVortex-87172"
 value: {
  dps: 147219.49298
  tps: 122358.05655
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-DeadeyeBadgeoftheShieldwall-93346"
 value: {
  dps: 151113.46009
  tps: 125614.77945
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-DelicateVialoftheSanguinaire-96895"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-DestructivePrimalDiamond"
 value: {
  dps: 153836.26693
  tps: 127281.47101
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-DisciplineofXuen-103986"
 value: {
  dps: 158038.31119
  tps: 131355.53325
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-Dominator'sArcaneBadge-93342"
 value: {
  dps: 147459.60003
  tps: 122685.12846
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-Dominator'sDeadeyeBadge-93341"
 value: {
  dps: 151113.46009
  tps: 125614.77945
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-Dominator'sDurableBadge-93345"
 value: {
  dps: 146977.40715
  tps: 122489.37021
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-Dominator'sKnightlyBadge-93344"
 value: {
  dps: 146977.40715
  tps: 122489.37021
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-Dominator'sMendingBadge-93343"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-DreadfulGladiator'sBadgeofConquest-84344"
 value: {
  dps: 150840.54967
  tps: 125003.8492
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-DreadfulGladiator'sBadgeofDominance-84488"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-DreadfulGladiator'sBadgeofVictory-84490"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-DreadfulGladiator'sEmblemofCruelty-84399"
 value: {
  dps: 148165.68883
  tps: 123392.03106
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-DreadfulGladiator'sEmblemofMeditation-84401"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-DreadfulGladiator'sEmblemofTenacity-84400"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-DreadfulGladiator'sInsigniaofConquest-84349"
 value: {
  dps: 152740.56762
  tps: 126412.32953
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-DreadfulGladiator'sInsigniaofDominance-84489"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-DreadfulGladiator'sInsigniaofVictory-84495"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-DurableBadgeoftheShieldwall-93350"
 value: {
  dps: 146977.40715
  tps: 122489.37021
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-EffulgentPrimalDiamond"
 value: {
  dps: 153052.67339
  tps: 126640.71814
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-EmberPrimalDiamond"
 value: {
  dps: 153052.67339
  tps: 126640.71814
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-EmblemofKypariZar-84077"
 value: {
  dps: 150235.66384
  tps: 125098.26297
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-EmblemoftheCatacombs-83733"
 value: {
  dps: 148672.59167
  tps: 123989.239
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-EmptyFruitBarrel-81133"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-EnchantWeapon-BloodyDancingSteel-5125"
 value: {
  dps: 156065.47248
  tps: 129220.23054
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-EnchantWeapon-Colossus-4445"
 value: {
  dps: 151896.84784
  tps: 126240.0295
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-EnchantWeapon-DancingSteel-4444"
 value: {
  dps: 156499.42076
  tps: 129566.62691
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-EnchantWeapon-JadeSpirit-4442"
 value: {
  dps: 151896.84784
  tps: 126240.0295
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-EnchantWeapon-River'sSong-4446"
 value: {
  dps: 151896.84784
  tps: 126240.0295
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-EnchantWeapon-SpiritofConquest-5124"
 value: {
  dps: 151896.84784
  tps: 126240.0295
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-EnchantWeapon-Windsong-4441"
 value: {
  dps: 152728.52834
  tps: 126718.25885
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-EnigmaticPrimalDiamond"
 value: {
  dps: 153836.26693
  tps: 127281.47101
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-EssenceofTerror-87175"
 value: {
  dps: 146894.13896
  tps: 122175.23345
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-EternalPrimalDiamond"
 value: {
  dps: 153052.67339
  tps: 126640.71814
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-EvilEyeofGalakras-105491"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-FabledFeatherofJi-Kun-96842"
 value: {
  dps: 146491.65649
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-FearwurmBadge-84074"
 value: {
  dps: 149855.37397
  tps: 124770.63486
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-FearwurmRelic-84070"
 value: {
  dps: 149139.72681
  tps: 124247.30175
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-FelsoulIdolofDestruction-101263"
 value: {
  dps: 148121.93209
  tps: 123457.47373
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-FelsoulStoneofDestruction-101266"
 value: {
  dps: 148833.28425
  tps: 124447.32364
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-Fen-Yu,FuryofXuen-102248"
 value: {
  dps: 170067.23134
  tps: 141765.96166
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-FlashfrozenResinGlobule-100951"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-FlashfrozenResinGlobule-81263"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-FlashingSteelTalisman-81265"
 value: {
  dps: 152668.83455
  tps: 125875.66204
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-FleetPrimalDiamond"
 value: {
  dps: 153526.84894
  tps: 126988.93274
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-ForlornPrimalDiamond"
 value: {
  dps: 153052.67339
  tps: 126640.71814
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-FortitudeoftheZandalari-94516"
 value: {
  dps: 148097.85804
  tps: 123477.11324
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-FortitudeoftheZandalari-95677"
 value: {
  dps: 148506.08157
  tps: 123890.15102
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-FortitudeoftheZandalari-96049"
 value: {
  dps: 148557.57751
  tps: 123897.12623
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-FortitudeoftheZandalari-96421"
 value: {
  dps: 148232.4901
  tps: 123597.3172
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-FortitudeoftheZandalari-96793"
 value: {
  dps: 148996.52623
  tps: 124203.50984
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-GazeoftheTwins-96915"
 value: {
  dps: 150278.4325
  tps: 124990.32813
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-Gerp'sPerfectArrow-87495"
 value: {
  dps: 153140.11922
  tps: 126526.7724
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-Gladiator'sPursuit"
 value: {
  dps: 152921.21802
  tps: 124436.71767
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-Gong-Lu,StrengthofXuen-102249"
 value: {
  dps: 160530.61317
  tps: 134204.87274
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-GrievousGladiator'sBadgeofConquest-100195"
 value: {
  dps: 154441.83361
  tps: 127488.25622
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-GrievousGladiator'sBadgeofConquest-100603"
 value: {
  dps: 154441.83361
  tps: 127488.25622
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-GrievousGladiator'sBadgeofConquest-102856"
 value: {
  dps: 154441.83361
  tps: 127488.25622
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-GrievousGladiator'sBadgeofConquest-103145"
 value: {
  dps: 154441.83361
  tps: 127488.25622
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-GrievousGladiator'sBadgeofDominance-100490"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-GrievousGladiator'sBadgeofDominance-100576"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-GrievousGladiator'sBadgeofDominance-102830"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-GrievousGladiator'sBadgeofDominance-103308"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-GrievousGladiator'sBadgeofVictory-100500"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-GrievousGladiator'sBadgeofVictory-100579"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-GrievousGladiator'sBadgeofVictory-102833"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-GrievousGladiator'sBadgeofVictory-103314"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-GrievousGladiator'sEmblemofCruelty-100305"
 value: {
  dps: 149304.29608
  tps: 124296.34382
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-GrievousGladiator'sEmblemofCruelty-100626"
 value: {
  dps: 149304.29608
  tps: 124296.34382
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-GrievousGladiator'sEmblemofCruelty-102877"
 value: {
  dps: 149304.29608
  tps: 124296.34382
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-GrievousGladiator'sEmblemofCruelty-103210"
 value: {
  dps: 149304.29608
  tps: 124296.34382
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-GrievousGladiator'sEmblemofMeditation-100307"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-GrievousGladiator'sEmblemofMeditation-100559"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-GrievousGladiator'sEmblemofMeditation-102813"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-GrievousGladiator'sEmblemofMeditation-103212"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-GrievousGladiator'sEmblemofTenacity-100306"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-GrievousGladiator'sEmblemofTenacity-100652"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-GrievousGladiator'sEmblemofTenacity-102903"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-GrievousGladiator'sEmblemofTenacity-103211"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-GrievousGladiator'sInsigniaofConquest-103150"
 value: {
  dps: 158037.26715
  tps: 130205.22612
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-GrievousGladiator'sInsigniaofDominance-103309"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-GrievousGladiator'sInsigniaofVictory-103319"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-Hawkmaster'sTalon-89082"
 value: {
  dps: 156499.22999
  tps: 129769.42807
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-Heart-LesionDefenderIdol-100999"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-Heart-LesionDefenderStone-101002"
 value: {
  dps: 147835.57346
  tps: 123282.72199
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-Heart-LesionIdolofBattle-100991"
 value: {
  dps: 148395.39066
  tps: 123552.37579
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-Heart-LesionStoneofBattle-100990"
 value: {
  dps: 147666.21255
  tps: 123192.60426
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-HeartofFire-81181"
 value: {
  dps: 148124.73394
  tps: 123638.72349
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-HeartwarmerMedallion-93260"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-HelmbreakerMedallion-93261"
 value: {
  dps: 148902.28454
  tps: 123773.55619
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-Horridon'sLastGasp-96757"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-ImpassivePrimalDiamond"
 value: {
  dps: 153836.26693
  tps: 127281.47101
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-IndomitablePrimalDiamond"
 value: {
  dps: 153052.67339
  tps: 126640.71814
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-InscribedBagofHydra-Spawn-96828"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-InsigniaofKypariZar-84078"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-IronBellyWok-89083"
 value: {
  dps: 150815.01086
  tps: 125521.98991
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-IronProtectorTalisman-85181"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-JadeBanditFigurine-86043"
 value: {
  dps: 156499.22999
  tps: 129769.42807
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-JadeBanditFigurine-86772"
 value: {
  dps: 153851.86096
  tps: 127583.55799
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-JadeCharioteerFigurine-86042"
 value: {
  dps: 150815.01086
  tps: 125521.98991
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-JadeCharioteerFigurine-86771"
 value: {
  dps: 148575.93341
  tps: 123538.46328
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-JadeCourtesanFigurine-86045"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-JadeCourtesanFigurine-86774"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-JadeMagistrateFigurine-86044"
 value: {
  dps: 148822.56189
  tps: 123725.07669
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-JadeMagistrateFigurine-86773"
 value: {
  dps: 148503.89649
  tps: 123443.48057
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-JadeWarlordFigurine-86046"
 value: {
  dps: 147704.18639
  tps: 123447.26664
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-JadeWarlordFigurine-86775"
 value: {
  dps: 148645.36805
  tps: 124226.5801
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-Ji-Kun'sRisingWinds-96843"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-KnightlyBadgeoftheShieldwall-93349"
 value: {
  dps: 146977.40715
  tps: 122489.37021
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-KnotofTenSongs-84073"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-Kor'kronBookofHurting-92785"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-Lao-Chin'sLiquidCourage-89079"
 value: {
  dps: 147704.18639
  tps: 123447.26664
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-LeiShen'sFinalOrders-87072"
 value: {
  dps: 148891.1253
  tps: 123701.3396
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-LessonsoftheDarkmaster-81268"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-LightdrinkerIdolofRage-101200"
 value: {
  dps: 154229.28073
  tps: 127766.33759
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-LightdrinkerStoneofRage-101203"
 value: {
  dps: 153787.32292
  tps: 127949.1515
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-LightoftheCosmos-87065"
 value: {
  dps: 148891.1253
  tps: 123701.3396
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-LightweaveEmbroidery(Rank3)-4892"
 value: {
  dps: 156229.48739
  tps: 129576.92578
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-MalevolentGladiator'sBadgeofConquest-84934"
 value: {
  dps: 151976.53096
  tps: 125780.45729
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-MalevolentGladiator'sBadgeofConquest-91452"
 value: {
  dps: 151602.70362
  tps: 125511.16973
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-MalevolentGladiator'sBadgeofDominance-84940"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-MalevolentGladiator'sBadgeofDominance-91753"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-MalevolentGladiator'sBadgeofVictory-84942"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-MalevolentGladiator'sBadgeofVictory-91763"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-MalevolentGladiator'sEmblemofCruelty-84936"
 value: {
  dps: 148395.39066
  tps: 123552.37579
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-MalevolentGladiator'sEmblemofCruelty-91562"
 value: {
  dps: 148330.27846
  tps: 123514.7617
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-MalevolentGladiator'sEmblemofMeditation-84939"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-MalevolentGladiator'sEmblemofMeditation-91564"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-MalevolentGladiator'sEmblemofTenacity-84938"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-MalevolentGladiator'sEmblemofTenacity-91563"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-MalevolentGladiator'sInsigniaofConquest-91457"
 value: {
  dps: 153602.93891
  tps: 126987.9531
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-MalevolentGladiator'sInsigniaofDominance-91754"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-MalevolentGladiator'sInsigniaofVictory-91768"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-MarkoftheCatacombs-83731"
 value: {
  dps: 148086.04329
  tps: 123280.26301
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-MarkoftheHardenedGrunt-92783"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-MedallionofMystifyingVapors-93257"
 value: {
  dps: 147372.98061
  tps: 123052.23102
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-MedallionoftheCatacombs-83734"
 value: {
  dps: 146415.68283
  tps: 121937.05273
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-MendingBadgeoftheShieldwall-93348"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-MirrorScope-4700"
 value: {
  dps: 152783.10065
  tps: 126986.51665
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-MistdancerDefenderIdol-101089"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-MistdancerDefenderStone-101087"
 value: {
  dps: 147703.74049
  tps: 123113.36314
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-MistdancerIdolofRage-101113"
 value: {
  dps: 154170.17464
  tps: 127794.32898
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-MistdancerStoneofRage-101117"
 value: {
  dps: 152966.03902
  tps: 127132.74369
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-MistdancerStoneofWisdom-101107"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-MithrilWristwatch-87572"
 value: {
  dps: 148270.97555
  tps: 123466.88102
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-MountainsageIdolofDestruction-101069"
 value: {
  dps: 147838.5874
  tps: 123070.82158
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-MountainsageStoneofDestruction-101072"
 value: {
  dps: 148523.68905
  tps: 124036.37439
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-OathswornDefenderIdol-101303"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-OathswornDefenderStone-101306"
 value: {
  dps: 148169.20027
  tps: 123774.68048
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-OathswornIdolofBattle-101295"
 value: {
  dps: 148395.39066
  tps: 123552.37579
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-OathswornStoneofBattle-101294"
 value: {
  dps: 147881.4443
  tps: 123434.02379
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-PhaseFingers-4697"
 value: {
  dps: 156198.10261
  tps: 129646.55975
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-PouchofWhiteAsh-103639"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-PowerfulPrimalDiamond"
 value: {
  dps: 153052.67339
  tps: 126640.71814
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-PriceofProgress-81266"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-PridefulGladiator'sBadgeofConquest-102659"
 value: {
  dps: 156917.95673
  tps: 129202.23753
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-PridefulGladiator'sBadgeofConquest-103342"
 value: {
  dps: 156917.95673
  tps: 129202.23753
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-PridefulGladiator'sBadgeofDominance-102633"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-PridefulGladiator'sBadgeofDominance-103505"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-PridefulGladiator'sBadgeofVictory-102636"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-PridefulGladiator'sBadgeofVictory-103511"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-PridefulGladiator'sEmblemofCruelty-102680"
 value: {
  dps: 150132.75685
  tps: 124974.92966
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-PridefulGladiator'sEmblemofCruelty-103407"
 value: {
  dps: 150132.75685
  tps: 124974.92966
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-PridefulGladiator'sEmblemofMeditation-102616"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-PridefulGladiator'sEmblemofMeditation-103409"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-PridefulGladiator'sEmblemofTenacity-102706"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-PridefulGladiator'sEmblemofTenacity-103408"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-PridefulGladiator'sInsigniaofConquest-103347"
 value: {
  dps: 161169.78011
  tps: 132306.23312
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-PridefulGladiator'sInsigniaofDominance-103506"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-PridefulGladiator'sInsigniaofVictory-103516"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-Primordius'TalismanofRage-96873"
 value: {
  dps: 149835.00724
  tps: 124726.1749
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-Qian-Le,CourageofNiuzao-102245"
 value: {
  dps: 161055.28087
  tps: 133065.21798
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-Qian-Ying,FortitudeofNiuzao-102250"
 value: {
  dps: 150221.38901
  tps: 124690.88461
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-Qin-xi'sPolarizingSeal-87075"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-RelicofChi-Ji-79330"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-RelicofKypariZar-84075"
 value: {
  dps: 148227.8033
  tps: 123337.78261
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-RelicofNiuzao-79329"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-RelicofXuen-79327"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-RelicofXuen-79328"
 value: {
  dps: 156025.74773
  tps: 128908.41144
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-RelicofYu'lon-79331"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-Renataki'sSoulCharm-96741"
 value: {
  dps: 159555.65927
  tps: 131152.61525
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-ResolveofNiuzao-103690"
 value: {
  dps: 148860.17646
  tps: 124158.68037
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-ResolveofNiuzao-103990"
 value: {
  dps: 148232.4901
  tps: 123597.3172
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-ReverberatingPrimalDiamond"
 value: {
  dps: 155381.63245
  tps: 128952.26278
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-RevitalizingPrimalDiamond"
 value: {
  dps: 155381.63245
  tps: 128952.26278
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-SI:7Operative'sManual-92784"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-ScrollofReveredAncestors-89080"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-SearingWords-81267"
 value: {
  dps: 155265.20036
  tps: 128243.89535
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-Shock-ChargerMedallion-93259"
 value: {
  dps: 148000.31119
  tps: 122930.07719
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-SigilofCompassion-83736"
 value: {
  dps: 146415.68283
  tps: 121937.05273
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-SigilofDevotion-83740"
 value: {
  dps: 149166.66427
  tps: 124416.88092
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-SigilofFidelity-83737"
 value: {
  dps: 148489.17742
  tps: 123372.47368
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-SigilofGrace-83738"
 value: {
  dps: 149125.8714
  tps: 124370.5233
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-SigilofKypariZar-84076"
 value: {
  dps: 149857.12947
  tps: 125118.85342
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-SigilofPatience-83739"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-SigiloftheCatacombs-83732"
 value: {
  dps: 149032.75963
  tps: 124106.64358
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-SinisterPrimalDiamond"
 value: {
  dps: 153690.84496
  tps: 127152.39479
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-SkullrenderMedallion-93256"
 value: {
  dps: 148902.28454
  tps: 123773.55619
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-SpiritsoftheSun-87163"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-SpringrainIdolofDestruction-101023"
 value: {
  dps: 148322.36196
  tps: 123405.13069
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-SpringrainIdolofRage-101009"
 value: {
  dps: 154237.29959
  tps: 127735.96684
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-SpringrainStoneofDestruction-101026"
 value: {
  dps: 146882.17638
  tps: 122476.55682
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-SpringrainStoneofRage-101012"
 value: {
  dps: 154011.50314
  tps: 128260.00109
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-SpringrainStoneofWisdom-101041"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-Static-Caster'sMedallion-93254"
 value: {
  dps: 148000.31119
  tps: 122930.07719
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-SteadfastFootman'sMedallion-92782"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-SteadfastTalismanoftheShado-PanAssault-94507"
 value: {
  dps: 148097.85804
  tps: 123477.11324
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-StreamtalkerIdolofDestruction-101222"
 value: {
  dps: 148200.37753
  tps: 123555.15868
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-StreamtalkerIdolofRage-101217"
 value: {
  dps: 154141.95061
  tps: 127796.30582
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-StreamtalkerStoneofDestruction-101225"
 value: {
  dps: 148050.803
  tps: 123620.69393
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-StreamtalkerStoneofRage-101220"
 value: {
  dps: 153649.22363
  tps: 128040.434
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-StreamtalkerStoneofWisdom-101250"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-StuffofNightmares-87160"
 value: {
  dps: 148368.03844
  tps: 123768.1235
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-SunsoulDefenderIdol-101160"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-SunsoulDefenderStone-101163"
 value: {
  dps: 148403.66883
  tps: 124074.23823
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-SunsoulIdolofBattle-101152"
 value: {
  dps: 148395.39066
  tps: 123552.37579
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-SunsoulStoneofBattle-101151"
 value: {
  dps: 147891.17175
  tps: 123475.31966
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-SunsoulStoneofWisdom-101138"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-SwordguardEmbroidery(Rank3)-4894"
 value: {
  dps: 158609.27766
  tps: 131224.98483
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-SymboloftheCatacombs-83735"
 value: {
  dps: 147586.82053
  tps: 122787.3669
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-SynapseSprings(MarkII)-4898"
 value: {
  dps: 158317.17721
  tps: 130982.37397
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-TalismanofBloodlust-96864"
 value: {
  dps: 159632.66971
  tps: 132154.82381
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-TheGloamingBlade-88149"
 value: {
  dps: 156469.91771
  tps: 129742.54899
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-Thousand-YearPickledEgg-87573"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-TrailseekerIdolofRage-101054"
 value: {
  dps: 154189.08024
  tps: 127681.43694
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-TrailseekerStoneofRage-101057"
 value: {
  dps: 153813.64183
  tps: 128097.01216
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-TyrannicalGladiator'sBadgeofConquest-100043"
 value: {
  dps: 152726.17297
  tps: 126306.4364
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-TyrannicalGladiator'sBadgeofConquest-91099"
 value: {
  dps: 152726.17297
  tps: 126306.4364
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-TyrannicalGladiator'sBadgeofConquest-94373"
 value: {
  dps: 152726.17297
  tps: 126306.4364
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-TyrannicalGladiator'sBadgeofConquest-99772"
 value: {
  dps: 152726.17297
  tps: 126306.4364
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-TyrannicalGladiator'sBadgeofDominance-100016"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-TyrannicalGladiator'sBadgeofDominance-91400"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-TyrannicalGladiator'sBadgeofDominance-94346"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-TyrannicalGladiator'sBadgeofDominance-99937"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-TyrannicalGladiator'sBadgeofVictory-100019"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-TyrannicalGladiator'sBadgeofVictory-91410"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-TyrannicalGladiator'sBadgeofVictory-94349"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-TyrannicalGladiator'sBadgeofVictory-99943"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-TyrannicalGladiator'sEmblemofCruelty-100066"
 value: {
  dps: 148573.92524
  tps: 123681.19158
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-TyrannicalGladiator'sEmblemofCruelty-91209"
 value: {
  dps: 148573.92524
  tps: 123681.19158
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-TyrannicalGladiator'sEmblemofCruelty-94396"
 value: {
  dps: 148573.92524
  tps: 123681.19158
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-TyrannicalGladiator'sEmblemofCruelty-99838"
 value: {
  dps: 148573.92524
  tps: 123681.19158
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-TyrannicalGladiator'sEmblemofMeditation-91211"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-TyrannicalGladiator'sEmblemofMeditation-94329"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-TyrannicalGladiator'sEmblemofMeditation-99840"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-TyrannicalGladiator'sEmblemofMeditation-99990"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-TyrannicalGladiator'sEmblemofTenacity-100092"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-TyrannicalGladiator'sEmblemofTenacity-91210"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-TyrannicalGladiator'sEmblemofTenacity-94422"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-TyrannicalGladiator'sEmblemofTenacity-99839"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-TyrannicalGladiator'sInsigniaofConquest-100026"
 value: {
  dps: 154969.29266
  tps: 127945.13497
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-TyrannicalGladiator'sInsigniaofDominance-100152"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-TyrannicalGladiator'sInsigniaofVictory-100085"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-TyrannicalPrimalDiamond"
 value: {
  dps: 153052.67339
  tps: 126640.71814
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-UnerringVisionofLeiShen-96930"
 value: {
  dps: 150349.4612
  tps: 125477.61948
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-VaporshieldMedallion-93262"
 value: {
  dps: 147372.98061
  tps: 123052.23102
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-VialofDragon'sBlood-87063"
 value: {
  dps: 148506.08157
  tps: 123890.15102
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-VialofIchorousBlood-100963"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-VialofIchorousBlood-81264"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-ViciousTalismanoftheShado-PanAssault-94511"
 value: {
  dps: 159384.97717
  tps: 131086.8568
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-VisionofthePredator-81192"
 value: {
  dps: 147108.61638
  tps: 122276.22364
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-VolatileTalismanoftheShado-PanAssault-94510"
 value: {
  dps: 146904.60593
  tps: 122211.5219
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-WindsweptPages-81125"
 value: {
  dps: 154602.66732
  tps: 128249.47827
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-WoundripperMedallion-93253"
 value: {
  dps: 154839.32231
  tps: 128237.94149
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-Wushoolay'sFinalChoice-96785"
 value: {
  dps: 146492.96534
  tps: 121948.22354
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-Xing-Ho,BreathofYu'lon-102246"
 value: {
  dps: 153714.9936
  tps: 127679.46731
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-YaungolFireCarrier-86518"
 value: {
  dps: 156469.91771
  tps: 129742.54899
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-YaungolSlayerBattlegear"
 value: {
  dps: 141462.5412
  tps: 117411.09758
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-Yu'lon'sBite-103987"
 value: {
  dps: 148187.3571
  tps: 122709.62213
 }
}
dps_results: {
 key: "TestMarksmanship-AllItems-ZenAlchemistStone-75274"
 value: {
  dps: 155306.07254
  tps: 128612.53478
 }
}
dps_results: {
 key: "TestMarksmanship-Average-Default"
 value: {
  dps: 158216.35759
  tps: 130604.46589
 }
}
dps_results: {
 key: "TestMarksmanship-Settings-Dwarf-p1-DefaultTalents-Basic-mm-FullBuffs-5.1yards-LongMultiTarget"
 value: {
  dps: 160134.83673
  tps: 133316.3454
 }
}
dps_results: {
 key: "TestMarksmanship-Settings-Dwarf-p1-DefaultTalents-Basic-mm-FullBuffs-5.1yards-LongSingleTarget"
 value: {
  dps: 156056.35456
  tps: 129826.16925
 }
}
dps_results: {
 key: "TestMarksmanship-Settings-Dwarf-p1-DefaultTalents-Basic-mm-FullBuffs-5.1yards-ShortSingleTarget"
 value: {
  dps: 217220.56802
  tps: 159439.25453
 }
}
dps_results: {
 key: "TestMarksmanship-Settings-Dwarf-p1-DefaultTalents-Basic-mm-NoBuffs-5.1yards-LongMultiTarget"
 value: {
  dps: 118087.75006
  tps: 101657.35919
 }
}
dps_results: {
 key: "TestMarksmanship-Settings-Dwarf-p1-DefaultTalents-Basic-mm-NoBuffs-5.1yards-LongSingleTarget"
 value: {
  dps: 114446.90805
  tps: 98314.63203
 }
}
dps_results: {
 key: "TestMarksmanship-Settings-Dwarf-p1-DefaultTalents-Basic-mm-NoBuffs-5.1yards-ShortSingleTarget"
 value: {
  dps: 132112.16584
  tps: 105866.06216
 }
}
dps_results: {
 key: "TestMarksmanship-Settings-Dwarf-p1-Row5_Talent0-Basic-mm-FullBuffs-5.1yards-LongMultiTarget"
 value: {
  dps: 155824.81619
  tps: 128965.86777
 }
}
dps_results: {
 key: "TestMarksmanship-Settings-Dwarf-p1-Row5_Talent0-Basic-mm-FullBuffs-5.1yards-LongSingleTarget"
 value: {
  dps: 151532.58439
  tps: 125156.44453
 }
}
dps_results: {
 key: "TestMarksmanship-Settings-Dwarf-p1-Row5_Talent0-Basic-mm-FullBuffs-5.1yards-ShortSingleTarget"
 value: {
  dps: 210744.72624
  tps: 151695.18586
 }
}
dps_results: {
 key: "TestMarksmanship-Settings-Dwarf-p1-Row5_Talent0-Basic-mm-NoBuffs-5.1yards-LongMultiTarget"
 value: {
  dps: 112481.03619
  tps: 96058.34002
 }
}
dps_results: {
 key: "TestMarksmanship-Settings-Dwarf-p1-Row5_Talent0-Basic-mm-NoBuffs-5.1yards-LongSingleTarget"
 value: {
  dps: 110223.25509
  tps: 94079.96977
 }
}
dps_results: {
 key: "TestMarksmanship-Settings-Dwarf-p1-Row5_Talent0-Basic-mm-NoBuffs-5.1yards-ShortSingleTarget"
 value: {
  dps: 128376.72186
  tps: 102246.41428
 }
}
dps_results: {
 key: "TestMarksmanship-Settings-Dwarf-p1-Row5_Talent2-Basic-mm-FullBuffs-5.1yards-LongMultiTarget"
 value: {
  dps: 159623.73234
  tps: 128965.86777
 }
}
dps_results: {
 key: "TestMarksmanship-Settings-Dwarf-p1-Row5_Talent2-Basic-mm-FullBuffs-5.1yards-LongSingleTarget"
 value: {
  dps: 155321.39451
  tps: 125156.44453
 }
}
dps_results: {
 key: "TestMarksmanship-Settings-Dwarf-p1-Row5_Talent2-Basic-mm-FullBuffs-5.1yards-ShortSingleTarget"
 value: {
  dps: 215793.00645
  tps: 151695.18586
 }
}
dps_results: {
 key: "TestMarksmanship-Settings-Dwarf-p1-Row5_Talent2-Basic-mm-NoBuffs-5.1yards-LongMultiTarget"
 value: {
  dps: 115348.57468
  tps: 96058.34002
 }
}
dps_results: {
 key: "TestMarksmanship-Settings-Dwarf-p1-Row5_Talent2-Basic-mm-NoBuffs-5.1yards-LongSingleTarget"
 value: {
  dps: 113089.81553
  tps: 94079.96977
 }
}
dps_results: {
 key: "TestMarksmanship-Settings-Dwarf-p1-Row5_Talent2-Basic-mm-NoBuffs-5.1yards-ShortSingleTarget"
 value: {
  dps: 131550.98253
  tps: 102246.41428
 }
}
dps_results: {
 key: "TestMarksmanship-Settings-Dwarf-p1-Row6_Talent0-Basic-mm-FullBuffs-5.1yards-LongMultiTarget"
 value: {
  dps: 157509.75665
  tps: 130950.07023
 }
}
dps_results: {
 key: "TestMarksmanship-Settings-Dwarf-p1-Row6_Talent0-Basic-mm-FullBuffs-5.1yards-LongSingleTarget"
 value: {
  dps: 152103.93368
  tps: 126214.91871
 }
}
dps_results: {
 key: "TestMarksmanship-Settings-Dwarf-p1-Row6_Talent0-Basic-mm-FullBuffs-5.1yards-ShortSingleTarget"
 value: {
  dps: 209874.94734
  tps: 152795.90066
 }
}
dps_results: {
 key: "TestMarksmanship-Settings-Dwarf-p1-Row6_Talent0-Basic-mm-NoBuffs-5.1yards-LongMultiTarget"
 value: {
  dps: 114139.15192
  tps: 97815.40045
 }
}
dps_results: {
 key: "TestMarksmanship-Settings-Dwarf-p1-Row6_Talent0-Basic-mm-NoBuffs-5.1yards-LongSingleTarget"
 value: {
  dps: 111928.43825
  tps: 95819.35387
 }
}
dps_results: {
 key: "TestMarksmanship-Settings-Dwarf-p1-Row6_Talent0-Basic-mm-NoBuffs-5.1yards-ShortSingleTarget"
 value: {
  dps: 129555.97197
  tps: 103496.42763
 }
}
dps_results: {
 key: "TestMarksmanship-Settings-Dwarf-p1-Row6_Talent2-Basic-mm-FullBuffs-5.1yards-LongMultiTarget"
 value: {
  dps: 157509.75665
  tps: 130950.07023
 }
}
dps_results: {
 key: "TestMarksmanship-Settings-Dwarf-p1-Row6_Talent2-Basic-mm-FullBuffs-5.1yards-LongSingleTarget"
 value: {
  dps: 152103.93368
  tps: 126214.91871
 }
}
dps_results: {
 key: "TestMarksmanship-Settings-Dwarf-p1-Row6_Talent2-Basic-mm-FullBuffs-5.1yards-ShortSingleTarget"
 value: {
  dps: 209874.94734
  tps: 152795.90066
 }
}
dps_results: {
 key: "TestMarksmanship-Settings-Dwarf-p1-Row6_Talent2-Basic-mm-NoBuffs-5.1yards-LongMultiTarget"
 value: {
  dps: 114139.15192
  tps: 97815.40045
 }
}
dps_results: {
 key: "TestMarksmanship-Settings-Dwarf-p1-Row6_Talent2-Basic-mm-NoBuffs-5.1yards-LongSingleTarget"
 value: {
  dps: 111928.43825
  tps: 95819.35387
 }
}
dps_results: {
 key: "TestMarksmanship-Settings-Dwarf-p1-Row6_Talent2-Basic-mm-NoBuffs-5.1yards-ShortSingleTarget"
 value: {
  dps: 129555.97197
  tps: 103496.42763
 }
}
dps_results: {
 key: "TestMarksmanship-Settings-Orc-p1-DefaultTalents-Basic-mm-FullBuffs-5.1yards-LongMultiTarget"
 value: {
  dps: 162927.28377
  tps: 134794.71296
 }
}
dps_results: {
 key: "TestMarksmanship-Settings-Orc-p1-DefaultTalents-Basic-mm-FullBuffs-5.1yards-LongSingleTarget"
 value: {
  dps: 158614.14411
  tps: 131101.98599
 }
}
dps_results: {
 key: "TestMarksmanship-Settings-Orc-p1-DefaultTalents-Basic-mm-FullBuffs-5.1yards-ShortSingleTarget"
 value: {
  dps: 223672.68639
  tps: 161851.35029
 }
}
dps_results: {
 key: "TestMarksmanship-Settings-Orc-p1-DefaultTalents-Basic-mm-NoBuffs-5.1yards-LongMultiTarget"
 value: {
  dps: 120121.82067
  tps: 102876.97396
 }
}
dps_results: {
 key: "TestMarksmanship-Settings-Orc-p1-DefaultTalents-Basic-mm-NoBuffs-5.1yards-LongSingleTarget"
 value: {
  dps: 116280.00543
  tps: 99343.34393
 }
}
dps_results: {
 key: "TestMarksmanship-Settings-Orc-p1-DefaultTalents-Basic-mm-NoBuffs-5.1yards-ShortSingleTarget"
 value: {
  dps: 135919.28004
  tps: 107543.5322
 }
}
dps_results: {
 key: "TestMarksmanship-Settings-Orc-p1-Row5_Talent0-Basic-mm-FullBuffs-5.1yards-LongMultiTarget"
 value: {
  dps: 158481.73547
  tps: 130300.43492
 }
}
dps_results: {
 key: "TestMarksmanship-Settings-Orc-p1-Row5_Talent0-Basic-mm-FullBuffs-5.1yards-LongSingleTarget"
 value: {
  dps: 153991.02913
  tps: 126310.16165
 }
}
dps_results: {
 key: "TestMarksmanship-Settings-Orc-p1-Row5_Talent0-Basic-mm-FullBuffs-5.1yards-ShortSingleTarget"
 value: {
  dps: 217050.55025
  tps: 153873.92765
 }
}
dps_results: {
 key: "TestMarksmanship-Settings-Orc-p1-Row5_Talent0-Basic-mm-NoBuffs-5.1yards-LongMultiTarget"
 value: {
  dps: 114340.6795
  tps: 97114.25156
 }
}
dps_results: {
 key: "TestMarksmanship-Settings-Orc-p1-Row5_Talent0-Basic-mm-NoBuffs-5.1yards-LongSingleTarget"
 value: {
  dps: 111901.35017
  tps: 94961.56488
 }
}
dps_results: {
 key: "TestMarksmanship-Settings-Orc-p1-Row5_Talent0-Basic-mm-NoBuffs-5.1yards-ShortSingleTarget"
 value: {
  dps: 131977.83446
  tps: 103738.36191
 }
}
dps_results: {
 key: "TestMarksmanship-Settings-Orc-p1-Row5_Talent2-Basic-mm-FullBuffs-5.1yards-LongMultiTarget"
 value: {
  dps: 162433.66378
  tps: 130300.43492
 }
}
dps_results: {
 key: "TestMarksmanship-Settings-Orc-p1-Row5_Talent2-Basic-mm-FullBuffs-5.1yards-LongSingleTarget"
 value: {
  dps: 157932.3155
  tps: 126310.16165
 }
}
dps_results: {
 key: "TestMarksmanship-Settings-Orc-p1-Row5_Talent2-Basic-mm-FullBuffs-5.1yards-ShortSingleTarget"
 value: {
  dps: 222364.56024
  tps: 153873.92765
 }
}
dps_results: {
 key: "TestMarksmanship-Settings-Orc-p1-Row5_Talent2-Basic-mm-NoBuffs-5.1yards-LongMultiTarget"
 value: {
  dps: 117325.21552
  tps: 97114.25156
 }
}
dps_results: {
 key: "TestMarksmanship-Settings-Orc-p1-Row5_Talent2-Basic-mm-NoBuffs-5.1yards-LongSingleTarget"
 value: {
  dps: 114887.04786
  tps: 94961.56488
 }
}
dps_results: {
 key: "TestMarksmanship-Settings-Orc-p1-Row5_Talent2-Basic-mm-NoBuffs-5.1yards-ShortSingleTarget"
 value: {
  dps: 135336.49476
  tps: 103738.36191
 }
}
dps_results: {
 key: "TestMarksmanship-Settings-Orc-p1-Row6_Talent0-Basic-mm-FullBuffs-5.1yards-LongMultiTarget"
 value: {
  dps: 160282.22963
  tps: 132424.05649
 }
}
dps_results: {
 key: "TestMarksmanship-Settings-Orc-p1-Row6_Talent0-Basic-mm-FullBuffs-5.1yards-LongSingleTarget"
 value: {
  dps: 154636.25511
  tps: 127474.74716
 }
}
dps_results: {
 key: "TestMarksmanship-Settings-Orc-p1-Row6_Talent0-Basic-mm-FullBuffs-5.1yards-ShortSingleTarget"
 value: {
  dps: 216139.77196
  tps: 155084.6152
 }
}
dps_results: {
 key: "TestMarksmanship-Settings-Orc-p1-Row6_Talent0-Basic-mm-NoBuffs-5.1yards-LongMultiTarget"
 value: {
  dps: 116011.14066
  tps: 98882.10805
 }
}
dps_results: {
 key: "TestMarksmanship-Settings-Orc-p1-Row6_Talent0-Basic-mm-NoBuffs-5.1yards-LongSingleTarget"
 value: {
  dps: 113637.8067
  tps: 96731.69409
 }
}
dps_results: {
 key: "TestMarksmanship-Settings-Orc-p1-Row6_Talent0-Basic-mm-NoBuffs-5.1yards-ShortSingleTarget"
 value: {
  dps: 133129.25818
  tps: 104953.85127
 }
}
dps_results: {
 key: "TestMarksmanship-Settings-Orc-p1-Row6_Talent2-Basic-mm-FullBuffs-5.1yards-LongMultiTarget"
 value: {
  dps: 160282.22963
  tps: 132424.05649
 }
}
dps_results: {
 key: "TestMarksmanship-Settings-Orc-p1-Row6_Talent2-Basic-mm-FullBuffs-5.1yards-LongSingleTarget"
 value: {
  dps: 154636.25511
  tps: 127474.74716
 }
}
dps_results: {
 key: "TestMarksmanship-Settings-Orc-p1-Row6_Talent2-Basic-mm-FullBuffs-5.1yards-ShortSingleTarget"
 value: {
  dps: 216139.77196
  tps: 155084.6152
 }
}
dps_results: {
 key: "TestMarksmanship-Settings-Orc-p1-Row6_Talent2-Basic-mm-NoBuffs-5.1yards-LongMultiTarget"
 value: {
  dps: 116011.14066
  tps: 98882.10805
 }
}
dps_results: {
 key: "TestMarksmanship-Settings-Orc-p1-Row6_Talent2-Basic-mm-NoBuffs-5.1yards-LongSingleTarget"
 value: {
  dps: 113637.8067
  tps: 96731.69409
 }
}
dps_results: {
 key: "TestMarksmanship-Settings-Orc-p1-Row6_Talent2-Basic-mm-NoBuffs-5.1yards-ShortSingleTarget"
 value: {
  dps: 133129.25818
  tps: 104953.85127
 }
}
dps_results: {
 key: "TestMarksmanship-SwitchInFrontOfTarget-Default"
 value: {
  dps: 156255.79407
  tps: 130945.28213
 }
}
