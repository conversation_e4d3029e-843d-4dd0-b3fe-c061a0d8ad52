character_stats_results: {
 key: "TestSurvival-CharacterStats-Default"
 value: {
  final_stats: 177.45
  final_stats: 20478.9375
  final_stats: 22600.6
  final_stats: 191.1
  final_stats: 195
  final_stats: 2550
  final_stats: 8556
  final_stats: 3452
  final_stats: 2566
  final_stats: 0
  final_stats: 0
  final_stats: 6296
  final_stats: 45229.6625
  final_stats: 61060.04438
  final_stats: 0
  final_stats: 0
  final_stats: 0
  final_stats: 25425
  final_stats: 0
  final_stats: 462811.4
  final_stats: 0
  final_stats: 0
  final_stats: 7.5
  final_stats: 15.04706
  final_stats: 33.98925
  final_stats: 19.26
  final_stats: 0
 }
}
dps_results: {
 key: "TestSurvival-AllItems-AgilePrimalDiamond"
 value: {
  dps: 154139.9006
  tps: 127443.69722
 }
}
dps_results: {
 key: "TestSurvival-AllItems-AlacrityofXuen-103989"
 value: {
  dps: 143192.45319
  tps: 118793.29348
 }
}
dps_results: {
 key: "TestSurvival-AllItems-ArcaneBadgeoftheShieldwall-93347"
 value: {
  dps: 143478.60018
  tps: 119016.64518
 }
}
dps_results: {
 key: "TestSurvival-AllItems-ArrowflightMedallion-93258"
 value: {
  dps: 152750.6376
  tps: 126674.35622
 }
}
dps_results: {
 key: "TestSurvival-AllItems-AssuranceofConsequence-105472"
 value: {
  dps: 156657.33901
  tps: 126678.51266
 }
}
dps_results: {
 key: "TestSurvival-AllItems-AusterePrimalDiamond"
 value: {
  dps: 150621.91849
  tps: 124214.91669
 }
}
dps_results: {
 key: "TestSurvival-AllItems-BadJuju-96781"
 value: {
  dps: 160394.79008
  tps: 132391.21337
 }
}
dps_results: {
 key: "TestSurvival-AllItems-BadgeofKypariZar-84079"
 value: {
  dps: 145056.81795
  tps: 120689.69148
 }
}
dps_results: {
 key: "TestSurvival-AllItems-BattlegearoftheSaurokStalker"
 value: {
  dps: 142233.72049
  tps: 115790.30751
 }
}
dps_results: {
 key: "TestSurvival-AllItems-BlossomofPureSnow-89081"
 value: {
  dps: 144802.03385
  tps: 120260.52725
 }
}
dps_results: {
 key: "TestSurvival-AllItems-BraidofTenSongs-84072"
 value: {
  dps: 144217.26644
  tps: 119951.7868
 }
}
dps_results: {
 key: "TestSurvival-AllItems-Brawler'sStatue-87571"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-BreathoftheHydra-96827"
 value: {
  dps: 144588.03917
  tps: 119783.15982
 }
}
dps_results: {
 key: "TestSurvival-AllItems-BroochofMunificentDeeds-87500"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-BrutalTalismanoftheShado-PanAssault-94508"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-BurningPrimalDiamond"
 value: {
  dps: 152646.26549
  tps: 126221.66816
 }
}
dps_results: {
 key: "TestSurvival-AllItems-CapacitivePrimalDiamond"
 value: {
  dps: 151233.39928
  tps: 124723.05559
 }
}
dps_results: {
 key: "TestSurvival-AllItems-CarbonicCarbuncle-81138"
 value: {
  dps: 145039.55709
  tps: 120503.80525
 }
}
dps_results: {
 key: "TestSurvival-AllItems-Cha-Ye'sEssenceofBrilliance-96888"
 value: {
  dps: 145868.6846
  tps: 121182.25398
 }
}
dps_results: {
 key: "TestSurvival-AllItems-CharmofTenSongs-84071"
 value: {
  dps: 144480.63706
  tps: 119977.8568
 }
}
dps_results: {
 key: "TestSurvival-AllItems-CommunalIdolofDestruction-101168"
 value: {
  dps: 144338.42754
  tps: 120101.80611
 }
}
dps_results: {
 key: "TestSurvival-AllItems-CommunalStoneofDestruction-101171"
 value: {
  dps: 144444.13071
  tps: 120351.51983
 }
}
dps_results: {
 key: "TestSurvival-AllItems-CommunalStoneofWisdom-101183"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-ContemplationofChi-Ji-103688"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-ContemplationofChi-Ji-103988"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-Coren'sColdChromiumCoaster-87574"
 value: {
  dps: 149838.41902
  tps: 123994.92527
 }
}
dps_results: {
 key: "TestSurvival-AllItems-CoreofDecency-87497"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-CourageousPrimalDiamond"
 value: {
  dps: 150621.91849
  tps: 124214.91669
 }
}
dps_results: {
 key: "TestSurvival-AllItems-CraftedDreadfulGladiator'sBadgeofConquest-93419"
 value: {
  dps: 147928.88434
  tps: 122703.69355
 }
}
dps_results: {
 key: "TestSurvival-AllItems-CraftedDreadfulGladiator'sBadgeofDominance-93600"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-CraftedDreadfulGladiator'sBadgeofVictory-93606"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-CraftedDreadfulGladiator'sEmblemofCruelty-93485"
 value: {
  dps: 144086.87069
  tps: 119767.73865
 }
}
dps_results: {
 key: "TestSurvival-AllItems-CraftedDreadfulGladiator'sEmblemofMeditation-93487"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-CraftedDreadfulGladiator'sEmblemofTenacity-93486"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-CraftedDreadfulGladiator'sInsigniaofConquest-93424"
 value: {
  dps: 150587.84031
  tps: 124303.99842
 }
}
dps_results: {
 key: "TestSurvival-AllItems-CraftedDreadfulGladiator'sInsigniaofDominance-93601"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-CraftedDreadfulGladiator'sInsigniaofVictory-93611"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-CraftedMalevolentGladiator'sBadgeofConquest-98755"
 value: {
  dps: 148848.86369
  tps: 123410.66202
 }
}
dps_results: {
 key: "TestSurvival-AllItems-CraftedMalevolentGladiator'sBadgeofDominance-98910"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-CraftedMalevolentGladiator'sBadgeofVictory-98912"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-CraftedMalevolentGladiator'sEmblemofCruelty-98811"
 value: {
  dps: 144414.43102
  tps: 120016.94015
 }
}
dps_results: {
 key: "TestSurvival-AllItems-CraftedMalevolentGladiator'sEmblemofMeditation-98813"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-CraftedMalevolentGladiator'sEmblemofTenacity-98812"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-CraftedMalevolentGladiator'sInsigniaofConquest-98760"
 value: {
  dps: 152403.92584
  tps: 125804.32377
 }
}
dps_results: {
 key: "TestSurvival-AllItems-CraftedMalevolentGladiator'sInsigniaofDominance-98911"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-CraftedMalevolentGladiator'sInsigniaofVictory-98917"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-CurseofHubris-102307"
 value: {
  dps: 147900.21993
  tps: 122595.24574
 }
}
dps_results: {
 key: "TestSurvival-AllItems-CurseofHubris-104649"
 value: {
  dps: 148540.38772
  tps: 123072.80399
 }
}
dps_results: {
 key: "TestSurvival-AllItems-CurseofHubris-104898"
 value: {
  dps: 147359.61571
  tps: 122179.12832
 }
}
dps_results: {
 key: "TestSurvival-AllItems-CurseofHubris-105147"
 value: {
  dps: 146911.91624
  tps: 121846.20431
 }
}
dps_results: {
 key: "TestSurvival-AllItems-CurseofHubris-105396"
 value: {
  dps: 148145.66157
  tps: 122770.78926
 }
}
dps_results: {
 key: "TestSurvival-AllItems-CurseofHubris-105645"
 value: {
  dps: 148880.89585
  tps: 123332.57793
 }
}
dps_results: {
 key: "TestSurvival-AllItems-CutstitcherMedallion-93255"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-Daelo'sFinalWords-87496"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-DarkglowEmbroidery(Rank3)-4893"
 value: {
  dps: 153821.16661
  tps: 127171.23048
 }
}
dps_results: {
 key: "TestSurvival-AllItems-DarkmistVortex-87172"
 value: {
  dps: 143855.36939
  tps: 119301.90814
 }
}
dps_results: {
 key: "TestSurvival-AllItems-DeadeyeBadgeoftheShieldwall-93346"
 value: {
  dps: 149120.68849
  tps: 123979.88806
 }
}
dps_results: {
 key: "TestSurvival-AllItems-DelicateVialoftheSanguinaire-96895"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-DestructivePrimalDiamond"
 value: {
  dps: 151417.55944
  tps: 124866.73281
 }
}
dps_results: {
 key: "TestSurvival-AllItems-DisciplineofXuen-103986"
 value: {
  dps: 156862.66624
  tps: 130481.1899
 }
}
dps_results: {
 key: "TestSurvival-AllItems-Dominator'sArcaneBadge-93342"
 value: {
  dps: 143478.60018
  tps: 119016.64518
 }
}
dps_results: {
 key: "TestSurvival-AllItems-Dominator'sDeadeyeBadge-93341"
 value: {
  dps: 149120.68849
  tps: 123979.88806
 }
}
dps_results: {
 key: "TestSurvival-AllItems-Dominator'sDurableBadge-93345"
 value: {
  dps: 143763.51342
  tps: 119686.97119
 }
}
dps_results: {
 key: "TestSurvival-AllItems-Dominator'sKnightlyBadge-93344"
 value: {
  dps: 143763.51342
  tps: 119686.97119
 }
}
dps_results: {
 key: "TestSurvival-AllItems-Dominator'sMendingBadge-93343"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-DreadfulGladiator'sBadgeofConquest-84344"
 value: {
  dps: 147928.88434
  tps: 122703.69355
 }
}
dps_results: {
 key: "TestSurvival-AllItems-DreadfulGladiator'sBadgeofDominance-84488"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-DreadfulGladiator'sBadgeofVictory-84490"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-DreadfulGladiator'sEmblemofCruelty-84399"
 value: {
  dps: 144086.87069
  tps: 119767.73865
 }
}
dps_results: {
 key: "TestSurvival-AllItems-DreadfulGladiator'sEmblemofMeditation-84401"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-DreadfulGladiator'sEmblemofTenacity-84400"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-DreadfulGladiator'sInsigniaofConquest-84349"
 value: {
  dps: 150917.56038
  tps: 124640.17763
 }
}
dps_results: {
 key: "TestSurvival-AllItems-DreadfulGladiator'sInsigniaofDominance-84489"
 value: {
  dps: 142727.39447
  tps: 118700.62891
 }
}
dps_results: {
 key: "TestSurvival-AllItems-DreadfulGladiator'sInsigniaofVictory-84495"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-DurableBadgeoftheShieldwall-93350"
 value: {
  dps: 143763.51342
  tps: 119686.97119
 }
}
dps_results: {
 key: "TestSurvival-AllItems-EffulgentPrimalDiamond"
 value: {
  dps: 150621.91849
  tps: 124214.91669
 }
}
dps_results: {
 key: "TestSurvival-AllItems-EmberPrimalDiamond"
 value: {
  dps: 150621.91849
  tps: 124214.91669
 }
}
dps_results: {
 key: "TestSurvival-AllItems-EmblemofKypariZar-84077"
 value: {
  dps: 144374.48803
  tps: 119901.20622
 }
}
dps_results: {
 key: "TestSurvival-AllItems-EmblemoftheCatacombs-83733"
 value: {
  dps: 144329.00998
  tps: 120038.11636
 }
}
dps_results: {
 key: "TestSurvival-AllItems-EmptyFruitBarrel-81133"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-EnchantWeapon-BloodyDancingSteel-5125"
 value: {
  dps: 153702.6018
  tps: 126909.79256
 }
}
dps_results: {
 key: "TestSurvival-AllItems-EnchantWeapon-Colossus-4445"
 value: {
  dps: 148556.95539
  tps: 122847.66408
 }
}
dps_results: {
 key: "TestSurvival-AllItems-EnchantWeapon-DancingSteel-4444"
 value: {
  dps: 154191.53523
  tps: 127274.99451
 }
}
dps_results: {
 key: "TestSurvival-AllItems-EnchantWeapon-JadeSpirit-4442"
 value: {
  dps: 148557.20175
  tps: 122847.91044
 }
}
dps_results: {
 key: "TestSurvival-AllItems-EnchantWeapon-River'sSong-4446"
 value: {
  dps: 148556.82483
  tps: 122847.53352
 }
}
dps_results: {
 key: "TestSurvival-AllItems-EnchantWeapon-SpiritofConquest-5124"
 value: {
  dps: 148556.95539
  tps: 122847.66408
 }
}
dps_results: {
 key: "TestSurvival-AllItems-EnchantWeapon-Windsong-4441"
 value: {
  dps: 150060.16902
  tps: 124266.07271
 }
}
dps_results: {
 key: "TestSurvival-AllItems-EnigmaticPrimalDiamond"
 value: {
  dps: 151417.55944
  tps: 124866.73281
 }
}
dps_results: {
 key: "TestSurvival-AllItems-EssenceofTerror-87175"
 value: {
  dps: 143427.4308
  tps: 118991.02634
 }
}
dps_results: {
 key: "TestSurvival-AllItems-EternalPrimalDiamond"
 value: {
  dps: 150621.91849
  tps: 124214.91669
 }
}
dps_results: {
 key: "TestSurvival-AllItems-EvilEyeofGalakras-105491"
 value: {
  dps: 142727.39447
  tps: 118700.62891
 }
}
dps_results: {
 key: "TestSurvival-AllItems-FabledFeatherofJi-Kun-96842"
 value: {
  dps: 142727.44233
  tps: 118702.46932
 }
}
dps_results: {
 key: "TestSurvival-AllItems-FearwurmBadge-84074"
 value: {
  dps: 144553.68339
  tps: 120209.84197
 }
}
dps_results: {
 key: "TestSurvival-AllItems-FearwurmRelic-84070"
 value: {
  dps: 144975.69327
  tps: 120423.83595
 }
}
dps_results: {
 key: "TestSurvival-AllItems-FelsoulIdolofDestruction-101263"
 value: {
  dps: 144574.30071
  tps: 120306.58982
 }
}
dps_results: {
 key: "TestSurvival-AllItems-FelsoulStoneofDestruction-101266"
 value: {
  dps: 144513.5505
  tps: 120390.60705
 }
}
dps_results: {
 key: "TestSurvival-AllItems-Fen-Yu,FuryofXuen-102248"
 value: {
  dps: 167679.40153
  tps: 139376.37702
 }
}
dps_results: {
 key: "TestSurvival-AllItems-FlashfrozenResinGlobule-100951"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-FlashfrozenResinGlobule-81263"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-FlashingSteelTalisman-81265"
 value: {
  dps: 149948.56424
  tps: 124061.14163
 }
}
dps_results: {
 key: "TestSurvival-AllItems-FleetPrimalDiamond"
 value: {
  dps: 151190.39425
  tps: 124775.53036
 }
}
dps_results: {
 key: "TestSurvival-AllItems-ForlornPrimalDiamond"
 value: {
  dps: 150621.91849
  tps: 124214.91669
 }
}
dps_results: {
 key: "TestSurvival-AllItems-FortitudeoftheZandalari-94516"
 value: {
  dps: 144591.91098
  tps: 120537.88452
 }
}
dps_results: {
 key: "TestSurvival-AllItems-FortitudeoftheZandalari-95677"
 value: {
  dps: 144275.56441
  tps: 120226.16505
 }
}
dps_results: {
 key: "TestSurvival-AllItems-FortitudeoftheZandalari-96049"
 value: {
  dps: 144699.90078
  tps: 120644.29478
 }
}
dps_results: {
 key: "TestSurvival-AllItems-FortitudeoftheZandalari-96421"
 value: {
  dps: 144833.29994
  tps: 120775.74275
 }
}
dps_results: {
 key: "TestSurvival-AllItems-FortitudeoftheZandalari-96793"
 value: {
  dps: 144953.99441
  tps: 120894.67187
 }
}
dps_results: {
 key: "TestSurvival-AllItems-GazeoftheTwins-96915"
 value: {
  dps: 145729.50805
  tps: 121050.48088
 }
}
dps_results: {
 key: "TestSurvival-AllItems-Gerp'sPerfectArrow-87495"
 value: {
  dps: 150038.11881
  tps: 124229.13031
 }
}
dps_results: {
 key: "TestSurvival-AllItems-Gladiator'sPursuit"
 value: {
  dps: 153710.19822
  tps: 124896.58338
 }
}
dps_results: {
 key: "TestSurvival-AllItems-Gong-Lu,StrengthofXuen-102249"
 value: {
  dps: 156863.10885
  tps: 130634.60717
 }
}
dps_results: {
 key: "TestSurvival-AllItems-GrievousGladiator'sBadgeofConquest-100195"
 value: {
  dps: 152345.87418
  tps: 126133.74369
 }
}
dps_results: {
 key: "TestSurvival-AllItems-GrievousGladiator'sBadgeofConquest-100603"
 value: {
  dps: 152345.87418
  tps: 126133.74369
 }
}
dps_results: {
 key: "TestSurvival-AllItems-GrievousGladiator'sBadgeofConquest-102856"
 value: {
  dps: 152345.87418
  tps: 126133.74369
 }
}
dps_results: {
 key: "TestSurvival-AllItems-GrievousGladiator'sBadgeofConquest-103145"
 value: {
  dps: 152345.87418
  tps: 126133.74369
 }
}
dps_results: {
 key: "TestSurvival-AllItems-GrievousGladiator'sBadgeofDominance-100490"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-GrievousGladiator'sBadgeofDominance-100576"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-GrievousGladiator'sBadgeofDominance-102830"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-GrievousGladiator'sBadgeofDominance-103308"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-GrievousGladiator'sBadgeofVictory-100500"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-GrievousGladiator'sBadgeofVictory-100579"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-GrievousGladiator'sBadgeofVictory-102833"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-GrievousGladiator'sBadgeofVictory-103314"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-GrievousGladiator'sEmblemofCruelty-100305"
 value: {
  dps: 145306.20475
  tps: 120724.41381
 }
}
dps_results: {
 key: "TestSurvival-AllItems-GrievousGladiator'sEmblemofCruelty-100626"
 value: {
  dps: 145306.20475
  tps: 120724.41381
 }
}
dps_results: {
 key: "TestSurvival-AllItems-GrievousGladiator'sEmblemofCruelty-102877"
 value: {
  dps: 145306.20475
  tps: 120724.41381
 }
}
dps_results: {
 key: "TestSurvival-AllItems-GrievousGladiator'sEmblemofCruelty-103210"
 value: {
  dps: 145306.20475
  tps: 120724.41381
 }
}
dps_results: {
 key: "TestSurvival-AllItems-GrievousGladiator'sEmblemofMeditation-100307"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-GrievousGladiator'sEmblemofMeditation-100559"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-GrievousGladiator'sEmblemofMeditation-102813"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-GrievousGladiator'sEmblemofMeditation-103212"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-GrievousGladiator'sEmblemofTenacity-100306"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-GrievousGladiator'sEmblemofTenacity-100652"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-GrievousGladiator'sEmblemofTenacity-102903"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-GrievousGladiator'sEmblemofTenacity-103211"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-GrievousGladiator'sInsigniaofConquest-103150"
 value: {
  dps: 157411.52947
  tps: 129501.98682
 }
}
dps_results: {
 key: "TestSurvival-AllItems-GrievousGladiator'sInsigniaofDominance-103309"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-GrievousGladiator'sInsigniaofVictory-103319"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-Hawkmaster'sTalon-89082"
 value: {
  dps: 151299.06315
  tps: 125354.84934
 }
}
dps_results: {
 key: "TestSurvival-AllItems-Heart-LesionDefenderIdol-100999"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-Heart-LesionDefenderStone-101002"
 value: {
  dps: 144463.02049
  tps: 120359.57281
 }
}
dps_results: {
 key: "TestSurvival-AllItems-Heart-LesionIdolofBattle-100991"
 value: {
  dps: 144517.55837
  tps: 120091.71582
 }
}
dps_results: {
 key: "TestSurvival-AllItems-Heart-LesionStoneofBattle-100990"
 value: {
  dps: 144440.74864
  tps: 120330.87203
 }
}
dps_results: {
 key: "TestSurvival-AllItems-HeartofFire-81181"
 value: {
  dps: 143804.22072
  tps: 119761.71556
 }
}
dps_results: {
 key: "TestSurvival-AllItems-HeartwarmerMedallion-93260"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-HelmbreakerMedallion-93261"
 value: {
  dps: 144943.49473
  tps: 120374.9911
 }
}
dps_results: {
 key: "TestSurvival-AllItems-Horridon'sLastGasp-96757"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-ImpassivePrimalDiamond"
 value: {
  dps: 151417.55944
  tps: 124866.73281
 }
}
dps_results: {
 key: "TestSurvival-AllItems-IndomitablePrimalDiamond"
 value: {
  dps: 150621.91849
  tps: 124214.91669
 }
}
dps_results: {
 key: "TestSurvival-AllItems-InscribedBagofHydra-Spawn-96828"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-InsigniaofKypariZar-84078"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-IronBellyWok-89083"
 value: {
  dps: 144135.51599
  tps: 119591.12292
 }
}
dps_results: {
 key: "TestSurvival-AllItems-IronProtectorTalisman-85181"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-JadeBanditFigurine-86043"
 value: {
  dps: 151299.06315
  tps: 125354.84934
 }
}
dps_results: {
 key: "TestSurvival-AllItems-JadeBanditFigurine-86772"
 value: {
  dps: 150149.51364
  tps: 124379.53108
 }
}
dps_results: {
 key: "TestSurvival-AllItems-JadeCharioteerFigurine-86042"
 value: {
  dps: 144135.51599
  tps: 119591.12292
 }
}
dps_results: {
 key: "TestSurvival-AllItems-JadeCharioteerFigurine-86771"
 value: {
  dps: 143777.71708
  tps: 119237.94111
 }
}
dps_results: {
 key: "TestSurvival-AllItems-JadeCourtesanFigurine-86045"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-JadeCourtesanFigurine-86774"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-JadeMagistrateFigurine-86044"
 value: {
  dps: 144802.03385
  tps: 120260.52725
 }
}
dps_results: {
 key: "TestSurvival-AllItems-JadeMagistrateFigurine-86773"
 value: {
  dps: 144590.53592
  tps: 120109.04719
 }
}
dps_results: {
 key: "TestSurvival-AllItems-JadeWarlordFigurine-86046"
 value: {
  dps: 144110.30587
  tps: 120017.09132
 }
}
dps_results: {
 key: "TestSurvival-AllItems-JadeWarlordFigurine-86775"
 value: {
  dps: 143952.28847
  tps: 119866.67073
 }
}
dps_results: {
 key: "TestSurvival-AllItems-Ji-Kun'sRisingWinds-96843"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-KnightlyBadgeoftheShieldwall-93349"
 value: {
  dps: 143763.51342
  tps: 119686.97119
 }
}
dps_results: {
 key: "TestSurvival-AllItems-KnotofTenSongs-84073"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-Kor'kronBookofHurting-92785"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-Lao-Chin'sLiquidCourage-89079"
 value: {
  dps: 144110.30587
  tps: 120017.09132
 }
}
dps_results: {
 key: "TestSurvival-AllItems-LeiShen'sFinalOrders-87072"
 value: {
  dps: 144119.19146
  tps: 119712.74913
 }
}
dps_results: {
 key: "TestSurvival-AllItems-LessonsoftheDarkmaster-81268"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-LightdrinkerIdolofRage-101200"
 value: {
  dps: 152087.90807
  tps: 125761.64445
 }
}
dps_results: {
 key: "TestSurvival-AllItems-LightdrinkerStoneofRage-101203"
 value: {
  dps: 151377.41941
  tps: 125917.7304
 }
}
dps_results: {
 key: "TestSurvival-AllItems-LightoftheCosmos-87065"
 value: {
  dps: 144119.19146
  tps: 119712.74913
 }
}
dps_results: {
 key: "TestSurvival-AllItems-LightweaveEmbroidery(Rank3)-4892"
 value: {
  dps: 153821.28979
  tps: 127171.35366
 }
}
dps_results: {
 key: "TestSurvival-AllItems-MalevolentGladiator'sBadgeofConquest-84934"
 value: {
  dps: 149311.22642
  tps: 123778.23622
 }
}
dps_results: {
 key: "TestSurvival-AllItems-MalevolentGladiator'sBadgeofConquest-91452"
 value: {
  dps: 148848.86369
  tps: 123410.66202
 }
}
dps_results: {
 key: "TestSurvival-AllItems-MalevolentGladiator'sBadgeofDominance-84940"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-MalevolentGladiator'sBadgeofDominance-91753"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-MalevolentGladiator'sBadgeofVictory-84942"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-MalevolentGladiator'sBadgeofVictory-91763"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-MalevolentGladiator'sEmblemofCruelty-84936"
 value: {
  dps: 144517.55837
  tps: 120091.71582
 }
}
dps_results: {
 key: "TestSurvival-AllItems-MalevolentGladiator'sEmblemofCruelty-91562"
 value: {
  dps: 144414.43102
  tps: 120016.94015
 }
}
dps_results: {
 key: "TestSurvival-AllItems-MalevolentGladiator'sEmblemofMeditation-84939"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-MalevolentGladiator'sEmblemofMeditation-91564"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-MalevolentGladiator'sEmblemofTenacity-84938"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-MalevolentGladiator'sEmblemofTenacity-91563"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-MalevolentGladiator'sInsigniaofConquest-91457"
 value: {
  dps: 152027.61204
  tps: 125372.23307
 }
}
dps_results: {
 key: "TestSurvival-AllItems-MalevolentGladiator'sInsigniaofDominance-91754"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-MalevolentGladiator'sInsigniaofVictory-91768"
 value: {
  dps: 142727.39447
  tps: 118700.62891
 }
}
dps_results: {
 key: "TestSurvival-AllItems-MarkoftheCatacombs-83731"
 value: {
  dps: 144670.94288
  tps: 120383.20003
 }
}
dps_results: {
 key: "TestSurvival-AllItems-MarkoftheHardenedGrunt-92783"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-MedallionofMystifyingVapors-93257"
 value: {
  dps: 144203.73222
  tps: 120106.02613
 }
}
dps_results: {
 key: "TestSurvival-AllItems-MedallionoftheCatacombs-83734"
 value: {
  dps: 143512.01304
  tps: 119473.78192
 }
}
dps_results: {
 key: "TestSurvival-AllItems-MendingBadgeoftheShieldwall-93348"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-MirrorScope-4700"
 value: {
  dps: 149239.88696
  tps: 123404.17385
 }
}
dps_results: {
 key: "TestSurvival-AllItems-MistdancerDefenderIdol-101089"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-MistdancerDefenderStone-101087"
 value: {
  dps: 144486.4695
  tps: 120382.10419
 }
}
dps_results: {
 key: "TestSurvival-AllItems-MistdancerIdolofRage-101113"
 value: {
  dps: 152237.25675
  tps: 126017.49035
 }
}
dps_results: {
 key: "TestSurvival-AllItems-MistdancerStoneofRage-101117"
 value: {
  dps: 151327.63015
  tps: 125882.61372
 }
}
dps_results: {
 key: "TestSurvival-AllItems-MistdancerStoneofWisdom-101107"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-MithrilWristwatch-87572"
 value: {
  dps: 144322.84039
  tps: 119946.66387
 }
}
dps_results: {
 key: "TestSurvival-AllItems-MountainsageIdolofDestruction-101069"
 value: {
  dps: 144786.66039
  tps: 120536.12619
 }
}
dps_results: {
 key: "TestSurvival-AllItems-MountainsageStoneofDestruction-101072"
 value: {
  dps: 144489.75474
  tps: 120376.32838
 }
}
dps_results: {
 key: "TestSurvival-AllItems-OathswornDefenderIdol-101303"
 value: {
  dps: 142727.39447
  tps: 118700.62891
 }
}
dps_results: {
 key: "TestSurvival-AllItems-OathswornDefenderStone-101306"
 value: {
  dps: 144436.41834
  tps: 120341.32808
 }
}
dps_results: {
 key: "TestSurvival-AllItems-OathswornIdolofBattle-101295"
 value: {
  dps: 144517.55837
  tps: 120091.71582
 }
}
dps_results: {
 key: "TestSurvival-AllItems-OathswornStoneofBattle-101294"
 value: {
  dps: 144429.87443
  tps: 120321.39482
 }
}
dps_results: {
 key: "TestSurvival-AllItems-PhaseFingers-4697"
 value: {
  dps: 153738.1437
  tps: 127138.73475
 }
}
dps_results: {
 key: "TestSurvival-AllItems-PouchofWhiteAsh-103639"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-PowerfulPrimalDiamond"
 value: {
  dps: 150621.91849
  tps: 124214.91669
 }
}
dps_results: {
 key: "TestSurvival-AllItems-PriceofProgress-81266"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-PridefulGladiator'sBadgeofConquest-102659"
 value: {
  dps: 155289.88852
  tps: 128443.85003
 }
}
dps_results: {
 key: "TestSurvival-AllItems-PridefulGladiator'sBadgeofConquest-103342"
 value: {
  dps: 155289.88852
  tps: 128443.85003
 }
}
dps_results: {
 key: "TestSurvival-AllItems-PridefulGladiator'sBadgeofDominance-102633"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-PridefulGladiator'sBadgeofDominance-103505"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-PridefulGladiator'sBadgeofVictory-102636"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-PridefulGladiator'sBadgeofVictory-103511"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-PridefulGladiator'sEmblemofCruelty-102680"
 value: {
  dps: 146215.26968
  tps: 121456.71624
 }
}
dps_results: {
 key: "TestSurvival-AllItems-PridefulGladiator'sEmblemofCruelty-103407"
 value: {
  dps: 146215.26968
  tps: 121456.71624
 }
}
dps_results: {
 key: "TestSurvival-AllItems-PridefulGladiator'sEmblemofMeditation-102616"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-PridefulGladiator'sEmblemofMeditation-103409"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-PridefulGladiator'sEmblemofTenacity-102706"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-PridefulGladiator'sEmblemofTenacity-103408"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-PridefulGladiator'sInsigniaofConquest-103347"
 value: {
  dps: 162090.3629
  tps: 133118.17892
 }
}
dps_results: {
 key: "TestSurvival-AllItems-PridefulGladiator'sInsigniaofDominance-103506"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-PridefulGladiator'sInsigniaofVictory-103516"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-Primordius'TalismanofRage-96873"
 value: {
  dps: 145868.6846
  tps: 121182.25398
 }
}
dps_results: {
 key: "TestSurvival-AllItems-Qian-Le,CourageofNiuzao-102245"
 value: {
  dps: 161131.80479
  tps: 133163.27968
 }
}
dps_results: {
 key: "TestSurvival-AllItems-Qian-Ying,FortitudeofNiuzao-102250"
 value: {
  dps: 147820.06915
  tps: 122383.02154
 }
}
dps_results: {
 key: "TestSurvival-AllItems-Qin-xi'sPolarizingSeal-87075"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-RelicofChi-Ji-79330"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-RelicofKypariZar-84075"
 value: {
  dps: 144959.49563
  tps: 120335.88362
 }
}
dps_results: {
 key: "TestSurvival-AllItems-RelicofNiuzao-79329"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-RelicofXuen-79327"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-RelicofXuen-79328"
 value: {
  dps: 154958.91244
  tps: 127995.60312
 }
}
dps_results: {
 key: "TestSurvival-AllItems-RelicofYu'lon-79331"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-Renataki'sSoulCharm-96741"
 value: {
  dps: 158445.7868
  tps: 130848.34767
 }
}
dps_results: {
 key: "TestSurvival-AllItems-ResolveofNiuzao-103690"
 value: {
  dps: 144191.71351
  tps: 120143.54062
 }
}
dps_results: {
 key: "TestSurvival-AllItems-ResolveofNiuzao-103990"
 value: {
  dps: 144833.29994
  tps: 120775.74275
 }
}
dps_results: {
 key: "TestSurvival-AllItems-ReverberatingPrimalDiamond"
 value: {
  dps: 152646.26549
  tps: 126221.66816
 }
}
dps_results: {
 key: "TestSurvival-AllItems-RevitalizingPrimalDiamond"
 value: {
  dps: 152646.26549
  tps: 126221.66816
 }
}
dps_results: {
 key: "TestSurvival-AllItems-SI:7Operative'sManual-92784"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-ScrollofReveredAncestors-89080"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-SearingWords-81267"
 value: {
  dps: 152742.95003
  tps: 125804.15579
 }
}
dps_results: {
 key: "TestSurvival-AllItems-Shock-ChargerMedallion-93259"
 value: {
  dps: 143732.32983
  tps: 119514.50349
 }
}
dps_results: {
 key: "TestSurvival-AllItems-SigilofCompassion-83736"
 value: {
  dps: 143511.26685
  tps: 119473.03573
 }
}
dps_results: {
 key: "TestSurvival-AllItems-SigilofDevotion-83740"
 value: {
  dps: 144798.53909
  tps: 120500.95026
 }
}
dps_results: {
 key: "TestSurvival-AllItems-SigilofFidelity-83737"
 value: {
  dps: 144883.1587
  tps: 120259.67865
 }
}
dps_results: {
 key: "TestSurvival-AllItems-SigilofGrace-83738"
 value: {
  dps: 144164.12281
  tps: 119843.59231
 }
}
dps_results: {
 key: "TestSurvival-AllItems-SigilofKypariZar-84076"
 value: {
  dps: 144909.95252
  tps: 120599.51383
 }
}
dps_results: {
 key: "TestSurvival-AllItems-SigilofPatience-83739"
 value: {
  dps: 142739.85112
  tps: 118712.15365
 }
}
dps_results: {
 key: "TestSurvival-AllItems-SigiloftheCatacombs-83732"
 value: {
  dps: 144403.098
  tps: 119854.67777
 }
}
dps_results: {
 key: "TestSurvival-AllItems-SinisterPrimalDiamond"
 value: {
  dps: 151233.39928
  tps: 124723.05559
 }
}
dps_results: {
 key: "TestSurvival-AllItems-SkullrenderMedallion-93256"
 value: {
  dps: 144943.49473
  tps: 120374.9911
 }
}
dps_results: {
 key: "TestSurvival-AllItems-SpiritsoftheSun-87163"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-SpringrainIdolofDestruction-101023"
 value: {
  dps: 144630.04196
  tps: 120291.66047
 }
}
dps_results: {
 key: "TestSurvival-AllItems-SpringrainIdolofRage-101009"
 value: {
  dps: 152120.87831
  tps: 125835.36926
 }
}
dps_results: {
 key: "TestSurvival-AllItems-SpringrainStoneofDestruction-101026"
 value: {
  dps: 144467.59788
  tps: 120354.9461
 }
}
dps_results: {
 key: "TestSurvival-AllItems-SpringrainStoneofRage-101012"
 value: {
  dps: 151342.64309
  tps: 125883.05657
 }
}
dps_results: {
 key: "TestSurvival-AllItems-SpringrainStoneofWisdom-101041"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-Static-Caster'sMedallion-93254"
 value: {
  dps: 143732.32983
  tps: 119514.50349
 }
}
dps_results: {
 key: "TestSurvival-AllItems-SteadfastFootman'sMedallion-92782"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-SteadfastTalismanoftheShado-PanAssault-94507"
 value: {
  dps: 144591.91098
  tps: 120537.88452
 }
}
dps_results: {
 key: "TestSurvival-AllItems-StreamtalkerIdolofDestruction-101222"
 value: {
  dps: 144505.83983
  tps: 120214.19129
 }
}
dps_results: {
 key: "TestSurvival-AllItems-StreamtalkerIdolofRage-101217"
 value: {
  dps: 152011.24493
  tps: 125835.22678
 }
}
dps_results: {
 key: "TestSurvival-AllItems-StreamtalkerStoneofDestruction-101225"
 value: {
  dps: 144456.21666
  tps: 120352.28722
 }
}
dps_results: {
 key: "TestSurvival-AllItems-StreamtalkerStoneofRage-101220"
 value: {
  dps: 151306.0779
  tps: 125863.02152
 }
}
dps_results: {
 key: "TestSurvival-AllItems-StreamtalkerStoneofWisdom-101250"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-StuffofNightmares-87160"
 value: {
  dps: 144379.7428
  tps: 120328.81966
 }
}
dps_results: {
 key: "TestSurvival-AllItems-SunsoulDefenderIdol-101160"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-SunsoulDefenderStone-101163"
 value: {
  dps: 144487.36387
  tps: 120370.26506
 }
}
dps_results: {
 key: "TestSurvival-AllItems-SunsoulIdolofBattle-101152"
 value: {
  dps: 144517.55837
  tps: 120091.71582
 }
}
dps_results: {
 key: "TestSurvival-AllItems-SunsoulStoneofBattle-101151"
 value: {
  dps: 144463.96178
  tps: 120344.07743
 }
}
dps_results: {
 key: "TestSurvival-AllItems-SunsoulStoneofWisdom-101138"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-SwordguardEmbroidery(Rank3)-4894"
 value: {
  dps: 156660.68376
  tps: 129168.66828
 }
}
dps_results: {
 key: "TestSurvival-AllItems-SymboloftheCatacombs-83735"
 value: {
  dps: 145102.61315
  tps: 120735.20158
 }
}
dps_results: {
 key: "TestSurvival-AllItems-SynapseSprings(MarkII)-4898"
 value: {
  dps: 156446.88745
  tps: 129127.99953
 }
}
dps_results: {
 key: "TestSurvival-AllItems-TalismanofBloodlust-96864"
 value: {
  dps: 157113.24187
  tps: 130044.20692
 }
}
dps_results: {
 key: "TestSurvival-AllItems-TheGloamingBlade-88149"
 value: {
  dps: 154139.9006
  tps: 127443.69722
 }
}
dps_results: {
 key: "TestSurvival-AllItems-Thousand-YearPickledEgg-87573"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-TrailseekerIdolofRage-101054"
 value: {
  dps: 152388.46335
  tps: 125974.08796
 }
}
dps_results: {
 key: "TestSurvival-AllItems-TrailseekerStoneofRage-101057"
 value: {
  dps: 151340.48759
  tps: 125884.76752
 }
}
dps_results: {
 key: "TestSurvival-AllItems-TyrannicalGladiator'sBadgeofConquest-100043"
 value: {
  dps: 150197.21873
  tps: 124462.41812
 }
}
dps_results: {
 key: "TestSurvival-AllItems-TyrannicalGladiator'sBadgeofConquest-91099"
 value: {
  dps: 150197.21873
  tps: 124462.41812
 }
}
dps_results: {
 key: "TestSurvival-AllItems-TyrannicalGladiator'sBadgeofConquest-94373"
 value: {
  dps: 150197.21873
  tps: 124462.41812
 }
}
dps_results: {
 key: "TestSurvival-AllItems-TyrannicalGladiator'sBadgeofConquest-99772"
 value: {
  dps: 150197.21873
  tps: 124462.41812
 }
}
dps_results: {
 key: "TestSurvival-AllItems-TyrannicalGladiator'sBadgeofDominance-100016"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-TyrannicalGladiator'sBadgeofDominance-91400"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-TyrannicalGladiator'sBadgeofDominance-94346"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-TyrannicalGladiator'sBadgeofDominance-99937"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-TyrannicalGladiator'sBadgeofVictory-100019"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-TyrannicalGladiator'sBadgeofVictory-91410"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-TyrannicalGladiator'sBadgeofVictory-94349"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-TyrannicalGladiator'sBadgeofVictory-99943"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-TyrannicalGladiator'sEmblemofCruelty-100066"
 value: {
  dps: 144756.90741
  tps: 120288.75734
 }
}
dps_results: {
 key: "TestSurvival-AllItems-TyrannicalGladiator'sEmblemofCruelty-91209"
 value: {
  dps: 144756.90741
  tps: 120288.75734
 }
}
dps_results: {
 key: "TestSurvival-AllItems-TyrannicalGladiator'sEmblemofCruelty-94396"
 value: {
  dps: 144756.90741
  tps: 120288.75734
 }
}
dps_results: {
 key: "TestSurvival-AllItems-TyrannicalGladiator'sEmblemofCruelty-99838"
 value: {
  dps: 144756.90741
  tps: 120288.75734
 }
}
dps_results: {
 key: "TestSurvival-AllItems-TyrannicalGladiator'sEmblemofMeditation-91211"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-TyrannicalGladiator'sEmblemofMeditation-94329"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-TyrannicalGladiator'sEmblemofMeditation-99840"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-TyrannicalGladiator'sEmblemofMeditation-99990"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-TyrannicalGladiator'sEmblemofTenacity-100092"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-TyrannicalGladiator'sEmblemofTenacity-91210"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-TyrannicalGladiator'sEmblemofTenacity-94422"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-TyrannicalGladiator'sEmblemofTenacity-99839"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-TyrannicalGladiator'sInsigniaofConquest-100026"
 value: {
  dps: 154024.81611
  tps: 126940.29987
 }
}
dps_results: {
 key: "TestSurvival-AllItems-TyrannicalGladiator'sInsigniaofDominance-100152"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-TyrannicalGladiator'sInsigniaofVictory-100085"
 value: {
  dps: 142727.39447
  tps: 118700.62891
 }
}
dps_results: {
 key: "TestSurvival-AllItems-TyrannicalPrimalDiamond"
 value: {
  dps: 150621.91849
  tps: 124214.91669
 }
}
dps_results: {
 key: "TestSurvival-AllItems-UnerringVisionofLeiShen-96930"
 value: {
  dps: 146339.60809
  tps: 121856.32774
 }
}
dps_results: {
 key: "TestSurvival-AllItems-VaporshieldMedallion-93262"
 value: {
  dps: 144203.73222
  tps: 120106.02613
 }
}
dps_results: {
 key: "TestSurvival-AllItems-VialofDragon'sBlood-87063"
 value: {
  dps: 144275.56441
  tps: 120226.16505
 }
}
dps_results: {
 key: "TestSurvival-AllItems-VialofIchorousBlood-100963"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-VialofIchorousBlood-81264"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-ViciousTalismanoftheShado-PanAssault-94511"
 value: {
  dps: 158545.57999
  tps: 129933.60405
 }
}
dps_results: {
 key: "TestSurvival-AllItems-VisionofthePredator-81192"
 value: {
  dps: 144853.34446
  tps: 120323.62007
 }
}
dps_results: {
 key: "TestSurvival-AllItems-VolatileTalismanoftheShado-PanAssault-94510"
 value: {
  dps: 143435.52984
  tps: 119111.98124
 }
}
dps_results: {
 key: "TestSurvival-AllItems-WindsweptPages-81125"
 value: {
  dps: 150099.35405
  tps: 124311.05472
 }
}
dps_results: {
 key: "TestSurvival-AllItems-WoundripperMedallion-93253"
 value: {
  dps: 152750.6376
  tps: 126674.35622
 }
}
dps_results: {
 key: "TestSurvival-AllItems-Wushoolay'sFinalChoice-96785"
 value: {
  dps: 142728.13417
  tps: 118701.36861
 }
}
dps_results: {
 key: "TestSurvival-AllItems-Xing-Ho,BreathofYu'lon-102246"
 value: {
  dps: 150301.74592
  tps: 124296.77331
 }
}
dps_results: {
 key: "TestSurvival-AllItems-YaungolFireCarrier-86518"
 value: {
  dps: 154079.24821
  tps: 127383.04483
 }
}
dps_results: {
 key: "TestSurvival-AllItems-YaungolSlayerBattlegear"
 value: {
  dps: 136848.52719
  tps: 112719.47341
 }
}
dps_results: {
 key: "TestSurvival-AllItems-Yu'lon'sBite-103987"
 value: {
  dps: 147120.31744
  tps: 121949.06109
 }
}
dps_results: {
 key: "TestSurvival-AllItems-ZenAlchemistStone-75274"
 value: {
  dps: 153413.84734
  tps: 126695.74297
 }
}
dps_results: {
 key: "TestSurvival-Average-Default"
 value: {
  dps: 157165.17596
  tps: 129663.50776
 }
}
dps_results: {
 key: "TestSurvival-Settings-Dwarf-p1-Basic-sv-FullBuffs-5.1yards-LongMultiTarget"
 value: {
  dps: 205462.25708
  tps: 179095.80758
 }
}
dps_results: {
 key: "TestSurvival-Settings-Dwarf-p1-Basic-sv-FullBuffs-5.1yards-LongSingleTarget"
 value: {
  dps: 153944.53091
  tps: 127727.71519
 }
}
dps_results: {
 key: "TestSurvival-Settings-Dwarf-p1-Basic-sv-FullBuffs-5.1yards-ShortSingleTarget"
 value: {
  dps: 204360.15617
  tps: 145837.82219
 }
}
dps_results: {
 key: "TestSurvival-Settings-Dwarf-p1-Basic-sv-NoBuffs-5.1yards-LongMultiTarget"
 value: {
  dps: 153281.90102
  tps: 136962.62761
 }
}
dps_results: {
 key: "TestSurvival-Settings-Dwarf-p1-Basic-sv-NoBuffs-5.1yards-LongSingleTarget"
 value: {
  dps: 114160.14913
  tps: 97958.67812
 }
}
dps_results: {
 key: "TestSurvival-Settings-Dwarf-p1-Basic-sv-NoBuffs-5.1yards-ShortSingleTarget"
 value: {
  dps: 127407.44984
  tps: 100097.76337
 }
}
dps_results: {
 key: "TestSurvival-Settings-Orc-p1-Basic-sv-FullBuffs-5.1yards-LongMultiTarget"
 value: {
  dps: 209252.79486
  tps: 181641.76293
 }
}
dps_results: {
 key: "TestSurvival-Settings-Orc-p1-Basic-sv-FullBuffs-5.1yards-LongSingleTarget"
 value: {
  dps: 156897.20924
  tps: 129459.3701
 }
}
dps_results: {
 key: "TestSurvival-Settings-Orc-p1-Basic-sv-FullBuffs-5.1yards-ShortSingleTarget"
 value: {
  dps: 211421.92992
  tps: 149151.158
 }
}
dps_results: {
 key: "TestSurvival-Settings-Orc-p1-Basic-sv-NoBuffs-5.1yards-LongMultiTarget"
 value: {
  dps: 156246.6487
  tps: 139165.44067
 }
}
dps_results: {
 key: "TestSurvival-Settings-Orc-p1-Basic-sv-NoBuffs-5.1yards-LongSingleTarget"
 value: {
  dps: 116411.09535
  tps: 99449.57055
 }
}
dps_results: {
 key: "TestSurvival-Settings-Orc-p1-Basic-sv-NoBuffs-5.1yards-ShortSingleTarget"
 value: {
  dps: 132018.18171
  tps: 102750.35687
 }
}
dps_results: {
 key: "TestSurvival-SwitchInFrontOfTarget-Default"
 value: {
  dps: 154738.3664
  tps: 129321.01363
 }
}
