character_stats_results: {
 key: "TestShadow-CharacterStats-Default"
 value: {
  final_stats: 135.45
  final_stats: 147
  final_stats: 15262.5
  final_stats: 14583.87
  final_stats: 4104
  final_stats: 5102
  final_stats: 837
  final_stats: 5119
  final_stats: 0
  final_stats: 0
  final_stats: 0
  final_stats: 5827
  final_stats: 0
  final_stats: 0
  final_stats: 23868.3027
  final_stats: 0
  final_stats: 0
  final_stats: 14259.3
  final_stats: 0
  final_stats: 360078
  final_stats: 300000
  final_stats: 15000
  final_stats: 15.00588
  final_stats: 15.00588
  final_stats: 9.575
  final_stats: 13.39111
  final_stats: 0
 }
}
dps_results: {
 key: "TestShadow-AllItems-AgilePrimalDiamond"
 value: {
  dps: 88619.9173
  tps: 83397.99095
 }
}
dps_results: {
 key: "TestShadow-AllItems-AlacrityofXuen-103989"
 value: {
  dps: 86724.13322
  tps: 81580.15572
 }
}
dps_results: {
 key: "TestShadow-AllItems-ArcaneBadgeoftheShieldwall-93347"
 value: {
  dps: 92201.86638
  tps: 86597.96407
 }
}
dps_results: {
 key: "TestShadow-AllItems-ArrowflightMedallion-93258"
 value: {
  dps: 88171.11131
  tps: 82589.53539
 }
}
dps_results: {
 key: "TestShadow-AllItems-AssuranceofConsequence-105472"
 value: {
  dps: 86724.13322
  tps: 81580.15572
 }
}
dps_results: {
 key: "TestShadow-AllItems-AusterePrimalDiamond"
 value: {
  dps: 88092.91834
  tps: 82885.06424
 }
}
dps_results: {
 key: "TestShadow-AllItems-BadJuju-96781"
 value: {
  dps: 88437.43078
  tps: 83299.52469
 }
}
dps_results: {
 key: "TestShadow-AllItems-BadgeofKypariZar-84079"
 value: {
  dps: 87357.24405
  tps: 82088.89129
 }
}
dps_results: {
 key: "TestShadow-AllItems-BlossomofPureSnow-89081"
 value: {
  dps: 92735.96145
  tps: 86840.18541
 }
}
dps_results: {
 key: "TestShadow-AllItems-BottleofInfiniteStars-87057"
 value: {
  dps: 87741.28548
  tps: 82606.22432
 }
}
dps_results: {
 key: "TestShadow-AllItems-BraidofTenSongs-84072"
 value: {
  dps: 87357.24405
  tps: 82088.89129
 }
}
dps_results: {
 key: "TestShadow-AllItems-Brawler'sStatue-87571"
 value: {
  dps: 86646.36834
  tps: 81503.56895
 }
}
dps_results: {
 key: "TestShadow-AllItems-BreathoftheHydra-96827"
 value: {
  dps: 100445.7422
  tps: 93973.91234
 }
}
dps_results: {
 key: "TestShadow-AllItems-BroochofMunificentDeeds-87500"
 value: {
  dps: 86646.36834
  tps: 81503.56895
 }
}
dps_results: {
 key: "TestShadow-AllItems-BrutalTalismanoftheShado-PanAssault-94508"
 value: {
  dps: 87865.48889
  tps: 82684.01797
 }
}
dps_results: {
 key: "TestShadow-AllItems-BurningPrimalDiamond"
 value: {
  dps: 89545.22412
  tps: 84252.43227
 }
}
dps_results: {
 key: "TestShadow-AllItems-CapacitivePrimalDiamond"
 value: {
  dps: 88639.75582
  tps: 83306.78477
 }
}
dps_results: {
 key: "TestShadow-AllItems-CarbonicCarbuncle-81138"
 value: {
  dps: 87912.88435
  tps: 82556.1274
 }
}
dps_results: {
 key: "TestShadow-AllItems-Cha-Ye'sEssenceofBrilliance-96888"
 value: {
  dps: 98025.38306
  tps: 91502.54907
 }
}
dps_results: {
 key: "TestShadow-AllItems-CharmofTenSongs-84071"
 value: {
  dps: 89021.76438
  tps: 83682.11895
 }
}
dps_results: {
 key: "TestShadow-AllItems-CommunalIdolofDestruction-101168"
 value: {
  dps: 90222.69107
  tps: 84980.35158
 }
}
dps_results: {
 key: "TestShadow-AllItems-CommunalStoneofDestruction-101171"
 value: {
  dps: 92381.23067
  tps: 86853.08381
 }
}
dps_results: {
 key: "TestShadow-AllItems-CommunalStoneofWisdom-101183"
 value: {
  dps: 90904.5621
  tps: 85414.7039
 }
}
dps_results: {
 key: "TestShadow-AllItems-ContemplationofChi-Ji-103688"
 value: {
  dps: 91866.17279
  tps: 86273.8814
 }
}
dps_results: {
 key: "TestShadow-AllItems-ContemplationofChi-Ji-103988"
 value: {
  dps: 94014.58566
  tps: 88248.58533
 }
}
dps_results: {
 key: "TestShadow-AllItems-Coren'sColdChromiumCoaster-87574"
 value: {
  dps: 87644.91239
  tps: 82239.81658
 }
}
dps_results: {
 key: "TestShadow-AllItems-CoreofDecency-87497"
 value: {
  dps: 87865.48889
  tps: 82684.01797
 }
}
dps_results: {
 key: "TestShadow-AllItems-CourageousPrimalDiamond"
 value: {
  dps: 89545.13867
  tps: 84237.35199
 }
}
dps_results: {
 key: "TestShadow-AllItems-CraftedDreadfulGladiator'sBadgeofConquest-93419"
 value: {
  dps: 86553.7864
  tps: 81405.76054
 }
}
dps_results: {
 key: "TestShadow-AllItems-CraftedDreadfulGladiator'sBadgeofDominance-93600"
 value: {
  dps: 89864.75372
  tps: 84201.92428
 }
}
dps_results: {
 key: "TestShadow-AllItems-CraftedDreadfulGladiator'sBadgeofVictory-93606"
 value: {
  dps: 86553.7864
  tps: 81405.76054
 }
}
dps_results: {
 key: "TestShadow-AllItems-CraftedDreadfulGladiator'sEmblemofCruelty-93485"
 value: {
  dps: 87644.93021
  tps: 82290.69777
 }
}
dps_results: {
 key: "TestShadow-AllItems-CraftedDreadfulGladiator'sEmblemofMeditation-93487"
 value: {
  dps: 87865.48889
  tps: 82684.01797
 }
}
dps_results: {
 key: "TestShadow-AllItems-CraftedDreadfulGladiator'sEmblemofTenacity-93486"
 value: {
  dps: 86646.36834
  tps: 81503.56895
 }
}
dps_results: {
 key: "TestShadow-AllItems-CraftedDreadfulGladiator'sInsigniaofConquest-93424"
 value: {
  dps: 86724.13322
  tps: 81580.15572
 }
}
dps_results: {
 key: "TestShadow-AllItems-CraftedDreadfulGladiator'sInsigniaofDominance-93601"
 value: {
  dps: 91618.28391
  tps: 85812.46587
 }
}
dps_results: {
 key: "TestShadow-AllItems-CraftedDreadfulGladiator'sInsigniaofVictory-93611"
 value: {
  dps: 86724.13322
  tps: 81580.15572
 }
}
dps_results: {
 key: "TestShadow-AllItems-CraftedMalevolentGladiator'sBadgeofConquest-98755"
 value: {
  dps: 86553.7864
  tps: 81405.76054
 }
}
dps_results: {
 key: "TestShadow-AllItems-CraftedMalevolentGladiator'sBadgeofDominance-98910"
 value: {
  dps: 90531.13377
  tps: 84776.89681
 }
}
dps_results: {
 key: "TestShadow-AllItems-CraftedMalevolentGladiator'sBadgeofVictory-98912"
 value: {
  dps: 86553.7864
  tps: 81405.76054
 }
}
dps_results: {
 key: "TestShadow-AllItems-CraftedMalevolentGladiator'sEmblemofCruelty-98811"
 value: {
  dps: 87899.12646
  tps: 82491.97176
 }
}
dps_results: {
 key: "TestShadow-AllItems-CraftedMalevolentGladiator'sEmblemofMeditation-98813"
 value: {
  dps: 87865.48889
  tps: 82684.01797
 }
}
dps_results: {
 key: "TestShadow-AllItems-CraftedMalevolentGladiator'sEmblemofTenacity-98812"
 value: {
  dps: 86646.36834
  tps: 81503.56895
 }
}
dps_results: {
 key: "TestShadow-AllItems-CraftedMalevolentGladiator'sInsigniaofConquest-98760"
 value: {
  dps: 86724.13322
  tps: 81580.15572
 }
}
dps_results: {
 key: "TestShadow-AllItems-CraftedMalevolentGladiator'sInsigniaofDominance-98911"
 value: {
  dps: 92620.37399
  tps: 86727.56591
 }
}
dps_results: {
 key: "TestShadow-AllItems-CraftedMalevolentGladiator'sInsigniaofVictory-98917"
 value: {
  dps: 86724.13322
  tps: 81580.15572
 }
}
dps_results: {
 key: "TestShadow-AllItems-CurseofHubris-102307"
 value: {
  dps: 90537.7948
  tps: 84126.66059
 }
}
dps_results: {
 key: "TestShadow-AllItems-CurseofHubris-104649"
 value: {
  dps: 91284.44088
  tps: 84669.50828
 }
}
dps_results: {
 key: "TestShadow-AllItems-CurseofHubris-104898"
 value: {
  dps: 90338.17025
  tps: 84053.96582
 }
}
dps_results: {
 key: "TestShadow-AllItems-CurseofHubris-105147"
 value: {
  dps: 90328.49218
  tps: 84163.76126
 }
}
dps_results: {
 key: "TestShadow-AllItems-CurseofHubris-105396"
 value: {
  dps: 90723.10361
  tps: 84227.26223
 }
}
dps_results: {
 key: "TestShadow-AllItems-CurseofHubris-105645"
 value: {
  dps: 91743.13449
  tps: 85128.5144
 }
}
dps_results: {
 key: "TestShadow-AllItems-CutstitcherMedallion-93255"
 value: {
  dps: 91882.14656
  tps: 86366.96872
 }
}
dps_results: {
 key: "TestShadow-AllItems-Daelo'sFinalWords-87496"
 value: {
  dps: 87865.48889
  tps: 82684.01797
 }
}
dps_results: {
 key: "TestShadow-AllItems-DarkglowEmbroidery(Rank3)-4893"
 value: {
  dps: 87006.10335
  tps: 82090.80902
 }
}
dps_results: {
 key: "TestShadow-AllItems-DarkmistVortex-87172"
 value: {
  dps: 89722.40299
  tps: 84225.07578
 }
}
dps_results: {
 key: "TestShadow-AllItems-DeadeyeBadgeoftheShieldwall-93346"
 value: {
  dps: 87917.86375
  tps: 82820.8908
 }
}
dps_results: {
 key: "TestShadow-AllItems-DelicateVialoftheSanguinaire-96895"
 value: {
  dps: 86724.13322
  tps: 81580.15572
 }
}
dps_results: {
 key: "TestShadow-AllItems-DestructivePrimalDiamond"
 value: {
  dps: 88749.99455
  tps: 83377.49561
 }
}
dps_results: {
 key: "TestShadow-AllItems-DisciplineofXuen-103986"
 value: {
  dps: 89027.16607
  tps: 83755.07776
 }
}
dps_results: {
 key: "TestShadow-AllItems-Dominator'sArcaneBadge-93342"
 value: {
  dps: 92201.86638
  tps: 86597.96407
 }
}
dps_results: {
 key: "TestShadow-AllItems-Dominator'sDeadeyeBadge-93341"
 value: {
  dps: 87917.86375
  tps: 82820.8908
 }
}
dps_results: {
 key: "TestShadow-AllItems-Dominator'sDurableBadge-93345"
 value: {
  dps: 87917.86375
  tps: 82820.8908
 }
}
dps_results: {
 key: "TestShadow-AllItems-Dominator'sKnightlyBadge-93344"
 value: {
  dps: 87917.86375
  tps: 82820.8908
 }
}
dps_results: {
 key: "TestShadow-AllItems-Dominator'sMendingBadge-93343"
 value: {
  dps: 90332.96422
  tps: 84959.11284
 }
}
dps_results: {
 key: "TestShadow-AllItems-DreadfulGladiator'sBadgeofConquest-84344"
 value: {
  dps: 86553.7864
  tps: 81405.76054
 }
}
dps_results: {
 key: "TestShadow-AllItems-DreadfulGladiator'sBadgeofDominance-84488"
 value: {
  dps: 89864.75372
  tps: 84201.92428
 }
}
dps_results: {
 key: "TestShadow-AllItems-DreadfulGladiator'sBadgeofVictory-84490"
 value: {
  dps: 86553.7864
  tps: 81405.76054
 }
}
dps_results: {
 key: "TestShadow-AllItems-DreadfulGladiator'sEmblemofCruelty-84399"
 value: {
  dps: 87644.93021
  tps: 82290.69777
 }
}
dps_results: {
 key: "TestShadow-AllItems-DreadfulGladiator'sEmblemofMeditation-84401"
 value: {
  dps: 87865.48889
  tps: 82684.01797
 }
}
dps_results: {
 key: "TestShadow-AllItems-DreadfulGladiator'sEmblemofTenacity-84400"
 value: {
  dps: 87462.32238
  tps: 82323.33296
 }
}
dps_results: {
 key: "TestShadow-AllItems-DreadfulGladiator'sInsigniaofConquest-84349"
 value: {
  dps: 86724.13322
  tps: 81580.15572
 }
}
dps_results: {
 key: "TestShadow-AllItems-DreadfulGladiator'sInsigniaofDominance-84489"
 value: {
  dps: 91475.22541
  tps: 85741.4883
 }
}
dps_results: {
 key: "TestShadow-AllItems-DreadfulGladiator'sInsigniaofVictory-84495"
 value: {
  dps: 86724.13322
  tps: 81580.15572
 }
}
dps_results: {
 key: "TestShadow-AllItems-DurableBadgeoftheShieldwall-93350"
 value: {
  dps: 87917.86375
  tps: 82820.8908
 }
}
dps_results: {
 key: "TestShadow-AllItems-EffulgentPrimalDiamond"
 value: {
  dps: 88092.91834
  tps: 82885.06424
 }
}
dps_results: {
 key: "TestShadow-AllItems-EmberPrimalDiamond"
 value: {
  dps: 89009.36891
  tps: 83731.68716
 }
}
dps_results: {
 key: "TestShadow-AllItems-EmblemofKypariZar-84077"
 value: {
  dps: 87385.47666
  tps: 82056.35289
 }
}
dps_results: {
 key: "TestShadow-AllItems-EmblemoftheCatacombs-83733"
 value: {
  dps: 88541.69523
  tps: 83308.63998
 }
}
dps_results: {
 key: "TestShadow-AllItems-EnchantWeapon-BloodyDancingSteel-5125"
 value: {
  dps: 86271.28355
  tps: 81286.33895
 }
}
dps_results: {
 key: "TestShadow-AllItems-EnchantWeapon-Colossus-4445"
 value: {
  dps: 86271.28355
  tps: 81286.33895
 }
}
dps_results: {
 key: "TestShadow-AllItems-EnchantWeapon-DancingSteel-4444"
 value: {
  dps: 86271.28355
  tps: 81286.33895
 }
}
dps_results: {
 key: "TestShadow-AllItems-EnchantWeapon-River'sSong-4446"
 value: {
  dps: 86271.28355
  tps: 81286.33895
 }
}
dps_results: {
 key: "TestShadow-AllItems-EnchantWeapon-SpiritofConquest-5124"
 value: {
  dps: 86271.28355
  tps: 81286.33895
 }
}
dps_results: {
 key: "TestShadow-AllItems-EnchantWeapon-Windsong-4441"
 value: {
  dps: 87125.78864
  tps: 82180.26025
 }
}
dps_results: {
 key: "TestShadow-AllItems-EnigmaticPrimalDiamond"
 value: {
  dps: 88749.99455
  tps: 83377.49561
 }
}
dps_results: {
 key: "TestShadow-AllItems-EssenceofTerror-87175"
 value: {
  dps: 95547.13252
  tps: 89636.92984
 }
}
dps_results: {
 key: "TestShadow-AllItems-EternalPrimalDiamond"
 value: {
  dps: 88092.91834
  tps: 82885.06424
 }
}
dps_results: {
 key: "TestShadow-AllItems-EvilEyeofGalakras-105491"
 value: {
  dps: 86724.13322
  tps: 81580.15572
 }
}
dps_results: {
 key: "TestShadow-AllItems-FabledFeatherofJi-Kun-96842"
 value: {
  dps: 87865.17373
  tps: 82683.70281
 }
}
dps_results: {
 key: "TestShadow-AllItems-FearwurmBadge-84074"
 value: {
  dps: 87385.47666
  tps: 82056.35289
 }
}
dps_results: {
 key: "TestShadow-AllItems-FearwurmRelic-84070"
 value: {
  dps: 88813.1731
  tps: 83501.82734
 }
}
dps_results: {
 key: "TestShadow-AllItems-FelsoulIdolofDestruction-101263"
 value: {
  dps: 90049.39992
  tps: 84578.10853
 }
}
dps_results: {
 key: "TestShadow-AllItems-FelsoulStoneofDestruction-101266"
 value: {
  dps: 92025.93258
  tps: 86619.93121
 }
}
dps_results: {
 key: "TestShadow-AllItems-Fen-Yu,FuryofXuen-102248"
 value: {
  dps: 86589.72621
  tps: 81771.58419
 }
}
dps_results: {
 key: "TestShadow-AllItems-FlashfrozenResinGlobule-100951"
 value: {
  dps: 92007.40163
  tps: 86271.46349
 }
}
dps_results: {
 key: "TestShadow-AllItems-FlashfrozenResinGlobule-81263"
 value: {
  dps: 92483.82239
  tps: 86661.88592
 }
}
dps_results: {
 key: "TestShadow-AllItems-FlashingSteelTalisman-81265"
 value: {
  dps: 87865.48889
  tps: 82684.01797
 }
}
dps_results: {
 key: "TestShadow-AllItems-FleetPrimalDiamond"
 value: {
  dps: 88539.90883
  tps: 83307.39633
 }
}
dps_results: {
 key: "TestShadow-AllItems-ForlornPrimalDiamond"
 value: {
  dps: 89009.36891
  tps: 83731.68716
 }
}
dps_results: {
 key: "TestShadow-AllItems-FortitudeoftheZandalari-94516"
 value: {
  dps: 88366.43321
  tps: 83328.5151
 }
}
dps_results: {
 key: "TestShadow-AllItems-FortitudeoftheZandalari-95677"
 value: {
  dps: 87841.09486
  tps: 82683.89049
 }
}
dps_results: {
 key: "TestShadow-AllItems-FortitudeoftheZandalari-96049"
 value: {
  dps: 88239.74379
  tps: 83198.93922
 }
}
dps_results: {
 key: "TestShadow-AllItems-FortitudeoftheZandalari-96421"
 value: {
  dps: 88185.18102
  tps: 83107.66578
 }
}
dps_results: {
 key: "TestShadow-AllItems-FortitudeoftheZandalari-96793"
 value: {
  dps: 88477.65954
  tps: 83318.27572
 }
}
dps_results: {
 key: "TestShadow-AllItems-GazeoftheTwins-96915"
 value: {
  dps: 88398.03242
  tps: 83034.26678
 }
}
dps_results: {
 key: "TestShadow-AllItems-Gerp'sPerfectArrow-87495"
 value: {
  dps: 87403.24218
  tps: 82050.89657
 }
}
dps_results: {
 key: "TestShadow-AllItems-Gong-Lu,StrengthofXuen-102249"
 value: {
  dps: 86589.72621
  tps: 81771.58419
 }
}
dps_results: {
 key: "TestShadow-AllItems-GrievousGladiator'sBadgeofConquest-100195"
 value: {
  dps: 86553.7864
  tps: 81405.76054
 }
}
dps_results: {
 key: "TestShadow-AllItems-GrievousGladiator'sBadgeofConquest-100603"
 value: {
  dps: 86553.7864
  tps: 81405.76054
 }
}
dps_results: {
 key: "TestShadow-AllItems-GrievousGladiator'sBadgeofConquest-102856"
 value: {
  dps: 86553.7864
  tps: 81405.76054
 }
}
dps_results: {
 key: "TestShadow-AllItems-GrievousGladiator'sBadgeofConquest-103145"
 value: {
  dps: 86553.7864
  tps: 81405.76054
 }
}
dps_results: {
 key: "TestShadow-AllItems-GrievousGladiator'sBadgeofDominance-100490"
 value: {
  dps: 92924.70013
  tps: 86843.23853
 }
}
dps_results: {
 key: "TestShadow-AllItems-GrievousGladiator'sBadgeofDominance-100576"
 value: {
  dps: 92924.70013
  tps: 86843.23853
 }
}
dps_results: {
 key: "TestShadow-AllItems-GrievousGladiator'sBadgeofDominance-102830"
 value: {
  dps: 92924.70013
  tps: 86843.23853
 }
}
dps_results: {
 key: "TestShadow-AllItems-GrievousGladiator'sBadgeofDominance-103308"
 value: {
  dps: 92924.70013
  tps: 86843.23853
 }
}
dps_results: {
 key: "TestShadow-AllItems-GrievousGladiator'sBadgeofVictory-100500"
 value: {
  dps: 86553.7864
  tps: 81405.76054
 }
}
dps_results: {
 key: "TestShadow-AllItems-GrievousGladiator'sBadgeofVictory-100579"
 value: {
  dps: 86553.7864
  tps: 81405.76054
 }
}
dps_results: {
 key: "TestShadow-AllItems-GrievousGladiator'sBadgeofVictory-102833"
 value: {
  dps: 86553.7864
  tps: 81405.76054
 }
}
dps_results: {
 key: "TestShadow-AllItems-GrievousGladiator'sBadgeofVictory-103314"
 value: {
  dps: 86553.7864
  tps: 81405.76054
 }
}
dps_results: {
 key: "TestShadow-AllItems-GrievousGladiator'sEmblemofCruelty-100305"
 value: {
  dps: 88870.63732
  tps: 83339.86531
 }
}
dps_results: {
 key: "TestShadow-AllItems-GrievousGladiator'sEmblemofCruelty-100626"
 value: {
  dps: 88870.63732
  tps: 83339.86531
 }
}
dps_results: {
 key: "TestShadow-AllItems-GrievousGladiator'sEmblemofCruelty-102877"
 value: {
  dps: 88870.63732
  tps: 83339.86531
 }
}
dps_results: {
 key: "TestShadow-AllItems-GrievousGladiator'sEmblemofCruelty-103210"
 value: {
  dps: 88870.63732
  tps: 83339.86531
 }
}
dps_results: {
 key: "TestShadow-AllItems-GrievousGladiator'sEmblemofMeditation-100307"
 value: {
  dps: 87865.48889
  tps: 82684.01797
 }
}
dps_results: {
 key: "TestShadow-AllItems-GrievousGladiator'sEmblemofMeditation-100559"
 value: {
  dps: 87865.48889
  tps: 82684.01797
 }
}
dps_results: {
 key: "TestShadow-AllItems-GrievousGladiator'sEmblemofMeditation-102813"
 value: {
  dps: 87865.48889
  tps: 82684.01797
 }
}
dps_results: {
 key: "TestShadow-AllItems-GrievousGladiator'sEmblemofMeditation-103212"
 value: {
  dps: 87865.48889
  tps: 82684.01797
 }
}
dps_results: {
 key: "TestShadow-AllItems-GrievousGladiator'sEmblemofTenacity-100306"
 value: {
  dps: 86646.36834
  tps: 81503.56895
 }
}
dps_results: {
 key: "TestShadow-AllItems-GrievousGladiator'sEmblemofTenacity-100652"
 value: {
  dps: 86646.36834
  tps: 81503.56895
 }
}
dps_results: {
 key: "TestShadow-AllItems-GrievousGladiator'sEmblemofTenacity-102903"
 value: {
  dps: 86646.36834
  tps: 81503.56895
 }
}
dps_results: {
 key: "TestShadow-AllItems-GrievousGladiator'sEmblemofTenacity-103211"
 value: {
  dps: 86646.36834
  tps: 81503.56895
 }
}
dps_results: {
 key: "TestShadow-AllItems-GrievousGladiator'sInsigniaofConquest-103150"
 value: {
  dps: 86724.13322
  tps: 81580.15572
 }
}
dps_results: {
 key: "TestShadow-AllItems-GrievousGladiator'sInsigniaofDominance-103309"
 value: {
  dps: 95663.36131
  tps: 89431.38356
 }
}
dps_results: {
 key: "TestShadow-AllItems-GrievousGladiator'sInsigniaofVictory-103319"
 value: {
  dps: 86724.13322
  tps: 81580.15572
 }
}
dps_results: {
 key: "TestShadow-AllItems-Hawkmaster'sTalon-89082"
 value: {
  dps: 89112.54514
  tps: 83759.39764
 }
}
dps_results: {
 key: "TestShadow-AllItems-Heart-LesionDefenderIdol-100999"
 value: {
  dps: 86724.13322
  tps: 81580.15572
 }
}
dps_results: {
 key: "TestShadow-AllItems-Heart-LesionDefenderStone-101002"
 value: {
  dps: 87900.83568
  tps: 82728.48165
 }
}
dps_results: {
 key: "TestShadow-AllItems-Heart-LesionIdolofBattle-100991"
 value: {
  dps: 87747.81149
  tps: 82293.72621
 }
}
dps_results: {
 key: "TestShadow-AllItems-Heart-LesionStoneofBattle-100990"
 value: {
  dps: 87506.03518
  tps: 82335.2153
 }
}
dps_results: {
 key: "TestShadow-AllItems-HeartofFire-81181"
 value: {
  dps: 87495.4193
  tps: 82349.80445
 }
}
dps_results: {
 key: "TestShadow-AllItems-HeartwarmerMedallion-93260"
 value: {
  dps: 91882.14656
  tps: 86366.96872
 }
}
dps_results: {
 key: "TestShadow-AllItems-HelmbreakerMedallion-93261"
 value: {
  dps: 88171.11131
  tps: 82589.53539
 }
}
dps_results: {
 key: "TestShadow-AllItems-Horridon'sLastGasp-96757"
 value: {
  dps: 93813.21191
  tps: 88110.11514
 }
}
dps_results: {
 key: "TestShadow-AllItems-ImpassivePrimalDiamond"
 value: {
  dps: 88749.99455
  tps: 83377.49561
 }
}
dps_results: {
 key: "TestShadow-AllItems-IndomitablePrimalDiamond"
 value: {
  dps: 88092.91834
  tps: 82885.06424
 }
}
dps_results: {
 key: "TestShadow-AllItems-InscribedBagofHydra-Spawn-96828"
 value: {
  dps: 87865.48889
  tps: 82684.01797
 }
}
dps_results: {
 key: "TestShadow-AllItems-InsigniaofKypariZar-84078"
 value: {
  dps: 86724.13322
  tps: 81580.15572
 }
}
dps_results: {
 key: "TestShadow-AllItems-IronBellyWok-89083"
 value: {
  dps: 89112.54514
  tps: 83759.39764
 }
}
dps_results: {
 key: "TestShadow-AllItems-IronProtectorTalisman-85181"
 value: {
  dps: 86724.13322
  tps: 81580.15572
 }
}
dps_results: {
 key: "TestShadow-AllItems-JadeBanditFigurine-86043"
 value: {
  dps: 89112.54514
  tps: 83759.39764
 }
}
dps_results: {
 key: "TestShadow-AllItems-JadeBanditFigurine-86772"
 value: {
  dps: 88516.19194
  tps: 83172.4662
 }
}
dps_results: {
 key: "TestShadow-AllItems-JadeCharioteerFigurine-86042"
 value: {
  dps: 89112.54514
  tps: 83759.39764
 }
}
dps_results: {
 key: "TestShadow-AllItems-JadeCharioteerFigurine-86771"
 value: {
  dps: 88516.19194
  tps: 83172.4662
 }
}
dps_results: {
 key: "TestShadow-AllItems-JadeCourtesanFigurine-86045"
 value: {
  dps: 91515.79367
  tps: 86061.97115
 }
}
dps_results: {
 key: "TestShadow-AllItems-JadeCourtesanFigurine-86774"
 value: {
  dps: 90925.91324
  tps: 85500.91365
 }
}
dps_results: {
 key: "TestShadow-AllItems-JadeMagistrateFigurine-86044"
 value: {
  dps: 92735.96145
  tps: 86840.18541
 }
}
dps_results: {
 key: "TestShadow-AllItems-JadeMagistrateFigurine-86773"
 value: {
  dps: 92031.07444
  tps: 86196.85436
 }
}
dps_results: {
 key: "TestShadow-AllItems-JadeWarlordFigurine-86046"
 value: {
  dps: 87861.7599
  tps: 82758.4712
 }
}
dps_results: {
 key: "TestShadow-AllItems-JadeWarlordFigurine-86775"
 value: {
  dps: 87557.64329
  tps: 82470.92324
 }
}
dps_results: {
 key: "TestShadow-AllItems-Ji-Kun'sRisingWinds-96843"
 value: {
  dps: 87865.48889
  tps: 82684.01797
 }
}
dps_results: {
 key: "TestShadow-AllItems-KnightlyBadgeoftheShieldwall-93349"
 value: {
  dps: 87917.86375
  tps: 82820.8908
 }
}
dps_results: {
 key: "TestShadow-AllItems-KnotofTenSongs-84073"
 value: {
  dps: 86724.13322
  tps: 81580.15572
 }
}
dps_results: {
 key: "TestShadow-AllItems-Kor'kronBookofHurting-92785"
 value: {
  dps: 86553.7864
  tps: 81405.76054
 }
}
dps_results: {
 key: "TestShadow-AllItems-Kri'tak,ImperialScepteroftheSwarm-86227"
 value: {
  dps: 89545.22412
  tps: 84252.43227
 }
}
dps_results: {
 key: "TestShadow-AllItems-Kri'tak,ImperialScepteroftheSwarm-86865"
 value: {
  dps: 89545.22412
  tps: 84252.43227
 }
}
dps_results: {
 key: "TestShadow-AllItems-Kri'tak,ImperialScepteroftheSwarm-86990"
 value: {
  dps: 89545.22412
  tps: 84252.43227
 }
}
dps_results: {
 key: "TestShadow-AllItems-Lao-Chin'sLiquidCourage-89079"
 value: {
  dps: 87861.7599
  tps: 82758.4712
 }
}
dps_results: {
 key: "TestShadow-AllItems-LeiShen'sFinalOrders-87072"
 value: {
  dps: 89351.39877
  tps: 84134.1356
 }
}
dps_results: {
 key: "TestShadow-AllItems-LessonsoftheDarkmaster-81268"
 value: {
  dps: 87865.48889
  tps: 82684.01797
 }
}
dps_results: {
 key: "TestShadow-AllItems-LightdrinkerIdolofRage-101200"
 value: {
  dps: 87658.69421
  tps: 82495.68478
 }
}
dps_results: {
 key: "TestShadow-AllItems-LightdrinkerStoneofRage-101203"
 value: {
  dps: 88034.3063
  tps: 82818.69547
 }
}
dps_results: {
 key: "TestShadow-AllItems-LightoftheCosmos-87065"
 value: {
  dps: 95188.43871
  tps: 89291.89817
 }
}
dps_results: {
 key: "TestShadow-AllItems-LordBlastington'sScopeofDoom-4699"
 value: {
  dps: 86271.28355
  tps: 81286.33895
 }
}
dps_results: {
 key: "TestShadow-AllItems-MalevolentGladiator'sBadgeofConquest-84934"
 value: {
  dps: 86553.7864
  tps: 81405.76054
 }
}
dps_results: {
 key: "TestShadow-AllItems-MalevolentGladiator'sBadgeofConquest-91452"
 value: {
  dps: 86553.7864
  tps: 81405.76054
 }
}
dps_results: {
 key: "TestShadow-AllItems-MalevolentGladiator'sBadgeofDominance-84940"
 value: {
  dps: 90817.26393
  tps: 85021.75388
 }
}
dps_results: {
 key: "TestShadow-AllItems-MalevolentGladiator'sBadgeofDominance-91753"
 value: {
  dps: 90531.13377
  tps: 84776.89681
 }
}
dps_results: {
 key: "TestShadow-AllItems-MalevolentGladiator'sBadgeofVictory-84942"
 value: {
  dps: 86553.7864
  tps: 81405.76054
 }
}
dps_results: {
 key: "TestShadow-AllItems-MalevolentGladiator'sBadgeofVictory-91763"
 value: {
  dps: 86553.7864
  tps: 81405.76054
 }
}
dps_results: {
 key: "TestShadow-AllItems-MalevolentGladiator'sEmblemofCruelty-84936"
 value: {
  dps: 87822.066
  tps: 82390.67753
 }
}
dps_results: {
 key: "TestShadow-AllItems-MalevolentGladiator'sEmblemofCruelty-91562"
 value: {
  dps: 87899.12646
  tps: 82491.97176
 }
}
dps_results: {
 key: "TestShadow-AllItems-MalevolentGladiator'sEmblemofMeditation-84939"
 value: {
  dps: 87865.48889
  tps: 82684.01797
 }
}
dps_results: {
 key: "TestShadow-AllItems-MalevolentGladiator'sEmblemofMeditation-91564"
 value: {
  dps: 87865.48889
  tps: 82684.01797
 }
}
dps_results: {
 key: "TestShadow-AllItems-MalevolentGladiator'sEmblemofTenacity-84938"
 value: {
  dps: 86646.36834
  tps: 81503.56895
 }
}
dps_results: {
 key: "TestShadow-AllItems-MalevolentGladiator'sEmblemofTenacity-91563"
 value: {
  dps: 86646.36834
  tps: 81503.56895
 }
}
dps_results: {
 key: "TestShadow-AllItems-MalevolentGladiator'sInsigniaofConquest-91457"
 value: {
  dps: 86724.13322
  tps: 81580.15572
 }
}
dps_results: {
 key: "TestShadow-AllItems-MalevolentGladiator'sInsigniaofDominance-91754"
 value: {
  dps: 92452.01254
  tps: 86566.68195
 }
}
dps_results: {
 key: "TestShadow-AllItems-MalevolentGladiator'sInsigniaofVictory-91768"
 value: {
  dps: 86724.13322
  tps: 81580.15572
 }
}
dps_results: {
 key: "TestShadow-AllItems-MarkoftheCatacombs-83731"
 value: {
  dps: 88861.16814
  tps: 83564.51382
 }
}
dps_results: {
 key: "TestShadow-AllItems-MarkoftheHardenedGrunt-92783"
 value: {
  dps: 86553.7864
  tps: 81405.76054
 }
}
dps_results: {
 key: "TestShadow-AllItems-MedallionofMystifyingVapors-93257"
 value: {
  dps: 87946.63934
  tps: 82845.51658
 }
}
dps_results: {
 key: "TestShadow-AllItems-MedallionoftheCatacombs-83734"
 value: {
  dps: 87318.64219
  tps: 82083.91009
 }
}
dps_results: {
 key: "TestShadow-AllItems-MendingBadgeoftheShieldwall-93348"
 value: {
  dps: 90332.96422
  tps: 84959.11284
 }
}
dps_results: {
 key: "TestShadow-AllItems-MirrorScope-4700"
 value: {
  dps: 86271.28355
  tps: 81286.33895
 }
}
dps_results: {
 key: "TestShadow-AllItems-MistdancerDefenderIdol-101089"
 value: {
  dps: 86724.13322
  tps: 81580.15572
 }
}
dps_results: {
 key: "TestShadow-AllItems-MistdancerDefenderStone-101087"
 value: {
  dps: 87772.17426
  tps: 82593.44406
 }
}
dps_results: {
 key: "TestShadow-AllItems-MistdancerIdolofRage-101113"
 value: {
  dps: 87658.69421
  tps: 82495.68478
 }
}
dps_results: {
 key: "TestShadow-AllItems-MistdancerStoneofRage-101117"
 value: {
  dps: 87860.42602
  tps: 82706.1324
 }
}
dps_results: {
 key: "TestShadow-AllItems-MistdancerStoneofWisdom-101107"
 value: {
  dps: 90904.5621
  tps: 85414.7039
 }
}
dps_results: {
 key: "TestShadow-AllItems-MithrilWristwatch-87572"
 value: {
  dps: 88522.19124
  tps: 83032.71372
 }
}
dps_results: {
 key: "TestShadow-AllItems-MountainsageIdolofDestruction-101069"
 value: {
  dps: 89876.30165
  tps: 84665.32916
 }
}
dps_results: {
 key: "TestShadow-AllItems-MountainsageStoneofDestruction-101072"
 value: {
  dps: 92295.53395
  tps: 86793.10035
 }
}
dps_results: {
 key: "TestShadow-AllItems-OathswornDefenderIdol-101303"
 value: {
  dps: 86724.13322
  tps: 81580.15572
 }
}
dps_results: {
 key: "TestShadow-AllItems-OathswornDefenderStone-101306"
 value: {
  dps: 87454.26323
  tps: 82280.36693
 }
}
dps_results: {
 key: "TestShadow-AllItems-OathswornIdolofBattle-101295"
 value: {
  dps: 87747.81149
  tps: 82293.72621
 }
}
dps_results: {
 key: "TestShadow-AllItems-OathswornStoneofBattle-101294"
 value: {
  dps: 87786.63069
  tps: 82681.06932
 }
}
dps_results: {
 key: "TestShadow-AllItems-PhaseFingers-4697"
 value: {
  dps: 88440.26456
  tps: 83311.96791
 }
}
dps_results: {
 key: "TestShadow-AllItems-PouchofWhiteAsh-103639"
 value: {
  dps: 86553.7864
  tps: 81405.76054
 }
}
dps_results: {
 key: "TestShadow-AllItems-PowerfulPrimalDiamond"
 value: {
  dps: 88092.91834
  tps: 82885.06424
 }
}
dps_results: {
 key: "TestShadow-AllItems-PriceofProgress-81266"
 value: {
  dps: 90164.38877
  tps: 84728.27962
 }
}
dps_results: {
 key: "TestShadow-AllItems-PridefulGladiator'sBadgeofConquest-102659"
 value: {
  dps: 86553.7864
  tps: 81405.76054
 }
}
dps_results: {
 key: "TestShadow-AllItems-PridefulGladiator'sBadgeofConquest-103342"
 value: {
  dps: 86553.7864
  tps: 81405.76054
 }
}
dps_results: {
 key: "TestShadow-AllItems-PridefulGladiator'sBadgeofDominance-102633"
 value: {
  dps: 94517.031
  tps: 88171.41743
 }
}
dps_results: {
 key: "TestShadow-AllItems-PridefulGladiator'sBadgeofDominance-103505"
 value: {
  dps: 94517.031
  tps: 88171.41743
 }
}
dps_results: {
 key: "TestShadow-AllItems-PridefulGladiator'sBadgeofVictory-102636"
 value: {
  dps: 86553.7864
  tps: 81405.76054
 }
}
dps_results: {
 key: "TestShadow-AllItems-PridefulGladiator'sBadgeofVictory-103511"
 value: {
  dps: 86553.7864
  tps: 81405.76054
 }
}
dps_results: {
 key: "TestShadow-AllItems-PridefulGladiator'sEmblemofCruelty-102680"
 value: {
  dps: 89477.22058
  tps: 83806.94615
 }
}
dps_results: {
 key: "TestShadow-AllItems-PridefulGladiator'sEmblemofCruelty-103407"
 value: {
  dps: 89477.22058
  tps: 83806.94615
 }
}
dps_results: {
 key: "TestShadow-AllItems-PridefulGladiator'sEmblemofMeditation-102616"
 value: {
  dps: 87865.48889
  tps: 82684.01797
 }
}
dps_results: {
 key: "TestShadow-AllItems-PridefulGladiator'sEmblemofMeditation-103409"
 value: {
  dps: 87865.48889
  tps: 82684.01797
 }
}
dps_results: {
 key: "TestShadow-AllItems-PridefulGladiator'sEmblemofTenacity-102706"
 value: {
  dps: 86646.36834
  tps: 81503.56895
 }
}
dps_results: {
 key: "TestShadow-AllItems-PridefulGladiator'sEmblemofTenacity-103408"
 value: {
  dps: 86646.36834
  tps: 81503.56895
 }
}
dps_results: {
 key: "TestShadow-AllItems-PridefulGladiator'sInsigniaofConquest-103347"
 value: {
  dps: 86724.13322
  tps: 81580.15572
 }
}
dps_results: {
 key: "TestShadow-AllItems-PridefulGladiator'sInsigniaofDominance-103506"
 value: {
  dps: 98578.47765
  tps: 91890.30625
 }
}
dps_results: {
 key: "TestShadow-AllItems-PridefulGladiator'sInsigniaofVictory-103516"
 value: {
  dps: 86724.13322
  tps: 81580.15572
 }
}
dps_results: {
 key: "TestShadow-AllItems-Primordius'TalismanofRage-96873"
 value: {
  dps: 89394.00941
  tps: 83781.31978
 }
}
dps_results: {
 key: "TestShadow-AllItems-Qian-Le,CourageofNiuzao-102245"
 value: {
  dps: 85251.2287
  tps: 80409.93159
 }
}
dps_results: {
 key: "TestShadow-AllItems-Qian-Ying,FortitudeofNiuzao-102250"
 value: {
  dps: 83887.03123
  tps: 79253.37748
 }
}
dps_results: {
 key: "TestShadow-AllItems-Qin-xi'sPolarizingSeal-87075"
 value: {
  dps: 87865.48889
  tps: 82684.01797
 }
}
dps_results: {
 key: "TestShadow-AllItems-RegaliaofTernionGlory"
 value: {
  dps: 100341.64196
  tps: 93753.56726
 }
}
dps_results: {
 key: "TestShadow-AllItems-RegaliaoftheExorcist"
 value: {
  dps: 102306.75044
  tps: 94945.27266
 }
}
dps_results: {
 key: "TestShadow-AllItems-RegaliaoftheGuardianSerpent"
 value: {
  dps: 92471.10851
  tps: 85867.01208
 }
}
dps_results: {
 key: "TestShadow-AllItems-RelicofChi-Ji-79330"
 value: {
  dps: 91491.0737
  tps: 85967.10899
 }
}
dps_results: {
 key: "TestShadow-AllItems-RelicofKypariZar-84075"
 value: {
  dps: 90015.23743
  tps: 84497.93995
 }
}
dps_results: {
 key: "TestShadow-AllItems-RelicofNiuzao-79329"
 value: {
  dps: 86553.7864
  tps: 81405.76054
 }
}
dps_results: {
 key: "TestShadow-AllItems-RelicofXuen-79327"
 value: {
  dps: 86724.13322
  tps: 81580.15572
 }
}
dps_results: {
 key: "TestShadow-AllItems-RelicofXuen-79328"
 value: {
  dps: 86724.13322
  tps: 81580.15572
 }
}
dps_results: {
 key: "TestShadow-AllItems-Renataki'sSoulCharm-96741"
 value: {
  dps: 87865.48889
  tps: 82684.01797
 }
}
dps_results: {
 key: "TestShadow-AllItems-ResolveofNiuzao-103690"
 value: {
  dps: 87651.49648
  tps: 82563.80459
 }
}
dps_results: {
 key: "TestShadow-AllItems-ResolveofNiuzao-103990"
 value: {
  dps: 88185.18102
  tps: 83107.66578
 }
}
dps_results: {
 key: "TestShadow-AllItems-ReverberatingPrimalDiamond"
 value: {
  dps: 88619.9173
  tps: 83397.99095
 }
}
dps_results: {
 key: "TestShadow-AllItems-RevitalizingPrimalDiamond"
 value: {
  dps: 88619.9173
  tps: 83397.99095
 }
}
dps_results: {
 key: "TestShadow-AllItems-SI:7Operative'sManual-92784"
 value: {
  dps: 86553.7864
  tps: 81405.76054
 }
}
dps_results: {
 key: "TestShadow-AllItems-ScrollofReveredAncestors-89080"
 value: {
  dps: 91515.79367
  tps: 86061.97115
 }
}
dps_results: {
 key: "TestShadow-AllItems-SearingWords-81267"
 value: {
  dps: 87619.92624
  tps: 82219.39485
 }
}
dps_results: {
 key: "TestShadow-AllItems-Shock-ChargerMedallion-93259"
 value: {
  dps: 93802.05705
  tps: 87555.64966
 }
}
dps_results: {
 key: "TestShadow-AllItems-SigilofCompassion-83736"
 value: {
  dps: 87864.89012
  tps: 82730.40449
 }
}
dps_results: {
 key: "TestShadow-AllItems-SigilofDevotion-83740"
 value: {
  dps: 87360.62201
  tps: 82052.86483
 }
}
dps_results: {
 key: "TestShadow-AllItems-SigilofFidelity-83737"
 value: {
  dps: 90056.74725
  tps: 84575.43166
 }
}
dps_results: {
 key: "TestShadow-AllItems-SigilofGrace-83738"
 value: {
  dps: 87318.64219
  tps: 82083.91009
 }
}
dps_results: {
 key: "TestShadow-AllItems-SigilofKypariZar-84076"
 value: {
  dps: 89028.79943
  tps: 83698.74033
 }
}
dps_results: {
 key: "TestShadow-AllItems-SigilofPatience-83739"
 value: {
  dps: 86724.13322
  tps: 81580.15572
 }
}
dps_results: {
 key: "TestShadow-AllItems-SigiloftheCatacombs-83732"
 value: {
  dps: 88649.39572
  tps: 83270.26968
 }
}
dps_results: {
 key: "TestShadow-AllItems-SinisterPrimalDiamond"
 value: {
  dps: 88639.75582
  tps: 83306.78477
 }
}
dps_results: {
 key: "TestShadow-AllItems-SkullrenderMedallion-93256"
 value: {
  dps: 88171.11131
  tps: 82589.53539
 }
}
dps_results: {
 key: "TestShadow-AllItems-SpiritsoftheSun-87163"
 value: {
  dps: 92074.47868
  tps: 86499.43912
 }
}
dps_results: {
 key: "TestShadow-AllItems-SpringrainIdolofDestruction-101023"
 value: {
  dps: 90484.34112
  tps: 85051.57669
 }
}
dps_results: {
 key: "TestShadow-AllItems-SpringrainIdolofRage-101009"
 value: {
  dps: 87658.69421
  tps: 82495.68478
 }
}
dps_results: {
 key: "TestShadow-AllItems-SpringrainStoneofDestruction-101026"
 value: {
  dps: 91932.11573
  tps: 86470.89304
 }
}
dps_results: {
 key: "TestShadow-AllItems-SpringrainStoneofRage-101012"
 value: {
  dps: 87901.56687
  tps: 82692.77118
 }
}
dps_results: {
 key: "TestShadow-AllItems-SpringrainStoneofWisdom-101041"
 value: {
  dps: 90904.5621
  tps: 85414.7039
 }
}
dps_results: {
 key: "TestShadow-AllItems-Static-Caster'sMedallion-93254"
 value: {
  dps: 93802.05705
  tps: 87555.64966
 }
}
dps_results: {
 key: "TestShadow-AllItems-SteadfastFootman'sMedallion-92782"
 value: {
  dps: 86553.7864
  tps: 81405.76054
 }
}
dps_results: {
 key: "TestShadow-AllItems-SteadfastTalismanoftheShado-PanAssault-94507"
 value: {
  dps: 88366.43321
  tps: 83328.5151
 }
}
dps_results: {
 key: "TestShadow-AllItems-StreamtalkerIdolofDestruction-101222"
 value: {
  dps: 90093.59252
  tps: 84879.88564
 }
}
dps_results: {
 key: "TestShadow-AllItems-StreamtalkerIdolofRage-101217"
 value: {
  dps: 87658.69421
  tps: 82495.68478
 }
}
dps_results: {
 key: "TestShadow-AllItems-StreamtalkerStoneofDestruction-101225"
 value: {
  dps: 92726.02588
  tps: 87251.96124
 }
}
dps_results: {
 key: "TestShadow-AllItems-StreamtalkerStoneofRage-101220"
 value: {
  dps: 88013.86399
  tps: 82881.15665
 }
}
dps_results: {
 key: "TestShadow-AllItems-StreamtalkerStoneofWisdom-101250"
 value: {
  dps: 90904.5621
  tps: 85414.7039
 }
}
dps_results: {
 key: "TestShadow-AllItems-StuffofNightmares-87160"
 value: {
  dps: 88085.4501
  tps: 82969.98544
 }
}
dps_results: {
 key: "TestShadow-AllItems-SunsoulDefenderIdol-101160"
 value: {
  dps: 86724.13322
  tps: 81580.15572
 }
}
dps_results: {
 key: "TestShadow-AllItems-SunsoulDefenderStone-101163"
 value: {
  dps: 87851.79931
  tps: 82665.24937
 }
}
dps_results: {
 key: "TestShadow-AllItems-SunsoulIdolofBattle-101152"
 value: {
  dps: 87747.81149
  tps: 82293.72621
 }
}
dps_results: {
 key: "TestShadow-AllItems-SunsoulStoneofBattle-101151"
 value: {
  dps: 87974.62753
  tps: 82864.79288
 }
}
dps_results: {
 key: "TestShadow-AllItems-SunsoulStoneofWisdom-101138"
 value: {
  dps: 90904.5621
  tps: 85414.7039
 }
}
dps_results: {
 key: "TestShadow-AllItems-SwordguardEmbroidery(Rank3)-4894"
 value: {
  dps: 87006.10335
  tps: 82090.80902
 }
}
dps_results: {
 key: "TestShadow-AllItems-SymboloftheCatacombs-83735"
 value: {
  dps: 87318.64219
  tps: 82083.91009
 }
}
dps_results: {
 key: "TestShadow-AllItems-SynapseSprings(MarkII)-4898"
 value: {
  dps: 90357.37114
  tps: 84824.16125
 }
}
dps_results: {
 key: "TestShadow-AllItems-TalismanofBloodlust-96864"
 value: {
  dps: 89530.81307
  tps: 84222.34923
 }
}
dps_results: {
 key: "TestShadow-AllItems-TerrorintheMists-87167"
 value: {
  dps: 88834.43348
  tps: 83223.94004
 }
}
dps_results: {
 key: "TestShadow-AllItems-TheGloamingBlade-88149"
 value: {
  dps: 89545.22412
  tps: 84252.43227
 }
}
dps_results: {
 key: "TestShadow-AllItems-Thousand-YearPickledEgg-87573"
 value: {
  dps: 90415.27542
  tps: 84965.11561
 }
}
dps_results: {
 key: "TestShadow-AllItems-TrailseekerIdolofRage-101054"
 value: {
  dps: 87658.69421
  tps: 82495.68478
 }
}
dps_results: {
 key: "TestShadow-AllItems-TrailseekerStoneofRage-101057"
 value: {
  dps: 87470.99673
  tps: 82422.33274
 }
}
dps_results: {
 key: "TestShadow-AllItems-TyrannicalGladiator'sBadgeofConquest-100043"
 value: {
  dps: 86553.7864
  tps: 81405.76054
 }
}
dps_results: {
 key: "TestShadow-AllItems-TyrannicalGladiator'sBadgeofConquest-91099"
 value: {
  dps: 86553.7864
  tps: 81405.76054
 }
}
dps_results: {
 key: "TestShadow-AllItems-TyrannicalGladiator'sBadgeofConquest-94373"
 value: {
  dps: 86553.7864
  tps: 81405.76054
 }
}
dps_results: {
 key: "TestShadow-AllItems-TyrannicalGladiator'sBadgeofConquest-99772"
 value: {
  dps: 86553.7864
  tps: 81405.76054
 }
}
dps_results: {
 key: "TestShadow-AllItems-TyrannicalGladiator'sBadgeofDominance-100016"
 value: {
  dps: 91361.23238
  tps: 85494.00508
 }
}
dps_results: {
 key: "TestShadow-AllItems-TyrannicalGladiator'sBadgeofDominance-91400"
 value: {
  dps: 91361.23238
  tps: 85494.00508
 }
}
dps_results: {
 key: "TestShadow-AllItems-TyrannicalGladiator'sBadgeofDominance-94346"
 value: {
  dps: 91361.23238
  tps: 85494.00508
 }
}
dps_results: {
 key: "TestShadow-AllItems-TyrannicalGladiator'sBadgeofDominance-99937"
 value: {
  dps: 91361.23238
  tps: 85494.00508
 }
}
dps_results: {
 key: "TestShadow-AllItems-TyrannicalGladiator'sBadgeofVictory-100019"
 value: {
  dps: 86553.7864
  tps: 81405.76054
 }
}
dps_results: {
 key: "TestShadow-AllItems-TyrannicalGladiator'sBadgeofVictory-91410"
 value: {
  dps: 86553.7864
  tps: 81405.76054
 }
}
dps_results: {
 key: "TestShadow-AllItems-TyrannicalGladiator'sBadgeofVictory-94349"
 value: {
  dps: 86553.7864
  tps: 81405.76054
 }
}
dps_results: {
 key: "TestShadow-AllItems-TyrannicalGladiator'sBadgeofVictory-99943"
 value: {
  dps: 86553.7864
  tps: 81405.76054
 }
}
dps_results: {
 key: "TestShadow-AllItems-TyrannicalGladiator'sEmblemofCruelty-100066"
 value: {
  dps: 87879.03008
  tps: 82407.13648
 }
}
dps_results: {
 key: "TestShadow-AllItems-TyrannicalGladiator'sEmblemofCruelty-91209"
 value: {
  dps: 87879.03008
  tps: 82407.13648
 }
}
dps_results: {
 key: "TestShadow-AllItems-TyrannicalGladiator'sEmblemofCruelty-94396"
 value: {
  dps: 87879.03008
  tps: 82407.13648
 }
}
dps_results: {
 key: "TestShadow-AllItems-TyrannicalGladiator'sEmblemofCruelty-99838"
 value: {
  dps: 87879.03008
  tps: 82407.13648
 }
}
dps_results: {
 key: "TestShadow-AllItems-TyrannicalGladiator'sEmblemofMeditation-91211"
 value: {
  dps: 87865.48889
  tps: 82684.01797
 }
}
dps_results: {
 key: "TestShadow-AllItems-TyrannicalGladiator'sEmblemofMeditation-94329"
 value: {
  dps: 87865.48889
  tps: 82684.01797
 }
}
dps_results: {
 key: "TestShadow-AllItems-TyrannicalGladiator'sEmblemofMeditation-99840"
 value: {
  dps: 87865.48889
  tps: 82684.01797
 }
}
dps_results: {
 key: "TestShadow-AllItems-TyrannicalGladiator'sEmblemofMeditation-99990"
 value: {
  dps: 87865.48889
  tps: 82684.01797
 }
}
dps_results: {
 key: "TestShadow-AllItems-TyrannicalGladiator'sEmblemofTenacity-100092"
 value: {
  dps: 86646.36834
  tps: 81503.56895
 }
}
dps_results: {
 key: "TestShadow-AllItems-TyrannicalGladiator'sEmblemofTenacity-91210"
 value: {
  dps: 86646.36834
  tps: 81503.56895
 }
}
dps_results: {
 key: "TestShadow-AllItems-TyrannicalGladiator'sEmblemofTenacity-94422"
 value: {
  dps: 86646.36834
  tps: 81503.56895
 }
}
dps_results: {
 key: "TestShadow-AllItems-TyrannicalGladiator'sEmblemofTenacity-99839"
 value: {
  dps: 86646.36834
  tps: 81503.56895
 }
}
dps_results: {
 key: "TestShadow-AllItems-TyrannicalGladiator'sInsigniaofConquest-100026"
 value: {
  dps: 86724.13322
  tps: 81580.15572
 }
}
dps_results: {
 key: "TestShadow-AllItems-TyrannicalGladiator'sInsigniaofDominance-100152"
 value: {
  dps: 93770.9257
  tps: 87663.935
 }
}
dps_results: {
 key: "TestShadow-AllItems-TyrannicalGladiator'sInsigniaofVictory-100085"
 value: {
  dps: 86724.13322
  tps: 81580.15572
 }
}
dps_results: {
 key: "TestShadow-AllItems-TyrannicalPrimalDiamond"
 value: {
  dps: 88092.91834
  tps: 82885.06424
 }
}
dps_results: {
 key: "TestShadow-AllItems-UnerringVisionofLeiShen-96930"
 value: {
  dps: 99127.95658
  tps: 92616.21564
 }
}
dps_results: {
 key: "TestShadow-AllItems-VaporshieldMedallion-93262"
 value: {
  dps: 87946.63934
  tps: 82845.51658
 }
}
dps_results: {
 key: "TestShadow-AllItems-VialofDragon'sBlood-87063"
 value: {
  dps: 87741.28548
  tps: 82606.22432
 }
}
dps_results: {
 key: "TestShadow-AllItems-VialofIchorousBlood-100963"
 value: {
  dps: 89840.52583
  tps: 84388.24772
 }
}
dps_results: {
 key: "TestShadow-AllItems-VialofIchorousBlood-81264"
 value: {
  dps: 90222.52361
  tps: 84746.41558
 }
}
dps_results: {
 key: "TestShadow-AllItems-ViciousTalismanoftheShado-PanAssault-94511"
 value: {
  dps: 87865.48889
  tps: 82684.01797
 }
}
dps_results: {
 key: "TestShadow-AllItems-VisionofthePredator-81192"
 value: {
  dps: 92223.40365
  tps: 86466.87123
 }
}
dps_results: {
 key: "TestShadow-AllItems-VolatileTalismanoftheShado-PanAssault-94510"
 value: {
  dps: 95488.46247
  tps: 89508.7087
 }
}
dps_results: {
 key: "TestShadow-AllItems-WindsweptPages-81125"
 value: {
  dps: 88206.08281
  tps: 82945.92169
 }
}
dps_results: {
 key: "TestShadow-AllItems-WoundripperMedallion-93253"
 value: {
  dps: 88171.11131
  tps: 82589.53539
 }
}
dps_results: {
 key: "TestShadow-AllItems-Wushoolay'sFinalChoice-96785"
 value: {
  dps: 118834.71689
  tps: 111225.327
 }
}
dps_results: {
 key: "TestShadow-AllItems-Xing-Ho,BreathofYu'lon-102246"
 value: {
  dps: 111337.21346
  tps: 105955.22685
 }
}
dps_results: {
 key: "TestShadow-AllItems-YaungolFireCarrier-86518"
 value: {
  dps: 89545.22412
  tps: 84252.43227
 }
}
dps_results: {
 key: "TestShadow-AllItems-Yu'lon'sBite-103987"
 value: {
  dps: 98240.88942
  tps: 91678.30487
 }
}
dps_results: {
 key: "TestShadow-AllItems-ZenAlchemistStone-75274"
 value: {
  dps: 92664.96094
  tps: 86965.16183
 }
}
dps_results: {
 key: "TestShadow-Average-Default"
 value: {
  dps: 91219.13868
  tps: 85639.09088
 }
}
dps_results: {
 key: "TestShadow-Settings-Draenei-p1-Basic-default-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 155115.84663
  tps: 165506.39945
 }
}
dps_results: {
 key: "TestShadow-Settings-Draenei-p1-Basic-default-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 147031.37067
  tps: 137486.151
 }
}
dps_results: {
 key: "TestShadow-Settings-Draenei-p1-Basic-default-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 202844.91726
  tps: 175819.75798
 }
}
dps_results: {
 key: "TestShadow-Settings-Draenei-p1-Basic-default-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 101902.92928
  tps: 116798.06009
 }
}
dps_results: {
 key: "TestShadow-Settings-Draenei-p1-Basic-default-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 95817.23233
  tps: 91499.69214
 }
}
dps_results: {
 key: "TestShadow-Settings-Draenei-p1-Basic-default-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 108581.17972
  tps: 99321.62304
 }
}
dps_results: {
 key: "TestShadow-Settings-Draenei-pre_raid-Basic-default-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 92264.42828
  tps: 106194.76657
 }
}
dps_results: {
 key: "TestShadow-Settings-Draenei-pre_raid-Basic-default-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 87205.19443
  tps: 82706.88984
 }
}
dps_results: {
 key: "TestShadow-Settings-Draenei-pre_raid-Basic-default-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 121850.18974
  tps: 106514.10239
 }
}
dps_results: {
 key: "TestShadow-Settings-Draenei-pre_raid-Basic-default-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 59462.10099
  tps: 75159.80487
 }
}
dps_results: {
 key: "TestShadow-Settings-Draenei-pre_raid-Basic-default-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 55718.54926
  tps: 54395.25386
 }
}
dps_results: {
 key: "TestShadow-Settings-Draenei-pre_raid-Basic-default-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 62786.26926
  tps: 58302.80834
 }
}
dps_results: {
 key: "TestShadow-Settings-NightElf-p1-Basic-default-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 155115.84663
  tps: 165506.39945
 }
}
dps_results: {
 key: "TestShadow-Settings-NightElf-p1-Basic-default-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 147031.37067
  tps: 137486.15295
 }
}
dps_results: {
 key: "TestShadow-Settings-NightElf-p1-Basic-default-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 202844.91726
  tps: 175819.76775
 }
}
dps_results: {
 key: "TestShadow-Settings-NightElf-p1-Basic-default-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 101902.92928
  tps: 116798.12394
 }
}
dps_results: {
 key: "TestShadow-Settings-NightElf-p1-Basic-default-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 95817.23233
  tps: 91499.6952
 }
}
dps_results: {
 key: "TestShadow-Settings-NightElf-p1-Basic-default-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 108581.17972
  tps: 99321.63833
 }
}
dps_results: {
 key: "TestShadow-Settings-NightElf-pre_raid-Basic-default-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 92264.42828
  tps: 106194.83898
 }
}
dps_results: {
 key: "TestShadow-Settings-NightElf-pre_raid-Basic-default-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 87205.19443
  tps: 82706.89314
 }
}
dps_results: {
 key: "TestShadow-Settings-NightElf-pre_raid-Basic-default-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 121850.18974
  tps: 106514.11889
 }
}
dps_results: {
 key: "TestShadow-Settings-NightElf-pre_raid-Basic-default-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 59462.10099
  tps: 75159.86721
 }
}
dps_results: {
 key: "TestShadow-Settings-NightElf-pre_raid-Basic-default-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 55718.54926
  tps: 54395.25662
 }
}
dps_results: {
 key: "TestShadow-Settings-NightElf-pre_raid-Basic-default-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 62786.26926
  tps: 58302.82212
 }
}
dps_results: {
 key: "TestShadow-Settings-Troll-p1-Basic-default-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 159448.31831
  tps: 168776.13878
 }
}
dps_results: {
 key: "TestShadow-Settings-Troll-p1-Basic-default-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 151807.6916
  tps: 140064.45818
 }
}
dps_results: {
 key: "TestShadow-Settings-Troll-p1-Basic-default-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 216983.75592
  tps: 185127.13139
 }
}
dps_results: {
 key: "TestShadow-Settings-Troll-p1-Basic-default-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 104485.2294
  tps: 118465.46136
 }
}
dps_results: {
 key: "TestShadow-Settings-Troll-p1-Basic-default-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 98904.27975
  tps: 93774.73552
 }
}
dps_results: {
 key: "TestShadow-Settings-Troll-p1-Basic-default-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 113318.28839
  tps: 102327.98019
 }
}
dps_results: {
 key: "TestShadow-Settings-Troll-pre_raid-Basic-default-FullBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 95993.18357
  tps: 109088.82094
 }
}
dps_results: {
 key: "TestShadow-Settings-Troll-pre_raid-Basic-default-FullBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 91550.18763
  tps: 85820.1263
 }
}
dps_results: {
 key: "TestShadow-Settings-Troll-pre_raid-Basic-default-FullBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 129330.90519
  tps: 110427.79713
 }
}
dps_results: {
 key: "TestShadow-Settings-Troll-pre_raid-Basic-default-NoBuffs-0.0yards-LongMultiTarget"
 value: {
  dps: 60975.60434
  tps: 76845.1728
 }
}
dps_results: {
 key: "TestShadow-Settings-Troll-pre_raid-Basic-default-NoBuffs-0.0yards-LongSingleTarget"
 value: {
  dps: 57293.70894
  tps: 55451.35391
 }
}
dps_results: {
 key: "TestShadow-Settings-Troll-pre_raid-Basic-default-NoBuffs-0.0yards-ShortSingleTarget"
 value: {
  dps: 66905.79557
  tps: 61379.77171
 }
}
dps_results: {
 key: "TestShadow-SwitchInFrontOfTarget-Default"
 value: {
  dps: 91450.16044
  tps: 85820.1263
 }
}
