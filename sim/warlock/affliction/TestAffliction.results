character_stats_results: {
 key: "TestAffliction-CharacterStats-Default"
 value: {
  final_stats: 157.5
  final_stats: 161.7
  final_stats: 18509.37
  final_stats: 16174.7775
  final_stats: 280
  final_stats: 4401
  final_stats: 1842
  final_stats: 3364
  final_stats: 703
  final_stats: 0
  final_stats: 0
  final_stats: 7030
  final_stats: 162.25
  final_stats: 0
  final_stats: 23447.35525
  final_stats: 0
  final_stats: 1054
  final_stats: 13312
  final_stats: 0
  final_stats: 446087.598
  final_stats: 300000
  final_stats: 15000
  final_stats: 12.94412
  final_stats: 15.01176
  final_stats: 10.69
  final_stats: 16.15402
  final_stats: 0
 }
}
dps_results: {
 key: "TestAffliction-AllItems-AgilePrimalDiamond"
 value: {
  dps: 88412.35794
  tps: 61617.397
 }
}
dps_results: {
 key: "TestAffliction-AllItems-AlacrityofXuen-103989"
 value: {
  dps: 84427.31272
  tps: 58871.80542
 }
}
dps_results: {
 key: "TestAffliction-AllItems-ArcaneBadgeoftheShieldwall-93347"
 value: {
  dps: 90047.82366
  tps: 62649.06746
 }
}
dps_results: {
 key: "TestAffliction-AllItems-ArrowflightMedallion-93258"
 value: {
  dps: 86295.71569
  tps: 60155.67764
 }
}
dps_results: {
 key: "TestAffliction-AllItems-AssuranceofConsequence-105472"
 value: {
  dps: 84427.31272
  tps: 58871.80542
 }
}
dps_results: {
 key: "TestAffliction-AllItems-AusterePrimalDiamond"
 value: {
  dps: 87859.3247
  tps: 61169.6631
 }
}
dps_results: {
 key: "TestAffliction-AllItems-BadJuju-96781"
 value: {
  dps: 87350.25263
  tps: 61183.78709
 }
}
dps_results: {
 key: "TestAffliction-AllItems-BadgeofKypariZar-84079"
 value: {
  dps: 85536.76194
  tps: 59749.35554
 }
}
dps_results: {
 key: "TestAffliction-AllItems-Balespider'sBurningVestments"
 value: {
  dps: 68280.26562
  tps: 46725.82724
 }
}
dps_results: {
 key: "TestAffliction-AllItems-BlossomofPureSnow-89081"
 value: {
  dps: 90201.13816
  tps: 62822.10041
 }
}
dps_results: {
 key: "TestAffliction-AllItems-BottleofInfiniteStars-87057"
 value: {
  dps: 86459.35656
  tps: 60479.10774
 }
}
dps_results: {
 key: "TestAffliction-AllItems-BraidofTenSongs-84072"
 value: {
  dps: 85536.76194
  tps: 59749.35554
 }
}
dps_results: {
 key: "TestAffliction-AllItems-Brawler'sStatue-87571"
 value: {
  dps: 84427.31272
  tps: 58871.80542
 }
}
dps_results: {
 key: "TestAffliction-AllItems-BreathoftheHydra-96827"
 value: {
  dps: 102102.15983
  tps: 71000.56047
 }
}
dps_results: {
 key: "TestAffliction-AllItems-BroochofMunificentDeeds-87500"
 value: {
  dps: 84522.89777
  tps: 59009.47335
 }
}
dps_results: {
 key: "TestAffliction-AllItems-BrutalTalismanoftheShado-PanAssault-94508"
 value: {
  dps: 84427.31272
  tps: 58871.80542
 }
}
dps_results: {
 key: "TestAffliction-AllItems-BurningPrimalDiamond"
 value: {
  dps: 89234.52968
  tps: 62192.45631
 }
}
dps_results: {
 key: "TestAffliction-AllItems-CapacitivePrimalDiamond"
 value: {
  dps: 88240.07425
  tps: 61392.2376
 }
}
dps_results: {
 key: "TestAffliction-AllItems-CarbonicCarbuncle-81138"
 value: {
  dps: 86085.89607
  tps: 59937.28964
 }
}
dps_results: {
 key: "TestAffliction-AllItems-Cha-Ye'sEssenceofBrilliance-96888"
 value: {
  dps: 98108.31822
  tps: 68233.40855
 }
}
dps_results: {
 key: "TestAffliction-AllItems-CharmofTenSongs-84071"
 value: {
  dps: 87665.03589
  tps: 61136.94661
 }
}
dps_results: {
 key: "TestAffliction-AllItems-CommunalIdolofDestruction-101168"
 value: {
  dps: 90180.74091
  tps: 62975.82848
 }
}
dps_results: {
 key: "TestAffliction-AllItems-CommunalStoneofDestruction-101171"
 value: {
  dps: 92387.97235
  tps: 64876.79554
 }
}
dps_results: {
 key: "TestAffliction-AllItems-CommunalStoneofWisdom-101183"
 value: {
  dps: 88308.57891
  tps: 61601.01018
 }
}
dps_results: {
 key: "TestAffliction-AllItems-ContemplationofChi-Ji-103688"
 value: {
  dps: 88703.07449
  tps: 61870.55401
 }
}
dps_results: {
 key: "TestAffliction-AllItems-ContemplationofChi-Ji-103988"
 value: {
  dps: 90594.02155
  tps: 63316.42773
 }
}
dps_results: {
 key: "TestAffliction-AllItems-Coren'sColdChromiumCoaster-87574"
 value: {
  dps: 85280.21659
  tps: 59381.81758
 }
}
dps_results: {
 key: "TestAffliction-AllItems-CoreofDecency-87497"
 value: {
  dps: 84414.52236
  tps: 58906.6604
 }
}
dps_results: {
 key: "TestAffliction-AllItems-CourageousPrimalDiamond"
 value: {
  dps: 89122.15145
  tps: 62047.94292
 }
}
dps_results: {
 key: "TestAffliction-AllItems-CraftedDreadfulGladiator'sBadgeofConquest-93419"
 value: {
  dps: 84360.75072
  tps: 58801.37407
 }
}
dps_results: {
 key: "TestAffliction-AllItems-CraftedDreadfulGladiator'sBadgeofDominance-93600"
 value: {
  dps: 87757.55944
  tps: 61408.30356
 }
}
dps_results: {
 key: "TestAffliction-AllItems-CraftedDreadfulGladiator'sBadgeofVictory-93606"
 value: {
  dps: 84360.75072
  tps: 58801.37407
 }
}
dps_results: {
 key: "TestAffliction-AllItems-CraftedDreadfulGladiator'sEmblemofCruelty-93485"
 value: {
  dps: 85163.66812
  tps: 59402.58119
 }
}
dps_results: {
 key: "TestAffliction-AllItems-CraftedDreadfulGladiator'sEmblemofMeditation-93487"
 value: {
  dps: 84491.71428
  tps: 58998.14688
 }
}
dps_results: {
 key: "TestAffliction-AllItems-CraftedDreadfulGladiator'sEmblemofTenacity-93486"
 value: {
  dps: 84639.0758
  tps: 59111.17427
 }
}
dps_results: {
 key: "TestAffliction-AllItems-CraftedDreadfulGladiator'sInsigniaofConquest-93424"
 value: {
  dps: 84427.31272
  tps: 58871.80542
 }
}
dps_results: {
 key: "TestAffliction-AllItems-CraftedDreadfulGladiator'sInsigniaofDominance-93601"
 value: {
  dps: 89406.89859
  tps: 62306.08516
 }
}
dps_results: {
 key: "TestAffliction-AllItems-CraftedDreadfulGladiator'sInsigniaofVictory-93611"
 value: {
  dps: 84427.31272
  tps: 58871.80542
 }
}
dps_results: {
 key: "TestAffliction-AllItems-CraftedMalevolentGladiator'sBadgeofConquest-98755"
 value: {
  dps: 84360.75072
  tps: 58801.37407
 }
}
dps_results: {
 key: "TestAffliction-AllItems-CraftedMalevolentGladiator'sBadgeofDominance-98910"
 value: {
  dps: 88471.49335
  tps: 61898.3248
 }
}
dps_results: {
 key: "TestAffliction-AllItems-CraftedMalevolentGladiator'sBadgeofVictory-98912"
 value: {
  dps: 84360.75072
  tps: 58801.37407
 }
}
dps_results: {
 key: "TestAffliction-AllItems-CraftedMalevolentGladiator'sEmblemofCruelty-98811"
 value: {
  dps: 85345.9828
  tps: 59503.56433
 }
}
dps_results: {
 key: "TestAffliction-AllItems-CraftedMalevolentGladiator'sEmblemofMeditation-98813"
 value: {
  dps: 84488.8224
  tps: 58991.68032
 }
}
dps_results: {
 key: "TestAffliction-AllItems-CraftedMalevolentGladiator'sEmblemofTenacity-98812"
 value: {
  dps: 84768.07183
  tps: 59238.60925
 }
}
dps_results: {
 key: "TestAffliction-AllItems-CraftedMalevolentGladiator'sInsigniaofConquest-98760"
 value: {
  dps: 84427.31272
  tps: 58871.80542
 }
}
dps_results: {
 key: "TestAffliction-AllItems-CraftedMalevolentGladiator'sInsigniaofDominance-98911"
 value: {
  dps: 90328.9376
  tps: 62959.53515
 }
}
dps_results: {
 key: "TestAffliction-AllItems-CraftedMalevolentGladiator'sInsigniaofVictory-98917"
 value: {
  dps: 84427.31272
  tps: 58871.80542
 }
}
dps_results: {
 key: "TestAffliction-AllItems-CurseofHubris-102307"
 value: {
  dps: 88300.90661
  tps: 61402.09963
 }
}
dps_results: {
 key: "TestAffliction-AllItems-CurseofHubris-104649"
 value: {
  dps: 88673.62292
  tps: 61654.53908
 }
}
dps_results: {
 key: "TestAffliction-AllItems-CurseofHubris-104898"
 value: {
  dps: 87883.3357
  tps: 61174.08414
 }
}
dps_results: {
 key: "TestAffliction-AllItems-CurseofHubris-105147"
 value: {
  dps: 87216.97225
  tps: 60652.38505
 }
}
dps_results: {
 key: "TestAffliction-AllItems-CurseofHubris-105396"
 value: {
  dps: 88663.41216
  tps: 61685.91
 }
}
dps_results: {
 key: "TestAffliction-AllItems-CurseofHubris-105645"
 value: {
  dps: 88923.82264
  tps: 61823.95809
 }
}
dps_results: {
 key: "TestAffliction-AllItems-CutstitcherMedallion-93255"
 value: {
  dps: 88757.64313
  tps: 61978.32531
 }
}
dps_results: {
 key: "TestAffliction-AllItems-Daelo'sFinalWords-87496"
 value: {
  dps: 84429.36635
  tps: 58863.90467
 }
}
dps_results: {
 key: "TestAffliction-AllItems-DarkglowEmbroidery(Rank3)-4893"
 value: {
  dps: 87200.7358
  tps: 60844.92071
 }
}
dps_results: {
 key: "TestAffliction-AllItems-DarkmistVortex-87172"
 value: {
  dps: 90192.74179
  tps: 62884.854
 }
}
dps_results: {
 key: "TestAffliction-AllItems-DeadeyeBadgeoftheShieldwall-93346"
 value: {
  dps: 86528.92084
  tps: 60670.63786
 }
}
dps_results: {
 key: "TestAffliction-AllItems-DelicateVialoftheSanguinaire-96895"
 value: {
  dps: 84427.31272
  tps: 58871.80542
 }
}
dps_results: {
 key: "TestAffliction-AllItems-DestructivePrimalDiamond"
 value: {
  dps: 88339.9962
  tps: 61447.68232
 }
}
dps_results: {
 key: "TestAffliction-AllItems-DisciplineofXuen-103986"
 value: {
  dps: 91526.99546
  tps: 64290.75565
 }
}
dps_results: {
 key: "TestAffliction-AllItems-Dominator'sArcaneBadge-93342"
 value: {
  dps: 90047.82366
  tps: 62649.06746
 }
}
dps_results: {
 key: "TestAffliction-AllItems-Dominator'sDeadeyeBadge-93341"
 value: {
  dps: 86528.92084
  tps: 60670.63786
 }
}
dps_results: {
 key: "TestAffliction-AllItems-Dominator'sDurableBadge-93345"
 value: {
  dps: 86528.92084
  tps: 60670.63786
 }
}
dps_results: {
 key: "TestAffliction-AllItems-Dominator'sKnightlyBadge-93344"
 value: {
  dps: 86528.92084
  tps: 60670.63786
 }
}
dps_results: {
 key: "TestAffliction-AllItems-Dominator'sMendingBadge-93343"
 value: {
  dps: 87494.06866
  tps: 61103.11197
 }
}
dps_results: {
 key: "TestAffliction-AllItems-DreadfulGladiator'sBadgeofConquest-84344"
 value: {
  dps: 84360.75072
  tps: 58801.37407
 }
}
dps_results: {
 key: "TestAffliction-AllItems-DreadfulGladiator'sBadgeofDominance-84488"
 value: {
  dps: 87757.55944
  tps: 61408.30356
 }
}
dps_results: {
 key: "TestAffliction-AllItems-DreadfulGladiator'sBadgeofVictory-84490"
 value: {
  dps: 84360.75072
  tps: 58801.37407
 }
}
dps_results: {
 key: "TestAffliction-AllItems-DreadfulGladiator'sEmblemofCruelty-84399"
 value: {
  dps: 85163.66812
  tps: 59402.58119
 }
}
dps_results: {
 key: "TestAffliction-AllItems-DreadfulGladiator'sEmblemofMeditation-84401"
 value: {
  dps: 84491.71428
  tps: 58998.14688
 }
}
dps_results: {
 key: "TestAffliction-AllItems-DreadfulGladiator'sEmblemofTenacity-84400"
 value: {
  dps: 85776.63081
  tps: 60001.5842
 }
}
dps_results: {
 key: "TestAffliction-AllItems-DreadfulGladiator'sInsigniaofConquest-84349"
 value: {
  dps: 84427.31272
  tps: 58871.80542
 }
}
dps_results: {
 key: "TestAffliction-AllItems-DreadfulGladiator'sInsigniaofDominance-84489"
 value: {
  dps: 89237.54864
  tps: 62209.02902
 }
}
dps_results: {
 key: "TestAffliction-AllItems-DreadfulGladiator'sInsigniaofVictory-84495"
 value: {
  dps: 84427.31272
  tps: 58871.80542
 }
}
dps_results: {
 key: "TestAffliction-AllItems-DurableBadgeoftheShieldwall-93350"
 value: {
  dps: 86528.92084
  tps: 60670.63786
 }
}
dps_results: {
 key: "TestAffliction-AllItems-EffulgentPrimalDiamond"
 value: {
  dps: 87859.3247
  tps: 61169.6631
 }
}
dps_results: {
 key: "TestAffliction-AllItems-EmberPrimalDiamond"
 value: {
  dps: 88699.6111
  tps: 61757.3255
 }
}
dps_results: {
 key: "TestAffliction-AllItems-EmblemofKypariZar-84077"
 value: {
  dps: 85049.38643
  tps: 59257.4786
 }
}
dps_results: {
 key: "TestAffliction-AllItems-EmblemoftheCatacombs-83733"
 value: {
  dps: 86251.48074
  tps: 60174.52139
 }
}
dps_results: {
 key: "TestAffliction-AllItems-EmptyFruitBarrel-81133"
 value: {
  dps: 84414.52236
  tps: 58906.6604
 }
}
dps_results: {
 key: "TestAffliction-AllItems-EnchantWeapon-BloodyDancingSteel-5125"
 value: {
  dps: 86297.816
  tps: 60187.30499
 }
}
dps_results: {
 key: "TestAffliction-AllItems-EnchantWeapon-Colossus-4445"
 value: {
  dps: 86297.816
  tps: 60187.30499
 }
}
dps_results: {
 key: "TestAffliction-AllItems-EnchantWeapon-DancingSteel-4444"
 value: {
  dps: 86297.816
  tps: 60187.30499
 }
}
dps_results: {
 key: "TestAffliction-AllItems-EnchantWeapon-River'sSong-4446"
 value: {
  dps: 86297.816
  tps: 60187.30499
 }
}
dps_results: {
 key: "TestAffliction-AllItems-EnchantWeapon-SpiritofConquest-5124"
 value: {
  dps: 86297.816
  tps: 60187.30499
 }
}
dps_results: {
 key: "TestAffliction-AllItems-EnchantWeapon-Windsong-4441"
 value: {
  dps: 87688.07458
  tps: 60936.68925
 }
}
dps_results: {
 key: "TestAffliction-AllItems-EnigmaticPrimalDiamond"
 value: {
  dps: 88339.9962
  tps: 61447.68232
 }
}
dps_results: {
 key: "TestAffliction-AllItems-EssenceofTerror-87175"
 value: {
  dps: 95241.83302
  tps: 66099.99627
 }
}
dps_results: {
 key: "TestAffliction-AllItems-EternalPrimalDiamond"
 value: {
  dps: 87848.1595
  tps: 61169.41769
 }
}
dps_results: {
 key: "TestAffliction-AllItems-EvilEyeofGalakras-105491"
 value: {
  dps: 84427.31272
  tps: 58871.80542
 }
}
dps_results: {
 key: "TestAffliction-AllItems-FabledFeatherofJi-Kun-96842"
 value: {
  dps: 84427.31272
  tps: 58871.80542
 }
}
dps_results: {
 key: "TestAffliction-AllItems-FearwurmBadge-84074"
 value: {
  dps: 85049.38643
  tps: 59257.4786
 }
}
dps_results: {
 key: "TestAffliction-AllItems-FearwurmRelic-84070"
 value: {
  dps: 87375.49544
  tps: 60833.42789
 }
}
dps_results: {
 key: "TestAffliction-AllItems-FelsoulIdolofDestruction-101263"
 value: {
  dps: 90392.7702
  tps: 62995.84276
 }
}
dps_results: {
 key: "TestAffliction-AllItems-FelsoulStoneofDestruction-101266"
 value: {
  dps: 92442.33667
  tps: 65028.03552
 }
}
dps_results: {
 key: "TestAffliction-AllItems-FlashfrozenResinGlobule-100951"
 value: {
  dps: 88007.90143
  tps: 61465.7124
 }
}
dps_results: {
 key: "TestAffliction-AllItems-FlashfrozenResinGlobule-81263"
 value: {
  dps: 88504.8951
  tps: 61811.11629
 }
}
dps_results: {
 key: "TestAffliction-AllItems-FlashingSteelTalisman-81265"
 value: {
  dps: 84429.36635
  tps: 58863.90467
 }
}
dps_results: {
 key: "TestAffliction-AllItems-FleetPrimalDiamond"
 value: {
  dps: 88594.62975
  tps: 61761.0096
 }
}
dps_results: {
 key: "TestAffliction-AllItems-ForlornPrimalDiamond"
 value: {
  dps: 88699.6111
  tps: 61757.3255
 }
}
dps_results: {
 key: "TestAffliction-AllItems-FortitudeoftheZandalari-94516"
 value: {
  dps: 86873.40233
  tps: 60869.15269
 }
}
dps_results: {
 key: "TestAffliction-AllItems-FortitudeoftheZandalari-95677"
 value: {
  dps: 86458.36265
  tps: 60540.8479
 }
}
dps_results: {
 key: "TestAffliction-AllItems-FortitudeoftheZandalari-96049"
 value: {
  dps: 87015.08255
  tps: 60981.22461
 }
}
dps_results: {
 key: "TestAffliction-AllItems-FortitudeoftheZandalari-96421"
 value: {
  dps: 87190.09928
  tps: 61119.66639
 }
}
dps_results: {
 key: "TestAffliction-AllItems-FortitudeoftheZandalari-96793"
 value: {
  dps: 87348.44775
  tps: 61244.92324
 }
}
dps_results: {
 key: "TestAffliction-AllItems-GazeoftheTwins-96915"
 value: {
  dps: 85985.57418
  tps: 60039.03364
 }
}
dps_results: {
 key: "TestAffliction-AllItems-Gerp'sPerfectArrow-87495"
 value: {
  dps: 85062.93931
  tps: 59268.74769
 }
}
dps_results: {
 key: "TestAffliction-AllItems-Gladiator'sFelshroud"
 value: {
  dps: 105961.1461
  tps: 73502.19412
 }
}
dps_results: {
 key: "TestAffliction-AllItems-GrievousGladiator'sBadgeofConquest-100195"
 value: {
  dps: 84360.75072
  tps: 58801.37407
 }
}
dps_results: {
 key: "TestAffliction-AllItems-GrievousGladiator'sBadgeofConquest-100603"
 value: {
  dps: 84360.75072
  tps: 58801.37407
 }
}
dps_results: {
 key: "TestAffliction-AllItems-GrievousGladiator'sBadgeofConquest-102856"
 value: {
  dps: 84360.75072
  tps: 58801.37407
 }
}
dps_results: {
 key: "TestAffliction-AllItems-GrievousGladiator'sBadgeofConquest-103145"
 value: {
  dps: 84360.75072
  tps: 58801.37407
 }
}
dps_results: {
 key: "TestAffliction-AllItems-GrievousGladiator'sBadgeofDominance-100490"
 value: {
  dps: 90544.26203
  tps: 63059.904
 }
}
dps_results: {
 key: "TestAffliction-AllItems-GrievousGladiator'sBadgeofDominance-100576"
 value: {
  dps: 90544.26203
  tps: 63059.904
 }
}
dps_results: {
 key: "TestAffliction-AllItems-GrievousGladiator'sBadgeofDominance-102830"
 value: {
  dps: 90544.26203
  tps: 63059.904
 }
}
dps_results: {
 key: "TestAffliction-AllItems-GrievousGladiator'sBadgeofDominance-103308"
 value: {
  dps: 90544.26203
  tps: 63059.904
 }
}
dps_results: {
 key: "TestAffliction-AllItems-GrievousGladiator'sBadgeofVictory-100500"
 value: {
  dps: 84360.75072
  tps: 58801.37407
 }
}
dps_results: {
 key: "TestAffliction-AllItems-GrievousGladiator'sBadgeofVictory-100579"
 value: {
  dps: 84360.75072
  tps: 58801.37407
 }
}
dps_results: {
 key: "TestAffliction-AllItems-GrievousGladiator'sBadgeofVictory-102833"
 value: {
  dps: 84360.75072
  tps: 58801.37407
 }
}
dps_results: {
 key: "TestAffliction-AllItems-GrievousGladiator'sBadgeofVictory-103314"
 value: {
  dps: 84360.75072
  tps: 58801.37407
 }
}
dps_results: {
 key: "TestAffliction-AllItems-GrievousGladiator'sEmblemofCruelty-100305"
 value: {
  dps: 85999.41077
  tps: 59909.97506
 }
}
dps_results: {
 key: "TestAffliction-AllItems-GrievousGladiator'sEmblemofCruelty-100626"
 value: {
  dps: 85999.41077
  tps: 59909.97506
 }
}
dps_results: {
 key: "TestAffliction-AllItems-GrievousGladiator'sEmblemofCruelty-102877"
 value: {
  dps: 85999.41077
  tps: 59909.97506
 }
}
dps_results: {
 key: "TestAffliction-AllItems-GrievousGladiator'sEmblemofCruelty-103210"
 value: {
  dps: 85999.41077
  tps: 59909.97506
 }
}
dps_results: {
 key: "TestAffliction-AllItems-GrievousGladiator'sEmblemofMeditation-100307"
 value: {
  dps: 84482.64967
  tps: 59029.66902
 }
}
dps_results: {
 key: "TestAffliction-AllItems-GrievousGladiator'sEmblemofMeditation-100559"
 value: {
  dps: 84482.64967
  tps: 59029.66902
 }
}
dps_results: {
 key: "TestAffliction-AllItems-GrievousGladiator'sEmblemofMeditation-102813"
 value: {
  dps: 84482.64967
  tps: 59029.66902
 }
}
dps_results: {
 key: "TestAffliction-AllItems-GrievousGladiator'sEmblemofMeditation-103212"
 value: {
  dps: 84482.64967
  tps: 59029.66902
 }
}
dps_results: {
 key: "TestAffliction-AllItems-GrievousGladiator'sEmblemofTenacity-100306"
 value: {
  dps: 84890.70691
  tps: 59430.42694
 }
}
dps_results: {
 key: "TestAffliction-AllItems-GrievousGladiator'sEmblemofTenacity-100652"
 value: {
  dps: 84890.70691
  tps: 59430.42694
 }
}
dps_results: {
 key: "TestAffliction-AllItems-GrievousGladiator'sEmblemofTenacity-102903"
 value: {
  dps: 84890.70691
  tps: 59430.42694
 }
}
dps_results: {
 key: "TestAffliction-AllItems-GrievousGladiator'sEmblemofTenacity-103211"
 value: {
  dps: 84890.70691
  tps: 59430.42694
 }
}
dps_results: {
 key: "TestAffliction-AllItems-GrievousGladiator'sInsigniaofConquest-103150"
 value: {
  dps: 84427.31272
  tps: 58871.80542
 }
}
dps_results: {
 key: "TestAffliction-AllItems-GrievousGladiator'sInsigniaofDominance-103309"
 value: {
  dps: 93511.63732
  tps: 64853.4413
 }
}
dps_results: {
 key: "TestAffliction-AllItems-GrievousGladiator'sInsigniaofVictory-103319"
 value: {
  dps: 84427.31272
  tps: 58871.80542
 }
}
dps_results: {
 key: "TestAffliction-AllItems-Hawkmaster'sTalon-89082"
 value: {
  dps: 87022.71217
  tps: 60748.46803
 }
}
dps_results: {
 key: "TestAffliction-AllItems-Heart-LesionDefenderIdol-100999"
 value: {
  dps: 84695.12495
  tps: 59220.22869
 }
}
dps_results: {
 key: "TestAffliction-AllItems-Heart-LesionDefenderStone-101002"
 value: {
  dps: 88268.67316
  tps: 61801.85615
 }
}
dps_results: {
 key: "TestAffliction-AllItems-Heart-LesionIdolofBattle-100991"
 value: {
  dps: 85498.40232
  tps: 59522.76307
 }
}
dps_results: {
 key: "TestAffliction-AllItems-Heart-LesionStoneofBattle-100990"
 value: {
  dps: 88026.30165
  tps: 61643.90543
 }
}
dps_results: {
 key: "TestAffliction-AllItems-HeartofFire-81181"
 value: {
  dps: 85840.40068
  tps: 59989.52715
 }
}
dps_results: {
 key: "TestAffliction-AllItems-HeartwarmerMedallion-93260"
 value: {
  dps: 88757.64313
  tps: 61978.32531
 }
}
dps_results: {
 key: "TestAffliction-AllItems-HelmbreakerMedallion-93261"
 value: {
  dps: 86295.71569
  tps: 60155.67764
 }
}
dps_results: {
 key: "TestAffliction-AllItems-Horridon'sLastGasp-96757"
 value: {
  dps: 91096.90961
  tps: 63608.2128
 }
}
dps_results: {
 key: "TestAffliction-AllItems-ImpassivePrimalDiamond"
 value: {
  dps: 88339.9962
  tps: 61447.68232
 }
}
dps_results: {
 key: "TestAffliction-AllItems-IndomitablePrimalDiamond"
 value: {
  dps: 87859.3247
  tps: 61169.6631
 }
}
dps_results: {
 key: "TestAffliction-AllItems-InscribedBagofHydra-Spawn-96828"
 value: {
  dps: 84335.95539
  tps: 58877.02283
 }
}
dps_results: {
 key: "TestAffliction-AllItems-InsigniaofKypariZar-84078"
 value: {
  dps: 84427.31272
  tps: 58871.80542
 }
}
dps_results: {
 key: "TestAffliction-AllItems-IronBellyWok-89083"
 value: {
  dps: 87022.71217
  tps: 60748.46803
 }
}
dps_results: {
 key: "TestAffliction-AllItems-IronProtectorTalisman-85181"
 value: {
  dps: 84641.11548
  tps: 59145.69125
 }
}
dps_results: {
 key: "TestAffliction-AllItems-JadeBanditFigurine-86043"
 value: {
  dps: 87022.71217
  tps: 60748.46803
 }
}
dps_results: {
 key: "TestAffliction-AllItems-JadeBanditFigurine-86772"
 value: {
  dps: 87143.60128
  tps: 60682.68085
 }
}
dps_results: {
 key: "TestAffliction-AllItems-JadeCharioteerFigurine-86042"
 value: {
  dps: 87022.71217
  tps: 60748.46803
 }
}
dps_results: {
 key: "TestAffliction-AllItems-JadeCharioteerFigurine-86771"
 value: {
  dps: 87143.60128
  tps: 60682.68085
 }
}
dps_results: {
 key: "TestAffliction-AllItems-JadeCourtesanFigurine-86045"
 value: {
  dps: 88521.08819
  tps: 61818.57018
 }
}
dps_results: {
 key: "TestAffliction-AllItems-JadeCourtesanFigurine-86774"
 value: {
  dps: 88059.81301
  tps: 61500.70289
 }
}
dps_results: {
 key: "TestAffliction-AllItems-JadeMagistrateFigurine-86044"
 value: {
  dps: 90201.13816
  tps: 62822.10041
 }
}
dps_results: {
 key: "TestAffliction-AllItems-JadeMagistrateFigurine-86773"
 value: {
  dps: 89366.00004
  tps: 62206.23469
 }
}
dps_results: {
 key: "TestAffliction-AllItems-JadeWarlordFigurine-86046"
 value: {
  dps: 87288.41571
  tps: 61248.31813
 }
}
dps_results: {
 key: "TestAffliction-AllItems-JadeWarlordFigurine-86775"
 value: {
  dps: 86953.43834
  tps: 61044.83385
 }
}
dps_results: {
 key: "TestAffliction-AllItems-Ji-Kun'sRisingWinds-96843"
 value: {
  dps: 84427.31272
  tps: 58871.80542
 }
}
dps_results: {
 key: "TestAffliction-AllItems-KnightlyBadgeoftheShieldwall-93349"
 value: {
  dps: 86528.92084
  tps: 60670.63786
 }
}
dps_results: {
 key: "TestAffliction-AllItems-KnotofTenSongs-84073"
 value: {
  dps: 84427.31272
  tps: 58871.80542
 }
}
dps_results: {
 key: "TestAffliction-AllItems-Kor'kronBookofHurting-92785"
 value: {
  dps: 84360.75072
  tps: 58801.37407
 }
}
dps_results: {
 key: "TestAffliction-AllItems-Lao-Chin'sLiquidCourage-89079"
 value: {
  dps: 87288.41571
  tps: 61248.31813
 }
}
dps_results: {
 key: "TestAffliction-AllItems-LeiShen'sFinalOrders-87072"
 value: {
  dps: 87069.624
  tps: 60758.77367
 }
}
dps_results: {
 key: "TestAffliction-AllItems-LessonsoftheDarkmaster-81268"
 value: {
  dps: 84427.31272
  tps: 58871.80542
 }
}
dps_results: {
 key: "TestAffliction-AllItems-LightdrinkerIdolofRage-101200"
 value: {
  dps: 86130.69265
  tps: 60219.14177
 }
}
dps_results: {
 key: "TestAffliction-AllItems-LightdrinkerStoneofRage-101203"
 value: {
  dps: 87990.20819
  tps: 61607.35357
 }
}
dps_results: {
 key: "TestAffliction-AllItems-LightoftheCosmos-87065"
 value: {
  dps: 93115.67827
  tps: 64705.98208
 }
}
dps_results: {
 key: "TestAffliction-AllItems-LordBlastington'sScopeofDoom-4699"
 value: {
  dps: 86297.816
  tps: 60187.30499
 }
}
dps_results: {
 key: "TestAffliction-AllItems-MalevolentGladiator'sBadgeofConquest-84934"
 value: {
  dps: 84360.75072
  tps: 58801.37407
 }
}
dps_results: {
 key: "TestAffliction-AllItems-MalevolentGladiator'sBadgeofConquest-91452"
 value: {
  dps: 84360.75072
  tps: 58801.37407
 }
}
dps_results: {
 key: "TestAffliction-AllItems-MalevolentGladiator'sBadgeofDominance-84940"
 value: {
  dps: 88706.2134
  tps: 62054.10317
 }
}
dps_results: {
 key: "TestAffliction-AllItems-MalevolentGladiator'sBadgeofDominance-91753"
 value: {
  dps: 88471.49335
  tps: 61898.3248
 }
}
dps_results: {
 key: "TestAffliction-AllItems-MalevolentGladiator'sBadgeofVictory-84942"
 value: {
  dps: 84360.75072
  tps: 58801.37407
 }
}
dps_results: {
 key: "TestAffliction-AllItems-MalevolentGladiator'sBadgeofVictory-91763"
 value: {
  dps: 84360.75072
  tps: 58801.37407
 }
}
dps_results: {
 key: "TestAffliction-AllItems-MalevolentGladiator'sEmblemofCruelty-84936"
 value: {
  dps: 85440.10454
  tps: 59562.4232
 }
}
dps_results: {
 key: "TestAffliction-AllItems-MalevolentGladiator'sEmblemofCruelty-91562"
 value: {
  dps: 85345.9828
  tps: 59503.56433
 }
}
dps_results: {
 key: "TestAffliction-AllItems-MalevolentGladiator'sEmblemofMeditation-84939"
 value: {
  dps: 84488.8224
  tps: 58991.68032
 }
}
dps_results: {
 key: "TestAffliction-AllItems-MalevolentGladiator'sEmblemofMeditation-91564"
 value: {
  dps: 84488.8224
  tps: 58991.68032
 }
}
dps_results: {
 key: "TestAffliction-AllItems-MalevolentGladiator'sEmblemofTenacity-84938"
 value: {
  dps: 84832.54745
  tps: 59333.82294
 }
}
dps_results: {
 key: "TestAffliction-AllItems-MalevolentGladiator'sEmblemofTenacity-91563"
 value: {
  dps: 84768.07183
  tps: 59238.60925
 }
}
dps_results: {
 key: "TestAffliction-AllItems-MalevolentGladiator'sInsigniaofConquest-91457"
 value: {
  dps: 84427.31272
  tps: 58871.80542
 }
}
dps_results: {
 key: "TestAffliction-AllItems-MalevolentGladiator'sInsigniaofDominance-91754"
 value: {
  dps: 90290.15903
  tps: 62988.26118
 }
}
dps_results: {
 key: "TestAffliction-AllItems-MalevolentGladiator'sInsigniaofVictory-91768"
 value: {
  dps: 84427.31272
  tps: 58871.80542
 }
}
dps_results: {
 key: "TestAffliction-AllItems-MarkoftheCatacombs-83731"
 value: {
  dps: 87136.6678
  tps: 60879.64925
 }
}
dps_results: {
 key: "TestAffliction-AllItems-MarkoftheHardenedGrunt-92783"
 value: {
  dps: 84360.75072
  tps: 58801.37407
 }
}
dps_results: {
 key: "TestAffliction-AllItems-MedallionofMystifyingVapors-93257"
 value: {
  dps: 87366.5424
  tps: 61192.27304
 }
}
dps_results: {
 key: "TestAffliction-AllItems-MedallionoftheCatacombs-83734"
 value: {
  dps: 85456.6814
  tps: 59686.01358
 }
}
dps_results: {
 key: "TestAffliction-AllItems-MendingBadgeoftheShieldwall-93348"
 value: {
  dps: 87494.06866
  tps: 61103.11197
 }
}
dps_results: {
 key: "TestAffliction-AllItems-MirrorScope-4700"
 value: {
  dps: 86297.816
  tps: 60187.30499
 }
}
dps_results: {
 key: "TestAffliction-AllItems-MistdancerDefenderIdol-101089"
 value: {
  dps: 84695.12495
  tps: 59220.22869
 }
}
dps_results: {
 key: "TestAffliction-AllItems-MistdancerDefenderStone-101087"
 value: {
  dps: 88396.14649
  tps: 61837.78591
 }
}
dps_results: {
 key: "TestAffliction-AllItems-MistdancerIdolofRage-101113"
 value: {
  dps: 86130.69265
  tps: 60219.14177
 }
}
dps_results: {
 key: "TestAffliction-AllItems-MistdancerStoneofRage-101117"
 value: {
  dps: 88479.66565
  tps: 62078.67278
 }
}
dps_results: {
 key: "TestAffliction-AllItems-MistdancerStoneofWisdom-101107"
 value: {
  dps: 88308.57891
  tps: 61601.01018
 }
}
dps_results: {
 key: "TestAffliction-AllItems-MithrilWristwatch-87572"
 value: {
  dps: 87083.77848
  tps: 60760.24185
 }
}
dps_results: {
 key: "TestAffliction-AllItems-MountainsageIdolofDestruction-101069"
 value: {
  dps: 90319.67816
  tps: 62925.19588
 }
}
dps_results: {
 key: "TestAffliction-AllItems-MountainsageStoneofDestruction-101072"
 value: {
  dps: 91749.64301
  tps: 64161.85669
 }
}
dps_results: {
 key: "TestAffliction-AllItems-OathswornDefenderIdol-101303"
 value: {
  dps: 84695.12495
  tps: 59220.22869
 }
}
dps_results: {
 key: "TestAffliction-AllItems-OathswornDefenderStone-101306"
 value: {
  dps: 88461.98664
  tps: 61966.16974
 }
}
dps_results: {
 key: "TestAffliction-AllItems-OathswornIdolofBattle-101295"
 value: {
  dps: 85498.40232
  tps: 59522.76307
 }
}
dps_results: {
 key: "TestAffliction-AllItems-OathswornStoneofBattle-101294"
 value: {
  dps: 88055.17317
  tps: 61653.7298
 }
}
dps_results: {
 key: "TestAffliction-AllItems-PhaseFingers-4697"
 value: {
  dps: 88918.59175
  tps: 61890.78296
 }
}
dps_results: {
 key: "TestAffliction-AllItems-PouchofWhiteAsh-103639"
 value: {
  dps: 84360.75072
  tps: 58801.37407
 }
}
dps_results: {
 key: "TestAffliction-AllItems-PowerfulPrimalDiamond"
 value: {
  dps: 87859.3247
  tps: 61169.6631
 }
}
dps_results: {
 key: "TestAffliction-AllItems-PriceofProgress-81266"
 value: {
  dps: 87649.30634
  tps: 61146.2115
 }
}
dps_results: {
 key: "TestAffliction-AllItems-PridefulGladiator'sBadgeofConquest-102659"
 value: {
  dps: 84360.75072
  tps: 58801.37407
 }
}
dps_results: {
 key: "TestAffliction-AllItems-PridefulGladiator'sBadgeofConquest-103342"
 value: {
  dps: 84360.75072
  tps: 58801.37407
 }
}
dps_results: {
 key: "TestAffliction-AllItems-PridefulGladiator'sBadgeofDominance-102633"
 value: {
  dps: 92648.81276
  tps: 64407.7044
 }
}
dps_results: {
 key: "TestAffliction-AllItems-PridefulGladiator'sBadgeofDominance-103505"
 value: {
  dps: 92648.81276
  tps: 64407.7044
 }
}
dps_results: {
 key: "TestAffliction-AllItems-PridefulGladiator'sBadgeofVictory-102636"
 value: {
  dps: 84360.75072
  tps: 58801.37407
 }
}
dps_results: {
 key: "TestAffliction-AllItems-PridefulGladiator'sBadgeofVictory-103511"
 value: {
  dps: 84360.75072
  tps: 58801.37407
 }
}
dps_results: {
 key: "TestAffliction-AllItems-PridefulGladiator'sEmblemofCruelty-102680"
 value: {
  dps: 86437.04527
  tps: 60174.28599
 }
}
dps_results: {
 key: "TestAffliction-AllItems-PridefulGladiator'sEmblemofCruelty-103407"
 value: {
  dps: 86437.04527
  tps: 60174.28599
 }
}
dps_results: {
 key: "TestAffliction-AllItems-PridefulGladiator'sEmblemofMeditation-102616"
 value: {
  dps: 84580.33846
  tps: 59111.38307
 }
}
dps_results: {
 key: "TestAffliction-AllItems-PridefulGladiator'sEmblemofMeditation-103409"
 value: {
  dps: 84580.33846
  tps: 59111.38307
 }
}
dps_results: {
 key: "TestAffliction-AllItems-PridefulGladiator'sEmblemofTenacity-102706"
 value: {
  dps: 84950.36447
  tps: 59446.39939
 }
}
dps_results: {
 key: "TestAffliction-AllItems-PridefulGladiator'sEmblemofTenacity-103408"
 value: {
  dps: 84950.36447
  tps: 59446.39939
 }
}
dps_results: {
 key: "TestAffliction-AllItems-PridefulGladiator'sInsigniaofConquest-103347"
 value: {
  dps: 84427.31272
  tps: 58871.80542
 }
}
dps_results: {
 key: "TestAffliction-AllItems-PridefulGladiator'sInsigniaofDominance-103506"
 value: {
  dps: 97083.66536
  tps: 67508.36613
 }
}
dps_results: {
 key: "TestAffliction-AllItems-PridefulGladiator'sInsigniaofVictory-103516"
 value: {
  dps: 84427.31272
  tps: 58871.80542
 }
}
dps_results: {
 key: "TestAffliction-AllItems-Primordius'TalismanofRage-96873"
 value: {
  dps: 86271.53359
  tps: 59985.5316
 }
}
dps_results: {
 key: "TestAffliction-AllItems-Qin-xi'sPolarizingSeal-87075"
 value: {
  dps: 84414.52236
  tps: 58906.6604
 }
}
dps_results: {
 key: "TestAffliction-AllItems-RegaliaoftheHornedNightmare"
 value: {
  dps: 97577.50565
  tps: 68322.13843
 }
}
dps_results: {
 key: "TestAffliction-AllItems-RegaliaoftheThousandfoldHells"
 value: {
  dps: 98925.88026
  tps: 68933.39964
 }
}
dps_results: {
 key: "TestAffliction-AllItems-RelicofChi-Ji-79330"
 value: {
  dps: 88763.29841
  tps: 61916.49431
 }
}
dps_results: {
 key: "TestAffliction-AllItems-RelicofKypariZar-84075"
 value: {
  dps: 87363.31177
  tps: 60875.93313
 }
}
dps_results: {
 key: "TestAffliction-AllItems-RelicofNiuzao-79329"
 value: {
  dps: 84781.99661
  tps: 59406.20972
 }
}
dps_results: {
 key: "TestAffliction-AllItems-RelicofXuen-79327"
 value: {
  dps: 84427.31272
  tps: 58871.80542
 }
}
dps_results: {
 key: "TestAffliction-AllItems-RelicofXuen-79328"
 value: {
  dps: 84427.31272
  tps: 58871.80542
 }
}
dps_results: {
 key: "TestAffliction-AllItems-Renataki'sSoulCharm-96741"
 value: {
  dps: 84427.31272
  tps: 58871.80542
 }
}
dps_results: {
 key: "TestAffliction-AllItems-ResolveofNiuzao-103690"
 value: {
  dps: 86349.24581
  tps: 60392.01254
 }
}
dps_results: {
 key: "TestAffliction-AllItems-ResolveofNiuzao-103990"
 value: {
  dps: 87191.75988
  tps: 61058.42278
 }
}
dps_results: {
 key: "TestAffliction-AllItems-ReverberatingPrimalDiamond"
 value: {
  dps: 88412.35794
  tps: 61617.397
 }
}
dps_results: {
 key: "TestAffliction-AllItems-RevitalizingPrimalDiamond"
 value: {
  dps: 88412.35794
  tps: 61617.397
 }
}
dps_results: {
 key: "TestAffliction-AllItems-SI:7Operative'sManual-92784"
 value: {
  dps: 84360.75072
  tps: 58801.37407
 }
}
dps_results: {
 key: "TestAffliction-AllItems-ScrollofReveredAncestors-89080"
 value: {
  dps: 88521.08819
  tps: 61818.57018
 }
}
dps_results: {
 key: "TestAffliction-AllItems-SearingWords-81267"
 value: {
  dps: 85223.91488
  tps: 59351.60886
 }
}
dps_results: {
 key: "TestAffliction-AllItems-Sha-SkinRegalia"
 value: {
  dps: 94098.95543
  tps: 66255.86951
 }
}
dps_results: {
 key: "TestAffliction-AllItems-ShadowflameRegalia"
 value: {
  dps: 64247.12903
  tps: 44138.91279
 }
}
dps_results: {
 key: "TestAffliction-AllItems-Shock-ChargerMedallion-93259"
 value: {
  dps: 92933.35569
  tps: 64396.89415
 }
}
dps_results: {
 key: "TestAffliction-AllItems-SigilofCompassion-83736"
 value: {
  dps: 85456.6814
  tps: 59686.01358
 }
}
dps_results: {
 key: "TestAffliction-AllItems-SigilofDevotion-83740"
 value: {
  dps: 84987.4588
  tps: 59230.30366
 }
}
dps_results: {
 key: "TestAffliction-AllItems-SigilofFidelity-83737"
 value: {
  dps: 87413.05793
  tps: 60964.2957
 }
}
dps_results: {
 key: "TestAffliction-AllItems-SigilofGrace-83738"
 value: {
  dps: 85456.6814
  tps: 59686.01358
 }
}
dps_results: {
 key: "TestAffliction-AllItems-SigilofKypariZar-84076"
 value: {
  dps: 86818.29973
  tps: 60658.76464
 }
}
dps_results: {
 key: "TestAffliction-AllItems-SigilofPatience-83739"
 value: {
  dps: 84427.31272
  tps: 58871.80542
 }
}
dps_results: {
 key: "TestAffliction-AllItems-SigiloftheCatacombs-83732"
 value: {
  dps: 87635.74223
  tps: 61014.74345
 }
}
dps_results: {
 key: "TestAffliction-AllItems-SinisterPrimalDiamond"
 value: {
  dps: 88240.07425
  tps: 61392.2376
 }
}
dps_results: {
 key: "TestAffliction-AllItems-SkullrenderMedallion-93256"
 value: {
  dps: 86295.71569
  tps: 60155.67764
 }
}
dps_results: {
 key: "TestAffliction-AllItems-SpiritsoftheSun-87163"
 value: {
  dps: 89355.50347
  tps: 62401.65588
 }
}
dps_results: {
 key: "TestAffliction-AllItems-SpringrainIdolofDestruction-101023"
 value: {
  dps: 90028.2661
  tps: 62695.53741
 }
}
dps_results: {
 key: "TestAffliction-AllItems-SpringrainIdolofRage-101009"
 value: {
  dps: 86130.69265
  tps: 60219.14177
 }
}
dps_results: {
 key: "TestAffliction-AllItems-SpringrainStoneofDestruction-101026"
 value: {
  dps: 92426.84802
  tps: 64766.85113
 }
}
dps_results: {
 key: "TestAffliction-AllItems-SpringrainStoneofRage-101012"
 value: {
  dps: 87820.87732
  tps: 61491.62979
 }
}
dps_results: {
 key: "TestAffliction-AllItems-SpringrainStoneofWisdom-101041"
 value: {
  dps: 88308.57891
  tps: 61601.01018
 }
}
dps_results: {
 key: "TestAffliction-AllItems-Static-Caster'sMedallion-93254"
 value: {
  dps: 92933.35569
  tps: 64396.89415
 }
}
dps_results: {
 key: "TestAffliction-AllItems-SteadfastFootman'sMedallion-92782"
 value: {
  dps: 84360.75072
  tps: 58801.37407
 }
}
dps_results: {
 key: "TestAffliction-AllItems-SteadfastTalismanoftheShado-PanAssault-94507"
 value: {
  dps: 86874.77439
  tps: 60807.69418
 }
}
dps_results: {
 key: "TestAffliction-AllItems-StreamtalkerIdolofDestruction-101222"
 value: {
  dps: 90423.59199
  tps: 62887.53501
 }
}
dps_results: {
 key: "TestAffliction-AllItems-StreamtalkerIdolofRage-101217"
 value: {
  dps: 86130.69265
  tps: 60219.14177
 }
}
dps_results: {
 key: "TestAffliction-AllItems-StreamtalkerStoneofDestruction-101225"
 value: {
  dps: 92269.76871
  tps: 64742.57186
 }
}
dps_results: {
 key: "TestAffliction-AllItems-StreamtalkerStoneofRage-101220"
 value: {
  dps: 88010.12422
  tps: 61573.76598
 }
}
dps_results: {
 key: "TestAffliction-AllItems-StreamtalkerStoneofWisdom-101250"
 value: {
  dps: 88308.57891
  tps: 61601.01018
 }
}
dps_results: {
 key: "TestAffliction-AllItems-StuffofNightmares-87160"
 value: {
  dps: 86596.16083
  tps: 60587.31693
 }
}
dps_results: {
 key: "TestAffliction-AllItems-SunsoulDefenderIdol-101160"
 value: {
  dps: 84695.12495
  tps: 59220.22869
 }
}
dps_results: {
 key: "TestAffliction-AllItems-SunsoulDefenderStone-101163"
 value: {
  dps: 88577.16282
  tps: 62115.38736
 }
}
dps_results: {
 key: "TestAffliction-AllItems-SunsoulIdolofBattle-101152"
 value: {
  dps: 85498.40232
  tps: 59522.76307
 }
}
dps_results: {
 key: "TestAffliction-AllItems-SunsoulStoneofBattle-101151"
 value: {
  dps: 88416.30511
  tps: 62031.4604
 }
}
dps_results: {
 key: "TestAffliction-AllItems-SunsoulStoneofWisdom-101138"
 value: {
  dps: 88308.57891
  tps: 61601.01018
 }
}
dps_results: {
 key: "TestAffliction-AllItems-SwordguardEmbroidery(Rank3)-4894"
 value: {
  dps: 87200.7358
  tps: 60844.92071
 }
}
dps_results: {
 key: "TestAffliction-AllItems-SymboloftheCatacombs-83735"
 value: {
  dps: 85456.6814
  tps: 59686.01358
 }
}
dps_results: {
 key: "TestAffliction-AllItems-SynapseSprings(MarkII)-4898"
 value: {
  dps: 90077.8216
  tps: 62524.42891
 }
}
dps_results: {
 key: "TestAffliction-AllItems-TalismanofBloodlust-96864"
 value: {
  dps: 88725.94578
  tps: 61928.08272
 }
}
dps_results: {
 key: "TestAffliction-AllItems-TerrorintheMists-87167"
 value: {
  dps: 87300.06856
  tps: 60731.86776
 }
}
dps_results: {
 key: "TestAffliction-AllItems-Thousand-YearPickledEgg-87573"
 value: {
  dps: 87864.94465
  tps: 61294.6139
 }
}
dps_results: {
 key: "TestAffliction-AllItems-TrailseekerIdolofRage-101054"
 value: {
  dps: 86130.69265
  tps: 60219.14177
 }
}
dps_results: {
 key: "TestAffliction-AllItems-TrailseekerStoneofRage-101057"
 value: {
  dps: 88290.53599
  tps: 61898.44347
 }
}
dps_results: {
 key: "TestAffliction-AllItems-TyrannicalGladiator'sBadgeofConquest-100043"
 value: {
  dps: 84360.75072
  tps: 58801.37407
 }
}
dps_results: {
 key: "TestAffliction-AllItems-TyrannicalGladiator'sBadgeofConquest-91099"
 value: {
  dps: 84360.75072
  tps: 58801.37407
 }
}
dps_results: {
 key: "TestAffliction-AllItems-TyrannicalGladiator'sBadgeofConquest-94373"
 value: {
  dps: 84360.75072
  tps: 58801.37407
 }
}
dps_results: {
 key: "TestAffliction-AllItems-TyrannicalGladiator'sBadgeofConquest-99772"
 value: {
  dps: 84360.75072
  tps: 58801.37407
 }
}
dps_results: {
 key: "TestAffliction-AllItems-TyrannicalGladiator'sBadgeofDominance-100016"
 value: {
  dps: 89379.10785
  tps: 62463.81833
 }
}
dps_results: {
 key: "TestAffliction-AllItems-TyrannicalGladiator'sBadgeofDominance-91400"
 value: {
  dps: 89379.10785
  tps: 62463.81833
 }
}
dps_results: {
 key: "TestAffliction-AllItems-TyrannicalGladiator'sBadgeofDominance-94346"
 value: {
  dps: 89379.10785
  tps: 62463.81833
 }
}
dps_results: {
 key: "TestAffliction-AllItems-TyrannicalGladiator'sBadgeofDominance-99937"
 value: {
  dps: 89379.10785
  tps: 62463.81833
 }
}
dps_results: {
 key: "TestAffliction-AllItems-TyrannicalGladiator'sBadgeofVictory-100019"
 value: {
  dps: 84360.75072
  tps: 58801.37407
 }
}
dps_results: {
 key: "TestAffliction-AllItems-TyrannicalGladiator'sBadgeofVictory-91410"
 value: {
  dps: 84360.75072
  tps: 58801.37407
 }
}
dps_results: {
 key: "TestAffliction-AllItems-TyrannicalGladiator'sBadgeofVictory-94349"
 value: {
  dps: 84360.75072
  tps: 58801.37407
 }
}
dps_results: {
 key: "TestAffliction-AllItems-TyrannicalGladiator'sBadgeofVictory-99943"
 value: {
  dps: 84360.75072
  tps: 58801.37407
 }
}
dps_results: {
 key: "TestAffliction-AllItems-TyrannicalGladiator'sEmblemofCruelty-100066"
 value: {
  dps: 85649.60251
  tps: 59695.11086
 }
}
dps_results: {
 key: "TestAffliction-AllItems-TyrannicalGladiator'sEmblemofCruelty-91209"
 value: {
  dps: 85649.60251
  tps: 59695.11086
 }
}
dps_results: {
 key: "TestAffliction-AllItems-TyrannicalGladiator'sEmblemofCruelty-94396"
 value: {
  dps: 85649.60251
  tps: 59695.11086
 }
}
dps_results: {
 key: "TestAffliction-AllItems-TyrannicalGladiator'sEmblemofCruelty-99838"
 value: {
  dps: 85649.60251
  tps: 59695.11086
 }
}
dps_results: {
 key: "TestAffliction-AllItems-TyrannicalGladiator'sEmblemofMeditation-91211"
 value: {
  dps: 84488.8224
  tps: 58991.68032
 }
}
dps_results: {
 key: "TestAffliction-AllItems-TyrannicalGladiator'sEmblemofMeditation-94329"
 value: {
  dps: 84488.8224
  tps: 58991.68032
 }
}
dps_results: {
 key: "TestAffliction-AllItems-TyrannicalGladiator'sEmblemofMeditation-99840"
 value: {
  dps: 84488.8224
  tps: 58991.68032
 }
}
dps_results: {
 key: "TestAffliction-AllItems-TyrannicalGladiator'sEmblemofMeditation-99990"
 value: {
  dps: 84488.8224
  tps: 58991.68032
 }
}
dps_results: {
 key: "TestAffliction-AllItems-TyrannicalGladiator'sEmblemofTenacity-100092"
 value: {
  dps: 84693.18255
  tps: 59269.71822
 }
}
dps_results: {
 key: "TestAffliction-AllItems-TyrannicalGladiator'sEmblemofTenacity-91210"
 value: {
  dps: 84693.18255
  tps: 59269.71822
 }
}
dps_results: {
 key: "TestAffliction-AllItems-TyrannicalGladiator'sEmblemofTenacity-94422"
 value: {
  dps: 84693.18255
  tps: 59269.71822
 }
}
dps_results: {
 key: "TestAffliction-AllItems-TyrannicalGladiator'sEmblemofTenacity-99839"
 value: {
  dps: 84693.18255
  tps: 59269.71822
 }
}
dps_results: {
 key: "TestAffliction-AllItems-TyrannicalGladiator'sInsigniaofConquest-100026"
 value: {
  dps: 84427.31272
  tps: 58871.80542
 }
}
dps_results: {
 key: "TestAffliction-AllItems-TyrannicalGladiator'sInsigniaofDominance-100152"
 value: {
  dps: 92024.99611
  tps: 64203.58091
 }
}
dps_results: {
 key: "TestAffliction-AllItems-TyrannicalGladiator'sInsigniaofVictory-100085"
 value: {
  dps: 84427.31272
  tps: 58871.80542
 }
}
dps_results: {
 key: "TestAffliction-AllItems-TyrannicalPrimalDiamond"
 value: {
  dps: 87848.1595
  tps: 61169.41769
 }
}
dps_results: {
 key: "TestAffliction-AllItems-UnerringVisionofLeiShen-96930"
 value: {
  dps: 98296.86091
  tps: 67940.73543
 }
}
dps_results: {
 key: "TestAffliction-AllItems-VaporshieldMedallion-93262"
 value: {
  dps: 87366.5424
  tps: 61192.27304
 }
}
dps_results: {
 key: "TestAffliction-AllItems-VestmentsoftheFacelessShroud"
 value: {
  dps: 69492.29827
  tps: 47797.1033
 }
}
dps_results: {
 key: "TestAffliction-AllItems-VialofDragon'sBlood-87063"
 value: {
  dps: 86459.35656
  tps: 60479.10774
 }
}
dps_results: {
 key: "TestAffliction-AllItems-VialofIchorousBlood-100963"
 value: {
  dps: 87290.38262
  tps: 60893.10124
 }
}
dps_results: {
 key: "TestAffliction-AllItems-VialofIchorousBlood-81264"
 value: {
  dps: 87649.30634
  tps: 61146.2115
 }
}
dps_results: {
 key: "TestAffliction-AllItems-ViciousTalismanoftheShado-PanAssault-94511"
 value: {
  dps: 84427.31272
  tps: 58871.80542
 }
}
dps_results: {
 key: "TestAffliction-AllItems-VolatileTalismanoftheShado-PanAssault-94510"
 value: {
  dps: 96474.52563
  tps: 66930.92392
 }
}
dps_results: {
 key: "TestAffliction-AllItems-WindsweptPages-81125"
 value: {
  dps: 86840.95852
  tps: 60521.52964
 }
}
dps_results: {
 key: "TestAffliction-AllItems-WoundripperMedallion-93253"
 value: {
  dps: 86295.71569
  tps: 60155.67764
 }
}
dps_results: {
 key: "TestAffliction-AllItems-Wushoolay'sFinalChoice-96785"
 value: {
  dps: 112639.40141
  tps: 78615.51607
 }
}
dps_results: {
 key: "TestAffliction-AllItems-Yu'lon'sBite-103987"
 value: {
  dps: 94275.46656
  tps: 65100.36071
 }
}
dps_results: {
 key: "TestAffliction-AllItems-ZenAlchemistStone-75274"
 value: {
  dps: 92524.01905
  tps: 64589.80892
 }
}
dps_results: {
 key: "TestAffliction-Average-Default"
 value: {
  dps: 90034.14677
  tps: 62392.75341
 }
}
dps_results: {
 key: "TestAffliction-Settings-Goblin-p1-Affliction Warlock-default-FullBuffs-25.0yards-LongMultiTarget"
 value: {
  dps: 143571.12027
  tps: 118046.51408
 }
}
dps_results: {
 key: "TestAffliction-Settings-Goblin-p1-Affliction Warlock-default-FullBuffs-25.0yards-LongSingleTarget"
 value: {
  dps: 143571.12027
  tps: 102053.42848
 }
}
dps_results: {
 key: "TestAffliction-Settings-Goblin-p1-Affliction Warlock-default-FullBuffs-25.0yards-ShortSingleTarget"
 value: {
  dps: 237443.74575
  tps: 154428.6143
 }
}
dps_results: {
 key: "TestAffliction-Settings-Goblin-p1-Affliction Warlock-default-NoBuffs-25.0yards-LongMultiTarget"
 value: {
  dps: 91309.79044
  tps: 81945.30604
 }
}
dps_results: {
 key: "TestAffliction-Settings-Goblin-p1-Affliction Warlock-default-NoBuffs-25.0yards-LongSingleTarget"
 value: {
  dps: 91309.79044
  tps: 66612.79518
 }
}
dps_results: {
 key: "TestAffliction-Settings-Goblin-p1-Affliction Warlock-default-NoBuffs-25.0yards-ShortSingleTarget"
 value: {
  dps: 120349.74153
  tps: 81993.44496
 }
}
dps_results: {
 key: "TestAffliction-Settings-Goblin-preraid-Affliction Warlock-default-FullBuffs-25.0yards-LongMultiTarget"
 value: {
  dps: 89472.5908
  tps: 75776.88705
 }
}
dps_results: {
 key: "TestAffliction-Settings-Goblin-preraid-Affliction Warlock-default-FullBuffs-25.0yards-LongSingleTarget"
 value: {
  dps: 89472.5908
  tps: 62534.21468
 }
}
dps_results: {
 key: "TestAffliction-Settings-Goblin-preraid-Affliction Warlock-default-FullBuffs-25.0yards-ShortSingleTarget"
 value: {
  dps: 148280.34895
  tps: 91731.68802
 }
}
dps_results: {
 key: "TestAffliction-Settings-Goblin-preraid-Affliction Warlock-default-NoBuffs-25.0yards-LongMultiTarget"
 value: {
  dps: 55221.04897
  tps: 52528.09892
 }
}
dps_results: {
 key: "TestAffliction-Settings-Goblin-preraid-Affliction Warlock-default-NoBuffs-25.0yards-LongSingleTarget"
 value: {
  dps: 55221.04897
  tps: 39655.52229
 }
}
dps_results: {
 key: "TestAffliction-Settings-Goblin-preraid-Affliction Warlock-default-NoBuffs-25.0yards-ShortSingleTarget"
 value: {
  dps: 72371.33801
  tps: 47441.52119
 }
}
dps_results: {
 key: "TestAffliction-Settings-Human-p1-Affliction Warlock-default-FullBuffs-25.0yards-LongMultiTarget"
 value: {
  dps: 142110.05433
  tps: 116887.08649
 }
}
dps_results: {
 key: "TestAffliction-Settings-Human-p1-Affliction Warlock-default-FullBuffs-25.0yards-LongSingleTarget"
 value: {
  dps: 142110.05433
  tps: 101151.95388
 }
}
dps_results: {
 key: "TestAffliction-Settings-Human-p1-Affliction Warlock-default-FullBuffs-25.0yards-ShortSingleTarget"
 value: {
  dps: 235814.5459
  tps: 153768.41023
 }
}
dps_results: {
 key: "TestAffliction-Settings-Human-p1-Affliction Warlock-default-NoBuffs-25.0yards-LongMultiTarget"
 value: {
  dps: 90409.99913
  tps: 81159.69769
 }
}
dps_results: {
 key: "TestAffliction-Settings-Human-p1-Affliction Warlock-default-NoBuffs-25.0yards-LongSingleTarget"
 value: {
  dps: 90409.99913
  tps: 66068.64369
 }
}
dps_results: {
 key: "TestAffliction-Settings-Human-p1-Affliction Warlock-default-NoBuffs-25.0yards-ShortSingleTarget"
 value: {
  dps: 119251.39581
  tps: 81133.58344
 }
}
dps_results: {
 key: "TestAffliction-Settings-Human-preraid-Affliction Warlock-default-FullBuffs-25.0yards-LongMultiTarget"
 value: {
  dps: 88220.56032
  tps: 75005.69652
 }
}
dps_results: {
 key: "TestAffliction-Settings-Human-preraid-Affliction Warlock-default-FullBuffs-25.0yards-LongSingleTarget"
 value: {
  dps: 88220.56032
  tps: 61551.1414
 }
}
dps_results: {
 key: "TestAffliction-Settings-Human-preraid-Affliction Warlock-default-FullBuffs-25.0yards-ShortSingleTarget"
 value: {
  dps: 146800.03708
  tps: 91168.36413
 }
}
dps_results: {
 key: "TestAffliction-Settings-Human-preraid-Affliction Warlock-default-NoBuffs-25.0yards-LongMultiTarget"
 value: {
  dps: 54487.6246
  tps: 51184.41619
 }
}
dps_results: {
 key: "TestAffliction-Settings-Human-preraid-Affliction Warlock-default-NoBuffs-25.0yards-LongSingleTarget"
 value: {
  dps: 54487.6246
  tps: 39010.35147
 }
}
dps_results: {
 key: "TestAffliction-Settings-Human-preraid-Affliction Warlock-default-NoBuffs-25.0yards-ShortSingleTarget"
 value: {
  dps: 70417.41621
  tps: 45677.48504
 }
}
dps_results: {
 key: "TestAffliction-Settings-Orc-p1-Affliction Warlock-default-FullBuffs-25.0yards-LongMultiTarget"
 value: {
  dps: 145704.67735
  tps: 119114.67307
 }
}
dps_results: {
 key: "TestAffliction-Settings-Orc-p1-Affliction Warlock-default-FullBuffs-25.0yards-LongSingleTarget"
 value: {
  dps: 145704.67735
  tps: 103379.00054
 }
}
dps_results: {
 key: "TestAffliction-Settings-Orc-p1-Affliction Warlock-default-FullBuffs-25.0yards-ShortSingleTarget"
 value: {
  dps: 244234.37424
  tps: 158700.55911
 }
}
dps_results: {
 key: "TestAffliction-Settings-Orc-p1-Affliction Warlock-default-NoBuffs-25.0yards-LongMultiTarget"
 value: {
  dps: 92705.48508
  tps: 82707.30172
 }
}
dps_results: {
 key: "TestAffliction-Settings-Orc-p1-Affliction Warlock-default-NoBuffs-25.0yards-LongSingleTarget"
 value: {
  dps: 92705.48508
  tps: 67495.01236
 }
}
dps_results: {
 key: "TestAffliction-Settings-Orc-p1-Affliction Warlock-default-NoBuffs-25.0yards-ShortSingleTarget"
 value: {
  dps: 123751.96912
  tps: 83976.69517
 }
}
dps_results: {
 key: "TestAffliction-Settings-Orc-preraid-Affliction Warlock-default-FullBuffs-25.0yards-LongMultiTarget"
 value: {
  dps: 90381.23923
  tps: 76217.96267
 }
}
dps_results: {
 key: "TestAffliction-Settings-Orc-preraid-Affliction Warlock-default-FullBuffs-25.0yards-LongSingleTarget"
 value: {
  dps: 90381.23923
  tps: 62762.84549
 }
}
dps_results: {
 key: "TestAffliction-Settings-Orc-preraid-Affliction Warlock-default-FullBuffs-25.0yards-ShortSingleTarget"
 value: {
  dps: 152994.1021
  tps: 94659.28479
 }
}
dps_results: {
 key: "TestAffliction-Settings-Orc-preraid-Affliction Warlock-default-NoBuffs-25.0yards-LongMultiTarget"
 value: {
  dps: 55957.55366
  tps: 52175.88114
 }
}
dps_results: {
 key: "TestAffliction-Settings-Orc-preraid-Affliction Warlock-default-NoBuffs-25.0yards-LongSingleTarget"
 value: {
  dps: 55957.55366
  tps: 39901.53414
 }
}
dps_results: {
 key: "TestAffliction-Settings-Orc-preraid-Affliction Warlock-default-NoBuffs-25.0yards-ShortSingleTarget"
 value: {
  dps: 73358.42263
  tps: 47418.97949
 }
}
dps_results: {
 key: "TestAffliction-Settings-Troll-p1-Affliction Warlock-default-FullBuffs-25.0yards-LongMultiTarget"
 value: {
  dps: 149224.85956
  tps: 122209.43444
 }
}
dps_results: {
 key: "TestAffliction-Settings-Troll-p1-Affliction Warlock-default-FullBuffs-25.0yards-LongSingleTarget"
 value: {
  dps: 149224.85956
  tps: 105442.48985
 }
}
dps_results: {
 key: "TestAffliction-Settings-Troll-p1-Affliction Warlock-default-FullBuffs-25.0yards-ShortSingleTarget"
 value: {
  dps: 253958.905
  tps: 164699.95718
 }
}
dps_results: {
 key: "TestAffliction-Settings-Troll-p1-Affliction Warlock-default-NoBuffs-25.0yards-LongMultiTarget"
 value: {
  dps: 93703.91384
  tps: 84041.15266
 }
}
dps_results: {
 key: "TestAffliction-Settings-Troll-p1-Affliction Warlock-default-NoBuffs-25.0yards-LongSingleTarget"
 value: {
  dps: 93703.91384
  tps: 68225.72807
 }
}
dps_results: {
 key: "TestAffliction-Settings-Troll-p1-Affliction Warlock-default-NoBuffs-25.0yards-ShortSingleTarget"
 value: {
  dps: 127625.00035
  tps: 87450.40476
 }
}
dps_results: {
 key: "TestAffliction-Settings-Troll-preraid-Affliction Warlock-default-FullBuffs-25.0yards-LongMultiTarget"
 value: {
  dps: 92189.64585
  tps: 77753.0848
 }
}
dps_results: {
 key: "TestAffliction-Settings-Troll-preraid-Affliction Warlock-default-FullBuffs-25.0yards-LongSingleTarget"
 value: {
  dps: 92189.64585
  tps: 63662.8814
 }
}
dps_results: {
 key: "TestAffliction-Settings-Troll-preraid-Affliction Warlock-default-FullBuffs-25.0yards-ShortSingleTarget"
 value: {
  dps: 160335.20788
  tps: 99661.49651
 }
}
dps_results: {
 key: "TestAffliction-Settings-Troll-preraid-Affliction Warlock-default-NoBuffs-25.0yards-LongMultiTarget"
 value: {
  dps: 56170.56866
  tps: 52625.13399
 }
}
dps_results: {
 key: "TestAffliction-Settings-Troll-preraid-Affliction Warlock-default-NoBuffs-25.0yards-LongSingleTarget"
 value: {
  dps: 56170.56866
  tps: 40151.70702
 }
}
dps_results: {
 key: "TestAffliction-Settings-Troll-preraid-Affliction Warlock-default-NoBuffs-25.0yards-ShortSingleTarget"
 value: {
  dps: 76144.03418
  tps: 50478.77908
 }
}
dps_results: {
 key: "TestAffliction-SwitchInFrontOfTarget-Default"
 value: {
  dps: 89542.82961
  tps: 62768.75125
 }
}
