character_stats_results: {
 key: "TestDestruction-CharacterStats-Default"
 value: {
  final_stats: 157.5
  final_stats: 161.7
  final_stats: 18509.37
  final_stats: 16097.6025
  final_stats: 280
  final_stats: 4328
  final_stats: 1730
  final_stats: 1625
  final_stats: 774
  final_stats: 0
  final_stats: 0
  final_stats: 8883
  final_stats: 162.25
  final_stats: 0
  final_stats: 23362.46275
  final_stats: 0
  final_stats: 1054
  final_stats: 13312
  final_stats: 0
  final_stats: 446087.598
  final_stats: 300000
  final_stats: 108750
  final_stats: 12.72941
  final_stats: 15.00588
  final_stats: 10.50333
  final_stats: 15.9369
  final_stats: 0
 }
}
dps_results: {
 key: "TestDestruction-AllItems-AgilePrimalDiamond"
 value: {
  dps: 94943.49883
  tps: 70835.37367
 }
}
dps_results: {
 key: "TestDestruction-AllItems-AlacrityofXuen-103989"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-ArcaneBadgeoftheShieldwall-93347"
 value: {
  dps: 95592.1376
  tps: 71104.72998
 }
}
dps_results: {
 key: "TestDestruction-AllItems-ArrowflightMedallion-93258"
 value: {
  dps: 92264.77602
  tps: 68814.46126
 }
}
dps_results: {
 key: "TestDestruction-AllItems-AssuranceofConsequence-105472"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-AusterePrimalDiamond"
 value: {
  dps: 93510.18585
  tps: 69541.45391
 }
}
dps_results: {
 key: "TestDestruction-AllItems-BadJuju-96781"
 value: {
  dps: 92724.4652
  tps: 69444.77939
 }
}
dps_results: {
 key: "TestDestruction-AllItems-BadgeofKypariZar-84079"
 value: {
  dps: 91127.01014
  tps: 67977.204
 }
}
dps_results: {
 key: "TestDestruction-AllItems-BlossomofPureSnow-89081"
 value: {
  dps: 96662.85102
  tps: 71935.78655
 }
}
dps_results: {
 key: "TestDestruction-AllItems-BottleofInfiniteStars-87057"
 value: {
  dps: 91939.69887
  tps: 68723.81779
 }
}
dps_results: {
 key: "TestDestruction-AllItems-BraidofTenSongs-84072"
 value: {
  dps: 91127.01014
  tps: 67977.204
 }
}
dps_results: {
 key: "TestDestruction-AllItems-Brawler'sStatue-87571"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-BreathoftheHydra-96827"
 value: {
  dps: 102950.37803
  tps: 76883.77502
 }
}
dps_results: {
 key: "TestDestruction-AllItems-BroochofMunificentDeeds-87500"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-BrutalTalismanoftheShado-PanAssault-94508"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-BurningPrimalDiamond"
 value: {
  dps: 95891.96738
  tps: 71586.86898
 }
}
dps_results: {
 key: "TestDestruction-AllItems-CapacitivePrimalDiamond"
 value: {
  dps: 94077.68598
  tps: 70013.67398
 }
}
dps_results: {
 key: "TestDestruction-AllItems-CarbonicCarbuncle-81138"
 value: {
  dps: 91625.31066
  tps: 68268.1734
 }
}
dps_results: {
 key: "TestDestruction-AllItems-Cha-Ye'sEssenceofBrilliance-96888"
 value: {
  dps: 103388.46313
  tps: 77468.97591
 }
}
dps_results: {
 key: "TestDestruction-AllItems-CharmofTenSongs-84071"
 value: {
  dps: 92502.99209
  tps: 68788.64907
 }
}
dps_results: {
 key: "TestDestruction-AllItems-CommunalIdolofDestruction-101168"
 value: {
  dps: 93933.54604
  tps: 70036.81812
 }
}
dps_results: {
 key: "TestDestruction-AllItems-CommunalStoneofDestruction-101171"
 value: {
  dps: 96899.64665
  tps: 72503.93678
 }
}
dps_results: {
 key: "TestDestruction-AllItems-CommunalStoneofWisdom-101183"
 value: {
  dps: 94510.44717
  tps: 70295.85635
 }
}
dps_results: {
 key: "TestDestruction-AllItems-ContemplationofChi-Ji-103688"
 value: {
  dps: 95030.49536
  tps: 70699.7526
 }
}
dps_results: {
 key: "TestDestruction-AllItems-ContemplationofChi-Ji-103988"
 value: {
  dps: 97409.98616
  tps: 72601.65987
 }
}
dps_results: {
 key: "TestDestruction-AllItems-Coren'sColdChromiumCoaster-87574"
 value: {
  dps: 91427.66417
  tps: 68085.30889
 }
}
dps_results: {
 key: "TestDestruction-AllItems-CoreofDecency-87497"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-CourageousPrimalDiamond"
 value: {
  dps: 94974.10865
  tps: 70606.07423
 }
}
dps_results: {
 key: "TestDestruction-AllItems-CraftedDreadfulGladiator'sBadgeofConquest-93419"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-CraftedDreadfulGladiator'sBadgeofDominance-93600"
 value: {
  dps: 93684.70815
  tps: 69870.03373
 }
}
dps_results: {
 key: "TestDestruction-AllItems-CraftedDreadfulGladiator'sBadgeofVictory-93606"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-CraftedDreadfulGladiator'sEmblemofCruelty-93485"
 value: {
  dps: 91324.34153
  tps: 68066.29015
 }
}
dps_results: {
 key: "TestDestruction-AllItems-CraftedDreadfulGladiator'sEmblemofMeditation-93487"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-CraftedDreadfulGladiator'sEmblemofTenacity-93486"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-CraftedDreadfulGladiator'sInsigniaofConquest-93424"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-CraftedDreadfulGladiator'sInsigniaofDominance-93601"
 value: {
  dps: 95227.5319
  tps: 70916.34464
 }
}
dps_results: {
 key: "TestDestruction-AllItems-CraftedDreadfulGladiator'sInsigniaofVictory-93611"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-CraftedMalevolentGladiator'sBadgeofConquest-98755"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-CraftedMalevolentGladiator'sBadgeofDominance-98910"
 value: {
  dps: 94308.08711
  tps: 70299.5092
 }
}
dps_results: {
 key: "TestDestruction-AllItems-CraftedMalevolentGladiator'sBadgeofVictory-98912"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-CraftedMalevolentGladiator'sEmblemofCruelty-98811"
 value: {
  dps: 91562.01742
  tps: 68199.62493
 }
}
dps_results: {
 key: "TestDestruction-AllItems-CraftedMalevolentGladiator'sEmblemofMeditation-98813"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-CraftedMalevolentGladiator'sEmblemofTenacity-98812"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-CraftedMalevolentGladiator'sInsigniaofConquest-98760"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-CraftedMalevolentGladiator'sInsigniaofDominance-98911"
 value: {
  dps: 96399.24183
  tps: 71906.62647
 }
}
dps_results: {
 key: "TestDestruction-AllItems-CraftedMalevolentGladiator'sInsigniaofVictory-98917"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-CurseofHubris-102307"
 value: {
  dps: 93375.60189
  tps: 69737.55897
 }
}
dps_results: {
 key: "TestDestruction-AllItems-CurseofHubris-104649"
 value: {
  dps: 94193.26633
  tps: 70384.12137
 }
}
dps_results: {
 key: "TestDestruction-AllItems-CurseofHubris-104898"
 value: {
  dps: 93008.01556
  tps: 69559.69596
 }
}
dps_results: {
 key: "TestDestruction-AllItems-CurseofHubris-105147"
 value: {
  dps: 92762.69224
  tps: 69284.95967
 }
}
dps_results: {
 key: "TestDestruction-AllItems-CurseofHubris-105396"
 value: {
  dps: 93701.44654
  tps: 69920.22472
 }
}
dps_results: {
 key: "TestDestruction-AllItems-CurseofHubris-105645"
 value: {
  dps: 94313.35199
  tps: 70445.54633
 }
}
dps_results: {
 key: "TestDestruction-AllItems-CutstitcherMedallion-93255"
 value: {
  dps: 95030.49536
  tps: 70699.7526
 }
}
dps_results: {
 key: "TestDestruction-AllItems-Daelo'sFinalWords-87496"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-DarkglowEmbroidery(Rank3)-4893"
 value: {
  dps: 92678.82656
  tps: 69133.22352
 }
}
dps_results: {
 key: "TestDestruction-AllItems-DarkmistVortex-87172"
 value: {
  dps: 93052.72079
  tps: 69041.9886
 }
}
dps_results: {
 key: "TestDestruction-AllItems-DeadeyeBadgeoftheShieldwall-93346"
 value: {
  dps: 91704.03506
  tps: 68531.99373
 }
}
dps_results: {
 key: "TestDestruction-AllItems-DelicateVialoftheSanguinaire-96895"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-DestructivePrimalDiamond"
 value: {
  dps: 94293.49614
  tps: 70161.38554
 }
}
dps_results: {
 key: "TestDestruction-AllItems-DisciplineofXuen-103986"
 value: {
  dps: 93878.30112
  tps: 70503.11032
 }
}
dps_results: {
 key: "TestDestruction-AllItems-Dominator'sArcaneBadge-93342"
 value: {
  dps: 95592.1376
  tps: 71104.72998
 }
}
dps_results: {
 key: "TestDestruction-AllItems-Dominator'sDeadeyeBadge-93341"
 value: {
  dps: 91704.03506
  tps: 68531.99373
 }
}
dps_results: {
 key: "TestDestruction-AllItems-Dominator'sDurableBadge-93345"
 value: {
  dps: 91704.03506
  tps: 68531.99373
 }
}
dps_results: {
 key: "TestDestruction-AllItems-Dominator'sKnightlyBadge-93344"
 value: {
  dps: 91704.03506
  tps: 68531.99373
 }
}
dps_results: {
 key: "TestDestruction-AllItems-Dominator'sMendingBadge-93343"
 value: {
  dps: 93621.76637
  tps: 69686.00104
 }
}
dps_results: {
 key: "TestDestruction-AllItems-DreadfulGladiator'sBadgeofConquest-84344"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-DreadfulGladiator'sBadgeofDominance-84488"
 value: {
  dps: 93684.70815
  tps: 69870.03373
 }
}
dps_results: {
 key: "TestDestruction-AllItems-DreadfulGladiator'sBadgeofVictory-84490"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-DreadfulGladiator'sEmblemofCruelty-84399"
 value: {
  dps: 91324.34153
  tps: 68066.29015
 }
}
dps_results: {
 key: "TestDestruction-AllItems-DreadfulGladiator'sEmblemofMeditation-84401"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-DreadfulGladiator'sEmblemofTenacity-84400"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-DreadfulGladiator'sInsigniaofConquest-84349"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-DreadfulGladiator'sInsigniaofDominance-84489"
 value: {
  dps: 95236.33963
  tps: 71068.11415
 }
}
dps_results: {
 key: "TestDestruction-AllItems-DreadfulGladiator'sInsigniaofVictory-84495"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-DurableBadgeoftheShieldwall-93350"
 value: {
  dps: 91704.03506
  tps: 68531.99373
 }
}
dps_results: {
 key: "TestDestruction-AllItems-EffulgentPrimalDiamond"
 value: {
  dps: 93510.18585
  tps: 69541.45391
 }
}
dps_results: {
 key: "TestDestruction-AllItems-EmberPrimalDiamond"
 value: {
  dps: 94438.90752
  tps: 70275.2233
 }
}
dps_results: {
 key: "TestDestruction-AllItems-EmblemofKypariZar-84077"
 value: {
  dps: 91121.66747
  tps: 67959.28086
 }
}
dps_results: {
 key: "TestDestruction-AllItems-EmblemoftheCatacombs-83733"
 value: {
  dps: 91621.98579
  tps: 68120.32115
 }
}
dps_results: {
 key: "TestDestruction-AllItems-EmptyFruitBarrel-81133"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-EnchantWeapon-BloodyDancingSteel-5125"
 value: {
  dps: 92718.14121
  tps: 69372.91294
 }
}
dps_results: {
 key: "TestDestruction-AllItems-EnchantWeapon-Colossus-4445"
 value: {
  dps: 92718.14121
  tps: 69372.91294
 }
}
dps_results: {
 key: "TestDestruction-AllItems-EnchantWeapon-DancingSteel-4444"
 value: {
  dps: 92718.14121
  tps: 69372.91294
 }
}
dps_results: {
 key: "TestDestruction-AllItems-EnchantWeapon-River'sSong-4446"
 value: {
  dps: 92718.14121
  tps: 69372.91294
 }
}
dps_results: {
 key: "TestDestruction-AllItems-EnchantWeapon-SpiritofConquest-5124"
 value: {
  dps: 92718.14121
  tps: 69372.91294
 }
}
dps_results: {
 key: "TestDestruction-AllItems-EnchantWeapon-Windsong-4441"
 value: {
  dps: 94308.44122
  tps: 70481.16619
 }
}
dps_results: {
 key: "TestDestruction-AllItems-EnigmaticPrimalDiamond"
 value: {
  dps: 94293.49614
  tps: 70161.38554
 }
}
dps_results: {
 key: "TestDestruction-AllItems-EssenceofTerror-87175"
 value: {
  dps: 98881.57745
  tps: 73368.88368
 }
}
dps_results: {
 key: "TestDestruction-AllItems-EternalPrimalDiamond"
 value: {
  dps: 93510.18585
  tps: 69541.45391
 }
}
dps_results: {
 key: "TestDestruction-AllItems-EvilEyeofGalakras-105491"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-FabledFeatherofJi-Kun-96842"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-FearwurmBadge-84074"
 value: {
  dps: 91121.66747
  tps: 67959.28086
 }
}
dps_results: {
 key: "TestDestruction-AllItems-FearwurmRelic-84070"
 value: {
  dps: 92641.14437
  tps: 68894.80606
 }
}
dps_results: {
 key: "TestDestruction-AllItems-FelsoulIdolofDestruction-101263"
 value: {
  dps: 93746.4765
  tps: 69913.61127
 }
}
dps_results: {
 key: "TestDestruction-AllItems-FelsoulStoneofDestruction-101266"
 value: {
  dps: 97054.95574
  tps: 72645.896
 }
}
dps_results: {
 key: "TestDestruction-AllItems-Fen-Yu,FuryofXuen-102248"
 value: {
  dps: 94190.17202
  tps: 70273.56323
 }
}
dps_results: {
 key: "TestDestruction-AllItems-FlashfrozenResinGlobule-100951"
 value: {
  dps: 93380.45783
  tps: 69403.50773
 }
}
dps_results: {
 key: "TestDestruction-AllItems-FlashfrozenResinGlobule-81263"
 value: {
  dps: 93760.96104
  tps: 69666.64903
 }
}
dps_results: {
 key: "TestDestruction-AllItems-FlashingSteelTalisman-81265"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-FleetPrimalDiamond"
 value: {
  dps: 94170.39341
  tps: 70148.37135
 }
}
dps_results: {
 key: "TestDestruction-AllItems-ForlornPrimalDiamond"
 value: {
  dps: 94438.90752
  tps: 70275.2233
 }
}
dps_results: {
 key: "TestDestruction-AllItems-FortitudeoftheZandalari-94516"
 value: {
  dps: 92305.62924
  tps: 69059.99652
 }
}
dps_results: {
 key: "TestDestruction-AllItems-FortitudeoftheZandalari-95677"
 value: {
  dps: 91939.69887
  tps: 68723.81779
 }
}
dps_results: {
 key: "TestDestruction-AllItems-FortitudeoftheZandalari-96049"
 value: {
  dps: 92430.54523
  tps: 69174.75632
 }
}
dps_results: {
 key: "TestDestruction-AllItems-FortitudeoftheZandalari-96421"
 value: {
  dps: 92584.85321
  tps: 69316.51843
 }
}
dps_results: {
 key: "TestDestruction-AllItems-FortitudeoftheZandalari-96793"
 value: {
  dps: 92724.4652
  tps: 69444.77939
 }
}
dps_results: {
 key: "TestDestruction-AllItems-GazeoftheTwins-96915"
 value: {
  dps: 92555.83625
  tps: 69245.65423
 }
}
dps_results: {
 key: "TestDestruction-AllItems-Gerp'sPerfectArrow-87495"
 value: {
  dps: 91133.313
  tps: 67970.92638
 }
}
dps_results: {
 key: "TestDestruction-AllItems-Gong-Lu,StrengthofXuen-102249"
 value: {
  dps: 94190.17202
  tps: 70273.56323
 }
}
dps_results: {
 key: "TestDestruction-AllItems-GrievousGladiator'sBadgeofConquest-100195"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-GrievousGladiator'sBadgeofConquest-100603"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-GrievousGladiator'sBadgeofConquest-102856"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-GrievousGladiator'sBadgeofConquest-103145"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-GrievousGladiator'sBadgeofDominance-100490"
 value: {
  dps: 96430.25217
  tps: 71842.06659
 }
}
dps_results: {
 key: "TestDestruction-AllItems-GrievousGladiator'sBadgeofDominance-100576"
 value: {
  dps: 96430.25217
  tps: 71842.06659
 }
}
dps_results: {
 key: "TestDestruction-AllItems-GrievousGladiator'sBadgeofDominance-102830"
 value: {
  dps: 96430.25217
  tps: 71842.06659
 }
}
dps_results: {
 key: "TestDestruction-AllItems-GrievousGladiator'sBadgeofDominance-103308"
 value: {
  dps: 96430.25217
  tps: 71842.06659
 }
}
dps_results: {
 key: "TestDestruction-AllItems-GrievousGladiator'sBadgeofVictory-100500"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-GrievousGladiator'sBadgeofVictory-100579"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-GrievousGladiator'sBadgeofVictory-102833"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-GrievousGladiator'sBadgeofVictory-103314"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-GrievousGladiator'sEmblemofCruelty-100305"
 value: {
  dps: 92035.07798
  tps: 68543.54575
 }
}
dps_results: {
 key: "TestDestruction-AllItems-GrievousGladiator'sEmblemofCruelty-100626"
 value: {
  dps: 92035.07798
  tps: 68543.54575
 }
}
dps_results: {
 key: "TestDestruction-AllItems-GrievousGladiator'sEmblemofCruelty-102877"
 value: {
  dps: 92035.07798
  tps: 68543.54575
 }
}
dps_results: {
 key: "TestDestruction-AllItems-GrievousGladiator'sEmblemofCruelty-103210"
 value: {
  dps: 92035.07798
  tps: 68543.54575
 }
}
dps_results: {
 key: "TestDestruction-AllItems-GrievousGladiator'sEmblemofMeditation-100307"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-GrievousGladiator'sEmblemofMeditation-100559"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-GrievousGladiator'sEmblemofMeditation-102813"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-GrievousGladiator'sEmblemofMeditation-103212"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-GrievousGladiator'sEmblemofTenacity-100306"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-GrievousGladiator'sEmblemofTenacity-100652"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-GrievousGladiator'sEmblemofTenacity-102903"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-GrievousGladiator'sEmblemofTenacity-103211"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-GrievousGladiator'sInsigniaofConquest-103150"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-GrievousGladiator'sInsigniaofDominance-103309"
 value: {
  dps: 99615.16161
  tps: 74234.61796
 }
}
dps_results: {
 key: "TestDestruction-AllItems-GrievousGladiator'sInsigniaofVictory-103319"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-Hawkmaster'sTalon-89082"
 value: {
  dps: 92322.66121
  tps: 68635.78789
 }
}
dps_results: {
 key: "TestDestruction-AllItems-Heart-LesionDefenderIdol-100999"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-Heart-LesionDefenderStone-101002"
 value: {
  dps: 92375.393
  tps: 69131.97857
 }
}
dps_results: {
 key: "TestDestruction-AllItems-Heart-LesionIdolofBattle-100991"
 value: {
  dps: 91677.52866
  tps: 68360.93446
 }
}
dps_results: {
 key: "TestDestruction-AllItems-Heart-LesionStoneofBattle-100990"
 value: {
  dps: 92409.00154
  tps: 69167.52948
 }
}
dps_results: {
 key: "TestDestruction-AllItems-HeartofFire-81181"
 value: {
  dps: 91394.47732
  tps: 68222.925
 }
}
dps_results: {
 key: "TestDestruction-AllItems-HeartwarmerMedallion-93260"
 value: {
  dps: 95030.49536
  tps: 70699.7526
 }
}
dps_results: {
 key: "TestDestruction-AllItems-HelmbreakerMedallion-93261"
 value: {
  dps: 92264.77602
  tps: 68814.46126
 }
}
dps_results: {
 key: "TestDestruction-AllItems-Horridon'sLastGasp-96757"
 value: {
  dps: 97773.88915
  tps: 72858.47844
 }
}
dps_results: {
 key: "TestDestruction-AllItems-ImpassivePrimalDiamond"
 value: {
  dps: 94293.49614
  tps: 70161.38554
 }
}
dps_results: {
 key: "TestDestruction-AllItems-IndomitablePrimalDiamond"
 value: {
  dps: 93510.18585
  tps: 69541.45391
 }
}
dps_results: {
 key: "TestDestruction-AllItems-InscribedBagofHydra-Spawn-96828"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-InsigniaofKypariZar-84078"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-IronBellyWok-89083"
 value: {
  dps: 92322.66121
  tps: 68635.78789
 }
}
dps_results: {
 key: "TestDestruction-AllItems-IronProtectorTalisman-85181"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-JadeBanditFigurine-86043"
 value: {
  dps: 92322.66121
  tps: 68635.78789
 }
}
dps_results: {
 key: "TestDestruction-AllItems-JadeBanditFigurine-86772"
 value: {
  dps: 91958.11715
  tps: 68499.32476
 }
}
dps_results: {
 key: "TestDestruction-AllItems-JadeCharioteerFigurine-86042"
 value: {
  dps: 92322.66121
  tps: 68635.78789
 }
}
dps_results: {
 key: "TestDestruction-AllItems-JadeCharioteerFigurine-86771"
 value: {
  dps: 91958.11715
  tps: 68499.32476
 }
}
dps_results: {
 key: "TestDestruction-AllItems-JadeCourtesanFigurine-86045"
 value: {
  dps: 94759.86403
  tps: 70493.2811
 }
}
dps_results: {
 key: "TestDestruction-AllItems-JadeCourtesanFigurine-86774"
 value: {
  dps: 94214.84365
  tps: 70099.38662
 }
}
dps_results: {
 key: "TestDestruction-AllItems-JadeMagistrateFigurine-86044"
 value: {
  dps: 96662.85102
  tps: 71935.78655
 }
}
dps_results: {
 key: "TestDestruction-AllItems-JadeMagistrateFigurine-86773"
 value: {
  dps: 95957.21248
  tps: 71491.52654
 }
}
dps_results: {
 key: "TestDestruction-AllItems-JadeWarlordFigurine-86046"
 value: {
  dps: 92224.63906
  tps: 69018.53662
 }
}
dps_results: {
 key: "TestDestruction-AllItems-JadeWarlordFigurine-86775"
 value: {
  dps: 91987.42371
  tps: 68796.84136
 }
}
dps_results: {
 key: "TestDestruction-AllItems-Ji-Kun'sRisingWinds-96843"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-KnightlyBadgeoftheShieldwall-93349"
 value: {
  dps: 91704.03506
  tps: 68531.99373
 }
}
dps_results: {
 key: "TestDestruction-AllItems-KnotofTenSongs-84073"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-Kor'kronBookofHurting-92785"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-Lao-Chin'sLiquidCourage-89079"
 value: {
  dps: 92224.63906
  tps: 69018.53662
 }
}
dps_results: {
 key: "TestDestruction-AllItems-LeiShen'sFinalOrders-87072"
 value: {
  dps: 92374.1404
  tps: 68729.30616
 }
}
dps_results: {
 key: "TestDestruction-AllItems-LessonsoftheDarkmaster-81268"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-LightdrinkerIdolofRage-101200"
 value: {
  dps: 91650.1877
  tps: 68457.84507
 }
}
dps_results: {
 key: "TestDestruction-AllItems-LightdrinkerStoneofRage-101203"
 value: {
  dps: 92491.37733
  tps: 69239.25423
 }
}
dps_results: {
 key: "TestDestruction-AllItems-LightoftheCosmos-87065"
 value: {
  dps: 99231.40006
  tps: 74128.35011
 }
}
dps_results: {
 key: "TestDestruction-AllItems-LordBlastington'sScopeofDoom-4699"
 value: {
  dps: 92718.14121
  tps: 69372.91294
 }
}
dps_results: {
 key: "TestDestruction-AllItems-MalevolentGladiator'sBadgeofConquest-84934"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-MalevolentGladiator'sBadgeofConquest-91452"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-MalevolentGladiator'sBadgeofDominance-84940"
 value: {
  dps: 94570.46347
  tps: 70498.39662
 }
}
dps_results: {
 key: "TestDestruction-AllItems-MalevolentGladiator'sBadgeofDominance-91753"
 value: {
  dps: 94308.08711
  tps: 70299.5092
 }
}
dps_results: {
 key: "TestDestruction-AllItems-MalevolentGladiator'sBadgeofVictory-84942"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-MalevolentGladiator'sBadgeofVictory-91763"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-MalevolentGladiator'sEmblemofCruelty-84936"
 value: {
  dps: 91677.52866
  tps: 68360.93446
 }
}
dps_results: {
 key: "TestDestruction-AllItems-MalevolentGladiator'sEmblemofCruelty-91562"
 value: {
  dps: 91562.01742
  tps: 68199.62493
 }
}
dps_results: {
 key: "TestDestruction-AllItems-MalevolentGladiator'sEmblemofMeditation-84939"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-MalevolentGladiator'sEmblemofMeditation-91564"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-MalevolentGladiator'sEmblemofTenacity-84938"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-MalevolentGladiator'sEmblemofTenacity-91563"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-MalevolentGladiator'sInsigniaofConquest-91457"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-MalevolentGladiator'sInsigniaofDominance-91754"
 value: {
  dps: 96395.90055
  tps: 71885.94104
 }
}
dps_results: {
 key: "TestDestruction-AllItems-MalevolentGladiator'sInsigniaofVictory-91768"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-MarkoftheCatacombs-83731"
 value: {
  dps: 92299.79376
  tps: 69038.5318
 }
}
dps_results: {
 key: "TestDestruction-AllItems-MarkoftheHardenedGrunt-92783"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-MedallionofMystifyingVapors-93257"
 value: {
  dps: 92364.89047
  tps: 69149.61193
 }
}
dps_results: {
 key: "TestDestruction-AllItems-MedallionoftheCatacombs-83734"
 value: {
  dps: 91056.46935
  tps: 67912.39847
 }
}
dps_results: {
 key: "TestDestruction-AllItems-MendingBadgeoftheShieldwall-93348"
 value: {
  dps: 93621.76637
  tps: 69686.00104
 }
}
dps_results: {
 key: "TestDestruction-AllItems-MirrorScope-4700"
 value: {
  dps: 92718.14121
  tps: 69372.91294
 }
}
dps_results: {
 key: "TestDestruction-AllItems-MistdancerDefenderIdol-101089"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-MistdancerDefenderStone-101087"
 value: {
  dps: 92579.19863
  tps: 69322.74222
 }
}
dps_results: {
 key: "TestDestruction-AllItems-MistdancerIdolofRage-101113"
 value: {
  dps: 91650.1877
  tps: 68457.84507
 }
}
dps_results: {
 key: "TestDestruction-AllItems-MistdancerStoneofRage-101117"
 value: {
  dps: 92346.60439
  tps: 69095.69298
 }
}
dps_results: {
 key: "TestDestruction-AllItems-MistdancerStoneofWisdom-101107"
 value: {
  dps: 94510.44717
  tps: 70295.85635
 }
}
dps_results: {
 key: "TestDestruction-AllItems-MithrilWristwatch-87572"
 value: {
  dps: 92542.94818
  tps: 68923.26695
 }
}
dps_results: {
 key: "TestDestruction-AllItems-MountainsageIdolofDestruction-101069"
 value: {
  dps: 93762.21135
  tps: 69866.27551
 }
}
dps_results: {
 key: "TestDestruction-AllItems-MountainsageStoneofDestruction-101072"
 value: {
  dps: 96911.37659
  tps: 72524.05874
 }
}
dps_results: {
 key: "TestDestruction-AllItems-OathswornDefenderIdol-101303"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-OathswornDefenderStone-101306"
 value: {
  dps: 92150.13841
  tps: 68905.45138
 }
}
dps_results: {
 key: "TestDestruction-AllItems-OathswornIdolofBattle-101295"
 value: {
  dps: 91677.52866
  tps: 68360.93446
 }
}
dps_results: {
 key: "TestDestruction-AllItems-OathswornStoneofBattle-101294"
 value: {
  dps: 92482.88205
  tps: 69235.48528
 }
}
dps_results: {
 key: "TestDestruction-AllItems-PhaseFingers-4697"
 value: {
  dps: 95623.561
  tps: 71339.94143
 }
}
dps_results: {
 key: "TestDestruction-AllItems-PouchofWhiteAsh-103639"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-PowerfulPrimalDiamond"
 value: {
  dps: 93510.18585
  tps: 69541.45391
 }
}
dps_results: {
 key: "TestDestruction-AllItems-PriceofProgress-81266"
 value: {
  dps: 93774.24272
  tps: 69800.25981
 }
}
dps_results: {
 key: "TestDestruction-AllItems-PridefulGladiator'sBadgeofConquest-102659"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-PridefulGladiator'sBadgeofConquest-103342"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-PridefulGladiator'sBadgeofDominance-102633"
 value: {
  dps: 98553.34197
  tps: 73479.84045
 }
}
dps_results: {
 key: "TestDestruction-AllItems-PridefulGladiator'sBadgeofDominance-103505"
 value: {
  dps: 98553.34197
  tps: 73479.84045
 }
}
dps_results: {
 key: "TestDestruction-AllItems-PridefulGladiator'sBadgeofVictory-102636"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-PridefulGladiator'sBadgeofVictory-103511"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-PridefulGladiator'sEmblemofCruelty-102680"
 value: {
  dps: 93165.15288
  tps: 69522.12526
 }
}
dps_results: {
 key: "TestDestruction-AllItems-PridefulGladiator'sEmblemofCruelty-103407"
 value: {
  dps: 93165.15288
  tps: 69522.12526
 }
}
dps_results: {
 key: "TestDestruction-AllItems-PridefulGladiator'sEmblemofMeditation-102616"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-PridefulGladiator'sEmblemofMeditation-103409"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-PridefulGladiator'sEmblemofTenacity-102706"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-PridefulGladiator'sEmblemofTenacity-103408"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-PridefulGladiator'sInsigniaofConquest-103347"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-PridefulGladiator'sInsigniaofDominance-103506"
 value: {
  dps: 102570.47462
  tps: 76658.81095
 }
}
dps_results: {
 key: "TestDestruction-AllItems-PridefulGladiator'sInsigniaofVictory-103516"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-Primordius'TalismanofRage-96873"
 value: {
  dps: 92803.07326
  tps: 69237.29913
 }
}
dps_results: {
 key: "TestDestruction-AllItems-Qian-Le,CourageofNiuzao-102245"
 value: {
  dps: 92509.11372
  tps: 69112.72698
 }
}
dps_results: {
 key: "TestDestruction-AllItems-Qian-Ying,FortitudeofNiuzao-102250"
 value: {
  dps: 91209.77655
  tps: 68216.38026
 }
}
dps_results: {
 key: "TestDestruction-AllItems-Qin-xi'sPolarizingSeal-87075"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-RegaliaoftheHornedNightmare"
 value: {
  dps: 105964.16682
  tps: 78772.32028
 }
}
dps_results: {
 key: "TestDestruction-AllItems-RegaliaoftheThousandfoldHells"
 value: {
  dps: 108047.65468
  tps: 80949.03618
 }
}
dps_results: {
 key: "TestDestruction-AllItems-RelicofChi-Ji-79330"
 value: {
  dps: 95050.57749
  tps: 70714.78838
 }
}
dps_results: {
 key: "TestDestruction-AllItems-RelicofKypariZar-84075"
 value: {
  dps: 92617.70855
  tps: 69028.06032
 }
}
dps_results: {
 key: "TestDestruction-AllItems-RelicofNiuzao-79329"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-RelicofXuen-79327"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-RelicofXuen-79328"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-Renataki'sSoulCharm-96741"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-ResolveofNiuzao-103690"
 value: {
  dps: 91842.70528
  tps: 68634.71018
 }
}
dps_results: {
 key: "TestDestruction-AllItems-ResolveofNiuzao-103990"
 value: {
  dps: 92584.85321
  tps: 69316.51843
 }
}
dps_results: {
 key: "TestDestruction-AllItems-ReverberatingPrimalDiamond"
 value: {
  dps: 94943.49883
  tps: 70835.37367
 }
}
dps_results: {
 key: "TestDestruction-AllItems-RevitalizingPrimalDiamond"
 value: {
  dps: 94943.49883
  tps: 70835.37367
 }
}
dps_results: {
 key: "TestDestruction-AllItems-SI:7Operative'sManual-92784"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-ScrollofReveredAncestors-89080"
 value: {
  dps: 94759.86403
  tps: 70493.2811
 }
}
dps_results: {
 key: "TestDestruction-AllItems-SearingWords-81267"
 value: {
  dps: 91390.08713
  tps: 68080.26078
 }
}
dps_results: {
 key: "TestDestruction-AllItems-Sha-SkinRegalia"
 value: {
  dps: 101112.26699
  tps: 75883.32302
 }
}
dps_results: {
 key: "TestDestruction-AllItems-ShadowflameRegalia"
 value: {
  dps: 71672.36607
  tps: 52830.16035
 }
}
dps_results: {
 key: "TestDestruction-AllItems-Shock-ChargerMedallion-93259"
 value: {
  dps: 98121.51118
  tps: 73020.98984
 }
}
dps_results: {
 key: "TestDestruction-AllItems-SigilofCompassion-83736"
 value: {
  dps: 91056.46935
  tps: 67912.39847
 }
}
dps_results: {
 key: "TestDestruction-AllItems-SigilofDevotion-83740"
 value: {
  dps: 91074.54615
  tps: 67906.35073
 }
}
dps_results: {
 key: "TestDestruction-AllItems-SigilofFidelity-83737"
 value: {
  dps: 92758.09736
  tps: 69072.35716
 }
}
dps_results: {
 key: "TestDestruction-AllItems-SigilofGrace-83738"
 value: {
  dps: 91056.46935
  tps: 67912.39847
 }
}
dps_results: {
 key: "TestDestruction-AllItems-SigilofKypariZar-84076"
 value: {
  dps: 92367.53754
  tps: 69108.971
 }
}
dps_results: {
 key: "TestDestruction-AllItems-SigilofPatience-83739"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-SigiloftheCatacombs-83732"
 value: {
  dps: 92333.23451
  tps: 68613.66114
 }
}
dps_results: {
 key: "TestDestruction-AllItems-SinisterPrimalDiamond"
 value: {
  dps: 94077.68598
  tps: 70013.67398
 }
}
dps_results: {
 key: "TestDestruction-AllItems-SkullrenderMedallion-93256"
 value: {
  dps: 92264.77602
  tps: 68814.46126
 }
}
dps_results: {
 key: "TestDestruction-AllItems-SpiritsoftheSun-87163"
 value: {
  dps: 95672.75889
  tps: 71197.88084
 }
}
dps_results: {
 key: "TestDestruction-AllItems-SpringrainIdolofDestruction-101023"
 value: {
  dps: 94077.23564
  tps: 70026.37668
 }
}
dps_results: {
 key: "TestDestruction-AllItems-SpringrainIdolofRage-101009"
 value: {
  dps: 91650.1877
  tps: 68457.84507
 }
}
dps_results: {
 key: "TestDestruction-AllItems-SpringrainStoneofDestruction-101026"
 value: {
  dps: 96997.44889
  tps: 72596.9422
 }
}
dps_results: {
 key: "TestDestruction-AllItems-SpringrainStoneofRage-101012"
 value: {
  dps: 92682.06677
  tps: 69426.605
 }
}
dps_results: {
 key: "TestDestruction-AllItems-SpringrainStoneofWisdom-101041"
 value: {
  dps: 94510.44717
  tps: 70295.85635
 }
}
dps_results: {
 key: "TestDestruction-AllItems-Static-Caster'sMedallion-93254"
 value: {
  dps: 98121.51118
  tps: 73020.98984
 }
}
dps_results: {
 key: "TestDestruction-AllItems-SteadfastFootman'sMedallion-92782"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-SteadfastTalismanoftheShado-PanAssault-94507"
 value: {
  dps: 92305.62924
  tps: 69059.99652
 }
}
dps_results: {
 key: "TestDestruction-AllItems-StreamtalkerIdolofDestruction-101222"
 value: {
  dps: 94144.2368
  tps: 70163.6053
 }
}
dps_results: {
 key: "TestDestruction-AllItems-StreamtalkerIdolofRage-101217"
 value: {
  dps: 91650.1877
  tps: 68457.84507
 }
}
dps_results: {
 key: "TestDestruction-AllItems-StreamtalkerStoneofDestruction-101225"
 value: {
  dps: 96743.03347
  tps: 72366.7722
 }
}
dps_results: {
 key: "TestDestruction-AllItems-StreamtalkerStoneofRage-101220"
 value: {
  dps: 92323.91133
  tps: 69098.2541
 }
}
dps_results: {
 key: "TestDestruction-AllItems-StreamtalkerStoneofWisdom-101250"
 value: {
  dps: 94510.44717
  tps: 70295.85635
 }
}
dps_results: {
 key: "TestDestruction-AllItems-StuffofNightmares-87160"
 value: {
  dps: 92060.20606
  tps: 68834.52725
 }
}
dps_results: {
 key: "TestDestruction-AllItems-SunsoulDefenderIdol-101160"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-SunsoulDefenderStone-101163"
 value: {
  dps: 92451.90136
  tps: 69200.75264
 }
}
dps_results: {
 key: "TestDestruction-AllItems-SunsoulIdolofBattle-101152"
 value: {
  dps: 91677.52866
  tps: 68360.93446
 }
}
dps_results: {
 key: "TestDestruction-AllItems-SunsoulStoneofBattle-101151"
 value: {
  dps: 92413.13563
  tps: 69159.4059
 }
}
dps_results: {
 key: "TestDestruction-AllItems-SunsoulStoneofWisdom-101138"
 value: {
  dps: 94510.44717
  tps: 70295.85635
 }
}
dps_results: {
 key: "TestDestruction-AllItems-SwordguardEmbroidery(Rank3)-4894"
 value: {
  dps: 92678.82656
  tps: 69133.22352
 }
}
dps_results: {
 key: "TestDestruction-AllItems-SymboloftheCatacombs-83735"
 value: {
  dps: 91056.46935
  tps: 67912.39847
 }
}
dps_results: {
 key: "TestDestruction-AllItems-SynapseSprings(MarkII)-4898"
 value: {
  dps: 97155.91612
  tps: 72602.50784
 }
}
dps_results: {
 key: "TestDestruction-AllItems-TalismanofBloodlust-96864"
 value: {
  dps: 93402.02671
  tps: 69560.68373
 }
}
dps_results: {
 key: "TestDestruction-AllItems-TerrorintheMists-87167"
 value: {
  dps: 93009.2851
  tps: 69489.15664
 }
}
dps_results: {
 key: "TestDestruction-AllItems-Thousand-YearPickledEgg-87573"
 value: {
  dps: 93992.80379
  tps: 69937.59045
 }
}
dps_results: {
 key: "TestDestruction-AllItems-TrailseekerIdolofRage-101054"
 value: {
  dps: 91650.1877
  tps: 68457.84507
 }
}
dps_results: {
 key: "TestDestruction-AllItems-TrailseekerStoneofRage-101057"
 value: {
  dps: 92478.40111
  tps: 69234.30297
 }
}
dps_results: {
 key: "TestDestruction-AllItems-TyrannicalGladiator'sBadgeofConquest-100043"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-TyrannicalGladiator'sBadgeofConquest-91099"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-TyrannicalGladiator'sBadgeofConquest-94373"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-TyrannicalGladiator'sBadgeofConquest-99772"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-TyrannicalGladiator'sBadgeofDominance-100016"
 value: {
  dps: 95087.15051
  tps: 70837.02913
 }
}
dps_results: {
 key: "TestDestruction-AllItems-TyrannicalGladiator'sBadgeofDominance-91400"
 value: {
  dps: 95087.15051
  tps: 70837.02913
 }
}
dps_results: {
 key: "TestDestruction-AllItems-TyrannicalGladiator'sBadgeofDominance-94346"
 value: {
  dps: 95087.15051
  tps: 70837.02913
 }
}
dps_results: {
 key: "TestDestruction-AllItems-TyrannicalGladiator'sBadgeofDominance-99937"
 value: {
  dps: 95087.15051
  tps: 70837.02913
 }
}
dps_results: {
 key: "TestDestruction-AllItems-TyrannicalGladiator'sBadgeofVictory-100019"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-TyrannicalGladiator'sBadgeofVictory-91410"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-TyrannicalGladiator'sBadgeofVictory-94349"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-TyrannicalGladiator'sBadgeofVictory-99943"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-TyrannicalGladiator'sEmblemofCruelty-100066"
 value: {
  dps: 91945.09356
  tps: 68674.37108
 }
}
dps_results: {
 key: "TestDestruction-AllItems-TyrannicalGladiator'sEmblemofCruelty-91209"
 value: {
  dps: 91945.09356
  tps: 68674.37108
 }
}
dps_results: {
 key: "TestDestruction-AllItems-TyrannicalGladiator'sEmblemofCruelty-94396"
 value: {
  dps: 91945.09356
  tps: 68674.37108
 }
}
dps_results: {
 key: "TestDestruction-AllItems-TyrannicalGladiator'sEmblemofCruelty-99838"
 value: {
  dps: 91945.09356
  tps: 68674.37108
 }
}
dps_results: {
 key: "TestDestruction-AllItems-TyrannicalGladiator'sEmblemofMeditation-91211"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-TyrannicalGladiator'sEmblemofMeditation-94329"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-TyrannicalGladiator'sEmblemofMeditation-99840"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-TyrannicalGladiator'sEmblemofMeditation-99990"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-TyrannicalGladiator'sEmblemofTenacity-100092"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-TyrannicalGladiator'sEmblemofTenacity-91210"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-TyrannicalGladiator'sEmblemofTenacity-94422"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-TyrannicalGladiator'sEmblemofTenacity-99839"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-TyrannicalGladiator'sInsigniaofConquest-100026"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-TyrannicalGladiator'sInsigniaofDominance-100152"
 value: {
  dps: 98032.35459
  tps: 73228.14552
 }
}
dps_results: {
 key: "TestDestruction-AllItems-TyrannicalGladiator'sInsigniaofVictory-100085"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-TyrannicalPrimalDiamond"
 value: {
  dps: 93510.18585
  tps: 69541.45391
 }
}
dps_results: {
 key: "TestDestruction-AllItems-UnerringVisionofLeiShen-96930"
 value: {
  dps: 101817.51857
  tps: 76393.10774
 }
}
dps_results: {
 key: "TestDestruction-AllItems-VaporshieldMedallion-93262"
 value: {
  dps: 92364.89047
  tps: 69149.61193
 }
}
dps_results: {
 key: "TestDestruction-AllItems-VialofDragon'sBlood-87063"
 value: {
  dps: 91939.69887
  tps: 68723.81779
 }
}
dps_results: {
 key: "TestDestruction-AllItems-VialofIchorousBlood-100963"
 value: {
  dps: 93383.94701
  tps: 69511.62628
 }
}
dps_results: {
 key: "TestDestruction-AllItems-VialofIchorousBlood-81264"
 value: {
  dps: 93774.24272
  tps: 69800.25981
 }
}
dps_results: {
 key: "TestDestruction-AllItems-ViciousTalismanoftheShado-PanAssault-94511"
 value: {
  dps: 90149.72623
  tps: 67079.37729
 }
}
dps_results: {
 key: "TestDestruction-AllItems-VolatileTalismanoftheShado-PanAssault-94510"
 value: {
  dps: 99024.54364
  tps: 73571.41658
 }
}
dps_results: {
 key: "TestDestruction-AllItems-WindsweptPages-81125"
 value: {
  dps: 91903.04483
  tps: 68295.0629
 }
}
dps_results: {
 key: "TestDestruction-AllItems-WoundripperMedallion-93253"
 value: {
  dps: 92264.77602
  tps: 68814.46126
 }
}
dps_results: {
 key: "TestDestruction-AllItems-Wushoolay'sFinalChoice-96785"
 value: {
  dps: 120263.50961
  tps: 89841.83155
 }
}
dps_results: {
 key: "TestDestruction-AllItems-Xing-Ho,BreathofYu'lon-102246"
 value: {
  dps: 121001.68774
  tps: 95371.61165
 }
}
dps_results: {
 key: "TestDestruction-AllItems-Yu'lon'sBite-103987"
 value: {
  dps: 101906.76545
  tps: 76090.43082
 }
}
dps_results: {
 key: "TestDestruction-AllItems-ZenAlchemistStone-75274"
 value: {
  dps: 96954.47669
  tps: 72475.14317
 }
}
dps_results: {
 key: "TestDestruction-Average-Default"
 value: {
  dps: 99077.87687
  tps: 74063.8202
 }
}
dps_results: {
 key: "TestDestruction-Settings-Goblin-p1-prebis-Destruction Warlock-default-FullBuffs-25.0yards-LongMultiTarget"
 value: {
  dps: 220435.79698
  tps: 188537.41494
 }
}
dps_results: {
 key: "TestDestruction-Settings-Goblin-p1-prebis-Destruction Warlock-default-FullBuffs-25.0yards-LongSingleTarget"
 value: {
  dps: 96571.15526
  tps: 72401.06201
 }
}
dps_results: {
 key: "TestDestruction-Settings-Goblin-p1-prebis-Destruction Warlock-default-FullBuffs-25.0yards-ShortSingleTarget"
 value: {
  dps: 150846.76758
  tps: 101106.54192
 }
}
dps_results: {
 key: "TestDestruction-Settings-Goblin-p1-prebis-Destruction Warlock-default-NoBuffs-25.0yards-LongMultiTarget"
 value: {
  dps: 143334.40403
  tps: 123609.45091
 }
}
dps_results: {
 key: "TestDestruction-Settings-Goblin-p1-prebis-Destruction Warlock-default-NoBuffs-25.0yards-LongSingleTarget"
 value: {
  dps: 61110.026
  tps: 45397.98025
 }
}
dps_results: {
 key: "TestDestruction-Settings-Goblin-p1-prebis-Destruction Warlock-default-NoBuffs-25.0yards-ShortSingleTarget"
 value: {
  dps: 80765.12299
  tps: 55000.81711
 }
}
dps_results: {
 key: "TestDestruction-Settings-Human-p1-prebis-Destruction Warlock-default-FullBuffs-25.0yards-LongMultiTarget"
 value: {
  dps: 219988.26917
  tps: 189089.84898
 }
}
dps_results: {
 key: "TestDestruction-Settings-Human-p1-prebis-Destruction Warlock-default-FullBuffs-25.0yards-LongSingleTarget"
 value: {
  dps: 95210.53865
  tps: 71361.25088
 }
}
dps_results: {
 key: "TestDestruction-Settings-Human-p1-prebis-Destruction Warlock-default-FullBuffs-25.0yards-ShortSingleTarget"
 value: {
  dps: 150226.27101
  tps: 100824.6239
 }
}
dps_results: {
 key: "TestDestruction-Settings-Human-p1-prebis-Destruction Warlock-default-NoBuffs-25.0yards-LongMultiTarget"
 value: {
  dps: 144609.93404
  tps: 125847.71194
 }
}
dps_results: {
 key: "TestDestruction-Settings-Human-p1-prebis-Destruction Warlock-default-NoBuffs-25.0yards-LongSingleTarget"
 value: {
  dps: 60788.52173
  tps: 45186.00585
 }
}
dps_results: {
 key: "TestDestruction-Settings-Human-p1-prebis-Destruction Warlock-default-NoBuffs-25.0yards-ShortSingleTarget"
 value: {
  dps: 80519.45801
  tps: 54980.15559
 }
}
dps_results: {
 key: "TestDestruction-Settings-Orc-p1-prebis-Destruction Warlock-default-FullBuffs-25.0yards-LongMultiTarget"
 value: {
  dps: 224258.61684
  tps: 192248.98656
 }
}
dps_results: {
 key: "TestDestruction-Settings-Orc-p1-prebis-Destruction Warlock-default-FullBuffs-25.0yards-LongSingleTarget"
 value: {
  dps: 97429.96378
  tps: 72854.81469
 }
}
dps_results: {
 key: "TestDestruction-Settings-Orc-p1-prebis-Destruction Warlock-default-FullBuffs-25.0yards-ShortSingleTarget"
 value: {
  dps: 155014.87416
  tps: 103699.05846
 }
}
dps_results: {
 key: "TestDestruction-Settings-Orc-p1-prebis-Destruction Warlock-default-NoBuffs-25.0yards-LongMultiTarget"
 value: {
  dps: 147593.20647
  tps: 127900.20592
 }
}
dps_results: {
 key: "TestDestruction-Settings-Orc-p1-prebis-Destruction Warlock-default-NoBuffs-25.0yards-LongSingleTarget"
 value: {
  dps: 62323.54472
  tps: 46222.28842
 }
}
dps_results: {
 key: "TestDestruction-Settings-Orc-p1-prebis-Destruction Warlock-default-NoBuffs-25.0yards-ShortSingleTarget"
 value: {
  dps: 83261.02636
  tps: 56656.28658
 }
}
dps_results: {
 key: "TestDestruction-Settings-Troll-p1-prebis-Destruction Warlock-default-FullBuffs-25.0yards-LongMultiTarget"
 value: {
  dps: 221818.03087
  tps: 187801.60126
 }
}
dps_results: {
 key: "TestDestruction-Settings-Troll-p1-prebis-Destruction Warlock-default-FullBuffs-25.0yards-LongSingleTarget"
 value: {
  dps: 97656.44234
  tps: 72920.07957
 }
}
dps_results: {
 key: "TestDestruction-Settings-Troll-p1-prebis-Destruction Warlock-default-FullBuffs-25.0yards-ShortSingleTarget"
 value: {
  dps: 154772.43009
  tps: 100641.20125
 }
}
dps_results: {
 key: "TestDestruction-Settings-Troll-p1-prebis-Destruction Warlock-default-NoBuffs-25.0yards-LongMultiTarget"
 value: {
  dps: 138270.56824
  tps: 115872.04352
 }
}
dps_results: {
 key: "TestDestruction-Settings-Troll-p1-prebis-Destruction Warlock-default-NoBuffs-25.0yards-LongSingleTarget"
 value: {
  dps: 61685.17785
  tps: 45806.86167
 }
}
dps_results: {
 key: "TestDestruction-Settings-Troll-p1-prebis-Destruction Warlock-default-NoBuffs-25.0yards-ShortSingleTarget"
 value: {
  dps: 81405.55747
  tps: 54023.39204
 }
}
dps_results: {
 key: "TestDestruction-SwitchInFrontOfTarget-Default"
 value: {
  dps: 97429.96378
  tps: 72854.81469
 }
}
