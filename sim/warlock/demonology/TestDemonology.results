character_stats_results: {
 key: "TestDemonology-CharacterStats-Default"
 value: {
  final_stats: 157.5
  final_stats: 161.7
  final_stats: 18509.37
  final_stats: 15163.785
  final_stats: 280
  final_stats: 4640
  final_stats: 1842
  final_stats: 4107
  final_stats: 568
  final_stats: 0
  final_stats: 0
  final_stats: 7030
  final_stats: 162.25
  final_stats: 0
  final_stats: 22335.2635
  final_stats: 0
  final_stats: 1054
  final_stats: 13312
  final_stats: 0
  final_stats: 446087.598
  final_stats: 300000
  final_stats: 15000
  final_stats: 13.64706
  final_stats: 15.31765
  final_stats: 10.69
  final_stats: 15.75499
  final_stats: 0
 }
}
dps_results: {
 key: "TestDemonology-AllItems-AgilePrimalDiamond"
 value: {
  dps: 83600.34052
  tps: 52660.26654
 }
}
dps_results: {
 key: "TestDemonology-AllItems-AlacrityofXuen-103989"
 value: {
  dps: 79009.03286
  tps: 50232.62871
 }
}
dps_results: {
 key: "TestDemonology-AllItems-ArcaneBadgeoftheShieldwall-93347"
 value: {
  dps: 82961.08071
  tps: 52629.55701
 }
}
dps_results: {
 key: "TestDemonology-AllItems-ArrowflightMedallion-93258"
 value: {
  dps: 81799.05908
  tps: 51586.53119
 }
}
dps_results: {
 key: "TestDemonology-AllItems-AssuranceofConsequence-105472"
 value: {
  dps: 79009.03286
  tps: 50232.62871
 }
}
dps_results: {
 key: "TestDemonology-AllItems-AusterePrimalDiamond"
 value: {
  dps: 83216.46865
  tps: 52226.90638
 }
}
dps_results: {
 key: "TestDemonology-AllItems-BadJuju-96781"
 value: {
  dps: 81283.84719
  tps: 51846.7612
 }
}
dps_results: {
 key: "TestDemonology-AllItems-BadgeofKypariZar-84079"
 value: {
  dps: 79872.47551
  tps: 50845.29886
 }
}
dps_results: {
 key: "TestDemonology-AllItems-BlossomofPureSnow-89081"
 value: {
  dps: 85688.97953
  tps: 54014.95257
 }
}
dps_results: {
 key: "TestDemonology-AllItems-BottleofInfiniteStars-87057"
 value: {
  dps: 80590.49625
  tps: 51354.78246
 }
}
dps_results: {
 key: "TestDemonology-AllItems-BraidofTenSongs-84072"
 value: {
  dps: 79872.47551
  tps: 50845.29886
 }
}
dps_results: {
 key: "TestDemonology-AllItems-Brawler'sStatue-87571"
 value: {
  dps: 79009.03286
  tps: 50232.62871
 }
}
dps_results: {
 key: "TestDemonology-AllItems-BreathoftheHydra-96827"
 value: {
  dps: 91130.16174
  tps: 57595.09387
 }
}
dps_results: {
 key: "TestDemonology-AllItems-BroochofMunificentDeeds-87500"
 value: {
  dps: 79796.96003
  tps: 50760.95373
 }
}
dps_results: {
 key: "TestDemonology-AllItems-BrutalTalismanoftheShado-PanAssault-94508"
 value: {
  dps: 80046.81129
  tps: 50922.12514
 }
}
dps_results: {
 key: "TestDemonology-AllItems-BurningPrimalDiamond"
 value: {
  dps: 84413.52126
  tps: 53187.94699
 }
}
dps_results: {
 key: "TestDemonology-AllItems-CapacitivePrimalDiamond"
 value: {
  dps: 83306.37632
  tps: 52224.67308
 }
}
dps_results: {
 key: "TestDemonology-AllItems-CarbonicCarbuncle-81138"
 value: {
  dps: 80468.53145
  tps: 50934.53653
 }
}
dps_results: {
 key: "TestDemonology-AllItems-Cha-Ye'sEssenceofBrilliance-96888"
 value: {
  dps: 90087.41983
  tps: 56954.19557
 }
}
dps_results: {
 key: "TestDemonology-AllItems-CharmofTenSongs-84071"
 value: {
  dps: 80706.26783
  tps: 51046.50905
 }
}
dps_results: {
 key: "TestDemonology-AllItems-CommunalIdolofDestruction-101168"
 value: {
  dps: 82253.81531
  tps: 52228.71186
 }
}
dps_results: {
 key: "TestDemonology-AllItems-CommunalStoneofDestruction-101171"
 value: {
  dps: 84768.05199
  tps: 53953.4386
 }
}
dps_results: {
 key: "TestDemonology-AllItems-CommunalStoneofWisdom-101183"
 value: {
  dps: 82794.78349
  tps: 52581.40254
 }
}
dps_results: {
 key: "TestDemonology-AllItems-ContemplationofChi-Ji-103688"
 value: {
  dps: 83242.59528
  tps: 52863.64474
 }
}
dps_results: {
 key: "TestDemonology-AllItems-ContemplationofChi-Ji-103988"
 value: {
  dps: 85170.84513
  tps: 54174.44283
 }
}
dps_results: {
 key: "TestDemonology-AllItems-Coren'sColdChromiumCoaster-87574"
 value: {
  dps: 80246.3165
  tps: 50932.99478
 }
}
dps_results: {
 key: "TestDemonology-AllItems-CoreofDecency-87497"
 value: {
  dps: 79016.60923
  tps: 50241.33442
 }
}
dps_results: {
 key: "TestDemonology-AllItems-CourageousPrimalDiamond"
 value: {
  dps: 84133.15676
  tps: 52766.66469
 }
}
dps_results: {
 key: "TestDemonology-AllItems-CraftedDreadfulGladiator'sBadgeofConquest-93419"
 value: {
  dps: 79009.03286
  tps: 50232.62871
 }
}
dps_results: {
 key: "TestDemonology-AllItems-CraftedDreadfulGladiator'sBadgeofDominance-93600"
 value: {
  dps: 82431.78253
  tps: 52429.47737
 }
}
dps_results: {
 key: "TestDemonology-AllItems-CraftedDreadfulGladiator'sBadgeofVictory-93606"
 value: {
  dps: 79009.03286
  tps: 50232.62871
 }
}
dps_results: {
 key: "TestDemonology-AllItems-CraftedDreadfulGladiator'sEmblemofCruelty-93485"
 value: {
  dps: 80086.76596
  tps: 50805.48147
 }
}
dps_results: {
 key: "TestDemonology-AllItems-CraftedDreadfulGladiator'sEmblemofMeditation-93487"
 value: {
  dps: 79084.37008
  tps: 50223.88859
 }
}
dps_results: {
 key: "TestDemonology-AllItems-CraftedDreadfulGladiator'sEmblemofTenacity-93486"
 value: {
  dps: 79701.32453
  tps: 50631.46433
 }
}
dps_results: {
 key: "TestDemonology-AllItems-CraftedDreadfulGladiator'sInsigniaofConquest-93424"
 value: {
  dps: 79009.03286
  tps: 50232.62871
 }
}
dps_results: {
 key: "TestDemonology-AllItems-CraftedDreadfulGladiator'sInsigniaofDominance-93601"
 value: {
  dps: 83997.41306
  tps: 53459.0037
 }
}
dps_results: {
 key: "TestDemonology-AllItems-CraftedDreadfulGladiator'sInsigniaofVictory-93611"
 value: {
  dps: 79009.03286
  tps: 50232.62871
 }
}
dps_results: {
 key: "TestDemonology-AllItems-CraftedMalevolentGladiator'sBadgeofConquest-98755"
 value: {
  dps: 79009.03286
  tps: 50232.62871
 }
}
dps_results: {
 key: "TestDemonology-AllItems-CraftedMalevolentGladiator'sBadgeofDominance-98910"
 value: {
  dps: 83043.39669
  tps: 52833.77198
 }
}
dps_results: {
 key: "TestDemonology-AllItems-CraftedMalevolentGladiator'sBadgeofVictory-98912"
 value: {
  dps: 79009.03286
  tps: 50232.62871
 }
}
dps_results: {
 key: "TestDemonology-AllItems-CraftedMalevolentGladiator'sEmblemofCruelty-98811"
 value: {
  dps: 80289.74651
  tps: 50891.41055
 }
}
dps_results: {
 key: "TestDemonology-AllItems-CraftedMalevolentGladiator'sEmblemofMeditation-98813"
 value: {
  dps: 79084.37008
  tps: 50223.88859
 }
}
dps_results: {
 key: "TestDemonology-AllItems-CraftedMalevolentGladiator'sEmblemofTenacity-98812"
 value: {
  dps: 79707.62686
  tps: 50628.15454
 }
}
dps_results: {
 key: "TestDemonology-AllItems-CraftedMalevolentGladiator'sInsigniaofConquest-98760"
 value: {
  dps: 79009.03286
  tps: 50232.62871
 }
}
dps_results: {
 key: "TestDemonology-AllItems-CraftedMalevolentGladiator'sInsigniaofDominance-98911"
 value: {
  dps: 84516.0875
  tps: 53844.38298
 }
}
dps_results: {
 key: "TestDemonology-AllItems-CraftedMalevolentGladiator'sInsigniaofVictory-98917"
 value: {
  dps: 79009.03286
  tps: 50232.62871
 }
}
dps_results: {
 key: "TestDemonology-AllItems-CurseofHubris-102307"
 value: {
  dps: 83154.77726
  tps: 52531.5304
 }
}
dps_results: {
 key: "TestDemonology-AllItems-CurseofHubris-104649"
 value: {
  dps: 83858.27194
  tps: 52632.40533
 }
}
dps_results: {
 key: "TestDemonology-AllItems-CurseofHubris-104898"
 value: {
  dps: 82663.80255
  tps: 52265.96509
 }
}
dps_results: {
 key: "TestDemonology-AllItems-CurseofHubris-105147"
 value: {
  dps: 82092.11314
  tps: 51861.17523
 }
}
dps_results: {
 key: "TestDemonology-AllItems-CurseofHubris-105396"
 value: {
  dps: 83629.5499
  tps: 52774.37056
 }
}
dps_results: {
 key: "TestDemonology-AllItems-CurseofHubris-105645"
 value: {
  dps: 84062.31563
  tps: 52836.32635
 }
}
dps_results: {
 key: "TestDemonology-AllItems-CutstitcherMedallion-93255"
 value: {
  dps: 83242.59528
  tps: 52863.64474
 }
}
dps_results: {
 key: "TestDemonology-AllItems-Daelo'sFinalWords-87496"
 value: {
  dps: 80046.81129
  tps: 50922.12514
 }
}
dps_results: {
 key: "TestDemonology-AllItems-DarkglowEmbroidery(Rank3)-4893"
 value: {
  dps: 80899.98941
  tps: 51996.81761
 }
}
dps_results: {
 key: "TestDemonology-AllItems-DarkmistVortex-87172"
 value: {
  dps: 82005.95745
  tps: 52012.85882
 }
}
dps_results: {
 key: "TestDemonology-AllItems-DeadeyeBadgeoftheShieldwall-93346"
 value: {
  dps: 80448.93509
  tps: 51296.62205
 }
}
dps_results: {
 key: "TestDemonology-AllItems-DelicateVialoftheSanguinaire-96895"
 value: {
  dps: 79009.03286
  tps: 50232.62871
 }
}
dps_results: {
 key: "TestDemonology-AllItems-DestructivePrimalDiamond"
 value: {
  dps: 83489.15062
  tps: 52343.2902
 }
}
dps_results: {
 key: "TestDemonology-AllItems-DisciplineofXuen-103986"
 value: {
  dps: 82154.01914
  tps: 52371.50434
 }
}
dps_results: {
 key: "TestDemonology-AllItems-Dominator'sArcaneBadge-93342"
 value: {
  dps: 82961.08071
  tps: 52629.55701
 }
}
dps_results: {
 key: "TestDemonology-AllItems-Dominator'sDeadeyeBadge-93341"
 value: {
  dps: 80448.93509
  tps: 51296.62205
 }
}
dps_results: {
 key: "TestDemonology-AllItems-Dominator'sDurableBadge-93345"
 value: {
  dps: 80448.93509
  tps: 51296.62205
 }
}
dps_results: {
 key: "TestDemonology-AllItems-Dominator'sKnightlyBadge-93344"
 value: {
  dps: 80448.93509
  tps: 51296.62205
 }
}
dps_results: {
 key: "TestDemonology-AllItems-Dominator'sMendingBadge-93343"
 value: {
  dps: 81995.22029
  tps: 52089.0473
 }
}
dps_results: {
 key: "TestDemonology-AllItems-DreadfulGladiator'sBadgeofConquest-84344"
 value: {
  dps: 79009.03286
  tps: 50232.62871
 }
}
dps_results: {
 key: "TestDemonology-AllItems-DreadfulGladiator'sBadgeofDominance-84488"
 value: {
  dps: 82431.78253
  tps: 52429.47737
 }
}
dps_results: {
 key: "TestDemonology-AllItems-DreadfulGladiator'sBadgeofVictory-84490"
 value: {
  dps: 79009.03286
  tps: 50232.62871
 }
}
dps_results: {
 key: "TestDemonology-AllItems-DreadfulGladiator'sEmblemofCruelty-84399"
 value: {
  dps: 80086.76596
  tps: 50805.48147
 }
}
dps_results: {
 key: "TestDemonology-AllItems-DreadfulGladiator'sEmblemofMeditation-84401"
 value: {
  dps: 79084.37008
  tps: 50223.88859
 }
}
dps_results: {
 key: "TestDemonology-AllItems-DreadfulGladiator'sEmblemofTenacity-84400"
 value: {
  dps: 79701.32453
  tps: 50631.46433
 }
}
dps_results: {
 key: "TestDemonology-AllItems-DreadfulGladiator'sInsigniaofConquest-84349"
 value: {
  dps: 79009.03286
  tps: 50232.62871
 }
}
dps_results: {
 key: "TestDemonology-AllItems-DreadfulGladiator'sInsigniaofDominance-84489"
 value: {
  dps: 83906.41581
  tps: 53495.38738
 }
}
dps_results: {
 key: "TestDemonology-AllItems-DreadfulGladiator'sInsigniaofVictory-84495"
 value: {
  dps: 79009.03286
  tps: 50232.62871
 }
}
dps_results: {
 key: "TestDemonology-AllItems-DurableBadgeoftheShieldwall-93350"
 value: {
  dps: 80448.93509
  tps: 51296.62205
 }
}
dps_results: {
 key: "TestDemonology-AllItems-EffulgentPrimalDiamond"
 value: {
  dps: 83216.46865
  tps: 52226.90638
 }
}
dps_results: {
 key: "TestDemonology-AllItems-EmberPrimalDiamond"
 value: {
  dps: 83751.84096
  tps: 52529.97872
 }
}
dps_results: {
 key: "TestDemonology-AllItems-EmblemofKypariZar-84077"
 value: {
  dps: 80006.75843
  tps: 50801.95315
 }
}
dps_results: {
 key: "TestDemonology-AllItems-EmblemoftheCatacombs-83733"
 value: {
  dps: 79839.86929
  tps: 50673.7256
 }
}
dps_results: {
 key: "TestDemonology-AllItems-EmptyFruitBarrel-81133"
 value: {
  dps: 79016.60189
  tps: 50241.32675
 }
}
dps_results: {
 key: "TestDemonology-AllItems-EnchantWeapon-BloodyDancingSteel-5125"
 value: {
  dps: 80431.95351
  tps: 51727.2976
 }
}
dps_results: {
 key: "TestDemonology-AllItems-EnchantWeapon-Colossus-4445"
 value: {
  dps: 80431.95351
  tps: 51727.2976
 }
}
dps_results: {
 key: "TestDemonology-AllItems-EnchantWeapon-DancingSteel-4444"
 value: {
  dps: 80431.95351
  tps: 51727.2976
 }
}
dps_results: {
 key: "TestDemonology-AllItems-EnchantWeapon-River'sSong-4446"
 value: {
  dps: 80431.95351
  tps: 51727.2976
 }
}
dps_results: {
 key: "TestDemonology-AllItems-EnchantWeapon-SpiritofConquest-5124"
 value: {
  dps: 80431.95351
  tps: 51727.2976
 }
}
dps_results: {
 key: "TestDemonology-AllItems-EnchantWeapon-Windsong-4441"
 value: {
  dps: 81230.45361
  tps: 52370.2414
 }
}
dps_results: {
 key: "TestDemonology-AllItems-EnigmaticPrimalDiamond"
 value: {
  dps: 83489.15062
  tps: 52343.2902
 }
}
dps_results: {
 key: "TestDemonology-AllItems-EssenceofTerror-87175"
 value: {
  dps: 87594.4473
  tps: 55528.39038
 }
}
dps_results: {
 key: "TestDemonology-AllItems-EternalPrimalDiamond"
 value: {
  dps: 82947.80476
  tps: 52011.42013
 }
}
dps_results: {
 key: "TestDemonology-AllItems-EvilEyeofGalakras-105491"
 value: {
  dps: 79009.03286
  tps: 50232.62871
 }
}
dps_results: {
 key: "TestDemonology-AllItems-FabledFeatherofJi-Kun-96842"
 value: {
  dps: 80046.81129
  tps: 50922.12514
 }
}
dps_results: {
 key: "TestDemonology-AllItems-FearwurmBadge-84074"
 value: {
  dps: 80006.75843
  tps: 50801.95315
 }
}
dps_results: {
 key: "TestDemonology-AllItems-FearwurmRelic-84070"
 value: {
  dps: 81985.06112
  tps: 51773.81644
 }
}
dps_results: {
 key: "TestDemonology-AllItems-FelsoulIdolofDestruction-101263"
 value: {
  dps: 82468.68308
  tps: 52539.26475
 }
}
dps_results: {
 key: "TestDemonology-AllItems-FelsoulStoneofDestruction-101266"
 value: {
  dps: 84795.30741
  tps: 53955.1307
 }
}
dps_results: {
 key: "TestDemonology-AllItems-Fen-Yu,FuryofXuen-102248"
 value: {
  dps: 81670.43741
  tps: 52303.70607
 }
}
dps_results: {
 key: "TestDemonology-AllItems-FlashfrozenResinGlobule-100951"
 value: {
  dps: 83293.98216
  tps: 52754.61553
 }
}
dps_results: {
 key: "TestDemonology-AllItems-FlashingSteelTalisman-81265"
 value: {
  dps: 80046.81129
  tps: 50922.12514
 }
}
dps_results: {
 key: "TestDemonology-AllItems-FleetPrimalDiamond"
 value: {
  dps: 83537.42722
  tps: 52426.08543
 }
}
dps_results: {
 key: "TestDemonology-AllItems-ForlornPrimalDiamond"
 value: {
  dps: 83751.84096
  tps: 52529.97872
 }
}
dps_results: {
 key: "TestDemonology-AllItems-FortitudeoftheZandalari-94516"
 value: {
  dps: 80930.08103
  tps: 51546.43126
 }
}
dps_results: {
 key: "TestDemonology-AllItems-FortitudeoftheZandalari-95677"
 value: {
  dps: 80606.89814
  tps: 51317.31356
 }
}
dps_results: {
 key: "TestDemonology-AllItems-FortitudeoftheZandalari-96049"
 value: {
  dps: 81040.40451
  tps: 51624.64412
 }
}
dps_results: {
 key: "TestDemonology-AllItems-FortitudeoftheZandalari-96421"
 value: {
  dps: 81176.68646
  tps: 51721.26002
 }
}
dps_results: {
 key: "TestDemonology-AllItems-FortitudeoftheZandalari-96793"
 value: {
  dps: 81299.98917
  tps: 51808.6744
 }
}
dps_results: {
 key: "TestDemonology-AllItems-GazeoftheTwins-96915"
 value: {
  dps: 80957.83671
  tps: 51177.7335
 }
}
dps_results: {
 key: "TestDemonology-AllItems-Gerp'sPerfectArrow-87495"
 value: {
  dps: 80038.70227
  tps: 50818.09348
 }
}
dps_results: {
 key: "TestDemonology-AllItems-Gong-Lu,StrengthofXuen-102249"
 value: {
  dps: 81670.43741
  tps: 52303.70607
 }
}
dps_results: {
 key: "TestDemonology-AllItems-GrievousGladiator'sBadgeofConquest-100195"
 value: {
  dps: 79009.03286
  tps: 50232.62871
 }
}
dps_results: {
 key: "TestDemonology-AllItems-GrievousGladiator'sBadgeofConquest-100603"
 value: {
  dps: 79009.03286
  tps: 50232.62871
 }
}
dps_results: {
 key: "TestDemonology-AllItems-GrievousGladiator'sBadgeofConquest-102856"
 value: {
  dps: 79009.03286
  tps: 50232.62871
 }
}
dps_results: {
 key: "TestDemonology-AllItems-GrievousGladiator'sBadgeofConquest-103145"
 value: {
  dps: 79009.03286
  tps: 50232.62871
 }
}
dps_results: {
 key: "TestDemonology-AllItems-GrievousGladiator'sBadgeofDominance-100490"
 value: {
  dps: 85198.74707
  tps: 54142.79647
 }
}
dps_results: {
 key: "TestDemonology-AllItems-GrievousGladiator'sBadgeofDominance-100576"
 value: {
  dps: 85198.74707
  tps: 54142.79647
 }
}
dps_results: {
 key: "TestDemonology-AllItems-GrievousGladiator'sBadgeofDominance-102830"
 value: {
  dps: 85198.74707
  tps: 54142.79647
 }
}
dps_results: {
 key: "TestDemonology-AllItems-GrievousGladiator'sBadgeofDominance-103308"
 value: {
  dps: 85198.74707
  tps: 54142.79647
 }
}
dps_results: {
 key: "TestDemonology-AllItems-GrievousGladiator'sBadgeofVictory-100500"
 value: {
  dps: 79009.03286
  tps: 50232.62871
 }
}
dps_results: {
 key: "TestDemonology-AllItems-GrievousGladiator'sBadgeofVictory-100579"
 value: {
  dps: 79009.03286
  tps: 50232.62871
 }
}
dps_results: {
 key: "TestDemonology-AllItems-GrievousGladiator'sBadgeofVictory-102833"
 value: {
  dps: 79009.03286
  tps: 50232.62871
 }
}
dps_results: {
 key: "TestDemonology-AllItems-GrievousGladiator'sBadgeofVictory-103314"
 value: {
  dps: 79009.03286
  tps: 50232.62871
 }
}
dps_results: {
 key: "TestDemonology-AllItems-GrievousGladiator'sEmblemofCruelty-100305"
 value: {
  dps: 81308.39511
  tps: 51433.00084
 }
}
dps_results: {
 key: "TestDemonology-AllItems-GrievousGladiator'sEmblemofCruelty-100626"
 value: {
  dps: 81308.39511
  tps: 51433.00084
 }
}
dps_results: {
 key: "TestDemonology-AllItems-GrievousGladiator'sEmblemofCruelty-102877"
 value: {
  dps: 81308.39511
  tps: 51433.00084
 }
}
dps_results: {
 key: "TestDemonology-AllItems-GrievousGladiator'sEmblemofCruelty-103210"
 value: {
  dps: 81308.39511
  tps: 51433.00084
 }
}
dps_results: {
 key: "TestDemonology-AllItems-GrievousGladiator'sEmblemofMeditation-100307"
 value: {
  dps: 79304.50838
  tps: 50379.90525
 }
}
dps_results: {
 key: "TestDemonology-AllItems-GrievousGladiator'sEmblemofMeditation-100559"
 value: {
  dps: 79304.50838
  tps: 50379.90525
 }
}
dps_results: {
 key: "TestDemonology-AllItems-GrievousGladiator'sEmblemofMeditation-102813"
 value: {
  dps: 79304.50838
  tps: 50379.90525
 }
}
dps_results: {
 key: "TestDemonology-AllItems-GrievousGladiator'sEmblemofMeditation-103212"
 value: {
  dps: 79304.50838
  tps: 50379.90525
 }
}
dps_results: {
 key: "TestDemonology-AllItems-GrievousGladiator'sEmblemofTenacity-100306"
 value: {
  dps: 79474.98042
  tps: 50497.89798
 }
}
dps_results: {
 key: "TestDemonology-AllItems-GrievousGladiator'sEmblemofTenacity-100652"
 value: {
  dps: 79474.98042
  tps: 50497.89798
 }
}
dps_results: {
 key: "TestDemonology-AllItems-GrievousGladiator'sEmblemofTenacity-102903"
 value: {
  dps: 79474.98042
  tps: 50497.89798
 }
}
dps_results: {
 key: "TestDemonology-AllItems-GrievousGladiator'sEmblemofTenacity-103211"
 value: {
  dps: 79474.98042
  tps: 50497.89798
 }
}
dps_results: {
 key: "TestDemonology-AllItems-GrievousGladiator'sInsigniaofConquest-103150"
 value: {
  dps: 79009.03286
  tps: 50232.62871
 }
}
dps_results: {
 key: "TestDemonology-AllItems-GrievousGladiator'sInsigniaofDominance-103309"
 value: {
  dps: 88025.52988
  tps: 56036.99732
 }
}
dps_results: {
 key: "TestDemonology-AllItems-GrievousGladiator'sInsigniaofVictory-103319"
 value: {
  dps: 79009.03286
  tps: 50232.62871
 }
}
dps_results: {
 key: "TestDemonology-AllItems-Hawkmaster'sTalon-89082"
 value: {
  dps: 81635.0797
  tps: 51808.44588
 }
}
dps_results: {
 key: "TestDemonology-AllItems-Heart-LesionDefenderIdol-100999"
 value: {
  dps: 79512.84928
  tps: 50516.47742
 }
}
dps_results: {
 key: "TestDemonology-AllItems-Heart-LesionDefenderStone-101002"
 value: {
  dps: 81396.79961
  tps: 51848.88494
 }
}
dps_results: {
 key: "TestDemonology-AllItems-Heart-LesionIdolofBattle-100991"
 value: {
  dps: 80421.1586
  tps: 51001.93864
 }
}
dps_results: {
 key: "TestDemonology-AllItems-Heart-LesionStoneofBattle-100990"
 value: {
  dps: 80796.11674
  tps: 51494.93358
 }
}
dps_results: {
 key: "TestDemonology-AllItems-HeartofFire-81181"
 value: {
  dps: 80108.78614
  tps: 51012.97701
 }
}
dps_results: {
 key: "TestDemonology-AllItems-HeartwarmerMedallion-93260"
 value: {
  dps: 83242.59528
  tps: 52863.64474
 }
}
dps_results: {
 key: "TestDemonology-AllItems-HelmbreakerMedallion-93261"
 value: {
  dps: 81799.05908
  tps: 51586.53119
 }
}
dps_results: {
 key: "TestDemonology-AllItems-Horridon'sLastGasp-96757"
 value: {
  dps: 85619.19719
  tps: 54439.9746
 }
}
dps_results: {
 key: "TestDemonology-AllItems-ImpassivePrimalDiamond"
 value: {
  dps: 83489.15062
  tps: 52343.2902
 }
}
dps_results: {
 key: "TestDemonology-AllItems-IndomitablePrimalDiamond"
 value: {
  dps: 83216.46865
  tps: 52226.90638
 }
}
dps_results: {
 key: "TestDemonology-AllItems-InscribedBagofHydra-Spawn-96828"
 value: {
  dps: 79171.43921
  tps: 50340.49056
 }
}
dps_results: {
 key: "TestDemonology-AllItems-InsigniaofKypariZar-84078"
 value: {
  dps: 79009.03286
  tps: 50232.62871
 }
}
dps_results: {
 key: "TestDemonology-AllItems-IronBellyWok-89083"
 value: {
  dps: 81635.0797
  tps: 51808.44588
 }
}
dps_results: {
 key: "TestDemonology-AllItems-IronProtectorTalisman-85181"
 value: {
  dps: 79697.88811
  tps: 50614.90514
 }
}
dps_results: {
 key: "TestDemonology-AllItems-JadeBanditFigurine-86043"
 value: {
  dps: 81635.0797
  tps: 51808.44588
 }
}
dps_results: {
 key: "TestDemonology-AllItems-JadeBanditFigurine-86772"
 value: {
  dps: 80882.1211
  tps: 51242.80862
 }
}
dps_results: {
 key: "TestDemonology-AllItems-JadeCharioteerFigurine-86042"
 value: {
  dps: 81635.0797
  tps: 51808.44588
 }
}
dps_results: {
 key: "TestDemonology-AllItems-JadeCharioteerFigurine-86771"
 value: {
  dps: 80882.1211
  tps: 51242.80862
 }
}
dps_results: {
 key: "TestDemonology-AllItems-JadeCourtesanFigurine-86045"
 value: {
  dps: 82992.53349
  tps: 52705.84692
 }
}
dps_results: {
 key: "TestDemonology-AllItems-JadeCourtesanFigurine-86774"
 value: {
  dps: 82571.72156
  tps: 52440.49346
 }
}
dps_results: {
 key: "TestDemonology-AllItems-JadeMagistrateFigurine-86044"
 value: {
  dps: 85688.97953
  tps: 54014.95257
 }
}
dps_results: {
 key: "TestDemonology-AllItems-JadeMagistrateFigurine-86773"
 value: {
  dps: 84995.50651
  tps: 53593.05051
 }
}
dps_results: {
 key: "TestDemonology-AllItems-JadeWarlordFigurine-86046"
 value: {
  dps: 81475.49423
  tps: 51980.34101
 }
}
dps_results: {
 key: "TestDemonology-AllItems-JadeWarlordFigurine-86775"
 value: {
  dps: 81321.75191
  tps: 51802.48548
 }
}
dps_results: {
 key: "TestDemonology-AllItems-Ji-Kun'sRisingWinds-96843"
 value: {
  dps: 80046.81129
  tps: 50922.12514
 }
}
dps_results: {
 key: "TestDemonology-AllItems-KnightlyBadgeoftheShieldwall-93349"
 value: {
  dps: 80448.93509
  tps: 51296.62205
 }
}
dps_results: {
 key: "TestDemonology-AllItems-KnotofTenSongs-84073"
 value: {
  dps: 79009.03286
  tps: 50232.62871
 }
}
dps_results: {
 key: "TestDemonology-AllItems-Kor'kronBookofHurting-92785"
 value: {
  dps: 79009.03286
  tps: 50232.62871
 }
}
dps_results: {
 key: "TestDemonology-AllItems-Lao-Chin'sLiquidCourage-89079"
 value: {
  dps: 81475.49423
  tps: 51980.34101
 }
}
dps_results: {
 key: "TestDemonology-AllItems-LeiShen'sFinalOrders-87072"
 value: {
  dps: 81608.45472
  tps: 51798.7315
 }
}
dps_results: {
 key: "TestDemonology-AllItems-LessonsoftheDarkmaster-81268"
 value: {
  dps: 80046.81129
  tps: 50922.12514
 }
}
dps_results: {
 key: "TestDemonology-AllItems-LightdrinkerIdolofRage-101200"
 value: {
  dps: 80334.70948
  tps: 51173.28469
 }
}
dps_results: {
 key: "TestDemonology-AllItems-LightdrinkerStoneofRage-101203"
 value: {
  dps: 80908.24133
  tps: 51550.74632
 }
}
dps_results: {
 key: "TestDemonology-AllItems-LightoftheCosmos-87065"
 value: {
  dps: 87022.17317
  tps: 55300.53137
 }
}
dps_results: {
 key: "TestDemonology-AllItems-LordBlastington'sScopeofDoom-4699"
 value: {
  dps: 80431.95351
  tps: 51727.2976
 }
}
dps_results: {
 key: "TestDemonology-AllItems-MalevolentGladiator'sBadgeofConquest-84934"
 value: {
  dps: 79009.03286
  tps: 50232.62871
 }
}
dps_results: {
 key: "TestDemonology-AllItems-MalevolentGladiator'sBadgeofConquest-91452"
 value: {
  dps: 79009.03286
  tps: 50232.62871
 }
}
dps_results: {
 key: "TestDemonology-AllItems-MalevolentGladiator'sBadgeofDominance-84940"
 value: {
  dps: 83327.76456
  tps: 52990.77112
 }
}
dps_results: {
 key: "TestDemonology-AllItems-MalevolentGladiator'sBadgeofDominance-91753"
 value: {
  dps: 83043.39669
  tps: 52833.77198
 }
}
dps_results: {
 key: "TestDemonology-AllItems-MalevolentGladiator'sBadgeofVictory-84942"
 value: {
  dps: 79009.03286
  tps: 50232.62871
 }
}
dps_results: {
 key: "TestDemonology-AllItems-MalevolentGladiator'sBadgeofVictory-91763"
 value: {
  dps: 79009.03286
  tps: 50232.62871
 }
}
dps_results: {
 key: "TestDemonology-AllItems-MalevolentGladiator'sEmblemofCruelty-84936"
 value: {
  dps: 80374.31808
  tps: 50916.27316
 }
}
dps_results: {
 key: "TestDemonology-AllItems-MalevolentGladiator'sEmblemofCruelty-91562"
 value: {
  dps: 80289.74651
  tps: 50891.41055
 }
}
dps_results: {
 key: "TestDemonology-AllItems-MalevolentGladiator'sEmblemofMeditation-84939"
 value: {
  dps: 79084.37008
  tps: 50223.88859
 }
}
dps_results: {
 key: "TestDemonology-AllItems-MalevolentGladiator'sEmblemofMeditation-91564"
 value: {
  dps: 79084.37008
  tps: 50223.88859
 }
}
dps_results: {
 key: "TestDemonology-AllItems-MalevolentGladiator'sEmblemofTenacity-84938"
 value: {
  dps: 79728.50112
  tps: 50580.80124
 }
}
dps_results: {
 key: "TestDemonology-AllItems-MalevolentGladiator'sEmblemofTenacity-91563"
 value: {
  dps: 79707.62686
  tps: 50628.15454
 }
}
dps_results: {
 key: "TestDemonology-AllItems-MalevolentGladiator'sInsigniaofConquest-91457"
 value: {
  dps: 79009.03286
  tps: 50232.62871
 }
}
dps_results: {
 key: "TestDemonology-AllItems-MalevolentGladiator'sInsigniaofDominance-91754"
 value: {
  dps: 84579.28073
  tps: 53857.79904
 }
}
dps_results: {
 key: "TestDemonology-AllItems-MalevolentGladiator'sInsigniaofVictory-91768"
 value: {
  dps: 79009.03286
  tps: 50232.62871
 }
}
dps_results: {
 key: "TestDemonology-AllItems-MarkoftheCatacombs-83731"
 value: {
  dps: 81129.33386
  tps: 51625.55637
 }
}
dps_results: {
 key: "TestDemonology-AllItems-MarkoftheHardenedGrunt-92783"
 value: {
  dps: 79009.03286
  tps: 50232.62871
 }
}
dps_results: {
 key: "TestDemonology-AllItems-MedallionofMystifyingVapors-93257"
 value: {
  dps: 81057.08445
  tps: 51750.49926
 }
}
dps_results: {
 key: "TestDemonology-AllItems-MedallionoftheCatacombs-83734"
 value: {
  dps: 79810.15183
  tps: 50801.07605
 }
}
dps_results: {
 key: "TestDemonology-AllItems-MendingBadgeoftheShieldwall-93348"
 value: {
  dps: 81995.22029
  tps: 52089.0473
 }
}
dps_results: {
 key: "TestDemonology-AllItems-MirrorScope-4700"
 value: {
  dps: 80431.95351
  tps: 51727.2976
 }
}
dps_results: {
 key: "TestDemonology-AllItems-MistdancerDefenderIdol-101089"
 value: {
  dps: 79512.84928
  tps: 50516.47742
 }
}
dps_results: {
 key: "TestDemonology-AllItems-MistdancerDefenderStone-101087"
 value: {
  dps: 81471.21833
  tps: 51887.77169
 }
}
dps_results: {
 key: "TestDemonology-AllItems-MistdancerIdolofRage-101113"
 value: {
  dps: 80334.70948
  tps: 51173.28469
 }
}
dps_results: {
 key: "TestDemonology-AllItems-MistdancerStoneofRage-101117"
 value: {
  dps: 80856.24414
  tps: 51504.74673
 }
}
dps_results: {
 key: "TestDemonology-AllItems-MistdancerStoneofWisdom-101107"
 value: {
  dps: 82794.78349
  tps: 52581.40254
 }
}
dps_results: {
 key: "TestDemonology-AllItems-MithrilWristwatch-87572"
 value: {
  dps: 81315.41731
  tps: 51624.53271
 }
}
dps_results: {
 key: "TestDemonology-AllItems-MountainsageIdolofDestruction-101069"
 value: {
  dps: 82311.17432
  tps: 52340.99274
 }
}
dps_results: {
 key: "TestDemonology-AllItems-MountainsageStoneofDestruction-101072"
 value: {
  dps: 84757.93328
  tps: 53964.76917
 }
}
dps_results: {
 key: "TestDemonology-AllItems-OathswornDefenderIdol-101303"
 value: {
  dps: 79512.84928
  tps: 50516.47742
 }
}
dps_results: {
 key: "TestDemonology-AllItems-OathswornDefenderStone-101306"
 value: {
  dps: 81020.84274
  tps: 51540.1976
 }
}
dps_results: {
 key: "TestDemonology-AllItems-OathswornIdolofBattle-101295"
 value: {
  dps: 80421.1586
  tps: 51001.93864
 }
}
dps_results: {
 key: "TestDemonology-AllItems-OathswornStoneofBattle-101294"
 value: {
  dps: 80953.8729
  tps: 51579.73569
 }
}
dps_results: {
 key: "TestDemonology-AllItems-PhaseFingers-4697"
 value: {
  dps: 84177.18875
  tps: 53021.07813
 }
}
dps_results: {
 key: "TestDemonology-AllItems-PouchofWhiteAsh-103639"
 value: {
  dps: 79009.03286
  tps: 50232.62871
 }
}
dps_results: {
 key: "TestDemonology-AllItems-PowerfulPrimalDiamond"
 value: {
  dps: 83216.46865
  tps: 52226.90638
 }
}
dps_results: {
 key: "TestDemonology-AllItems-PriceofProgress-81266"
 value: {
  dps: 82195.70306
  tps: 52202.23898
 }
}
dps_results: {
 key: "TestDemonology-AllItems-PridefulGladiator'sBadgeofConquest-102659"
 value: {
  dps: 79009.03286
  tps: 50232.62871
 }
}
dps_results: {
 key: "TestDemonology-AllItems-PridefulGladiator'sBadgeofConquest-103342"
 value: {
  dps: 79009.03286
  tps: 50232.62871
 }
}
dps_results: {
 key: "TestDemonology-AllItems-PridefulGladiator'sBadgeofDominance-102633"
 value: {
  dps: 87309.15161
  tps: 55436.61031
 }
}
dps_results: {
 key: "TestDemonology-AllItems-PridefulGladiator'sBadgeofDominance-103505"
 value: {
  dps: 87309.15161
  tps: 55436.61031
 }
}
dps_results: {
 key: "TestDemonology-AllItems-PridefulGladiator'sBadgeofVictory-102636"
 value: {
  dps: 79009.03286
  tps: 50232.62871
 }
}
dps_results: {
 key: "TestDemonology-AllItems-PridefulGladiator'sBadgeofVictory-103511"
 value: {
  dps: 79009.03286
  tps: 50232.62871
 }
}
dps_results: {
 key: "TestDemonology-AllItems-PridefulGladiator'sEmblemofCruelty-102680"
 value: {
  dps: 81714.04915
  tps: 51629.66784
 }
}
dps_results: {
 key: "TestDemonology-AllItems-PridefulGladiator'sEmblemofCruelty-103407"
 value: {
  dps: 81714.04915
  tps: 51629.66784
 }
}
dps_results: {
 key: "TestDemonology-AllItems-PridefulGladiator'sEmblemofMeditation-102616"
 value: {
  dps: 79359.42494
  tps: 50456.9413
 }
}
dps_results: {
 key: "TestDemonology-AllItems-PridefulGladiator'sEmblemofMeditation-103409"
 value: {
  dps: 79359.42494
  tps: 50456.9413
 }
}
dps_results: {
 key: "TestDemonology-AllItems-PridefulGladiator'sEmblemofTenacity-102706"
 value: {
  dps: 79282.91311
  tps: 50412.52964
 }
}
dps_results: {
 key: "TestDemonology-AllItems-PridefulGladiator'sEmblemofTenacity-103408"
 value: {
  dps: 79282.91311
  tps: 50412.52964
 }
}
dps_results: {
 key: "TestDemonology-AllItems-PridefulGladiator'sInsigniaofConquest-103347"
 value: {
  dps: 79009.03286
  tps: 50232.62871
 }
}
dps_results: {
 key: "TestDemonology-AllItems-PridefulGladiator'sInsigniaofDominance-103506"
 value: {
  dps: 90830.91282
  tps: 57911.77802
 }
}
dps_results: {
 key: "TestDemonology-AllItems-PridefulGladiator'sInsigniaofVictory-103516"
 value: {
  dps: 79009.03286
  tps: 50232.62871
 }
}
dps_results: {
 key: "TestDemonology-AllItems-Primordius'TalismanofRage-96873"
 value: {
  dps: 81606.66008
  tps: 51648.81978
 }
}
dps_results: {
 key: "TestDemonology-AllItems-Qian-Le,CourageofNiuzao-102245"
 value: {
  dps: 80345.05035
  tps: 51609.02914
 }
}
dps_results: {
 key: "TestDemonology-AllItems-Qian-Ying,FortitudeofNiuzao-102250"
 value: {
  dps: 78773.89873
  tps: 50676.73875
 }
}
dps_results: {
 key: "TestDemonology-AllItems-Qin-xi'sPolarizingSeal-87075"
 value: {
  dps: 78999.1051
  tps: 50195.54125
 }
}
dps_results: {
 key: "TestDemonology-AllItems-RegaliaoftheHornedNightmare"
 value: {
  dps: 96974.83617
  tps: 61639.35146
 }
}
dps_results: {
 key: "TestDemonology-AllItems-RegaliaoftheThousandfoldHells"
 value: {
  dps: 94263.52316
  tps: 59235.89114
 }
}
dps_results: {
 key: "TestDemonology-AllItems-RelicofChi-Ji-79330"
 value: {
  dps: 83259.64505
  tps: 52874.37506
 }
}
dps_results: {
 key: "TestDemonology-AllItems-RelicofKypariZar-84075"
 value: {
  dps: 81159.05951
  tps: 51478.73979
 }
}
dps_results: {
 key: "TestDemonology-AllItems-RelicofNiuzao-79329"
 value: {
  dps: 79598.86887
  tps: 50514.33988
 }
}
dps_results: {
 key: "TestDemonology-AllItems-RelicofXuen-79327"
 value: {
  dps: 79009.03286
  tps: 50232.62871
 }
}
dps_results: {
 key: "TestDemonology-AllItems-RelicofXuen-79328"
 value: {
  dps: 79009.03286
  tps: 50232.62871
 }
}
dps_results: {
 key: "TestDemonology-AllItems-Renataki'sSoulCharm-96741"
 value: {
  dps: 80046.81129
  tps: 50922.12514
 }
}
dps_results: {
 key: "TestDemonology-AllItems-ResolveofNiuzao-103690"
 value: {
  dps: 80504.80119
  tps: 51293.9761
 }
}
dps_results: {
 key: "TestDemonology-AllItems-ResolveofNiuzao-103990"
 value: {
  dps: 81160.49824
  tps: 51759.23689
 }
}
dps_results: {
 key: "TestDemonology-AllItems-ReverberatingPrimalDiamond"
 value: {
  dps: 83600.34052
  tps: 52660.26654
 }
}
dps_results: {
 key: "TestDemonology-AllItems-RevitalizingPrimalDiamond"
 value: {
  dps: 83604.72342
  tps: 52722.57269
 }
}
dps_results: {
 key: "TestDemonology-AllItems-SI:7Operative'sManual-92784"
 value: {
  dps: 79009.03286
  tps: 50232.62871
 }
}
dps_results: {
 key: "TestDemonology-AllItems-ScrollofReveredAncestors-89080"
 value: {
  dps: 82992.53349
  tps: 52705.84692
 }
}
dps_results: {
 key: "TestDemonology-AllItems-SearingWords-81267"
 value: {
  dps: 80193.51563
  tps: 50907.65411
 }
}
dps_results: {
 key: "TestDemonology-AllItems-Sha-SkinRegalia"
 value: {
  dps: 87650.89034
  tps: 56348.57643
 }
}
dps_results: {
 key: "TestDemonology-AllItems-ShadowflameRegalia"
 value: {
  dps: 62981.28984
  tps: 39511.35219
 }
}
dps_results: {
 key: "TestDemonology-AllItems-Shock-ChargerMedallion-93259"
 value: {
  dps: 86786.20627
  tps: 55111.86338
 }
}
dps_results: {
 key: "TestDemonology-AllItems-SigilofCompassion-83736"
 value: {
  dps: 79810.15183
  tps: 50801.07605
 }
}
dps_results: {
 key: "TestDemonology-AllItems-SigilofDevotion-83740"
 value: {
  dps: 79981.4404
  tps: 50788.34388
 }
}
dps_results: {
 key: "TestDemonology-AllItems-SigilofFidelity-83737"
 value: {
  dps: 81051.79625
  tps: 51341.19316
 }
}
dps_results: {
 key: "TestDemonology-AllItems-SigilofGrace-83738"
 value: {
  dps: 79810.15183
  tps: 50801.07605
 }
}
dps_results: {
 key: "TestDemonology-AllItems-SigilofKypariZar-84076"
 value: {
  dps: 81164.20474
  tps: 51649.42184
 }
}
dps_results: {
 key: "TestDemonology-AllItems-SigilofPatience-83739"
 value: {
  dps: 79009.03286
  tps: 50232.62871
 }
}
dps_results: {
 key: "TestDemonology-AllItems-SigiloftheCatacombs-83732"
 value: {
  dps: 81366.25209
  tps: 51287.91613
 }
}
dps_results: {
 key: "TestDemonology-AllItems-SinisterPrimalDiamond"
 value: {
  dps: 83306.37632
  tps: 52224.67308
 }
}
dps_results: {
 key: "TestDemonology-AllItems-SkullrenderMedallion-93256"
 value: {
  dps: 81799.05908
  tps: 51586.53119
 }
}
dps_results: {
 key: "TestDemonology-AllItems-SpiritsoftheSun-87163"
 value: {
  dps: 83774.00511
  tps: 53195.77007
 }
}
dps_results: {
 key: "TestDemonology-AllItems-SpringrainIdolofDestruction-101023"
 value: {
  dps: 82000.18827
  tps: 52305.59304
 }
}
dps_results: {
 key: "TestDemonology-AllItems-SpringrainIdolofRage-101009"
 value: {
  dps: 80334.70948
  tps: 51173.28469
 }
}
dps_results: {
 key: "TestDemonology-AllItems-SpringrainStoneofDestruction-101026"
 value: {
  dps: 84803.93261
  tps: 54085.45462
 }
}
dps_results: {
 key: "TestDemonology-AllItems-SpringrainStoneofRage-101012"
 value: {
  dps: 81048.67403
  tps: 51594.89506
 }
}
dps_results: {
 key: "TestDemonology-AllItems-SpringrainStoneofWisdom-101041"
 value: {
  dps: 82794.78349
  tps: 52581.40254
 }
}
dps_results: {
 key: "TestDemonology-AllItems-Static-Caster'sMedallion-93254"
 value: {
  dps: 86786.20627
  tps: 55111.86338
 }
}
dps_results: {
 key: "TestDemonology-AllItems-SteadfastFootman'sMedallion-92782"
 value: {
  dps: 79009.03286
  tps: 50232.62871
 }
}
dps_results: {
 key: "TestDemonology-AllItems-SteadfastTalismanoftheShado-PanAssault-94507"
 value: {
  dps: 80913.80034
  tps: 51584.18828
 }
}
dps_results: {
 key: "TestDemonology-AllItems-StreamtalkerIdolofDestruction-101222"
 value: {
  dps: 82881.53456
  tps: 52843.00047
 }
}
dps_results: {
 key: "TestDemonology-AllItems-StreamtalkerIdolofRage-101217"
 value: {
  dps: 80334.70948
  tps: 51173.28469
 }
}
dps_results: {
 key: "TestDemonology-AllItems-StreamtalkerStoneofDestruction-101225"
 value: {
  dps: 84501.6108
  tps: 53800.80136
 }
}
dps_results: {
 key: "TestDemonology-AllItems-StreamtalkerStoneofRage-101220"
 value: {
  dps: 80649.51915
  tps: 51402.43386
 }
}
dps_results: {
 key: "TestDemonology-AllItems-StreamtalkerStoneofWisdom-101250"
 value: {
  dps: 82794.78349
  tps: 52581.40254
 }
}
dps_results: {
 key: "TestDemonology-AllItems-StuffofNightmares-87160"
 value: {
  dps: 80696.96587
  tps: 51430.32976
 }
}
dps_results: {
 key: "TestDemonology-AllItems-SunsoulDefenderIdol-101160"
 value: {
  dps: 79512.84928
  tps: 50516.47742
 }
}
dps_results: {
 key: "TestDemonology-AllItems-SunsoulDefenderStone-101163"
 value: {
  dps: 81618.11032
  tps: 51906.42971
 }
}
dps_results: {
 key: "TestDemonology-AllItems-SunsoulIdolofBattle-101152"
 value: {
  dps: 80421.1586
  tps: 51001.93864
 }
}
dps_results: {
 key: "TestDemonology-AllItems-SunsoulStoneofBattle-101151"
 value: {
  dps: 80678.94884
  tps: 51459.80321
 }
}
dps_results: {
 key: "TestDemonology-AllItems-SunsoulStoneofWisdom-101138"
 value: {
  dps: 82794.78349
  tps: 52581.40254
 }
}
dps_results: {
 key: "TestDemonology-AllItems-SwordguardEmbroidery(Rank3)-4894"
 value: {
  dps: 80899.98941
  tps: 51996.81761
 }
}
dps_results: {
 key: "TestDemonology-AllItems-SymboloftheCatacombs-83735"
 value: {
  dps: 79810.15183
  tps: 50801.07605
 }
}
dps_results: {
 key: "TestDemonology-AllItems-SynapseSprings(MarkII)-4898"
 value: {
  dps: 85274.68555
  tps: 53796.42268
 }
}
dps_results: {
 key: "TestDemonology-AllItems-TalismanofBloodlust-96864"
 value: {
  dps: 82005.82018
  tps: 52007.0215
 }
}
dps_results: {
 key: "TestDemonology-AllItems-TerrorintheMists-87167"
 value: {
  dps: 81965.84027
  tps: 51592.17184
 }
}
dps_results: {
 key: "TestDemonology-AllItems-Thousand-YearPickledEgg-87573"
 value: {
  dps: 82392.75247
  tps: 52327.24666
 }
}
dps_results: {
 key: "TestDemonology-AllItems-TrailseekerIdolofRage-101054"
 value: {
  dps: 80334.70948
  tps: 51173.28469
 }
}
dps_results: {
 key: "TestDemonology-AllItems-TrailseekerStoneofRage-101057"
 value: {
  dps: 80984.69951
  tps: 51539.67802
 }
}
dps_results: {
 key: "TestDemonology-AllItems-TyrannicalGladiator'sBadgeofConquest-100043"
 value: {
  dps: 79009.03286
  tps: 50232.62871
 }
}
dps_results: {
 key: "TestDemonology-AllItems-TyrannicalGladiator'sBadgeofConquest-91099"
 value: {
  dps: 79009.03286
  tps: 50232.62871
 }
}
dps_results: {
 key: "TestDemonology-AllItems-TyrannicalGladiator'sBadgeofConquest-94373"
 value: {
  dps: 79009.03286
  tps: 50232.62871
 }
}
dps_results: {
 key: "TestDemonology-AllItems-TyrannicalGladiator'sBadgeofConquest-99772"
 value: {
  dps: 79009.03286
  tps: 50232.62871
 }
}
dps_results: {
 key: "TestDemonology-AllItems-TyrannicalGladiator'sBadgeofDominance-100016"
 value: {
  dps: 83860.07968
  tps: 53332.91785
 }
}
dps_results: {
 key: "TestDemonology-AllItems-TyrannicalGladiator'sBadgeofDominance-91400"
 value: {
  dps: 83860.07968
  tps: 53332.91785
 }
}
dps_results: {
 key: "TestDemonology-AllItems-TyrannicalGladiator'sBadgeofDominance-94346"
 value: {
  dps: 83860.07968
  tps: 53332.91785
 }
}
dps_results: {
 key: "TestDemonology-AllItems-TyrannicalGladiator'sBadgeofDominance-99937"
 value: {
  dps: 83860.07968
  tps: 53332.91785
 }
}
dps_results: {
 key: "TestDemonology-AllItems-TyrannicalGladiator'sBadgeofVictory-100019"
 value: {
  dps: 79009.03286
  tps: 50232.62871
 }
}
dps_results: {
 key: "TestDemonology-AllItems-TyrannicalGladiator'sBadgeofVictory-91410"
 value: {
  dps: 79009.03286
  tps: 50232.62871
 }
}
dps_results: {
 key: "TestDemonology-AllItems-TyrannicalGladiator'sBadgeofVictory-94349"
 value: {
  dps: 79009.03286
  tps: 50232.62871
 }
}
dps_results: {
 key: "TestDemonology-AllItems-TyrannicalGladiator'sBadgeofVictory-99943"
 value: {
  dps: 79009.03286
  tps: 50232.62871
 }
}
dps_results: {
 key: "TestDemonology-AllItems-TyrannicalGladiator'sEmblemofCruelty-100066"
 value: {
  dps: 80708.1931
  tps: 51102.8148
 }
}
dps_results: {
 key: "TestDemonology-AllItems-TyrannicalGladiator'sEmblemofCruelty-91209"
 value: {
  dps: 80708.1931
  tps: 51102.8148
 }
}
dps_results: {
 key: "TestDemonology-AllItems-TyrannicalGladiator'sEmblemofCruelty-94396"
 value: {
  dps: 80708.1931
  tps: 51102.8148
 }
}
dps_results: {
 key: "TestDemonology-AllItems-TyrannicalGladiator'sEmblemofCruelty-99838"
 value: {
  dps: 80708.1931
  tps: 51102.8148
 }
}
dps_results: {
 key: "TestDemonology-AllItems-TyrannicalGladiator'sEmblemofMeditation-91211"
 value: {
  dps: 79084.37008
  tps: 50223.88859
 }
}
dps_results: {
 key: "TestDemonology-AllItems-TyrannicalGladiator'sEmblemofMeditation-94329"
 value: {
  dps: 79084.37008
  tps: 50223.88859
 }
}
dps_results: {
 key: "TestDemonology-AllItems-TyrannicalGladiator'sEmblemofMeditation-99840"
 value: {
  dps: 79084.37008
  tps: 50223.88859
 }
}
dps_results: {
 key: "TestDemonology-AllItems-TyrannicalGladiator'sEmblemofMeditation-99990"
 value: {
  dps: 79084.37008
  tps: 50223.88859
 }
}
dps_results: {
 key: "TestDemonology-AllItems-TyrannicalGladiator'sEmblemofTenacity-100092"
 value: {
  dps: 79674.05187
  tps: 50585.37428
 }
}
dps_results: {
 key: "TestDemonology-AllItems-TyrannicalGladiator'sEmblemofTenacity-91210"
 value: {
  dps: 79674.05187
  tps: 50585.37428
 }
}
dps_results: {
 key: "TestDemonology-AllItems-TyrannicalGladiator'sEmblemofTenacity-94422"
 value: {
  dps: 79674.05187
  tps: 50585.37428
 }
}
dps_results: {
 key: "TestDemonology-AllItems-TyrannicalGladiator'sEmblemofTenacity-99839"
 value: {
  dps: 79674.05187
  tps: 50585.37428
 }
}
dps_results: {
 key: "TestDemonology-AllItems-TyrannicalGladiator'sInsigniaofConquest-100026"
 value: {
  dps: 79009.03286
  tps: 50232.62871
 }
}
dps_results: {
 key: "TestDemonology-AllItems-TyrannicalGladiator'sInsigniaofDominance-100152"
 value: {
  dps: 85806.31559
  tps: 54622.05243
 }
}
dps_results: {
 key: "TestDemonology-AllItems-TyrannicalGladiator'sInsigniaofVictory-100085"
 value: {
  dps: 79009.03286
  tps: 50232.62871
 }
}
dps_results: {
 key: "TestDemonology-AllItems-TyrannicalPrimalDiamond"
 value: {
  dps: 82947.80476
  tps: 52011.42013
 }
}
dps_results: {
 key: "TestDemonology-AllItems-UnerringVisionofLeiShen-96930"
 value: {
  dps: 92119.73618
  tps: 57640.90785
 }
}
dps_results: {
 key: "TestDemonology-AllItems-VaporshieldMedallion-93262"
 value: {
  dps: 81057.08445
  tps: 51750.49926
 }
}
dps_results: {
 key: "TestDemonology-AllItems-VialofDragon'sBlood-87063"
 value: {
  dps: 80590.49625
  tps: 51354.78246
 }
}
dps_results: {
 key: "TestDemonology-AllItems-VialofIchorousBlood-100963"
 value: {
  dps: 81789.98106
  tps: 51957.01873
 }
}
dps_results: {
 key: "TestDemonology-AllItems-VialofIchorousBlood-81264"
 value: {
  dps: 82195.70306
  tps: 52202.23898
 }
}
dps_results: {
 key: "TestDemonology-AllItems-ViciousTalismanoftheShado-PanAssault-94511"
 value: {
  dps: 80046.81129
  tps: 50922.12514
 }
}
dps_results: {
 key: "TestDemonology-AllItems-VisionofthePredator-81192"
 value: {
  dps: 84578.73852
  tps: 53456.49846
 }
}
dps_results: {
 key: "TestDemonology-AllItems-VolatileTalismanoftheShado-PanAssault-94510"
 value: {
  dps: 87275.71542
  tps: 55414.56549
 }
}
dps_results: {
 key: "TestDemonology-AllItems-WindsweptPages-81125"
 value: {
  dps: 80361.98843
  tps: 51145.52331
 }
}
dps_results: {
 key: "TestDemonology-AllItems-WoundripperMedallion-93253"
 value: {
  dps: 81799.05908
  tps: 51586.53119
 }
}
dps_results: {
 key: "TestDemonology-AllItems-Wushoolay'sFinalChoice-96785"
 value: {
  dps: 91016.4802
  tps: 57839.06545
 }
}
dps_results: {
 key: "TestDemonology-AllItems-Xing-Ho,BreathofYu'lon-102246"
 value: {
  dps: 116660.79864
  tps: 84741.94518
 }
}
dps_results: {
 key: "TestDemonology-AllItems-Yu'lon'sBite-103987"
 value: {
  dps: 91602.51063
  tps: 57555.22889
 }
}
dps_results: {
 key: "TestDemonology-AllItems-ZenAlchemistStone-75274"
 value: {
  dps: 85401.29116
  tps: 54277.34905
 }
}
dps_results: {
 key: "TestDemonology-Average-Default"
 value: {
  dps: 86003.19959
  tps: 54402.40995
 }
}
dps_results: {
 key: "TestDemonology-Settings-Goblin-preraid-Demonology Warlock-default-FullBuffs-25.0yards-LongMultiTarget"
 value: {
  dps: 209761.03047
  tps: 148296.50381
 }
}
dps_results: {
 key: "TestDemonology-Settings-Goblin-preraid-Demonology Warlock-default-FullBuffs-25.0yards-LongSingleTarget"
 value: {
  dps: 83012.35044
  tps: 52856.08473
 }
}
dps_results: {
 key: "TestDemonology-Settings-Goblin-preraid-Demonology Warlock-default-FullBuffs-25.0yards-ShortSingleTarget"
 value: {
  dps: 135627.7595
  tps: 73455.07781
 }
}
dps_results: {
 key: "TestDemonology-Settings-Goblin-preraid-Demonology Warlock-default-NoBuffs-25.0yards-LongMultiTarget"
 value: {
  dps: 132712.73581
  tps: 96279.86857
 }
}
dps_results: {
 key: "TestDemonology-Settings-Goblin-preraid-Demonology Warlock-default-NoBuffs-25.0yards-LongSingleTarget"
 value: {
  dps: 50394.17642
  tps: 33243.62498
 }
}
dps_results: {
 key: "TestDemonology-Settings-Goblin-preraid-Demonology Warlock-default-NoBuffs-25.0yards-ShortSingleTarget"
 value: {
  dps: 68472.67508
  tps: 38466.77167
 }
}
dps_results: {
 key: "TestDemonology-Settings-Human-preraid-Demonology Warlock-default-FullBuffs-25.0yards-LongMultiTarget"
 value: {
  dps: 207419.78255
  tps: 146476.31463
 }
}
dps_results: {
 key: "TestDemonology-Settings-Human-preraid-Demonology Warlock-default-FullBuffs-25.0yards-LongSingleTarget"
 value: {
  dps: 82751.6861
  tps: 52584.54252
 }
}
dps_results: {
 key: "TestDemonology-Settings-Human-preraid-Demonology Warlock-default-FullBuffs-25.0yards-ShortSingleTarget"
 value: {
  dps: 135789.79845
  tps: 74382.98528
 }
}
dps_results: {
 key: "TestDemonology-Settings-Human-preraid-Demonology Warlock-default-NoBuffs-25.0yards-LongMultiTarget"
 value: {
  dps: 132248.14774
  tps: 96191.32769
 }
}
dps_results: {
 key: "TestDemonology-Settings-Human-preraid-Demonology Warlock-default-NoBuffs-25.0yards-LongSingleTarget"
 value: {
  dps: 50131.09544
  tps: 33122.69344
 }
}
dps_results: {
 key: "TestDemonology-Settings-Human-preraid-Demonology Warlock-default-NoBuffs-25.0yards-ShortSingleTarget"
 value: {
  dps: 68283.07628
  tps: 38582.14835
 }
}
dps_results: {
 key: "TestDemonology-Settings-Orc-preraid-Demonology Warlock-default-FullBuffs-25.0yards-LongMultiTarget"
 value: {
  dps: 213554.63042
  tps: 148765.09312
 }
}
dps_results: {
 key: "TestDemonology-Settings-Orc-preraid-Demonology Warlock-default-FullBuffs-25.0yards-LongSingleTarget"
 value: {
  dps: 85514.61783
  tps: 53966.18336
 }
}
dps_results: {
 key: "TestDemonology-Settings-Orc-preraid-Demonology Warlock-default-FullBuffs-25.0yards-ShortSingleTarget"
 value: {
  dps: 140839.67557
  tps: 76419.00262
 }
}
dps_results: {
 key: "TestDemonology-Settings-Orc-preraid-Demonology Warlock-default-NoBuffs-25.0yards-LongMultiTarget"
 value: {
  dps: 136278.14211
  tps: 97591.99554
 }
}
dps_results: {
 key: "TestDemonology-Settings-Orc-preraid-Demonology Warlock-default-NoBuffs-25.0yards-LongSingleTarget"
 value: {
  dps: 51859.96753
  tps: 34006.10638
 }
}
dps_results: {
 key: "TestDemonology-Settings-Orc-preraid-Demonology Warlock-default-NoBuffs-25.0yards-ShortSingleTarget"
 value: {
  dps: 70794.16579
  tps: 39554.5662
 }
}
dps_results: {
 key: "TestDemonology-Settings-Troll-preraid-Demonology Warlock-default-FullBuffs-25.0yards-LongMultiTarget"
 value: {
  dps: 215992.91252
  tps: 153326.50639
 }
}
dps_results: {
 key: "TestDemonology-Settings-Troll-preraid-Demonology Warlock-default-FullBuffs-25.0yards-LongSingleTarget"
 value: {
  dps: 86032.68221
  tps: 54311.13867
 }
}
dps_results: {
 key: "TestDemonology-Settings-Troll-preraid-Demonology Warlock-default-FullBuffs-25.0yards-ShortSingleTarget"
 value: {
  dps: 146366.88071
  tps: 77140.37392
 }
}
dps_results: {
 key: "TestDemonology-Settings-Troll-preraid-Demonology Warlock-default-NoBuffs-25.0yards-LongMultiTarget"
 value: {
  dps: 138603.44004
  tps: 101173.74133
 }
}
dps_results: {
 key: "TestDemonology-Settings-Troll-preraid-Demonology Warlock-default-NoBuffs-25.0yards-LongSingleTarget"
 value: {
  dps: 51269.78579
  tps: 33774.63853
 }
}
dps_results: {
 key: "TestDemonology-Settings-Troll-preraid-Demonology Warlock-default-NoBuffs-25.0yards-ShortSingleTarget"
 value: {
  dps: 73675.12535
  tps: 41265.22559
 }
}
dps_results: {
 key: "TestDemonology-SwitchInFrontOfTarget-Default"
 value: {
  dps: 83832.3782
  tps: 53850.08722
 }
}
