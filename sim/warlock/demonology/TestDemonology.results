character_stats_results: {
 key: "TestDemonology-CharacterStats-Default"
 value: {
  final_stats: 157.5
  final_stats: 161.7
  final_stats: 18509.37
  final_stats: 15240.96
  final_stats: 280
  final_stats: 4640
  final_stats: 1842
  final_stats: 4107
  final_stats: 568
  final_stats: 0
  final_stats: 0
  final_stats: 7030
  final_stats: 162.25
  final_stats: 0
  final_stats: 22420.156
  final_stats: 0
  final_stats: 1054
  final_stats: 13312
  final_stats: 0
  final_stats: 446087.598
  final_stats: 300000
  final_stats: 15000
  final_stats: 13.64706
  final_stats: 15.31765
  final_stats: 10.69
  final_stats: 15.78545
  final_stats: 0
 }
}
dps_results: {
 key: "TestDemonology-AllItems-AgilePrimalDiamond"
 value: {
  dps: 84832.49087
  tps: 52622.12025
 }
}
dps_results: {
 key: "TestDemonology-AllItems-AlacrityofXuen-103989"
 value: {
  dps: 79628.24121
  tps: 50019.64582
 }
}
dps_results: {
 key: "TestDemonology-AllItems-ArcaneBadgeoftheShieldwall-93347"
 value: {
  dps: 84771.89731
  tps: 52953.25271
 }
}
dps_results: {
 key: "TestDemonology-AllItems-ArrowflightMedallion-93258"
 value: {
  dps: 82294.24514
  tps: 51316.27914
 }
}
dps_results: {
 key: "TestDemonology-AllItems-AssuranceofConsequence-105472"
 value: {
  dps: 79628.24121
  tps: 50019.64582
 }
}
dps_results: {
 key: "TestDemonology-AllItems-AusterePrimalDiamond"
 value: {
  dps: 84301.49115
  tps: 52034.54676
 }
}
dps_results: {
 key: "TestDemonology-AllItems-BadJuju-96781"
 value: {
  dps: 81927.69463
  tps: 51640.21596
 }
}
dps_results: {
 key: "TestDemonology-AllItems-BadgeofKypariZar-84079"
 value: {
  dps: 80501.03603
  tps: 50634.75949
 }
}
dps_results: {
 key: "TestDemonology-AllItems-Balespider'sBurningVestments"
 value: {
  dps: 65951.75535
  tps: 40644.21865
 }
}
dps_results: {
 key: "TestDemonology-AllItems-BlossomofPureSnow-89081"
 value: {
  dps: 86233.52171
  tps: 53736.95097
 }
}
dps_results: {
 key: "TestDemonology-AllItems-BottleofInfiniteStars-87057"
 value: {
  dps: 81226.83383
  tps: 51146.27506
 }
}
dps_results: {
 key: "TestDemonology-AllItems-BraidofTenSongs-84072"
 value: {
  dps: 80501.03603
  tps: 50634.75949
 }
}
dps_results: {
 key: "TestDemonology-AllItems-Brawler'sStatue-87571"
 value: {
  dps: 79628.24121
  tps: 50019.64582
 }
}
dps_results: {
 key: "TestDemonology-AllItems-BreathoftheHydra-96827"
 value: {
  dps: 93502.6183
  tps: 58325.26774
 }
}
dps_results: {
 key: "TestDemonology-AllItems-BroochofMunificentDeeds-87500"
 value: {
  dps: 79708.36832
  tps: 49961.53177
 }
}
dps_results: {
 key: "TestDemonology-AllItems-BrutalTalismanoftheShado-PanAssault-94508"
 value: {
  dps: 80747.83162
  tps: 50530.7859
 }
}
dps_results: {
 key: "TestDemonology-AllItems-BurningPrimalDiamond"
 value: {
  dps: 85621.98243
  tps: 53111.47122
 }
}
dps_results: {
 key: "TestDemonology-AllItems-CapacitivePrimalDiamond"
 value: {
  dps: 84719.16311
  tps: 52261.0712
 }
}
dps_results: {
 key: "TestDemonology-AllItems-CarbonicCarbuncle-81138"
 value: {
  dps: 81068.64446
  tps: 50790.804
 }
}
dps_results: {
 key: "TestDemonology-AllItems-Cha-Ye'sEssenceofBrilliance-96888"
 value: {
  dps: 90730.73585
  tps: 56505.27599
 }
}
dps_results: {
 key: "TestDemonology-AllItems-CharmofTenSongs-84071"
 value: {
  dps: 81930.92377
  tps: 51254.82595
 }
}
dps_results: {
 key: "TestDemonology-AllItems-CommunalIdolofDestruction-101168"
 value: {
  dps: 82744.5992
  tps: 52000.9015
 }
}
dps_results: {
 key: "TestDemonology-AllItems-CommunalStoneofDestruction-101171"
 value: {
  dps: 85327.4124
  tps: 53675.40611
 }
}
dps_results: {
 key: "TestDemonology-AllItems-CommunalStoneofWisdom-101183"
 value: {
  dps: 83353.76965
  tps: 52342.14527
 }
}
dps_results: {
 key: "TestDemonology-AllItems-ContemplationofChi-Ji-103688"
 value: {
  dps: 83840.04723
  tps: 52654.90808
 }
}
dps_results: {
 key: "TestDemonology-AllItems-ContemplationofChi-Ji-103988"
 value: {
  dps: 85870.04205
  tps: 53947.47454
 }
}
dps_results: {
 key: "TestDemonology-AllItems-Coren'sColdChromiumCoaster-87574"
 value: {
  dps: 80678.43295
  tps: 50609.75045
 }
}
dps_results: {
 key: "TestDemonology-AllItems-CoreofDecency-87497"
 value: {
  dps: 79683.77997
  tps: 50041.26608
 }
}
dps_results: {
 key: "TestDemonology-AllItems-CourageousPrimalDiamond"
 value: {
  dps: 85407.30547
  tps: 52728.60559
 }
}
dps_results: {
 key: "TestDemonology-AllItems-CraftedDreadfulGladiator'sBadgeofConquest-93419"
 value: {
  dps: 79628.24121
  tps: 50019.64582
 }
}
dps_results: {
 key: "TestDemonology-AllItems-CraftedDreadfulGladiator'sBadgeofDominance-93600"
 value: {
  dps: 83322.0657
  tps: 52361.26271
 }
}
dps_results: {
 key: "TestDemonology-AllItems-CraftedDreadfulGladiator'sBadgeofVictory-93606"
 value: {
  dps: 79628.24121
  tps: 50019.64582
 }
}
dps_results: {
 key: "TestDemonology-AllItems-CraftedDreadfulGladiator'sEmblemofCruelty-93485"
 value: {
  dps: 80577.78107
  tps: 50571.87805
 }
}
dps_results: {
 key: "TestDemonology-AllItems-CraftedDreadfulGladiator'sEmblemofMeditation-93487"
 value: {
  dps: 79720.09618
  tps: 50078.96982
 }
}
dps_results: {
 key: "TestDemonology-AllItems-CraftedDreadfulGladiator'sEmblemofTenacity-93486"
 value: {
  dps: 79979.62979
  tps: 50215.24354
 }
}
dps_results: {
 key: "TestDemonology-AllItems-CraftedDreadfulGladiator'sInsigniaofConquest-93424"
 value: {
  dps: 79628.24121
  tps: 50019.64582
 }
}
dps_results: {
 key: "TestDemonology-AllItems-CraftedDreadfulGladiator'sInsigniaofDominance-93601"
 value: {
  dps: 84435.26487
  tps: 53077.01862
 }
}
dps_results: {
 key: "TestDemonology-AllItems-CraftedDreadfulGladiator'sInsigniaofVictory-93611"
 value: {
  dps: 79628.24121
  tps: 50019.64582
 }
}
dps_results: {
 key: "TestDemonology-AllItems-CraftedMalevolentGladiator'sBadgeofConquest-98755"
 value: {
  dps: 79628.24121
  tps: 50019.64582
 }
}
dps_results: {
 key: "TestDemonology-AllItems-CraftedMalevolentGladiator'sBadgeofDominance-98910"
 value: {
  dps: 83893.92671
  tps: 52739.76761
 }
}
dps_results: {
 key: "TestDemonology-AllItems-CraftedMalevolentGladiator'sBadgeofVictory-98912"
 value: {
  dps: 79628.24121
  tps: 50019.64582
 }
}
dps_results: {
 key: "TestDemonology-AllItems-CraftedMalevolentGladiator'sEmblemofCruelty-98811"
 value: {
  dps: 80746.62349
  tps: 50627.38872
 }
}
dps_results: {
 key: "TestDemonology-AllItems-CraftedMalevolentGladiator'sEmblemofMeditation-98813"
 value: {
  dps: 79720.09618
  tps: 50078.96982
 }
}
dps_results: {
 key: "TestDemonology-AllItems-CraftedMalevolentGladiator'sEmblemofTenacity-98812"
 value: {
  dps: 80017.11556
  tps: 50228.05075
 }
}
dps_results: {
 key: "TestDemonology-AllItems-CraftedMalevolentGladiator'sInsigniaofConquest-98760"
 value: {
  dps: 79628.24121
  tps: 50019.64582
 }
}
dps_results: {
 key: "TestDemonology-AllItems-CraftedMalevolentGladiator'sInsigniaofDominance-98911"
 value: {
  dps: 85262.31338
  tps: 53593.19539
 }
}
dps_results: {
 key: "TestDemonology-AllItems-CraftedMalevolentGladiator'sInsigniaofVictory-98917"
 value: {
  dps: 79628.24121
  tps: 50019.64582
 }
}
dps_results: {
 key: "TestDemonology-AllItems-CurseofHubris-102307"
 value: {
  dps: 83277.35329
  tps: 51859.64111
 }
}
dps_results: {
 key: "TestDemonology-AllItems-CurseofHubris-104649"
 value: {
  dps: 83977.19031
  tps: 52369.4268
 }
}
dps_results: {
 key: "TestDemonology-AllItems-CurseofHubris-104898"
 value: {
  dps: 83020.86244
  tps: 51854.68862
 }
}
dps_results: {
 key: "TestDemonology-AllItems-CurseofHubris-105147"
 value: {
  dps: 83150.02892
  tps: 51928.02796
 }
}
dps_results: {
 key: "TestDemonology-AllItems-CurseofHubris-105396"
 value: {
  dps: 83622.17467
  tps: 52087.6161
 }
}
dps_results: {
 key: "TestDemonology-AllItems-CurseofHubris-105645"
 value: {
  dps: 84205.81459
  tps: 52409.13106
 }
}
dps_results: {
 key: "TestDemonology-AllItems-CutstitcherMedallion-93255"
 value: {
  dps: 83840.04723
  tps: 52654.90808
 }
}
dps_results: {
 key: "TestDemonology-AllItems-Daelo'sFinalWords-87496"
 value: {
  dps: 80747.83162
  tps: 50530.7859
 }
}
dps_results: {
 key: "TestDemonology-AllItems-DarkglowEmbroidery(Rank3)-4893"
 value: {
  dps: 81318.08315
  tps: 51457.65871
 }
}
dps_results: {
 key: "TestDemonology-AllItems-DarkmistVortex-87172"
 value: {
  dps: 82980.51074
  tps: 51925.61678
 }
}
dps_results: {
 key: "TestDemonology-AllItems-DeadeyeBadgeoftheShieldwall-93346"
 value: {
  dps: 81278.9426
  tps: 51196.12568
 }
}
dps_results: {
 key: "TestDemonology-AllItems-DelicateVialoftheSanguinaire-96895"
 value: {
  dps: 79628.24121
  tps: 50019.64582
 }
}
dps_results: {
 key: "TestDemonology-AllItems-DestructivePrimalDiamond"
 value: {
  dps: 84868.64493
  tps: 52339.66177
 }
}
dps_results: {
 key: "TestDemonology-AllItems-DisciplineofXuen-103986"
 value: {
  dps: 83405.42946
  tps: 52641.2216
 }
}
dps_results: {
 key: "TestDemonology-AllItems-Dominator'sArcaneBadge-93342"
 value: {
  dps: 84771.89731
  tps: 52953.25271
 }
}
dps_results: {
 key: "TestDemonology-AllItems-Dominator'sDeadeyeBadge-93341"
 value: {
  dps: 81278.9426
  tps: 51196.12568
 }
}
dps_results: {
 key: "TestDemonology-AllItems-Dominator'sDurableBadge-93345"
 value: {
  dps: 81278.9426
  tps: 51196.12568
 }
}
dps_results: {
 key: "TestDemonology-AllItems-Dominator'sKnightlyBadge-93344"
 value: {
  dps: 81278.9426
  tps: 51196.12568
 }
}
dps_results: {
 key: "TestDemonology-AllItems-Dominator'sMendingBadge-93343"
 value: {
  dps: 82598.95635
  tps: 51868.34101
 }
}
dps_results: {
 key: "TestDemonology-AllItems-DreadfulGladiator'sBadgeofConquest-84344"
 value: {
  dps: 79628.24121
  tps: 50019.64582
 }
}
dps_results: {
 key: "TestDemonology-AllItems-DreadfulGladiator'sBadgeofDominance-84488"
 value: {
  dps: 83322.0657
  tps: 52361.26271
 }
}
dps_results: {
 key: "TestDemonology-AllItems-DreadfulGladiator'sBadgeofVictory-84490"
 value: {
  dps: 79628.24121
  tps: 50019.64582
 }
}
dps_results: {
 key: "TestDemonology-AllItems-DreadfulGladiator'sEmblemofCruelty-84399"
 value: {
  dps: 80577.78107
  tps: 50571.87805
 }
}
dps_results: {
 key: "TestDemonology-AllItems-DreadfulGladiator'sEmblemofMeditation-84401"
 value: {
  dps: 79720.09618
  tps: 50078.96982
 }
}
dps_results: {
 key: "TestDemonology-AllItems-DreadfulGladiator'sEmblemofTenacity-84400"
 value: {
  dps: 80560.06426
  tps: 50672.82811
 }
}
dps_results: {
 key: "TestDemonology-AllItems-DreadfulGladiator'sInsigniaofConquest-84349"
 value: {
  dps: 79628.24121
  tps: 50019.64582
 }
}
dps_results: {
 key: "TestDemonology-AllItems-DreadfulGladiator'sInsigniaofDominance-84489"
 value: {
  dps: 84397.62479
  tps: 53096.90338
 }
}
dps_results: {
 key: "TestDemonology-AllItems-DreadfulGladiator'sInsigniaofVictory-84495"
 value: {
  dps: 79628.24121
  tps: 50019.64582
 }
}
dps_results: {
 key: "TestDemonology-AllItems-DurableBadgeoftheShieldwall-93350"
 value: {
  dps: 81278.9426
  tps: 51196.12568
 }
}
dps_results: {
 key: "TestDemonology-AllItems-EffulgentPrimalDiamond"
 value: {
  dps: 84301.49115
  tps: 52034.54676
 }
}
dps_results: {
 key: "TestDemonology-AllItems-EmberPrimalDiamond"
 value: {
  dps: 84925.04241
  tps: 52427.28206
 }
}
dps_results: {
 key: "TestDemonology-AllItems-EmblemofKypariZar-84077"
 value: {
  dps: 80367.40573
  tps: 50456.77087
 }
}
dps_results: {
 key: "TestDemonology-AllItems-EmblemoftheCatacombs-83733"
 value: {
  dps: 81966.50416
  tps: 51290.45359
 }
}
dps_results: {
 key: "TestDemonology-AllItems-EmptyFruitBarrel-81133"
 value: {
  dps: 79720.09618
  tps: 50078.3756
 }
}
dps_results: {
 key: "TestDemonology-AllItems-EnchantWeapon-BloodyDancingSteel-5125"
 value: {
  dps: 80686.92301
  tps: 51097.04352
 }
}
dps_results: {
 key: "TestDemonology-AllItems-EnchantWeapon-Colossus-4445"
 value: {
  dps: 80686.92301
  tps: 51097.04352
 }
}
dps_results: {
 key: "TestDemonology-AllItems-EnchantWeapon-DancingSteel-4444"
 value: {
  dps: 80686.92301
  tps: 51097.04352
 }
}
dps_results: {
 key: "TestDemonology-AllItems-EnchantWeapon-River'sSong-4446"
 value: {
  dps: 80686.92301
  tps: 51097.04352
 }
}
dps_results: {
 key: "TestDemonology-AllItems-EnchantWeapon-SpiritofConquest-5124"
 value: {
  dps: 80686.92301
  tps: 51097.04352
 }
}
dps_results: {
 key: "TestDemonology-AllItems-EnchantWeapon-Windsong-4441"
 value: {
  dps: 82095.69642
  tps: 52095.74241
 }
}
dps_results: {
 key: "TestDemonology-AllItems-EnigmaticPrimalDiamond"
 value: {
  dps: 84868.64493
  tps: 52339.66177
 }
}
dps_results: {
 key: "TestDemonology-AllItems-EssenceofTerror-87175"
 value: {
  dps: 88159.60761
  tps: 54956.11221
 }
}
dps_results: {
 key: "TestDemonology-AllItems-EternalPrimalDiamond"
 value: {
  dps: 84143.03892
  tps: 51945.34145
 }
}
dps_results: {
 key: "TestDemonology-AllItems-EvilEyeofGalakras-105491"
 value: {
  dps: 79628.24121
  tps: 50019.64582
 }
}
dps_results: {
 key: "TestDemonology-AllItems-FabledFeatherofJi-Kun-96842"
 value: {
  dps: 80747.83162
  tps: 50530.7859
 }
}
dps_results: {
 key: "TestDemonology-AllItems-FearwurmBadge-84074"
 value: {
  dps: 80367.40573
  tps: 50456.77087
 }
}
dps_results: {
 key: "TestDemonology-AllItems-FearwurmRelic-84070"
 value: {
  dps: 81352.26337
  tps: 50864.2539
 }
}
dps_results: {
 key: "TestDemonology-AllItems-FelsoulIdolofDestruction-101263"
 value: {
  dps: 83022.26826
  tps: 52111.96025
 }
}
dps_results: {
 key: "TestDemonology-AllItems-FelsoulStoneofDestruction-101266"
 value: {
  dps: 85378.79971
  tps: 53853.98921
 }
}
dps_results: {
 key: "TestDemonology-AllItems-FlashfrozenResinGlobule-100951"
 value: {
  dps: 83907.9533
  tps: 52253.91967
 }
}
dps_results: {
 key: "TestDemonology-AllItems-FlashingSteelTalisman-81265"
 value: {
  dps: 80747.83162
  tps: 50530.7859
 }
}
dps_results: {
 key: "TestDemonology-AllItems-FleetPrimalDiamond"
 value: {
  dps: 84743.5684
  tps: 52363.23675
 }
}
dps_results: {
 key: "TestDemonology-AllItems-ForlornPrimalDiamond"
 value: {
  dps: 84925.04241
  tps: 52427.28206
 }
}
dps_results: {
 key: "TestDemonology-AllItems-FortitudeoftheZandalari-94516"
 value: {
  dps: 81421.94998
  tps: 51280.07339
 }
}
dps_results: {
 key: "TestDemonology-AllItems-FortitudeoftheZandalari-95677"
 value: {
  dps: 81095.79565
  tps: 51050.27997
 }
}
dps_results: {
 key: "TestDemonology-AllItems-FortitudeoftheZandalari-96049"
 value: {
  dps: 81533.2878
  tps: 51358.51693
 }
}
dps_results: {
 key: "TestDemonology-AllItems-FortitudeoftheZandalari-96421"
 value: {
  dps: 81670.82276
  tps: 51455.41778
 }
}
dps_results: {
 key: "TestDemonology-AllItems-FortitudeoftheZandalari-96793"
 value: {
  dps: 81795.25914
  tps: 51543.08997
 }
}
dps_results: {
 key: "TestDemonology-AllItems-GazeoftheTwins-96915"
 value: {
  dps: 81663.92103
  tps: 51191.20907
 }
}
dps_results: {
 key: "TestDemonology-AllItems-Gerp'sPerfectArrow-87495"
 value: {
  dps: 80494.45863
  tps: 50533.06721
 }
}
dps_results: {
 key: "TestDemonology-AllItems-Gladiator'sFelshroud"
 value: {
  dps: 103331.5535
  tps: 63705.63449
 }
}
dps_results: {
 key: "TestDemonology-AllItems-GrievousGladiator'sBadgeofConquest-100195"
 value: {
  dps: 79628.24121
  tps: 50019.64582
 }
}
dps_results: {
 key: "TestDemonology-AllItems-GrievousGladiator'sBadgeofConquest-100603"
 value: {
  dps: 79628.24121
  tps: 50019.64582
 }
}
dps_results: {
 key: "TestDemonology-AllItems-GrievousGladiator'sBadgeofConquest-102856"
 value: {
  dps: 79628.24121
  tps: 50019.64582
 }
}
dps_results: {
 key: "TestDemonology-AllItems-GrievousGladiator'sBadgeofConquest-103145"
 value: {
  dps: 79628.24121
  tps: 50019.64582
 }
}
dps_results: {
 key: "TestDemonology-AllItems-GrievousGladiator'sBadgeofDominance-100490"
 value: {
  dps: 86059.9385
  tps: 54031.26956
 }
}
dps_results: {
 key: "TestDemonology-AllItems-GrievousGladiator'sBadgeofDominance-100576"
 value: {
  dps: 86059.9385
  tps: 54031.26956
 }
}
dps_results: {
 key: "TestDemonology-AllItems-GrievousGladiator'sBadgeofDominance-102830"
 value: {
  dps: 86059.9385
  tps: 54031.26956
 }
}
dps_results: {
 key: "TestDemonology-AllItems-GrievousGladiator'sBadgeofDominance-103308"
 value: {
  dps: 86059.9385
  tps: 54031.26956
 }
}
dps_results: {
 key: "TestDemonology-AllItems-GrievousGladiator'sBadgeofVictory-100500"
 value: {
  dps: 79628.24121
  tps: 50019.64582
 }
}
dps_results: {
 key: "TestDemonology-AllItems-GrievousGladiator'sBadgeofVictory-100579"
 value: {
  dps: 79628.24121
  tps: 50019.64582
 }
}
dps_results: {
 key: "TestDemonology-AllItems-GrievousGladiator'sBadgeofVictory-102833"
 value: {
  dps: 79628.24121
  tps: 50019.64582
 }
}
dps_results: {
 key: "TestDemonology-AllItems-GrievousGladiator'sBadgeofVictory-103314"
 value: {
  dps: 79628.24121
  tps: 50019.64582
 }
}
dps_results: {
 key: "TestDemonology-AllItems-GrievousGladiator'sEmblemofCruelty-100305"
 value: {
  dps: 81753.99041
  tps: 51176.41791
 }
}
dps_results: {
 key: "TestDemonology-AllItems-GrievousGladiator'sEmblemofCruelty-100626"
 value: {
  dps: 81753.99041
  tps: 51176.41791
 }
}
dps_results: {
 key: "TestDemonology-AllItems-GrievousGladiator'sEmblemofCruelty-102877"
 value: {
  dps: 81753.99041
  tps: 51176.41791
 }
}
dps_results: {
 key: "TestDemonology-AllItems-GrievousGladiator'sEmblemofCruelty-103210"
 value: {
  dps: 81753.99041
  tps: 51176.41791
 }
}
dps_results: {
 key: "TestDemonology-AllItems-GrievousGladiator'sEmblemofMeditation-100307"
 value: {
  dps: 79752.99343
  tps: 50106.30754
 }
}
dps_results: {
 key: "TestDemonology-AllItems-GrievousGladiator'sEmblemofMeditation-100559"
 value: {
  dps: 79752.99343
  tps: 50106.30754
 }
}
dps_results: {
 key: "TestDemonology-AllItems-GrievousGladiator'sEmblemofMeditation-102813"
 value: {
  dps: 79752.99343
  tps: 50106.30754
 }
}
dps_results: {
 key: "TestDemonology-AllItems-GrievousGladiator'sEmblemofMeditation-103212"
 value: {
  dps: 79752.99343
  tps: 50106.30754
 }
}
dps_results: {
 key: "TestDemonology-AllItems-GrievousGladiator'sEmblemofTenacity-100306"
 value: {
  dps: 80232.45711
  tps: 50433.42721
 }
}
dps_results: {
 key: "TestDemonology-AllItems-GrievousGladiator'sEmblemofTenacity-100652"
 value: {
  dps: 80232.45711
  tps: 50433.42721
 }
}
dps_results: {
 key: "TestDemonology-AllItems-GrievousGladiator'sEmblemofTenacity-102903"
 value: {
  dps: 80232.45711
  tps: 50433.42721
 }
}
dps_results: {
 key: "TestDemonology-AllItems-GrievousGladiator'sEmblemofTenacity-103211"
 value: {
  dps: 80232.45711
  tps: 50433.42721
 }
}
dps_results: {
 key: "TestDemonology-AllItems-GrievousGladiator'sInsigniaofConquest-103150"
 value: {
  dps: 79628.24121
  tps: 50019.64582
 }
}
dps_results: {
 key: "TestDemonology-AllItems-GrievousGladiator'sInsigniaofDominance-103309"
 value: {
  dps: 88911.58057
  tps: 56001.86347
 }
}
dps_results: {
 key: "TestDemonology-AllItems-GrievousGladiator'sInsigniaofVictory-103319"
 value: {
  dps: 79628.24121
  tps: 50019.64582
 }
}
dps_results: {
 key: "TestDemonology-AllItems-Hawkmaster'sTalon-89082"
 value: {
  dps: 82805.8947
  tps: 51726.83407
 }
}
dps_results: {
 key: "TestDemonology-AllItems-Heart-LesionDefenderIdol-100999"
 value: {
  dps: 80016.92604
  tps: 50319.48319
 }
}
dps_results: {
 key: "TestDemonology-AllItems-Heart-LesionDefenderStone-101002"
 value: {
  dps: 82336.69783
  tps: 51954.05025
 }
}
dps_results: {
 key: "TestDemonology-AllItems-Heart-LesionIdolofBattle-100991"
 value: {
  dps: 80806.19986
  tps: 50663.51781
 }
}
dps_results: {
 key: "TestDemonology-AllItems-Heart-LesionStoneofBattle-100990"
 value: {
  dps: 81702.69689
  tps: 51586.90645
 }
}
dps_results: {
 key: "TestDemonology-AllItems-HeartofFire-81181"
 value: {
  dps: 80739.90619
  tps: 50803.10638
 }
}
dps_results: {
 key: "TestDemonology-AllItems-HeartwarmerMedallion-93260"
 value: {
  dps: 83840.04723
  tps: 52654.90808
 }
}
dps_results: {
 key: "TestDemonology-AllItems-HelmbreakerMedallion-93261"
 value: {
  dps: 82294.24514
  tps: 51316.27914
 }
}
dps_results: {
 key: "TestDemonology-AllItems-Horridon'sLastGasp-96757"
 value: {
  dps: 86221.25091
  tps: 54173.93819
 }
}
dps_results: {
 key: "TestDemonology-AllItems-ImpassivePrimalDiamond"
 value: {
  dps: 84868.64493
  tps: 52339.66177
 }
}
dps_results: {
 key: "TestDemonology-AllItems-IndomitablePrimalDiamond"
 value: {
  dps: 84301.49115
  tps: 52034.54676
 }
}
dps_results: {
 key: "TestDemonology-AllItems-InscribedBagofHydra-Spawn-96828"
 value: {
  dps: 79728.2746
  tps: 50072.6999
 }
}
dps_results: {
 key: "TestDemonology-AllItems-InsigniaofKypariZar-84078"
 value: {
  dps: 79628.24121
  tps: 50019.64582
 }
}
dps_results: {
 key: "TestDemonology-AllItems-IronBellyWok-89083"
 value: {
  dps: 82805.8947
  tps: 51726.83407
 }
}
dps_results: {
 key: "TestDemonology-AllItems-IronProtectorTalisman-85181"
 value: {
  dps: 79993.28118
  tps: 50214.32232
 }
}
dps_results: {
 key: "TestDemonology-AllItems-JadeBanditFigurine-86043"
 value: {
  dps: 82805.8947
  tps: 51726.83407
 }
}
dps_results: {
 key: "TestDemonology-AllItems-JadeBanditFigurine-86772"
 value: {
  dps: 82717.49257
  tps: 51859.86116
 }
}
dps_results: {
 key: "TestDemonology-AllItems-JadeCharioteerFigurine-86042"
 value: {
  dps: 82805.8947
  tps: 51726.83407
 }
}
dps_results: {
 key: "TestDemonology-AllItems-JadeCharioteerFigurine-86771"
 value: {
  dps: 82717.49257
  tps: 51859.86116
 }
}
dps_results: {
 key: "TestDemonology-AllItems-JadeCourtesanFigurine-86045"
 value: {
  dps: 83565.78652
  tps: 52479.81963
 }
}
dps_results: {
 key: "TestDemonology-AllItems-JadeCourtesanFigurine-86774"
 value: {
  dps: 83121.45659
  tps: 52195.8427
 }
}
dps_results: {
 key: "TestDemonology-AllItems-JadeMagistrateFigurine-86044"
 value: {
  dps: 86233.52171
  tps: 53736.95097
 }
}
dps_results: {
 key: "TestDemonology-AllItems-JadeMagistrateFigurine-86773"
 value: {
  dps: 85538.13068
  tps: 53316.68998
 }
}
dps_results: {
 key: "TestDemonology-AllItems-JadeWarlordFigurine-86046"
 value: {
  dps: 82115.86049
  tps: 51841.72335
 }
}
dps_results: {
 key: "TestDemonology-AllItems-JadeWarlordFigurine-86775"
 value: {
  dps: 81876.62601
  tps: 51616.86479
 }
}
dps_results: {
 key: "TestDemonology-AllItems-Ji-Kun'sRisingWinds-96843"
 value: {
  dps: 80747.83162
  tps: 50530.7859
 }
}
dps_results: {
 key: "TestDemonology-AllItems-KnightlyBadgeoftheShieldwall-93349"
 value: {
  dps: 81278.9426
  tps: 51196.12568
 }
}
dps_results: {
 key: "TestDemonology-AllItems-KnotofTenSongs-84073"
 value: {
  dps: 79628.24121
  tps: 50019.64582
 }
}
dps_results: {
 key: "TestDemonology-AllItems-Kor'kronBookofHurting-92785"
 value: {
  dps: 79628.24121
  tps: 50019.64582
 }
}
dps_results: {
 key: "TestDemonology-AllItems-Lao-Chin'sLiquidCourage-89079"
 value: {
  dps: 82115.86049
  tps: 51841.72335
 }
}
dps_results: {
 key: "TestDemonology-AllItems-LeiShen'sFinalOrders-87072"
 value: {
  dps: 82315.85314
  tps: 51335.75009
 }
}
dps_results: {
 key: "TestDemonology-AllItems-LessonsoftheDarkmaster-81268"
 value: {
  dps: 80747.83162
  tps: 50530.7859
 }
}
dps_results: {
 key: "TestDemonology-AllItems-LightdrinkerIdolofRage-101200"
 value: {
  dps: 80968.27657
  tps: 50964.05342
 }
}
dps_results: {
 key: "TestDemonology-AllItems-LightdrinkerStoneofRage-101203"
 value: {
  dps: 81708.27131
  tps: 51565.5562
 }
}
dps_results: {
 key: "TestDemonology-AllItems-LightoftheCosmos-87065"
 value: {
  dps: 87796.44352
  tps: 54793.08168
 }
}
dps_results: {
 key: "TestDemonology-AllItems-LordBlastington'sScopeofDoom-4699"
 value: {
  dps: 80686.92301
  tps: 51097.04352
 }
}
dps_results: {
 key: "TestDemonology-AllItems-MalevolentGladiator'sBadgeofConquest-84934"
 value: {
  dps: 79628.24121
  tps: 50019.64582
 }
}
dps_results: {
 key: "TestDemonology-AllItems-MalevolentGladiator'sBadgeofConquest-91452"
 value: {
  dps: 79628.24121
  tps: 50019.64582
 }
}
dps_results: {
 key: "TestDemonology-AllItems-MalevolentGladiator'sBadgeofDominance-84940"
 value: {
  dps: 84101.25139
  tps: 52872.82073
 }
}
dps_results: {
 key: "TestDemonology-AllItems-MalevolentGladiator'sBadgeofDominance-91753"
 value: {
  dps: 83893.92671
  tps: 52739.76761
 }
}
dps_results: {
 key: "TestDemonology-AllItems-MalevolentGladiator'sBadgeofVictory-84942"
 value: {
  dps: 79628.24121
  tps: 50019.64582
 }
}
dps_results: {
 key: "TestDemonology-AllItems-MalevolentGladiator'sBadgeofVictory-91763"
 value: {
  dps: 79628.24121
  tps: 50019.64582
 }
}
dps_results: {
 key: "TestDemonology-AllItems-MalevolentGladiator'sEmblemofCruelty-84936"
 value: {
  dps: 80803.9231
  tps: 50661.51314
 }
}
dps_results: {
 key: "TestDemonology-AllItems-MalevolentGladiator'sEmblemofCruelty-91562"
 value: {
  dps: 80746.62349
  tps: 50627.38872
 }
}
dps_results: {
 key: "TestDemonology-AllItems-MalevolentGladiator'sEmblemofMeditation-84939"
 value: {
  dps: 79719.18931
  tps: 50087.35659
 }
}
dps_results: {
 key: "TestDemonology-AllItems-MalevolentGladiator'sEmblemofMeditation-91564"
 value: {
  dps: 79720.09618
  tps: 50078.96982
 }
}
dps_results: {
 key: "TestDemonology-AllItems-MalevolentGladiator'sEmblemofTenacity-84938"
 value: {
  dps: 80019.43338
  tps: 50227.94445
 }
}
dps_results: {
 key: "TestDemonology-AllItems-MalevolentGladiator'sEmblemofTenacity-91563"
 value: {
  dps: 80017.11556
  tps: 50228.05075
 }
}
dps_results: {
 key: "TestDemonology-AllItems-MalevolentGladiator'sInsigniaofConquest-91457"
 value: {
  dps: 79628.24121
  tps: 50019.64582
 }
}
dps_results: {
 key: "TestDemonology-AllItems-MalevolentGladiator'sInsigniaofDominance-91754"
 value: {
  dps: 85391.30876
  tps: 53743.36631
 }
}
dps_results: {
 key: "TestDemonology-AllItems-MalevolentGladiator'sInsigniaofVictory-91768"
 value: {
  dps: 79628.24121
  tps: 50019.64582
 }
}
dps_results: {
 key: "TestDemonology-AllItems-MarkoftheCatacombs-83731"
 value: {
  dps: 81435.314
  tps: 51279.14375
 }
}
dps_results: {
 key: "TestDemonology-AllItems-MarkoftheHardenedGrunt-92783"
 value: {
  dps: 79628.24121
  tps: 50019.64582
 }
}
dps_results: {
 key: "TestDemonology-AllItems-MedallionofMystifyingVapors-93257"
 value: {
  dps: 81903.36124
  tps: 51651.75616
 }
}
dps_results: {
 key: "TestDemonology-AllItems-MedallionoftheCatacombs-83734"
 value: {
  dps: 80438.03731
  tps: 50590.3603
 }
}
dps_results: {
 key: "TestDemonology-AllItems-MendingBadgeoftheShieldwall-93348"
 value: {
  dps: 82598.95635
  tps: 51868.34101
 }
}
dps_results: {
 key: "TestDemonology-AllItems-MirrorScope-4700"
 value: {
  dps: 80686.92301
  tps: 51097.04352
 }
}
dps_results: {
 key: "TestDemonology-AllItems-MistdancerDefenderIdol-101089"
 value: {
  dps: 80016.92604
  tps: 50319.48319
 }
}
dps_results: {
 key: "TestDemonology-AllItems-MistdancerDefenderStone-101087"
 value: {
  dps: 81896.87205
  tps: 51703.52477
 }
}
dps_results: {
 key: "TestDemonology-AllItems-MistdancerIdolofRage-101113"
 value: {
  dps: 80968.27657
  tps: 50964.05342
 }
}
dps_results: {
 key: "TestDemonology-AllItems-MistdancerStoneofRage-101117"
 value: {
  dps: 81562.87555
  tps: 51274.74508
 }
}
dps_results: {
 key: "TestDemonology-AllItems-MistdancerStoneofWisdom-101107"
 value: {
  dps: 83353.76965
  tps: 52342.14527
 }
}
dps_results: {
 key: "TestDemonology-AllItems-MithrilWristwatch-87572"
 value: {
  dps: 81873.16639
  tps: 51340.46704
 }
}
dps_results: {
 key: "TestDemonology-AllItems-MountainsageIdolofDestruction-101069"
 value: {
  dps: 83496.68269
  tps: 52471.79512
 }
}
dps_results: {
 key: "TestDemonology-AllItems-MountainsageStoneofDestruction-101072"
 value: {
  dps: 85550.81647
  tps: 53883.48744
 }
}
dps_results: {
 key: "TestDemonology-AllItems-OathswornDefenderIdol-101303"
 value: {
  dps: 80016.92604
  tps: 50319.48319
 }
}
dps_results: {
 key: "TestDemonology-AllItems-OathswornDefenderStone-101306"
 value: {
  dps: 81942.43879
  tps: 51706.98054
 }
}
dps_results: {
 key: "TestDemonology-AllItems-OathswornIdolofBattle-101295"
 value: {
  dps: 80806.19986
  tps: 50663.51781
 }
}
dps_results: {
 key: "TestDemonology-AllItems-OathswornStoneofBattle-101294"
 value: {
  dps: 81563.9404
  tps: 51322.29321
 }
}
dps_results: {
 key: "TestDemonology-AllItems-PhaseFingers-4697"
 value: {
  dps: 85381.30889
  tps: 52943.36042
 }
}
dps_results: {
 key: "TestDemonology-AllItems-PouchofWhiteAsh-103639"
 value: {
  dps: 79628.24121
  tps: 50019.64582
 }
}
dps_results: {
 key: "TestDemonology-AllItems-PowerfulPrimalDiamond"
 value: {
  dps: 84301.49115
  tps: 52034.54676
 }
}
dps_results: {
 key: "TestDemonology-AllItems-PriceofProgress-81266"
 value: {
  dps: 82734.34169
  tps: 51950.65948
 }
}
dps_results: {
 key: "TestDemonology-AllItems-PridefulGladiator'sBadgeofConquest-102659"
 value: {
  dps: 79628.24121
  tps: 50019.64582
 }
}
dps_results: {
 key: "TestDemonology-AllItems-PridefulGladiator'sBadgeofConquest-103342"
 value: {
  dps: 79628.24121
  tps: 50019.64582
 }
}
dps_results: {
 key: "TestDemonology-AllItems-PridefulGladiator'sBadgeofDominance-102633"
 value: {
  dps: 88113.78171
  tps: 55263.64809
 }
}
dps_results: {
 key: "TestDemonology-AllItems-PridefulGladiator'sBadgeofDominance-103505"
 value: {
  dps: 88113.78171
  tps: 55263.64809
 }
}
dps_results: {
 key: "TestDemonology-AllItems-PridefulGladiator'sBadgeofVictory-102636"
 value: {
  dps: 79628.24121
  tps: 50019.64582
 }
}
dps_results: {
 key: "TestDemonology-AllItems-PridefulGladiator'sBadgeofVictory-103511"
 value: {
  dps: 79628.24121
  tps: 50019.64582
 }
}
dps_results: {
 key: "TestDemonology-AllItems-PridefulGladiator'sEmblemofCruelty-102680"
 value: {
  dps: 82290.4376
  tps: 51494.80105
 }
}
dps_results: {
 key: "TestDemonology-AllItems-PridefulGladiator'sEmblemofCruelty-103407"
 value: {
  dps: 82290.4376
  tps: 51494.80105
 }
}
dps_results: {
 key: "TestDemonology-AllItems-PridefulGladiator'sEmblemofMeditation-102616"
 value: {
  dps: 79728.2746
  tps: 50072.99701
 }
}
dps_results: {
 key: "TestDemonology-AllItems-PridefulGladiator'sEmblemofMeditation-103409"
 value: {
  dps: 79728.2746
  tps: 50072.99701
 }
}
dps_results: {
 key: "TestDemonology-AllItems-PridefulGladiator'sEmblemofTenacity-102706"
 value: {
  dps: 79976.22077
  tps: 50251.78172
 }
}
dps_results: {
 key: "TestDemonology-AllItems-PridefulGladiator'sEmblemofTenacity-103408"
 value: {
  dps: 79976.22077
  tps: 50251.78172
 }
}
dps_results: {
 key: "TestDemonology-AllItems-PridefulGladiator'sInsigniaofConquest-103347"
 value: {
  dps: 79628.24121
  tps: 50019.64582
 }
}
dps_results: {
 key: "TestDemonology-AllItems-PridefulGladiator'sInsigniaofDominance-103506"
 value: {
  dps: 92113.4694
  tps: 58119.8557
 }
}
dps_results: {
 key: "TestDemonology-AllItems-PridefulGladiator'sInsigniaofVictory-103516"
 value: {
  dps: 79628.24121
  tps: 50019.64582
 }
}
dps_results: {
 key: "TestDemonology-AllItems-Primordius'TalismanofRage-96873"
 value: {
  dps: 82110.08475
  tps: 51390.54752
 }
}
dps_results: {
 key: "TestDemonology-AllItems-Qin-xi'sPolarizingSeal-87075"
 value: {
  dps: 79706.42702
  tps: 50063.37856
 }
}
dps_results: {
 key: "TestDemonology-AllItems-RegaliaoftheHornedNightmare"
 value: {
  dps: 98201.57184
  tps: 61652.78749
 }
}
dps_results: {
 key: "TestDemonology-AllItems-RegaliaoftheThousandfoldHells"
 value: {
  dps: 95375.14487
  tps: 59046.12347
 }
}
dps_results: {
 key: "TestDemonology-AllItems-RelicofChi-Ji-79330"
 value: {
  dps: 83859.37651
  tps: 52665.62899
 }
}
dps_results: {
 key: "TestDemonology-AllItems-RelicofKypariZar-84075"
 value: {
  dps: 83038.11707
  tps: 51930.35674
 }
}
dps_results: {
 key: "TestDemonology-AllItems-RelicofNiuzao-79329"
 value: {
  dps: 80103.23812
  tps: 50313.47648
 }
}
dps_results: {
 key: "TestDemonology-AllItems-RelicofXuen-79327"
 value: {
  dps: 79628.24121
  tps: 50019.64582
 }
}
dps_results: {
 key: "TestDemonology-AllItems-RelicofXuen-79328"
 value: {
  dps: 79628.24121
  tps: 50019.64582
 }
}
dps_results: {
 key: "TestDemonology-AllItems-Renataki'sSoulCharm-96741"
 value: {
  dps: 80747.83162
  tps: 50530.7859
 }
}
dps_results: {
 key: "TestDemonology-AllItems-ResolveofNiuzao-103690"
 value: {
  dps: 81140.21058
  tps: 51085.22619
 }
}
dps_results: {
 key: "TestDemonology-AllItems-ResolveofNiuzao-103990"
 value: {
  dps: 81803.00965
  tps: 51552.34258
 }
}
dps_results: {
 key: "TestDemonology-AllItems-ReverberatingPrimalDiamond"
 value: {
  dps: 84832.49087
  tps: 52622.12025
 }
}
dps_results: {
 key: "TestDemonology-AllItems-RevitalizingPrimalDiamond"
 value: {
  dps: 84855.10838
  tps: 52634.55897
 }
}
dps_results: {
 key: "TestDemonology-AllItems-SI:7Operative'sManual-92784"
 value: {
  dps: 79628.24121
  tps: 50019.64582
 }
}
dps_results: {
 key: "TestDemonology-AllItems-ScrollofReveredAncestors-89080"
 value: {
  dps: 83565.78652
  tps: 52479.81963
 }
}
dps_results: {
 key: "TestDemonology-AllItems-SearingWords-81267"
 value: {
  dps: 80610.49859
  tps: 50584.76136
 }
}
dps_results: {
 key: "TestDemonology-AllItems-Sha-SkinRegalia"
 value: {
  dps: 88193.59769
  tps: 55725.40912
 }
}
dps_results: {
 key: "TestDemonology-AllItems-ShadowflameRegalia"
 value: {
  dps: 62922.40724
  tps: 38841.37766
 }
}
dps_results: {
 key: "TestDemonology-AllItems-Shock-ChargerMedallion-93259"
 value: {
  dps: 88275.29129
  tps: 55012.24762
 }
}
dps_results: {
 key: "TestDemonology-AllItems-SigilofCompassion-83736"
 value: {
  dps: 80438.03731
  tps: 50590.3603
 }
}
dps_results: {
 key: "TestDemonology-AllItems-SigilofDevotion-83740"
 value: {
  dps: 80364.29475
  tps: 50446.95469
 }
}
dps_results: {
 key: "TestDemonology-AllItems-SigilofFidelity-83737"
 value: {
  dps: 82864.96165
  tps: 51827.22272
 }
}
dps_results: {
 key: "TestDemonology-AllItems-SigilofGrace-83738"
 value: {
  dps: 80438.03731
  tps: 50590.3603
 }
}
dps_results: {
 key: "TestDemonology-AllItems-SigilofKypariZar-84076"
 value: {
  dps: 81563.76821
  tps: 51341.47069
 }
}
dps_results: {
 key: "TestDemonology-AllItems-SigilofPatience-83739"
 value: {
  dps: 79628.24121
  tps: 50019.64582
 }
}
dps_results: {
 key: "TestDemonology-AllItems-SigiloftheCatacombs-83732"
 value: {
  dps: 81670.12826
  tps: 51083.60997
 }
}
dps_results: {
 key: "TestDemonology-AllItems-SinisterPrimalDiamond"
 value: {
  dps: 84719.16311
  tps: 52261.0712
 }
}
dps_results: {
 key: "TestDemonology-AllItems-SkullrenderMedallion-93256"
 value: {
  dps: 82294.24514
  tps: 51316.27914
 }
}
dps_results: {
 key: "TestDemonology-AllItems-SpiritsoftheSun-87163"
 value: {
  dps: 84613.83559
  tps: 53154.04361
 }
}
dps_results: {
 key: "TestDemonology-AllItems-SpringrainIdolofDestruction-101023"
 value: {
  dps: 82767.92346
  tps: 51891.12332
 }
}
dps_results: {
 key: "TestDemonology-AllItems-SpringrainIdolofRage-101009"
 value: {
  dps: 80968.27657
  tps: 50964.05342
 }
}
dps_results: {
 key: "TestDemonology-AllItems-SpringrainStoneofDestruction-101026"
 value: {
  dps: 85573.9416
  tps: 53944.65012
 }
}
dps_results: {
 key: "TestDemonology-AllItems-SpringrainStoneofRage-101012"
 value: {
  dps: 81760.17595
  tps: 51575.21112
 }
}
dps_results: {
 key: "TestDemonology-AllItems-SpringrainStoneofWisdom-101041"
 value: {
  dps: 83353.76965
  tps: 52342.14527
 }
}
dps_results: {
 key: "TestDemonology-AllItems-Static-Caster'sMedallion-93254"
 value: {
  dps: 88275.29129
  tps: 55012.24762
 }
}
dps_results: {
 key: "TestDemonology-AllItems-SteadfastFootman'sMedallion-92782"
 value: {
  dps: 79628.24121
  tps: 50019.64582
 }
}
dps_results: {
 key: "TestDemonology-AllItems-SteadfastTalismanoftheShado-PanAssault-94507"
 value: {
  dps: 81553.63971
  tps: 51376.59582
 }
}
dps_results: {
 key: "TestDemonology-AllItems-StreamtalkerIdolofDestruction-101222"
 value: {
  dps: 84155.46328
  tps: 52691.58396
 }
}
dps_results: {
 key: "TestDemonology-AllItems-StreamtalkerIdolofRage-101217"
 value: {
  dps: 80968.27657
  tps: 50964.05342
 }
}
dps_results: {
 key: "TestDemonology-AllItems-StreamtalkerStoneofDestruction-101225"
 value: {
  dps: 85541.91523
  tps: 53918.76355
 }
}
dps_results: {
 key: "TestDemonology-AllItems-StreamtalkerStoneofRage-101220"
 value: {
  dps: 81500.52124
  tps: 51283.45716
 }
}
dps_results: {
 key: "TestDemonology-AllItems-StreamtalkerStoneofWisdom-101250"
 value: {
  dps: 83353.76965
  tps: 52342.14527
 }
}
dps_results: {
 key: "TestDemonology-AllItems-StuffofNightmares-87160"
 value: {
  dps: 81334.45665
  tps: 51222.12367
 }
}
dps_results: {
 key: "TestDemonology-AllItems-SunsoulDefenderIdol-101160"
 value: {
  dps: 80016.92604
  tps: 50319.48319
 }
}
dps_results: {
 key: "TestDemonology-AllItems-SunsoulDefenderStone-101163"
 value: {
  dps: 82042.74526
  tps: 51671.12042
 }
}
dps_results: {
 key: "TestDemonology-AllItems-SunsoulIdolofBattle-101152"
 value: {
  dps: 80806.19986
  tps: 50663.51781
 }
}
dps_results: {
 key: "TestDemonology-AllItems-SunsoulStoneofBattle-101151"
 value: {
  dps: 82232.29945
  tps: 51905.98481
 }
}
dps_results: {
 key: "TestDemonology-AllItems-SunsoulStoneofWisdom-101138"
 value: {
  dps: 83353.76965
  tps: 52342.14527
 }
}
dps_results: {
 key: "TestDemonology-AllItems-SwordguardEmbroidery(Rank3)-4894"
 value: {
  dps: 81318.08315
  tps: 51457.65871
 }
}
dps_results: {
 key: "TestDemonology-AllItems-SymboloftheCatacombs-83735"
 value: {
  dps: 80438.03731
  tps: 50590.3603
 }
}
dps_results: {
 key: "TestDemonology-AllItems-SynapseSprings(MarkII)-4898"
 value: {
  dps: 86675.20839
  tps: 53788.81231
 }
}
dps_results: {
 key: "TestDemonology-AllItems-TalismanofBloodlust-96864"
 value: {
  dps: 82045.03618
  tps: 51439.4722
 }
}
dps_results: {
 key: "TestDemonology-AllItems-TerrorintheMists-87167"
 value: {
  dps: 82746.53229
  tps: 51770.49777
 }
}
dps_results: {
 key: "TestDemonology-AllItems-Thousand-YearPickledEgg-87573"
 value: {
  dps: 82941.00622
  tps: 52082.0384
 }
}
dps_results: {
 key: "TestDemonology-AllItems-TrailseekerIdolofRage-101054"
 value: {
  dps: 80968.27657
  tps: 50964.05342
 }
}
dps_results: {
 key: "TestDemonology-AllItems-TrailseekerStoneofRage-101057"
 value: {
  dps: 81658.16497
  tps: 51414.18112
 }
}
dps_results: {
 key: "TestDemonology-AllItems-TyrannicalGladiator'sBadgeofConquest-100043"
 value: {
  dps: 79628.24121
  tps: 50019.64582
 }
}
dps_results: {
 key: "TestDemonology-AllItems-TyrannicalGladiator'sBadgeofConquest-91099"
 value: {
  dps: 79628.24121
  tps: 50019.64582
 }
}
dps_results: {
 key: "TestDemonology-AllItems-TyrannicalGladiator'sBadgeofConquest-94373"
 value: {
  dps: 79628.24121
  tps: 50019.64582
 }
}
dps_results: {
 key: "TestDemonology-AllItems-TyrannicalGladiator'sBadgeofConquest-99772"
 value: {
  dps: 79628.24121
  tps: 50019.64582
 }
}
dps_results: {
 key: "TestDemonology-AllItems-TyrannicalGladiator'sBadgeofDominance-100016"
 value: {
  dps: 84728.43799
  tps: 53258.36292
 }
}
dps_results: {
 key: "TestDemonology-AllItems-TyrannicalGladiator'sBadgeofDominance-91400"
 value: {
  dps: 84728.43799
  tps: 53258.36292
 }
}
dps_results: {
 key: "TestDemonology-AllItems-TyrannicalGladiator'sBadgeofDominance-94346"
 value: {
  dps: 84728.43799
  tps: 53258.36292
 }
}
dps_results: {
 key: "TestDemonology-AllItems-TyrannicalGladiator'sBadgeofDominance-99937"
 value: {
  dps: 84728.43799
  tps: 53258.36292
 }
}
dps_results: {
 key: "TestDemonology-AllItems-TyrannicalGladiator'sBadgeofVictory-100019"
 value: {
  dps: 79628.24121
  tps: 50019.64582
 }
}
dps_results: {
 key: "TestDemonology-AllItems-TyrannicalGladiator'sBadgeofVictory-91410"
 value: {
  dps: 79628.24121
  tps: 50019.64582
 }
}
dps_results: {
 key: "TestDemonology-AllItems-TyrannicalGladiator'sBadgeofVictory-94349"
 value: {
  dps: 79628.24121
  tps: 50019.64582
 }
}
dps_results: {
 key: "TestDemonology-AllItems-TyrannicalGladiator'sBadgeofVictory-99943"
 value: {
  dps: 79628.24121
  tps: 50019.64582
 }
}
dps_results: {
 key: "TestDemonology-AllItems-TyrannicalGladiator'sEmblemofCruelty-100066"
 value: {
  dps: 81267.82707
  tps: 50934.40693
 }
}
dps_results: {
 key: "TestDemonology-AllItems-TyrannicalGladiator'sEmblemofCruelty-91209"
 value: {
  dps: 81267.82707
  tps: 50934.40693
 }
}
dps_results: {
 key: "TestDemonology-AllItems-TyrannicalGladiator'sEmblemofCruelty-94396"
 value: {
  dps: 81267.82707
  tps: 50934.40693
 }
}
dps_results: {
 key: "TestDemonology-AllItems-TyrannicalGladiator'sEmblemofCruelty-99838"
 value: {
  dps: 81267.82707
  tps: 50934.40693
 }
}
dps_results: {
 key: "TestDemonology-AllItems-TyrannicalGladiator'sEmblemofMeditation-91211"
 value: {
  dps: 79694.36926
  tps: 50057.49155
 }
}
dps_results: {
 key: "TestDemonology-AllItems-TyrannicalGladiator'sEmblemofMeditation-94329"
 value: {
  dps: 79694.36926
  tps: 50057.49155
 }
}
dps_results: {
 key: "TestDemonology-AllItems-TyrannicalGladiator'sEmblemofMeditation-99840"
 value: {
  dps: 79694.36926
  tps: 50057.49155
 }
}
dps_results: {
 key: "TestDemonology-AllItems-TyrannicalGladiator'sEmblemofMeditation-99990"
 value: {
  dps: 79694.36926
  tps: 50057.49155
 }
}
dps_results: {
 key: "TestDemonology-AllItems-TyrannicalGladiator'sEmblemofTenacity-100092"
 value: {
  dps: 80100.24899
  tps: 50275.34372
 }
}
dps_results: {
 key: "TestDemonology-AllItems-TyrannicalGladiator'sEmblemofTenacity-91210"
 value: {
  dps: 80100.24899
  tps: 50275.34372
 }
}
dps_results: {
 key: "TestDemonology-AllItems-TyrannicalGladiator'sEmblemofTenacity-94422"
 value: {
  dps: 80100.24899
  tps: 50275.34372
 }
}
dps_results: {
 key: "TestDemonology-AllItems-TyrannicalGladiator'sEmblemofTenacity-99839"
 value: {
  dps: 80100.24899
  tps: 50275.34372
 }
}
dps_results: {
 key: "TestDemonology-AllItems-TyrannicalGladiator'sInsigniaofConquest-100026"
 value: {
  dps: 79628.24121
  tps: 50019.64582
 }
}
dps_results: {
 key: "TestDemonology-AllItems-TyrannicalGladiator'sInsigniaofDominance-100152"
 value: {
  dps: 86976.83594
  tps: 54799.21817
 }
}
dps_results: {
 key: "TestDemonology-AllItems-TyrannicalGladiator'sInsigniaofVictory-100085"
 value: {
  dps: 79628.24121
  tps: 50019.64582
 }
}
dps_results: {
 key: "TestDemonology-AllItems-TyrannicalPrimalDiamond"
 value: {
  dps: 84143.03892
  tps: 51945.34145
 }
}
dps_results: {
 key: "TestDemonology-AllItems-UnerringVisionofLeiShen-96930"
 value: {
  dps: 94759.22728
  tps: 57819.25773
 }
}
dps_results: {
 key: "TestDemonology-AllItems-VaporshieldMedallion-93262"
 value: {
  dps: 81903.36124
  tps: 51651.75616
 }
}
dps_results: {
 key: "TestDemonology-AllItems-VestmentsoftheFacelessShroud"
 value: {
  dps: 64128.08426
  tps: 39040.34741
 }
}
dps_results: {
 key: "TestDemonology-AllItems-VialofDragon'sBlood-87063"
 value: {
  dps: 81226.83383
  tps: 51146.27506
 }
}
dps_results: {
 key: "TestDemonology-AllItems-VialofIchorousBlood-100963"
 value: {
  dps: 82297.66676
  tps: 51670.97728
 }
}
dps_results: {
 key: "TestDemonology-AllItems-VialofIchorousBlood-81264"
 value: {
  dps: 82734.34169
  tps: 51950.65948
 }
}
dps_results: {
 key: "TestDemonology-AllItems-ViciousTalismanoftheShado-PanAssault-94511"
 value: {
  dps: 80747.83162
  tps: 50530.7859
 }
}
dps_results: {
 key: "TestDemonology-AllItems-VisionofthePredator-81192"
 value: {
  dps: 84618.75955
  tps: 52933.69335
 }
}
dps_results: {
 key: "TestDemonology-AllItems-VolatileTalismanoftheShado-PanAssault-94510"
 value: {
  dps: 88855.67665
  tps: 55680.51944
 }
}
dps_results: {
 key: "TestDemonology-AllItems-WindsweptPages-81125"
 value: {
  dps: 81223.14659
  tps: 51020.93346
 }
}
dps_results: {
 key: "TestDemonology-AllItems-WoundripperMedallion-93253"
 value: {
  dps: 82294.24514
  tps: 51316.27914
 }
}
dps_results: {
 key: "TestDemonology-AllItems-Wushoolay'sFinalChoice-96785"
 value: {
  dps: 107582.8696
  tps: 67288.72616
 }
}
dps_results: {
 key: "TestDemonology-AllItems-Yu'lon'sBite-103987"
 value: {
  dps: 91028.06939
  tps: 56663.22908
 }
}
dps_results: {
 key: "TestDemonology-AllItems-ZenAlchemistStone-75274"
 value: {
  dps: 86603.64215
  tps: 54632.79035
 }
}
dps_results: {
 key: "TestDemonology-Average-Default"
 value: {
  dps: 86845.68655
  tps: 54025.43961
 }
}
dps_results: {
 key: "TestDemonology-Settings-Goblin-preraid-Demonology Warlock-default-FullBuffs-25.0yards-LongMultiTarget"
 value: {
  dps: 213562.68738
  tps: 151413.01016
 }
}
dps_results: {
 key: "TestDemonology-Settings-Goblin-preraid-Demonology Warlock-default-FullBuffs-25.0yards-LongSingleTarget"
 value: {
  dps: 84929.0166
  tps: 53314.6093
 }
}
dps_results: {
 key: "TestDemonology-Settings-Goblin-preraid-Demonology Warlock-default-FullBuffs-25.0yards-ShortSingleTarget"
 value: {
  dps: 147636.62006
  tps: 79433.95186
 }
}
dps_results: {
 key: "TestDemonology-Settings-Goblin-preraid-Demonology Warlock-default-NoBuffs-25.0yards-LongMultiTarget"
 value: {
  dps: 132737.7547
  tps: 97238.67856
 }
}
dps_results: {
 key: "TestDemonology-Settings-Goblin-preraid-Demonology Warlock-default-NoBuffs-25.0yards-LongSingleTarget"
 value: {
  dps: 50439.25141
  tps: 32925.59811
 }
}
dps_results: {
 key: "TestDemonology-Settings-Goblin-preraid-Demonology Warlock-default-NoBuffs-25.0yards-ShortSingleTarget"
 value: {
  dps: 72050.74141
  tps: 39379.09477
 }
}
dps_results: {
 key: "TestDemonology-Settings-Human-preraid-Demonology Warlock-default-FullBuffs-25.0yards-LongMultiTarget"
 value: {
  dps: 212479.39302
  tps: 150563.66374
 }
}
dps_results: {
 key: "TestDemonology-Settings-Human-preraid-Demonology Warlock-default-FullBuffs-25.0yards-LongSingleTarget"
 value: {
  dps: 84184.74573
  tps: 52698.83834
 }
}
dps_results: {
 key: "TestDemonology-Settings-Human-preraid-Demonology Warlock-default-FullBuffs-25.0yards-ShortSingleTarget"
 value: {
  dps: 145669.97975
  tps: 78378.92367
 }
}
dps_results: {
 key: "TestDemonology-Settings-Human-preraid-Demonology Warlock-default-NoBuffs-25.0yards-LongMultiTarget"
 value: {
  dps: 134057.37812
  tps: 98786.02887
 }
}
dps_results: {
 key: "TestDemonology-Settings-Human-preraid-Demonology Warlock-default-NoBuffs-25.0yards-LongSingleTarget"
 value: {
  dps: 50777.21967
  tps: 33021.97105
 }
}
dps_results: {
 key: "TestDemonology-Settings-Human-preraid-Demonology Warlock-default-NoBuffs-25.0yards-ShortSingleTarget"
 value: {
  dps: 71750.39744
  tps: 39167.75381
 }
}
dps_results: {
 key: "TestDemonology-Settings-Orc-preraid-Demonology Warlock-default-FullBuffs-25.0yards-LongMultiTarget"
 value: {
  dps: 218643.33703
  tps: 152834.88755
 }
}
dps_results: {
 key: "TestDemonology-Settings-Orc-preraid-Demonology Warlock-default-FullBuffs-25.0yards-LongSingleTarget"
 value: {
  dps: 86919.86899
  tps: 53959.92414
 }
}
dps_results: {
 key: "TestDemonology-Settings-Orc-preraid-Demonology Warlock-default-FullBuffs-25.0yards-ShortSingleTarget"
 value: {
  dps: 151412.57843
  tps: 80733.38056
 }
}
dps_results: {
 key: "TestDemonology-Settings-Orc-preraid-Demonology Warlock-default-NoBuffs-25.0yards-LongMultiTarget"
 value: {
  dps: 137995.56454
  tps: 100074.89485
 }
}
dps_results: {
 key: "TestDemonology-Settings-Orc-preraid-Demonology Warlock-default-NoBuffs-25.0yards-LongSingleTarget"
 value: {
  dps: 52520.66419
  tps: 33865.61477
 }
}
dps_results: {
 key: "TestDemonology-Settings-Orc-preraid-Demonology Warlock-default-NoBuffs-25.0yards-ShortSingleTarget"
 value: {
  dps: 74593.61093
  tps: 40232.57939
 }
}
dps_results: {
 key: "TestDemonology-Settings-Troll-preraid-Demonology Warlock-default-FullBuffs-25.0yards-LongMultiTarget"
 value: {
  dps: 217130.77197
  tps: 154253.62397
 }
}
dps_results: {
 key: "TestDemonology-Settings-Troll-preraid-Demonology Warlock-default-FullBuffs-25.0yards-LongSingleTarget"
 value: {
  dps: 86404.10806
  tps: 53674.32642
 }
}
dps_results: {
 key: "TestDemonology-Settings-Troll-preraid-Demonology Warlock-default-FullBuffs-25.0yards-ShortSingleTarget"
 value: {
  dps: 154464.2383
  tps: 80278.94109
 }
}
dps_results: {
 key: "TestDemonology-Settings-Troll-preraid-Demonology Warlock-default-NoBuffs-25.0yards-LongMultiTarget"
 value: {
  dps: 138749.21699
  tps: 101080.03901
 }
}
dps_results: {
 key: "TestDemonology-Settings-Troll-preraid-Demonology Warlock-default-NoBuffs-25.0yards-LongSingleTarget"
 value: {
  dps: 52052.97429
  tps: 33933.74038
 }
}
dps_results: {
 key: "TestDemonology-Settings-Troll-preraid-Demonology Warlock-default-NoBuffs-25.0yards-ShortSingleTarget"
 value: {
  dps: 77668.71598
  tps: 42963.75196
 }
}
dps_results: {
 key: "TestDemonology-SwitchInFrontOfTarget-Default"
 value: {
  dps: 85424.11512
  tps: 54012.88561
 }
}
