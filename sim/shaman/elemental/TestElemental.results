character_stats_results: {
 key: "TestElemental-CharacterStats-Default"
 value: {
  final_stats: 234.15
  final_stats: 176.4
  final_stats: 22808.5
  final_stats: 19770.03
  final_stats: 4891
  final_stats: 5111
  final_stats: 1465
  final_stats: 6755
  final_stats: 0
  final_stats: 0
  final_stats: 0
  final_stats: 8086
  final_stats: 810.645
  final_stats: 0
  final_stats: 31021.133
  final_stats: 0
  final_stats: 0
  final_stats: 25484
  final_stats: 0
  final_stats: 465722
  final_stats: 300000
  final_stats: 3000
  final_stats: 15.03235
  final_stats: 15.03235
  final_stats: 10.50172
  final_stats: 17.4447
  final_stats: 0
 }
}
dps_results: {
 key: "TestElemental-AllItems-AgilePrimalDiamond"
 value: {
  dps: 128148.76764
  tps: 97279.63777
 }
}
dps_results: {
 key: "TestElemental-AllItems-AlacrityofXuen-103989"
 value: {
  dps: 119945.61102
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-ArcaneBadgeoftheShieldwall-93347"
 value: {
  dps: 125230.58438
  tps: 94922.23648
 }
}
dps_results: {
 key: "TestElemental-AllItems-ArrowflightMedallion-93258"
 value: {
  dps: 121346.75173
  tps: 92185.63944
 }
}
dps_results: {
 key: "TestElemental-AllItems-AssuranceofConsequence-105472"
 value: {
  dps: 120028.09547
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-AusterePrimalDiamond"
 value: {
  dps: 125694.37494
  tps: 95256.11926
 }
}
dps_results: {
 key: "TestElemental-AllItems-BadJuju-96781"
 value: {
  dps: 122033.10031
  tps: 90367.82286
 }
}
dps_results: {
 key: "TestElemental-AllItems-BadgeofKypariZar-84079"
 value: {
  dps: 121533.51376
  tps: 91680.61337
 }
}
dps_results: {
 key: "TestElemental-AllItems-BattlegearoftheFirebird"
 value: {
  dps: 87223.88472
  tps: 66659.68648
 }
}
dps_results: {
 key: "TestElemental-AllItems-BattlegearoftheWitchDoctor"
 value: {
  dps: 88662.4642
  tps: 67202.65322
 }
}
dps_results: {
 key: "TestElemental-AllItems-BlossomofPureSnow-89081"
 value: {
  dps: 126549.94597
  tps: 96089.53964
 }
}
dps_results: {
 key: "TestElemental-AllItems-BottleofInfiniteStars-87057"
 value: {
  dps: 121917.43778
  tps: 91298.63941
 }
}
dps_results: {
 key: "TestElemental-AllItems-BraidofTenSongs-84072"
 value: {
  dps: 121533.51376
  tps: 91680.61337
 }
}
dps_results: {
 key: "TestElemental-AllItems-Brawler'sStatue-87571"
 value: {
  dps: 119945.61102
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-BreathoftheHydra-96827"
 value: {
  dps: 134249.86096
  tps: 101625.50072
 }
}
dps_results: {
 key: "TestElemental-AllItems-BroochofMunificentDeeds-87500"
 value: {
  dps: 119945.61102
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-BrutalTalismanoftheShado-PanAssault-94508"
 value: {
  dps: 119945.61102
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-BurningPrimalDiamond"
 value: {
  dps: 129233.405
  tps: 98102.47898
 }
}
dps_results: {
 key: "TestElemental-AllItems-CapacitivePrimalDiamond"
 value: {
  dps: 126179.39058
  tps: 95630.32268
 }
}
dps_results: {
 key: "TestElemental-AllItems-CarbonicCarbuncle-81138"
 value: {
  dps: 121651.77024
  tps: 92458.35785
 }
}
dps_results: {
 key: "TestElemental-AllItems-CelestialHarmonyBattlegear"
 value: {
  dps: 89437.0682
  tps: 66591.9765
 }
}
dps_results: {
 key: "TestElemental-AllItems-CelestialHarmonyRegalia"
 value: {
  dps: 134579.13224
  tps: 99629.71546
 }
}
dps_results: {
 key: "TestElemental-AllItems-Cha-Ye'sEssenceofBrilliance-96888"
 value: {
  dps: 132789.33997
  tps: 100690.57285
 }
}
dps_results: {
 key: "TestElemental-AllItems-CharmofTenSongs-84071"
 value: {
  dps: 122753.09786
  tps: 93357.90617
 }
}
dps_results: {
 key: "TestElemental-AllItems-CommunalIdolofDestruction-101168"
 value: {
  dps: 123331.61122
  tps: 92277.69575
 }
}
dps_results: {
 key: "TestElemental-AllItems-CommunalStoneofDestruction-101171"
 value: {
  dps: 126281.40682
  tps: 94627.13758
 }
}
dps_results: {
 key: "TestElemental-AllItems-CommunalStoneofWisdom-101183"
 value: {
  dps: 124988.14973
  tps: 94961.59293
 }
}
dps_results: {
 key: "TestElemental-AllItems-ContemplationofChi-Ji-103688"
 value: {
  dps: 125584.98836
  tps: 95412.54617
 }
}
dps_results: {
 key: "TestElemental-AllItems-ContemplationofChi-Ji-103988"
 value: {
  dps: 127997.30244
  tps: 97219.66967
 }
}
dps_results: {
 key: "TestElemental-AllItems-Coren'sColdChromiumCoaster-87574"
 value: {
  dps: 121726.03483
  tps: 92616.50416
 }
}
dps_results: {
 key: "TestElemental-AllItems-CoreofDecency-87497"
 value: {
  dps: 119945.61102
  tps: 91184.85711
 }
}
dps_results: {
 key: "TestElemental-AllItems-CourageousPrimalDiamond"
 value: {
  dps: 127233.97673
  tps: 96412.33315
 }
}
dps_results: {
 key: "TestElemental-AllItems-CraftedDreadfulGladiator'sBadgeofConquest-93419"
 value: {
  dps: 119983.69574
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-CraftedDreadfulGladiator'sBadgeofDominance-93600"
 value: {
  dps: 123955.7935
  tps: 94161.33027
 }
}
dps_results: {
 key: "TestElemental-AllItems-CraftedDreadfulGladiator'sBadgeofVictory-93606"
 value: {
  dps: 119945.61102
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-CraftedDreadfulGladiator'sEmblemofCruelty-93485"
 value: {
  dps: 121528.33217
  tps: 92451.2974
 }
}
dps_results: {
 key: "TestElemental-AllItems-CraftedDreadfulGladiator'sEmblemofMeditation-93487"
 value: {
  dps: 119945.61102
  tps: 91184.64163
 }
}
dps_results: {
 key: "TestElemental-AllItems-CraftedDreadfulGladiator'sEmblemofTenacity-93486"
 value: {
  dps: 119945.61102
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-CraftedDreadfulGladiator'sInsigniaofConquest-93424"
 value: {
  dps: 120002.56316
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-CraftedDreadfulGladiator'sInsigniaofDominance-93601"
 value: {
  dps: 125537.40509
  tps: 95314.22016
 }
}
dps_results: {
 key: "TestElemental-AllItems-CraftedDreadfulGladiator'sInsigniaofVictory-93611"
 value: {
  dps: 119945.61102
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-CraftedMalevolentGladiator'sBadgeofConquest-98755"
 value: {
  dps: 119988.74287
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-CraftedMalevolentGladiator'sBadgeofDominance-98910"
 value: {
  dps: 124661.33124
  tps: 94678.5691
 }
}
dps_results: {
 key: "TestElemental-AllItems-CraftedMalevolentGladiator'sBadgeofVictory-98912"
 value: {
  dps: 119945.61102
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-CraftedMalevolentGladiator'sEmblemofCruelty-98811"
 value: {
  dps: 121777.9951
  tps: 92649.7081
 }
}
dps_results: {
 key: "TestElemental-AllItems-CraftedMalevolentGladiator'sEmblemofMeditation-98813"
 value: {
  dps: 119945.61102
  tps: 91184.45104
 }
}
dps_results: {
 key: "TestElemental-AllItems-CraftedMalevolentGladiator'sEmblemofTenacity-98812"
 value: {
  dps: 119945.61102
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-CraftedMalevolentGladiator'sInsigniaofConquest-98760"
 value: {
  dps: 119996.71393
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-CraftedMalevolentGladiator'sInsigniaofDominance-98911"
 value: {
  dps: 126633.35459
  tps: 96172.57907
 }
}
dps_results: {
 key: "TestElemental-AllItems-CraftedMalevolentGladiator'sInsigniaofVictory-98917"
 value: {
  dps: 119945.61102
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-CurseofHubris-102307"
 value: {
  dps: 122584.20933
  tps: 92902.16825
 }
}
dps_results: {
 key: "TestElemental-AllItems-CurseofHubris-104649"
 value: {
  dps: 122937.63987
  tps: 93133.61155
 }
}
dps_results: {
 key: "TestElemental-AllItems-CurseofHubris-104898"
 value: {
  dps: 122338.55813
  tps: 92725.99443
 }
}
dps_results: {
 key: "TestElemental-AllItems-CurseofHubris-105147"
 value: {
  dps: 122138.35915
  tps: 92603.1279
 }
}
dps_results: {
 key: "TestElemental-AllItems-CurseofHubris-105396"
 value: {
  dps: 122743.10703
  tps: 92996.11315
 }
}
dps_results: {
 key: "TestElemental-AllItems-CurseofHubris-105645"
 value: {
  dps: 123148.31363
  tps: 93273.32639
 }
}
dps_results: {
 key: "TestElemental-AllItems-CutstitcherMedallion-93255"
 value: {
  dps: 125584.98836
  tps: 95412.54617
 }
}
dps_results: {
 key: "TestElemental-AllItems-Daelo'sFinalWords-87496"
 value: {
  dps: 119945.61102
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-DarkglowEmbroidery(Rank3)-4893"
 value: {
  dps: 126356.16683
  tps: 95940.10201
 }
}
dps_results: {
 key: "TestElemental-AllItems-DarkmistVortex-87172"
 value: {
  dps: 122756.98145
  tps: 93260.84955
 }
}
dps_results: {
 key: "TestElemental-AllItems-DeadeyeBadgeoftheShieldwall-93346"
 value: {
  dps: 121640.25271
  tps: 91379.53996
 }
}
dps_results: {
 key: "TestElemental-AllItems-DelicateVialoftheSanguinaire-96895"
 value: {
  dps: 119945.61102
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-DestructivePrimalDiamond"
 value: {
  dps: 126372.46543
  tps: 95782.29179
 }
}
dps_results: {
 key: "TestElemental-AllItems-DisciplineofXuen-103986"
 value: {
  dps: 123578.29586
  tps: 90965.50425
 }
}
dps_results: {
 key: "TestElemental-AllItems-Dominator'sArcaneBadge-93342"
 value: {
  dps: 125230.58438
  tps: 94922.23648
 }
}
dps_results: {
 key: "TestElemental-AllItems-Dominator'sDeadeyeBadge-93341"
 value: {
  dps: 121640.25271
  tps: 91379.53996
 }
}
dps_results: {
 key: "TestElemental-AllItems-Dominator'sDurableBadge-93345"
 value: {
  dps: 121640.25271
  tps: 91379.53996
 }
}
dps_results: {
 key: "TestElemental-AllItems-Dominator'sKnightlyBadge-93344"
 value: {
  dps: 121640.25271
  tps: 91379.53996
 }
}
dps_results: {
 key: "TestElemental-AllItems-Dominator'sMendingBadge-93343"
 value: {
  dps: 123870.13962
  tps: 94116.91643
 }
}
dps_results: {
 key: "TestElemental-AllItems-DreadfulGladiator'sBadgeofConquest-84344"
 value: {
  dps: 119983.69574
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-DreadfulGladiator'sBadgeofDominance-84488"
 value: {
  dps: 123955.7935
  tps: 94161.33027
 }
}
dps_results: {
 key: "TestElemental-AllItems-DreadfulGladiator'sBadgeofVictory-84490"
 value: {
  dps: 119945.61102
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-DreadfulGladiator'sEmblemofCruelty-84399"
 value: {
  dps: 121528.33217
  tps: 92451.2974
 }
}
dps_results: {
 key: "TestElemental-AllItems-DreadfulGladiator'sEmblemofMeditation-84401"
 value: {
  dps: 119945.61102
  tps: 91184.64163
 }
}
dps_results: {
 key: "TestElemental-AllItems-DreadfulGladiator'sEmblemofTenacity-84400"
 value: {
  dps: 119945.61102
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-DreadfulGladiator'sInsigniaofConquest-84349"
 value: {
  dps: 120002.76011
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-DreadfulGladiator'sInsigniaofDominance-84489"
 value: {
  dps: 125425.66244
  tps: 95264.24938
 }
}
dps_results: {
 key: "TestElemental-AllItems-DreadfulGladiator'sInsigniaofVictory-84495"
 value: {
  dps: 119945.61102
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-DurableBadgeoftheShieldwall-93350"
 value: {
  dps: 121640.25271
  tps: 91379.53996
 }
}
dps_results: {
 key: "TestElemental-AllItems-EffulgentPrimalDiamond"
 value: {
  dps: 125694.37494
  tps: 95256.11926
 }
}
dps_results: {
 key: "TestElemental-AllItems-EmberPrimalDiamond"
 value: {
  dps: 126754.72454
  tps: 96058.45983
 }
}
dps_results: {
 key: "TestElemental-AllItems-EmblemofKypariZar-84077"
 value: {
  dps: 121177.54754
  tps: 92130.98104
 }
}
dps_results: {
 key: "TestElemental-AllItems-EmblemoftheCatacombs-83733"
 value: {
  dps: 121282.30907
  tps: 91963.89924
 }
}
dps_results: {
 key: "TestElemental-AllItems-EmptyFruitBarrel-81133"
 value: {
  dps: 119945.61102
  tps: 91184.59236
 }
}
dps_results: {
 key: "TestElemental-AllItems-EnchantWeapon-BloodyDancingSteel-5125"
 value: {
  dps: 125762.03077
  tps: 95529.1976
 }
}
dps_results: {
 key: "TestElemental-AllItems-EnchantWeapon-Colossus-4445"
 value: {
  dps: 125762.03077
  tps: 95529.1976
 }
}
dps_results: {
 key: "TestElemental-AllItems-EnchantWeapon-DancingSteel-4444"
 value: {
  dps: 125762.03077
  tps: 95529.1976
 }
}
dps_results: {
 key: "TestElemental-AllItems-EnchantWeapon-River'sSong-4446"
 value: {
  dps: 125762.03077
  tps: 95529.1976
 }
}
dps_results: {
 key: "TestElemental-AllItems-EnchantWeapon-SpiritofConquest-5124"
 value: {
  dps: 125762.03077
  tps: 95529.1976
 }
}
dps_results: {
 key: "TestElemental-AllItems-EnchantWeapon-Windsong-4441"
 value: {
  dps: 127305.2634
  tps: 96351.46874
 }
}
dps_results: {
 key: "TestElemental-AllItems-EnigmaticPrimalDiamond"
 value: {
  dps: 126372.46543
  tps: 95782.29179
 }
}
dps_results: {
 key: "TestElemental-AllItems-EternalPrimalDiamond"
 value: {
  dps: 125694.37494
  tps: 95256.11926
 }
}
dps_results: {
 key: "TestElemental-AllItems-EvilEyeofGalakras-105491"
 value: {
  dps: 119945.61102
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-FabledFeatherofJi-Kun-96842"
 value: {
  dps: 119945.61102
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-FearwurmBadge-84074"
 value: {
  dps: 121177.54754
  tps: 92130.98104
 }
}
dps_results: {
 key: "TestElemental-AllItems-FearwurmRelic-84070"
 value: {
  dps: 122200.45183
  tps: 92800.94337
 }
}
dps_results: {
 key: "TestElemental-AllItems-FelsoulIdolofDestruction-101263"
 value: {
  dps: 123088.90412
  tps: 92109.00929
 }
}
dps_results: {
 key: "TestElemental-AllItems-FelsoulStoneofDestruction-101266"
 value: {
  dps: 126709.29347
  tps: 94731.17973
 }
}
dps_results: {
 key: "TestElemental-AllItems-Fen-Yu,FuryofXuen-102248"
 value: {
  dps: 123877.17556
  tps: 93523.15868
 }
}
dps_results: {
 key: "TestElemental-AllItems-FlashfrozenResinGlobule-100951"
 value: {
  dps: 124448.94698
  tps: 94261.7449
 }
}
dps_results: {
 key: "TestElemental-AllItems-FlashfrozenResinGlobule-81263"
 value: {
  dps: 124986.47448
  tps: 94624.16644
 }
}
dps_results: {
 key: "TestElemental-AllItems-FlashingSteelTalisman-81265"
 value: {
  dps: 119994.80112
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-FleetPrimalDiamond"
 value: {
  dps: 126743.47269
  tps: 95821.60395
 }
}
dps_results: {
 key: "TestElemental-AllItems-ForlornPrimalDiamond"
 value: {
  dps: 126754.72454
  tps: 96058.45983
 }
}
dps_results: {
 key: "TestElemental-AllItems-FortitudeoftheZandalari-94516"
 value: {
  dps: 122153.97202
  tps: 91212.65734
 }
}
dps_results: {
 key: "TestElemental-AllItems-FortitudeoftheZandalari-95677"
 value: {
  dps: 121862.32779
  tps: 91298.63941
 }
}
dps_results: {
 key: "TestElemental-AllItems-FortitudeoftheZandalari-96049"
 value: {
  dps: 121855.63933
  tps: 90729.84986
 }
}
dps_results: {
 key: "TestElemental-AllItems-FortitudeoftheZandalari-96421"
 value: {
  dps: 121737.16238
  tps: 90371.68143
 }
}
dps_results: {
 key: "TestElemental-AllItems-FortitudeoftheZandalari-96793"
 value: {
  dps: 121908.83195
  tps: 90367.82286
 }
}
dps_results: {
 key: "TestElemental-AllItems-GazeoftheTwins-96915"
 value: {
  dps: 122156.33705
  tps: 92901.12259
 }
}
dps_results: {
 key: "TestElemental-AllItems-Gerp'sPerfectArrow-87495"
 value: {
  dps: 121278.88696
  tps: 92187.43251
 }
}
dps_results: {
 key: "TestElemental-AllItems-Gladiator'sEarthshaker"
 value: {
  dps: 91396.27124
  tps: 69010.95494
 }
}
dps_results: {
 key: "TestElemental-AllItems-Gong-Lu,StrengthofXuen-102249"
 value: {
  dps: 123877.17556
  tps: 93523.15868
 }
}
dps_results: {
 key: "TestElemental-AllItems-GrievousGladiator'sBadgeofConquest-100195"
 value: {
  dps: 120005.25043
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-GrievousGladiator'sBadgeofConquest-100603"
 value: {
  dps: 120005.25043
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-GrievousGladiator'sBadgeofConquest-102856"
 value: {
  dps: 120005.25043
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-GrievousGladiator'sBadgeofConquest-103145"
 value: {
  dps: 120005.25043
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-GrievousGladiator'sBadgeofDominance-100490"
 value: {
  dps: 127344.68625
  tps: 96709.60568
 }
}
dps_results: {
 key: "TestElemental-AllItems-GrievousGladiator'sBadgeofDominance-100576"
 value: {
  dps: 127344.68625
  tps: 96709.60568
 }
}
dps_results: {
 key: "TestElemental-AllItems-GrievousGladiator'sBadgeofDominance-102830"
 value: {
  dps: 127344.68625
  tps: 96709.60568
 }
}
dps_results: {
 key: "TestElemental-AllItems-GrievousGladiator'sBadgeofDominance-103308"
 value: {
  dps: 127344.68625
  tps: 96709.60568
 }
}
dps_results: {
 key: "TestElemental-AllItems-GrievousGladiator'sBadgeofVictory-100500"
 value: {
  dps: 119945.61102
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-GrievousGladiator'sBadgeofVictory-100579"
 value: {
  dps: 119945.61102
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-GrievousGladiator'sBadgeofVictory-102833"
 value: {
  dps: 119945.61102
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-GrievousGladiator'sBadgeofVictory-103314"
 value: {
  dps: 119945.61102
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-GrievousGladiator'sEmblemofCruelty-100305"
 value: {
  dps: 122297.14831
  tps: 93029.80406
 }
}
dps_results: {
 key: "TestElemental-AllItems-GrievousGladiator'sEmblemofCruelty-100626"
 value: {
  dps: 122297.14831
  tps: 93029.80406
 }
}
dps_results: {
 key: "TestElemental-AllItems-GrievousGladiator'sEmblemofCruelty-102877"
 value: {
  dps: 122297.14831
  tps: 93029.80406
 }
}
dps_results: {
 key: "TestElemental-AllItems-GrievousGladiator'sEmblemofCruelty-103210"
 value: {
  dps: 122297.14831
  tps: 93029.80406
 }
}
dps_results: {
 key: "TestElemental-AllItems-GrievousGladiator'sEmblemofMeditation-100307"
 value: {
  dps: 119945.61102
  tps: 91183.80077
 }
}
dps_results: {
 key: "TestElemental-AllItems-GrievousGladiator'sEmblemofMeditation-100559"
 value: {
  dps: 119945.61102
  tps: 91183.80077
 }
}
dps_results: {
 key: "TestElemental-AllItems-GrievousGladiator'sEmblemofMeditation-102813"
 value: {
  dps: 119945.61102
  tps: 91183.80077
 }
}
dps_results: {
 key: "TestElemental-AllItems-GrievousGladiator'sEmblemofMeditation-103212"
 value: {
  dps: 119945.61102
  tps: 91183.80077
 }
}
dps_results: {
 key: "TestElemental-AllItems-GrievousGladiator'sEmblemofTenacity-100306"
 value: {
  dps: 119945.61102
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-GrievousGladiator'sEmblemofTenacity-100652"
 value: {
  dps: 119945.61102
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-GrievousGladiator'sEmblemofTenacity-102903"
 value: {
  dps: 119945.61102
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-GrievousGladiator'sEmblemofTenacity-103211"
 value: {
  dps: 119945.61102
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-GrievousGladiator'sInsigniaofConquest-103150"
 value: {
  dps: 120042.12051
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-GrievousGladiator'sInsigniaofDominance-103309"
 value: {
  dps: 129937.65383
  tps: 98637.65713
 }
}
dps_results: {
 key: "TestElemental-AllItems-GrievousGladiator'sInsigniaofVictory-103319"
 value: {
  dps: 119945.61102
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-Hawkmaster'sTalon-89082"
 value: {
  dps: 121213.58841
  tps: 92038.54514
 }
}
dps_results: {
 key: "TestElemental-AllItems-Heart-LesionDefenderIdol-100999"
 value: {
  dps: 119945.61102
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-Heart-LesionDefenderStone-101002"
 value: {
  dps: 121079.18638
  tps: 90555.51207
 }
}
dps_results: {
 key: "TestElemental-AllItems-Heart-LesionIdolofBattle-100991"
 value: {
  dps: 121824.52096
  tps: 92682.19344
 }
}
dps_results: {
 key: "TestElemental-AllItems-Heart-LesionStoneofBattle-100990"
 value: {
  dps: 122072.06815
  tps: 91176.61962
 }
}
dps_results: {
 key: "TestElemental-AllItems-HeartofFire-81181"
 value: {
  dps: 121813.95578
  tps: 91719.0199
 }
}
dps_results: {
 key: "TestElemental-AllItems-HeartwarmerMedallion-93260"
 value: {
  dps: 125584.98836
  tps: 95412.54617
 }
}
dps_results: {
 key: "TestElemental-AllItems-HelmbreakerMedallion-93261"
 value: {
  dps: 121346.75173
  tps: 92185.63944
 }
}
dps_results: {
 key: "TestElemental-AllItems-Horridon'sLastGasp-96757"
 value: {
  dps: 128485.00311
  tps: 97598.81253
 }
}
dps_results: {
 key: "TestElemental-AllItems-ImpassivePrimalDiamond"
 value: {
  dps: 126372.46543
  tps: 95782.29179
 }
}
dps_results: {
 key: "TestElemental-AllItems-IndomitablePrimalDiamond"
 value: {
  dps: 125694.37494
  tps: 95256.11926
 }
}
dps_results: {
 key: "TestElemental-AllItems-InscribedBagofHydra-Spawn-96828"
 value: {
  dps: 119945.61102
  tps: 91183.45298
 }
}
dps_results: {
 key: "TestElemental-AllItems-InsigniaofKypariZar-84078"
 value: {
  dps: 119945.61102
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-IronBellyWok-89083"
 value: {
  dps: 121213.58841
  tps: 92038.54514
 }
}
dps_results: {
 key: "TestElemental-AllItems-IronProtectorTalisman-85181"
 value: {
  dps: 119945.61102
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-JadeBanditFigurine-86043"
 value: {
  dps: 121213.58841
  tps: 92038.54514
 }
}
dps_results: {
 key: "TestElemental-AllItems-JadeBanditFigurine-86772"
 value: {
  dps: 121428.52892
  tps: 92122.47494
 }
}
dps_results: {
 key: "TestElemental-AllItems-JadeCharioteerFigurine-86042"
 value: {
  dps: 121213.58841
  tps: 92038.54514
 }
}
dps_results: {
 key: "TestElemental-AllItems-JadeCharioteerFigurine-86771"
 value: {
  dps: 121428.52892
  tps: 92122.47494
 }
}
dps_results: {
 key: "TestElemental-AllItems-JadeCourtesanFigurine-86045"
 value: {
  dps: 125247.36297
  tps: 95155.54077
 }
}
dps_results: {
 key: "TestElemental-AllItems-JadeCourtesanFigurine-86774"
 value: {
  dps: 124690.28389
  tps: 94744.94882
 }
}
dps_results: {
 key: "TestElemental-AllItems-JadeMagistrateFigurine-86044"
 value: {
  dps: 126549.94597
  tps: 96089.53964
 }
}
dps_results: {
 key: "TestElemental-AllItems-JadeMagistrateFigurine-86773"
 value: {
  dps: 125807.17946
  tps: 95517.52783
 }
}
dps_results: {
 key: "TestElemental-AllItems-JadeWarlordFigurine-86046"
 value: {
  dps: 121815.17327
  tps: 91198.694
 }
}
dps_results: {
 key: "TestElemental-AllItems-JadeWarlordFigurine-86775"
 value: {
  dps: 121664.40478
  tps: 91068.14896
 }
}
dps_results: {
 key: "TestElemental-AllItems-Ji-Kun'sRisingWinds-96843"
 value: {
  dps: 119945.61102
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-KnightlyBadgeoftheShieldwall-93349"
 value: {
  dps: 121640.25271
  tps: 91379.53996
 }
}
dps_results: {
 key: "TestElemental-AllItems-KnotofTenSongs-84073"
 value: {
  dps: 119945.61102
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-Kor'kronBookofHurting-92785"
 value: {
  dps: 119945.61102
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-Lao-Chin'sLiquidCourage-89079"
 value: {
  dps: 121815.17327
  tps: 91198.694
 }
}
dps_results: {
 key: "TestElemental-AllItems-LeiShen'sFinalOrders-87072"
 value: {
  dps: 122403.58821
  tps: 92811.32521
 }
}
dps_results: {
 key: "TestElemental-AllItems-LessonsoftheDarkmaster-81268"
 value: {
  dps: 119945.61102
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-LightdrinkerIdolofRage-101200"
 value: {
  dps: 121687.25954
  tps: 91367.82925
 }
}
dps_results: {
 key: "TestElemental-AllItems-LightdrinkerStoneofRage-101203"
 value: {
  dps: 121926.82102
  tps: 91240.17485
 }
}
dps_results: {
 key: "TestElemental-AllItems-LightoftheCosmos-87065"
 value: {
  dps: 128208.16412
  tps: 97096.81642
 }
}
dps_results: {
 key: "TestElemental-AllItems-LordBlastington'sScopeofDoom-4699"
 value: {
  dps: 125762.03077
  tps: 95529.1976
 }
}
dps_results: {
 key: "TestElemental-AllItems-MalevolentGladiator'sBadgeofConquest-84934"
 value: {
  dps: 119990.53943
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-MalevolentGladiator'sBadgeofConquest-91452"
 value: {
  dps: 119988.74287
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-MalevolentGladiator'sBadgeofDominance-84940"
 value: {
  dps: 125049.3857
  tps: 94977.29498
 }
}
dps_results: {
 key: "TestElemental-AllItems-MalevolentGladiator'sBadgeofDominance-91753"
 value: {
  dps: 124661.33124
  tps: 94678.5691
 }
}
dps_results: {
 key: "TestElemental-AllItems-MalevolentGladiator'sBadgeofVictory-84942"
 value: {
  dps: 119945.61102
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-MalevolentGladiator'sBadgeofVictory-91763"
 value: {
  dps: 119945.61102
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-MalevolentGladiator'sEmblemofCruelty-84936"
 value: {
  dps: 121824.52096
  tps: 92682.19344
 }
}
dps_results: {
 key: "TestElemental-AllItems-MalevolentGladiator'sEmblemofCruelty-91562"
 value: {
  dps: 121777.9951
  tps: 92649.7081
 }
}
dps_results: {
 key: "TestElemental-AllItems-MalevolentGladiator'sEmblemofMeditation-84939"
 value: {
  dps: 119945.61102
  tps: 91184.36676
 }
}
dps_results: {
 key: "TestElemental-AllItems-MalevolentGladiator'sEmblemofMeditation-91564"
 value: {
  dps: 119945.61102
  tps: 91184.45104
 }
}
dps_results: {
 key: "TestElemental-AllItems-MalevolentGladiator'sEmblemofTenacity-84938"
 value: {
  dps: 119945.61102
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-MalevolentGladiator'sEmblemofTenacity-91563"
 value: {
  dps: 119945.61102
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-MalevolentGladiator'sInsigniaofConquest-91457"
 value: {
  dps: 119998.61707
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-MalevolentGladiator'sInsigniaofDominance-91754"
 value: {
  dps: 126619.39527
  tps: 96155.63597
 }
}
dps_results: {
 key: "TestElemental-AllItems-MalevolentGladiator'sInsigniaofVictory-91768"
 value: {
  dps: 119945.61102
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-MarkoftheCatacombs-83731"
 value: {
  dps: 121882.62016
  tps: 91643.52963
 }
}
dps_results: {
 key: "TestElemental-AllItems-MarkoftheHardenedGrunt-92783"
 value: {
  dps: 119945.61102
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-MedallionofMystifyingVapors-93257"
 value: {
  dps: 122118.01133
  tps: 91364.62398
 }
}
dps_results: {
 key: "TestElemental-AllItems-MedallionoftheCatacombs-83734"
 value: {
  dps: 121766.92782
  tps: 91989.13185
 }
}
dps_results: {
 key: "TestElemental-AllItems-MendingBadgeoftheShieldwall-93348"
 value: {
  dps: 123870.13962
  tps: 94116.91643
 }
}
dps_results: {
 key: "TestElemental-AllItems-MirrorScope-4700"
 value: {
  dps: 125762.03077
  tps: 95529.1976
 }
}
dps_results: {
 key: "TestElemental-AllItems-MistdancerDefenderIdol-101089"
 value: {
  dps: 119945.61102
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-MistdancerDefenderStone-101087"
 value: {
  dps: 122211.78437
  tps: 91631.6212
 }
}
dps_results: {
 key: "TestElemental-AllItems-MistdancerIdolofRage-101113"
 value: {
  dps: 121677.71254
  tps: 91367.82925
 }
}
dps_results: {
 key: "TestElemental-AllItems-MistdancerStoneofRage-101117"
 value: {
  dps: 122279.43663
  tps: 91254.57313
 }
}
dps_results: {
 key: "TestElemental-AllItems-MistdancerStoneofWisdom-101107"
 value: {
  dps: 124988.14973
  tps: 94961.59293
 }
}
dps_results: {
 key: "TestElemental-AllItems-MithrilWristwatch-87572"
 value: {
  dps: 123099.17259
  tps: 93636.98902
 }
}
dps_results: {
 key: "TestElemental-AllItems-MountainsageIdolofDestruction-101069"
 value: {
  dps: 123875.92004
  tps: 92806.10672
 }
}
dps_results: {
 key: "TestElemental-AllItems-MountainsageStoneofDestruction-101072"
 value: {
  dps: 127386.29255
  tps: 95170.19588
 }
}
dps_results: {
 key: "TestElemental-AllItems-OathswornDefenderIdol-101303"
 value: {
  dps: 119945.61102
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-OathswornDefenderStone-101306"
 value: {
  dps: 121728.0942
  tps: 91131.61878
 }
}
dps_results: {
 key: "TestElemental-AllItems-OathswornIdolofBattle-101295"
 value: {
  dps: 121824.52096
  tps: 92682.19344
 }
}
dps_results: {
 key: "TestElemental-AllItems-OathswornStoneofBattle-101294"
 value: {
  dps: 121842.74903
  tps: 91067.45924
 }
}
dps_results: {
 key: "TestElemental-AllItems-PhaseFingers-4697"
 value: {
  dps: 129184.0724
  tps: 98230.08243
 }
}
dps_results: {
 key: "TestElemental-AllItems-PouchofWhiteAsh-103639"
 value: {
  dps: 119945.61102
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-PowerfulPrimalDiamond"
 value: {
  dps: 125694.37494
  tps: 95256.11926
 }
}
dps_results: {
 key: "TestElemental-AllItems-PriceofProgress-81266"
 value: {
  dps: 124085.67737
  tps: 94288.49359
 }
}
dps_results: {
 key: "TestElemental-AllItems-PridefulGladiator'sBadgeofConquest-102659"
 value: {
  dps: 120030.24903
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-PridefulGladiator'sBadgeofConquest-103342"
 value: {
  dps: 120030.24903
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-PridefulGladiator'sBadgeofDominance-102633"
 value: {
  dps: 129412.5241
  tps: 98239.25821
 }
}
dps_results: {
 key: "TestElemental-AllItems-PridefulGladiator'sBadgeofDominance-103505"
 value: {
  dps: 129412.5241
  tps: 98239.25821
 }
}
dps_results: {
 key: "TestElemental-AllItems-PridefulGladiator'sBadgeofVictory-102636"
 value: {
  dps: 119945.61102
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-PridefulGladiator'sBadgeofVictory-103511"
 value: {
  dps: 119945.61102
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-PridefulGladiator'sEmblemofCruelty-102680"
 value: {
  dps: 123009.59933
  tps: 93588.51656
 }
}
dps_results: {
 key: "TestElemental-AllItems-PridefulGladiator'sEmblemofCruelty-103407"
 value: {
  dps: 123009.59933
  tps: 93588.51656
 }
}
dps_results: {
 key: "TestElemental-AllItems-PridefulGladiator'sEmblemofMeditation-102616"
 value: {
  dps: 119945.61102
  tps: 91183.25494
 }
}
dps_results: {
 key: "TestElemental-AllItems-PridefulGladiator'sEmblemofMeditation-103409"
 value: {
  dps: 119945.61102
  tps: 91183.25494
 }
}
dps_results: {
 key: "TestElemental-AllItems-PridefulGladiator'sEmblemofTenacity-102706"
 value: {
  dps: 119945.61102
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-PridefulGladiator'sEmblemofTenacity-103408"
 value: {
  dps: 119945.61102
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-PridefulGladiator'sInsigniaofConquest-103347"
 value: {
  dps: 120093.0267
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-PridefulGladiator'sInsigniaofDominance-103506"
 value: {
  dps: 133179.3924
  tps: 101056.88831
 }
}
dps_results: {
 key: "TestElemental-AllItems-PridefulGladiator'sInsigniaofVictory-103516"
 value: {
  dps: 119945.61102
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-Primordius'TalismanofRage-96873"
 value: {
  dps: 122771.09737
  tps: 93397.99872
 }
}
dps_results: {
 key: "TestElemental-AllItems-Qian-Le,CourageofNiuzao-102245"
 value: {
  dps: 123802.20323
  tps: 93477.04609
 }
}
dps_results: {
 key: "TestElemental-AllItems-Qian-Ying,FortitudeofNiuzao-102250"
 value: {
  dps: 122262.68565
  tps: 92277.06804
 }
}
dps_results: {
 key: "TestElemental-AllItems-Qin-xi'sPolarizingSeal-87075"
 value: {
  dps: 119945.61102
  tps: 91184.11133
 }
}
dps_results: {
 key: "TestElemental-AllItems-RegaliaoftheFirebird"
 value: {
  dps: 115400.57378
  tps: 88713.46902
 }
}
dps_results: {
 key: "TestElemental-AllItems-RegaliaoftheWitchDoctor"
 value: {
  dps: 123880.34433
  tps: 93450.46485
 }
}
dps_results: {
 key: "TestElemental-AllItems-RelicofChi-Ji-79330"
 value: {
  dps: 125606.99022
  tps: 95429.05845
 }
}
dps_results: {
 key: "TestElemental-AllItems-RelicofKypariZar-84075"
 value: {
  dps: 122489.61071
  tps: 92949.4291
 }
}
dps_results: {
 key: "TestElemental-AllItems-RelicofNiuzao-79329"
 value: {
  dps: 119945.61102
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-RelicofXuen-79327"
 value: {
  dps: 119945.61102
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-RelicofXuen-79328"
 value: {
  dps: 119945.61102
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-Renataki'sSoulCharm-96741"
 value: {
  dps: 120370.29435
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-ResolveofNiuzao-103690"
 value: {
  dps: 121980.94207
  tps: 91351.15169
 }
}
dps_results: {
 key: "TestElemental-AllItems-ResolveofNiuzao-103990"
 value: {
  dps: 121737.16238
  tps: 90371.68143
 }
}
dps_results: {
 key: "TestElemental-AllItems-ReverberatingPrimalDiamond"
 value: {
  dps: 128148.76764
  tps: 97279.63777
 }
}
dps_results: {
 key: "TestElemental-AllItems-RevitalizingPrimalDiamond"
 value: {
  dps: 128148.76764
  tps: 97279.00599
 }
}
dps_results: {
 key: "TestElemental-AllItems-SI:7Operative'sManual-92784"
 value: {
  dps: 119945.61102
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-ScrollofReveredAncestors-89080"
 value: {
  dps: 125247.36297
  tps: 95155.54077
 }
}
dps_results: {
 key: "TestElemental-AllItems-SearingWords-81267"
 value: {
  dps: 121672.12979
  tps: 92523.13886
 }
}
dps_results: {
 key: "TestElemental-AllItems-Shock-ChargerMedallion-93259"
 value: {
  dps: 127919.83753
  tps: 96727.91535
 }
}
dps_results: {
 key: "TestElemental-AllItems-SigilofCompassion-83736"
 value: {
  dps: 121766.92782
  tps: 91989.13185
 }
}
dps_results: {
 key: "TestElemental-AllItems-SigilofDevotion-83740"
 value: {
  dps: 121056.33466
  tps: 92025.28776
 }
}
dps_results: {
 key: "TestElemental-AllItems-SigilofFidelity-83737"
 value: {
  dps: 122313.89341
  tps: 92731.94141
 }
}
dps_results: {
 key: "TestElemental-AllItems-SigilofGrace-83738"
 value: {
  dps: 121766.92782
  tps: 91989.13185
 }
}
dps_results: {
 key: "TestElemental-AllItems-SigilofKypariZar-84076"
 value: {
  dps: 121936.60049
  tps: 91651.21992
 }
}
dps_results: {
 key: "TestElemental-AllItems-SigilofPatience-83739"
 value: {
  dps: 119945.61102
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-SigiloftheCatacombs-83732"
 value: {
  dps: 122182.46838
  tps: 92859.49043
 }
}
dps_results: {
 key: "TestElemental-AllItems-SinisterPrimalDiamond"
 value: {
  dps: 126179.39058
  tps: 95630.32268
 }
}
dps_results: {
 key: "TestElemental-AllItems-SkullrenderMedallion-93256"
 value: {
  dps: 121346.75173
  tps: 92185.63944
 }
}
dps_results: {
 key: "TestElemental-AllItems-SpiritsoftheSun-87163"
 value: {
  dps: 126272.97246
  tps: 95922.45009
 }
}
dps_results: {
 key: "TestElemental-AllItems-SpringrainIdolofDestruction-101023"
 value: {
  dps: 124765.39654
  tps: 93575.35898
 }
}
dps_results: {
 key: "TestElemental-AllItems-SpringrainIdolofRage-101009"
 value: {
  dps: 121702.74846
  tps: 91367.82925
 }
}
dps_results: {
 key: "TestElemental-AllItems-SpringrainStoneofDestruction-101026"
 value: {
  dps: 127397.88704
  tps: 94999.07318
 }
}
dps_results: {
 key: "TestElemental-AllItems-SpringrainStoneofRage-101012"
 value: {
  dps: 121699.78204
  tps: 90825.39949
 }
}
dps_results: {
 key: "TestElemental-AllItems-SpringrainStoneofWisdom-101041"
 value: {
  dps: 124988.14973
  tps: 94961.59293
 }
}
dps_results: {
 key: "TestElemental-AllItems-Static-Caster'sMedallion-93254"
 value: {
  dps: 127919.83753
  tps: 96727.91535
 }
}
dps_results: {
 key: "TestElemental-AllItems-SteadfastFootman'sMedallion-92782"
 value: {
  dps: 119945.61102
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-SteadfastTalismanoftheShado-PanAssault-94507"
 value: {
  dps: 122153.97202
  tps: 91212.65734
 }
}
dps_results: {
 key: "TestElemental-AllItems-StreamtalkerIdolofDestruction-101222"
 value: {
  dps: 124248.58872
  tps: 93100.12524
 }
}
dps_results: {
 key: "TestElemental-AllItems-StreamtalkerIdolofRage-101217"
 value: {
  dps: 121682.84496
  tps: 91367.82925
 }
}
dps_results: {
 key: "TestElemental-AllItems-StreamtalkerStoneofDestruction-101225"
 value: {
  dps: 127178.67013
  tps: 94990.81241
 }
}
dps_results: {
 key: "TestElemental-AllItems-StreamtalkerStoneofRage-101220"
 value: {
  dps: 121147.22272
  tps: 90544.12366
 }
}
dps_results: {
 key: "TestElemental-AllItems-StreamtalkerStoneofWisdom-101250"
 value: {
  dps: 124988.14973
  tps: 94961.59293
 }
}
dps_results: {
 key: "TestElemental-AllItems-StuffofNightmares-87160"
 value: {
  dps: 121666.51705
  tps: 91100.76393
 }
}
dps_results: {
 key: "TestElemental-AllItems-SunsoulDefenderIdol-101160"
 value: {
  dps: 119945.61102
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-SunsoulDefenderStone-101163"
 value: {
  dps: 122072.28677
  tps: 91300.63776
 }
}
dps_results: {
 key: "TestElemental-AllItems-SunsoulIdolofBattle-101152"
 value: {
  dps: 121824.52096
  tps: 92682.19344
 }
}
dps_results: {
 key: "TestElemental-AllItems-SunsoulStoneofBattle-101151"
 value: {
  dps: 122157.25007
  tps: 91348.03495
 }
}
dps_results: {
 key: "TestElemental-AllItems-SunsoulStoneofWisdom-101138"
 value: {
  dps: 124988.14973
  tps: 94961.59293
 }
}
dps_results: {
 key: "TestElemental-AllItems-SwordguardEmbroidery(Rank3)-4894"
 value: {
  dps: 126356.16683
  tps: 95940.10201
 }
}
dps_results: {
 key: "TestElemental-AllItems-SymboloftheCatacombs-83735"
 value: {
  dps: 121766.92782
  tps: 91989.13185
 }
}
dps_results: {
 key: "TestElemental-AllItems-SynapseSprings(MarkII)-4898"
 value: {
  dps: 131181.53593
  tps: 99716.95982
 }
}
dps_results: {
 key: "TestElemental-AllItems-TalismanofBloodlust-96864"
 value: {
  dps: 122341.94704
  tps: 93136.58443
 }
}
dps_results: {
 key: "TestElemental-AllItems-TerrorintheMists-87167"
 value: {
  dps: 122181.61035
  tps: 92712.70474
 }
}
dps_results: {
 key: "TestElemental-AllItems-TheGloamingBlade-88149"
 value: {
  dps: 129233.405
  tps: 98102.47898
 }
}
dps_results: {
 key: "TestElemental-AllItems-Thousand-YearPickledEgg-87573"
 value: {
  dps: 124393.923
  tps: 94527.98995
 }
}
dps_results: {
 key: "TestElemental-AllItems-TrailseekerIdolofRage-101054"
 value: {
  dps: 121696.2546
  tps: 91367.82925
 }
}
dps_results: {
 key: "TestElemental-AllItems-TrailseekerStoneofRage-101057"
 value: {
  dps: 122156.84341
  tps: 91148.90342
 }
}
dps_results: {
 key: "TestElemental-AllItems-TyrannicalGladiator'sBadgeofConquest-100043"
 value: {
  dps: 119990.53943
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-TyrannicalGladiator'sBadgeofConquest-91099"
 value: {
  dps: 119990.53943
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-TyrannicalGladiator'sBadgeofConquest-94373"
 value: {
  dps: 119990.53943
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-TyrannicalGladiator'sBadgeofConquest-99772"
 value: {
  dps: 119990.53943
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-TyrannicalGladiator'sBadgeofDominance-100016"
 value: {
  dps: 125703.44888
  tps: 95472.21264
 }
}
dps_results: {
 key: "TestElemental-AllItems-TyrannicalGladiator'sBadgeofDominance-91400"
 value: {
  dps: 125703.44888
  tps: 95472.21264
 }
}
dps_results: {
 key: "TestElemental-AllItems-TyrannicalGladiator'sBadgeofDominance-94346"
 value: {
  dps: 125703.44888
  tps: 95472.21264
 }
}
dps_results: {
 key: "TestElemental-AllItems-TyrannicalGladiator'sBadgeofDominance-99937"
 value: {
  dps: 125703.44888
  tps: 95472.21264
 }
}
dps_results: {
 key: "TestElemental-AllItems-TyrannicalGladiator'sBadgeofVictory-100019"
 value: {
  dps: 119945.61102
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-TyrannicalGladiator'sBadgeofVictory-91410"
 value: {
  dps: 119945.61102
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-TyrannicalGladiator'sBadgeofVictory-94349"
 value: {
  dps: 119945.61102
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-TyrannicalGladiator'sBadgeofVictory-99943"
 value: {
  dps: 119945.61102
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-TyrannicalGladiator'sEmblemofCruelty-100066"
 value: {
  dps: 121955.16966
  tps: 92773.13013
 }
}
dps_results: {
 key: "TestElemental-AllItems-TyrannicalGladiator'sEmblemofCruelty-91209"
 value: {
  dps: 121955.16966
  tps: 92773.13013
 }
}
dps_results: {
 key: "TestElemental-AllItems-TyrannicalGladiator'sEmblemofCruelty-94396"
 value: {
  dps: 121955.16966
  tps: 92773.13013
 }
}
dps_results: {
 key: "TestElemental-AllItems-TyrannicalGladiator'sEmblemofCruelty-99838"
 value: {
  dps: 121955.16966
  tps: 92773.13013
 }
}
dps_results: {
 key: "TestElemental-AllItems-TyrannicalGladiator'sEmblemofMeditation-91211"
 value: {
  dps: 119945.61102
  tps: 91184.1969
 }
}
dps_results: {
 key: "TestElemental-AllItems-TyrannicalGladiator'sEmblemofMeditation-94329"
 value: {
  dps: 119945.61102
  tps: 91184.1969
 }
}
dps_results: {
 key: "TestElemental-AllItems-TyrannicalGladiator'sEmblemofMeditation-99840"
 value: {
  dps: 119945.61102
  tps: 91184.1969
 }
}
dps_results: {
 key: "TestElemental-AllItems-TyrannicalGladiator'sEmblemofMeditation-99990"
 value: {
  dps: 119945.61102
  tps: 91184.1969
 }
}
dps_results: {
 key: "TestElemental-AllItems-TyrannicalGladiator'sEmblemofTenacity-100092"
 value: {
  dps: 119945.61102
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-TyrannicalGladiator'sEmblemofTenacity-91210"
 value: {
  dps: 119945.61102
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-TyrannicalGladiator'sEmblemofTenacity-94422"
 value: {
  dps: 119945.61102
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-TyrannicalGladiator'sEmblemofTenacity-99839"
 value: {
  dps: 119945.61102
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-TyrannicalGladiator'sInsigniaofConquest-100026"
 value: {
  dps: 120006.04202
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-TyrannicalGladiator'sInsigniaofDominance-100152"
 value: {
  dps: 128101.61688
  tps: 97247.83806
 }
}
dps_results: {
 key: "TestElemental-AllItems-TyrannicalGladiator'sInsigniaofVictory-100085"
 value: {
  dps: 119945.61102
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-TyrannicalPrimalDiamond"
 value: {
  dps: 125694.37494
  tps: 95256.11926
 }
}
dps_results: {
 key: "TestElemental-AllItems-UnerringVisionofLeiShen-96930"
 value: {
  dps: 132820.21885
  tps: 100934.77885
 }
}
dps_results: {
 key: "TestElemental-AllItems-VaporshieldMedallion-93262"
 value: {
  dps: 122118.01133
  tps: 91364.62398
 }
}
dps_results: {
 key: "TestElemental-AllItems-VialofDragon'sBlood-87063"
 value: {
  dps: 121862.32779
  tps: 91298.63941
 }
}
dps_results: {
 key: "TestElemental-AllItems-VialofIchorousBlood-100963"
 value: {
  dps: 123598.33311
  tps: 93925.7133
 }
}
dps_results: {
 key: "TestElemental-AllItems-VialofIchorousBlood-81264"
 value: {
  dps: 124085.67737
  tps: 94288.49359
 }
}
dps_results: {
 key: "TestElemental-AllItems-ViciousTalismanoftheShado-PanAssault-94511"
 value: {
  dps: 120086.51012
  tps: 91185.77107
 }
}
dps_results: {
 key: "TestElemental-AllItems-VisionofthePredator-81192"
 value: {
  dps: 125785.34074
  tps: 95520.23313
 }
}
dps_results: {
 key: "TestElemental-AllItems-VolatileTalismanoftheShado-PanAssault-94510"
 value: {
  dps: 130267.53955
  tps: 99124.8034
 }
}
dps_results: {
 key: "TestElemental-AllItems-WindsweptPages-81125"
 value: {
  dps: 121786.18374
  tps: 92384.72988
 }
}
dps_results: {
 key: "TestElemental-AllItems-WoundripperMedallion-93253"
 value: {
  dps: 121346.75173
  tps: 92185.63944
 }
}
dps_results: {
 key: "TestElemental-AllItems-Wushoolay'sFinalChoice-96785"
 value: {
  dps: 154087.75831
  tps: 116326.76225
 }
}
dps_results: {
 key: "TestElemental-AllItems-Xing-Ho,BreathofYu'lon-102246"
 value: {
  dps: 158660.66242
  tps: 126185.33222
 }
}
dps_results: {
 key: "TestElemental-AllItems-YaungolFireCarrier-86518"
 value: {
  dps: 129233.405
  tps: 98102.47898
 }
}
dps_results: {
 key: "TestElemental-AllItems-Yu'lon'sBite-103987"
 value: {
  dps: 131147.64682
  tps: 99307.20827
 }
}
dps_results: {
 key: "TestElemental-AllItems-ZenAlchemistStone-75274"
 value: {
  dps: 128000.06554
  tps: 96279.89611
 }
}
dps_results: {
 key: "TestElemental-Average-Default"
 value: {
  dps: 132844.16807
  tps: 100516.4383
 }
}
dps_results: {
 key: "TestElemental-Settings-AlliancePandaren-p1-DefaultTalents-Standard-aoe-FullBuffs-20.0yards-LongMultiTarget"
 value: {
  dps: 189865.01625
  tps: 260085.68794
 }
}
dps_results: {
 key: "TestElemental-Settings-AlliancePandaren-p1-DefaultTalents-Standard-aoe-FullBuffs-20.0yards-LongSingleTarget"
 value: {
  dps: 39148.35471
  tps: 32075.29726
 }
}
dps_results: {
 key: "TestElemental-Settings-AlliancePandaren-p1-DefaultTalents-Standard-aoe-FullBuffs-20.0yards-ShortSingleTarget"
 value: {
  dps: 76898.6526
  tps: 47938.1413
 }
}
dps_results: {
 key: "TestElemental-Settings-AlliancePandaren-p1-DefaultTalents-Standard-aoe-NoBuffs-20.0yards-LongMultiTarget"
 value: {
  dps: 156426.72574
  tps: 242215.93344
 }
}
dps_results: {
 key: "TestElemental-Settings-AlliancePandaren-p1-DefaultTalents-Standard-aoe-NoBuffs-20.0yards-LongSingleTarget"
 value: {
  dps: 31173.9874
  tps: 26246.72409
 }
}
dps_results: {
 key: "TestElemental-Settings-AlliancePandaren-p1-DefaultTalents-Standard-aoe-NoBuffs-20.0yards-ShortSingleTarget"
 value: {
  dps: 60031.30351
  tps: 39535.4651
 }
}
dps_results: {
 key: "TestElemental-Settings-AlliancePandaren-p1-DefaultTalents-Standard-default-FullBuffs-20.0yards-LongMultiTarget"
 value: {
  dps: 134680.10046
  tps: 121972.98624
 }
}
dps_results: {
 key: "TestElemental-Settings-AlliancePandaren-p1-DefaultTalents-Standard-default-FullBuffs-20.0yards-LongSingleTarget"
 value: {
  dps: 128488.61662
  tps: 97579.33635
 }
}
dps_results: {
 key: "TestElemental-Settings-AlliancePandaren-p1-DefaultTalents-Standard-default-FullBuffs-20.0yards-ShortSingleTarget"
 value: {
  dps: 191965.41345
  tps: 128476.93056
 }
}
dps_results: {
 key: "TestElemental-Settings-AlliancePandaren-p1-DefaultTalents-Standard-default-NoBuffs-20.0yards-LongMultiTarget"
 value: {
  dps: 114258.71981
  tps: 108658.66045
 }
}
dps_results: {
 key: "TestElemental-Settings-AlliancePandaren-p1-DefaultTalents-Standard-default-NoBuffs-20.0yards-LongSingleTarget"
 value: {
  dps: 109781.11638
  tps: 84689.23987
 }
}
dps_results: {
 key: "TestElemental-Settings-AlliancePandaren-p1-DefaultTalents-Standard-default-NoBuffs-20.0yards-ShortSingleTarget"
 value: {
  dps: 154436.0275
  tps: 108527.54919
 }
}
dps_results: {
 key: "TestElemental-Settings-AlliancePandaren-p1-TalentsEMPrimal-Standard-aoe-FullBuffs-20.0yards-LongMultiTarget"
 value: {
  dps: 200732.64818
  tps: 260942.72054
 }
}
dps_results: {
 key: "TestElemental-Settings-AlliancePandaren-p1-TalentsEMPrimal-Standard-aoe-FullBuffs-20.0yards-LongSingleTarget"
 value: {
  dps: 45946.36874
  tps: 33440.28232
 }
}
dps_results: {
 key: "TestElemental-Settings-AlliancePandaren-p1-TalentsEMPrimal-Standard-aoe-FullBuffs-20.0yards-ShortSingleTarget"
 value: {
  dps: 98996.18661
  tps: 46982.30419
 }
}
dps_results: {
 key: "TestElemental-Settings-AlliancePandaren-p1-TalentsEMPrimal-Standard-aoe-NoBuffs-20.0yards-LongMultiTarget"
 value: {
  dps: 165118.50805
  tps: 243163.16841
 }
}
dps_results: {
 key: "TestElemental-Settings-AlliancePandaren-p1-TalentsEMPrimal-Standard-aoe-NoBuffs-20.0yards-LongSingleTarget"
 value: {
  dps: 36038.9332
  tps: 26742.82483
 }
}
dps_results: {
 key: "TestElemental-Settings-AlliancePandaren-p1-TalentsEMPrimal-Standard-aoe-NoBuffs-20.0yards-ShortSingleTarget"
 value: {
  dps: 77461.04064
  tps: 38156.16135
 }
}
dps_results: {
 key: "TestElemental-Settings-AlliancePandaren-p1-TalentsEMPrimal-Standard-default-FullBuffs-20.0yards-LongMultiTarget"
 value: {
  dps: 142377.45164
  tps: 124544.65998
 }
}
dps_results: {
 key: "TestElemental-Settings-AlliancePandaren-p1-TalentsEMPrimal-Standard-default-FullBuffs-20.0yards-LongSingleTarget"
 value: {
  dps: 132478.07917
  tps: 97420.00934
 }
}
dps_results: {
 key: "TestElemental-Settings-AlliancePandaren-p1-TalentsEMPrimal-Standard-default-FullBuffs-20.0yards-ShortSingleTarget"
 value: {
  dps: 213483.68834
  tps: 127831.46307
 }
}
dps_results: {
 key: "TestElemental-Settings-AlliancePandaren-p1-TalentsEMPrimal-Standard-default-NoBuffs-20.0yards-LongMultiTarget"
 value: {
  dps: 120228.26866
  tps: 111108.8813
 }
}
dps_results: {
 key: "TestElemental-Settings-AlliancePandaren-p1-TalentsEMPrimal-Standard-default-NoBuffs-20.0yards-LongSingleTarget"
 value: {
  dps: 112045.4841
  tps: 83676.66165
 }
}
dps_results: {
 key: "TestElemental-Settings-AlliancePandaren-p1-TalentsEMPrimal-Standard-default-NoBuffs-20.0yards-ShortSingleTarget"
 value: {
  dps: 171878.02937
  tps: 107458.35515
 }
}
dps_results: {
 key: "TestElemental-Settings-AlliancePandaren-p1-TalentsEchoUnleashed-Standard-aoe-FullBuffs-20.0yards-LongMultiTarget"
 value: {
  dps: 449031.5791
  tps: 323494.01996
 }
}
dps_results: {
 key: "TestElemental-Settings-AlliancePandaren-p1-TalentsEchoUnleashed-Standard-aoe-FullBuffs-20.0yards-LongSingleTarget"
 value: {
  dps: 38607.74265
  tps: 30408.31981
 }
}
dps_results: {
 key: "TestElemental-Settings-AlliancePandaren-p1-TalentsEchoUnleashed-Standard-aoe-FullBuffs-20.0yards-ShortSingleTarget"
 value: {
  dps: 74271.86636
  tps: 44676.61436
 }
}
dps_results: {
 key: "TestElemental-Settings-AlliancePandaren-p1-TalentsEchoUnleashed-Standard-aoe-NoBuffs-20.0yards-LongMultiTarget"
 value: {
  dps: 374218.16786
  tps: 292220.48377
 }
}
dps_results: {
 key: "TestElemental-Settings-AlliancePandaren-p1-TalentsEchoUnleashed-Standard-aoe-NoBuffs-20.0yards-LongSingleTarget"
 value: {
  dps: 31148.71576
  tps: 25460.6072
 }
}
dps_results: {
 key: "TestElemental-Settings-AlliancePandaren-p1-TalentsEchoUnleashed-Standard-aoe-NoBuffs-20.0yards-ShortSingleTarget"
 value: {
  dps: 57882.39147
  tps: 36788.24972
 }
}
dps_results: {
 key: "TestElemental-Settings-AlliancePandaren-p1-TalentsEchoUnleashed-Standard-default-FullBuffs-20.0yards-LongMultiTarget"
 value: {
  dps: 136425.03413
  tps: 122414.40054
 }
}
dps_results: {
 key: "TestElemental-Settings-AlliancePandaren-p1-TalentsEchoUnleashed-Standard-default-FullBuffs-20.0yards-LongSingleTarget"
 value: {
  dps: 131145.43513
  tps: 97071.25254
 }
}
dps_results: {
 key: "TestElemental-Settings-AlliancePandaren-p1-TalentsEchoUnleashed-Standard-default-FullBuffs-20.0yards-ShortSingleTarget"
 value: {
  dps: 195338.55218
  tps: 129358.59475
 }
}
dps_results: {
 key: "TestElemental-Settings-AlliancePandaren-p1-TalentsEchoUnleashed-Standard-default-NoBuffs-20.0yards-LongMultiTarget"
 value: {
  dps: 116027.75438
  tps: 109157.35102
 }
}
dps_results: {
 key: "TestElemental-Settings-AlliancePandaren-p1-TalentsEchoUnleashed-Standard-default-NoBuffs-20.0yards-LongSingleTarget"
 value: {
  dps: 111139.66592
  tps: 82966.98594
 }
}
dps_results: {
 key: "TestElemental-Settings-AlliancePandaren-p1-TalentsEchoUnleashed-Standard-default-NoBuffs-20.0yards-ShortSingleTarget"
 value: {
  dps: 154065.6658
  tps: 106006.64098
 }
}
dps_results: {
 key: "TestElemental-Settings-Draenei-p1-DefaultTalents-Standard-aoe-FullBuffs-20.0yards-LongMultiTarget"
 value: {
  dps: 189871.38639
  tps: 260090.19873
 }
}
dps_results: {
 key: "TestElemental-Settings-Draenei-p1-DefaultTalents-Standard-aoe-FullBuffs-20.0yards-LongSingleTarget"
 value: {
  dps: 39149.6386
  tps: 32076.27305
 }
}
dps_results: {
 key: "TestElemental-Settings-Draenei-p1-DefaultTalents-Standard-aoe-FullBuffs-20.0yards-ShortSingleTarget"
 value: {
  dps: 76901.18354
  tps: 47939.61977
 }
}
dps_results: {
 key: "TestElemental-Settings-Draenei-p1-DefaultTalents-Standard-aoe-NoBuffs-20.0yards-LongMultiTarget"
 value: {
  dps: 156431.89648
  tps: 242219.59945
 }
}
dps_results: {
 key: "TestElemental-Settings-Draenei-p1-DefaultTalents-Standard-aoe-NoBuffs-20.0yards-LongSingleTarget"
 value: {
  dps: 31174.9935
  tps: 26247.49798
 }
}
dps_results: {
 key: "TestElemental-Settings-Draenei-p1-DefaultTalents-Standard-aoe-NoBuffs-20.0yards-ShortSingleTarget"
 value: {
  dps: 60033.25542
  tps: 39536.65204
 }
}
dps_results: {
 key: "TestElemental-Settings-Draenei-p1-DefaultTalents-Standard-default-FullBuffs-20.0yards-LongMultiTarget"
 value: {
  dps: 134684.63893
  tps: 121976.24482
 }
}
dps_results: {
 key: "TestElemental-Settings-Draenei-p1-DefaultTalents-Standard-default-FullBuffs-20.0yards-LongSingleTarget"
 value: {
  dps: 128492.93976
  tps: 97582.5819
 }
}
dps_results: {
 key: "TestElemental-Settings-Draenei-p1-DefaultTalents-Standard-default-FullBuffs-20.0yards-ShortSingleTarget"
 value: {
  dps: 191971.71075
  tps: 128481.10659
 }
}
dps_results: {
 key: "TestElemental-Settings-Draenei-p1-DefaultTalents-Standard-default-NoBuffs-20.0yards-LongMultiTarget"
 value: {
  dps: 114262.51827
  tps: 108661.41285
 }
}
dps_results: {
 key: "TestElemental-Settings-Draenei-p1-DefaultTalents-Standard-default-NoBuffs-20.0yards-LongSingleTarget"
 value: {
  dps: 109784.76048
  tps: 84692.01299
 }
}
dps_results: {
 key: "TestElemental-Settings-Draenei-p1-DefaultTalents-Standard-default-NoBuffs-20.0yards-ShortSingleTarget"
 value: {
  dps: 154441.03168
  tps: 108531.0332
 }
}
dps_results: {
 key: "TestElemental-Settings-Draenei-p1-TalentsEMPrimal-Standard-aoe-FullBuffs-20.0yards-LongMultiTarget"
 value: {
  dps: 200739.36922
  tps: 260947.26754
 }
}
dps_results: {
 key: "TestElemental-Settings-Draenei-p1-TalentsEMPrimal-Standard-aoe-FullBuffs-20.0yards-LongSingleTarget"
 value: {
  dps: 45947.87177
  tps: 33441.30214
 }
}
dps_results: {
 key: "TestElemental-Settings-Draenei-p1-TalentsEMPrimal-Standard-aoe-FullBuffs-20.0yards-ShortSingleTarget"
 value: {
  dps: 98999.43024
  tps: 46983.75646
 }
}
dps_results: {
 key: "TestElemental-Settings-Draenei-p1-TalentsEMPrimal-Standard-aoe-NoBuffs-20.0yards-LongMultiTarget"
 value: {
  dps: 165140.84465
  tps: 243165.97867
 }
}
dps_results: {
 key: "TestElemental-Settings-Draenei-p1-TalentsEMPrimal-Standard-aoe-NoBuffs-20.0yards-LongSingleTarget"
 value: {
  dps: 36040.09472
  tps: 26743.61514
 }
}
dps_results: {
 key: "TestElemental-Settings-Draenei-p1-TalentsEMPrimal-Standard-aoe-NoBuffs-20.0yards-ShortSingleTarget"
 value: {
  dps: 77463.54257
  tps: 38157.30554
 }
}
dps_results: {
 key: "TestElemental-Settings-Draenei-p1-TalentsEMPrimal-Standard-default-FullBuffs-20.0yards-LongMultiTarget"
 value: {
  dps: 142382.23102
  tps: 124547.89594
 }
}
dps_results: {
 key: "TestElemental-Settings-Draenei-p1-TalentsEMPrimal-Standard-default-FullBuffs-20.0yards-LongSingleTarget"
 value: {
  dps: 132482.52036
  tps: 97423.24065
 }
}
dps_results: {
 key: "TestElemental-Settings-Draenei-p1-TalentsEMPrimal-Standard-default-FullBuffs-20.0yards-ShortSingleTarget"
 value: {
  dps: 213490.64766
  tps: 127835.58878
 }
}
dps_results: {
 key: "TestElemental-Settings-Draenei-p1-TalentsEMPrimal-Standard-default-NoBuffs-20.0yards-LongMultiTarget"
 value: {
  dps: 120232.25384
  tps: 111111.61412
 }
}
dps_results: {
 key: "TestElemental-Settings-Draenei-p1-TalentsEMPrimal-Standard-default-NoBuffs-20.0yards-LongSingleTarget"
 value: {
  dps: 112065.9581
  tps: 83696.16142
 }
}
dps_results: {
 key: "TestElemental-Settings-Draenei-p1-TalentsEMPrimal-Standard-default-NoBuffs-20.0yards-ShortSingleTarget"
 value: {
  dps: 171883.54847
  tps: 107461.77318
 }
}
dps_results: {
 key: "TestElemental-Settings-Draenei-p1-TalentsEchoUnleashed-Standard-aoe-FullBuffs-20.0yards-LongMultiTarget"
 value: {
  dps: 449073.57084
  tps: 323520.39597
 }
}
dps_results: {
 key: "TestElemental-Settings-Draenei-p1-TalentsEchoUnleashed-Standard-aoe-FullBuffs-20.0yards-LongSingleTarget"
 value: {
  dps: 38609.01254
  tps: 30409.23656
 }
}
dps_results: {
 key: "TestElemental-Settings-Draenei-p1-TalentsEchoUnleashed-Standard-aoe-FullBuffs-20.0yards-ShortSingleTarget"
 value: {
  dps: 74274.30662
  tps: 44677.97419
 }
}
dps_results: {
 key: "TestElemental-Settings-Draenei-p1-TalentsEchoUnleashed-Standard-aoe-NoBuffs-20.0yards-LongMultiTarget"
 value: {
  dps: 374234.81061
  tps: 292226.15376
 }
}
dps_results: {
 key: "TestElemental-Settings-Draenei-p1-TalentsEchoUnleashed-Standard-aoe-NoBuffs-20.0yards-LongSingleTarget"
 value: {
  dps: 31149.72592
  tps: 25461.35063
 }
}
dps_results: {
 key: "TestElemental-Settings-Draenei-p1-TalentsEchoUnleashed-Standard-aoe-NoBuffs-20.0yards-ShortSingleTarget"
 value: {
  dps: 57884.27005
  tps: 36789.33796
 }
}
dps_results: {
 key: "TestElemental-Settings-Draenei-p1-TalentsEchoUnleashed-Standard-default-FullBuffs-20.0yards-LongMultiTarget"
 value: {
  dps: 136429.62809
  tps: 122417.61957
 }
}
dps_results: {
 key: "TestElemental-Settings-Draenei-p1-TalentsEchoUnleashed-Standard-default-FullBuffs-20.0yards-LongSingleTarget"
 value: {
  dps: 131149.84343
  tps: 97074.47618
 }
}
dps_results: {
 key: "TestElemental-Settings-Draenei-p1-TalentsEchoUnleashed-Standard-default-FullBuffs-20.0yards-ShortSingleTarget"
 value: {
  dps: 195344.94464
  tps: 129362.78194
 }
}
dps_results: {
 key: "TestElemental-Settings-Draenei-p1-TalentsEchoUnleashed-Standard-default-NoBuffs-20.0yards-LongMultiTarget"
 value: {
  dps: 116032.5128
  tps: 109160.0723
 }
}
dps_results: {
 key: "TestElemental-Settings-Draenei-p1-TalentsEchoUnleashed-Standard-default-NoBuffs-20.0yards-LongSingleTarget"
 value: {
  dps: 111143.35778
  tps: 82969.7006
 }
}
dps_results: {
 key: "TestElemental-Settings-Draenei-p1-TalentsEchoUnleashed-Standard-default-NoBuffs-20.0yards-ShortSingleTarget"
 value: {
  dps: 154070.67751
  tps: 106010.05401
 }
}
dps_results: {
 key: "TestElemental-Settings-Orc-p1-DefaultTalents-Standard-aoe-FullBuffs-20.0yards-LongMultiTarget"
 value: {
  dps: 192471.96977
  tps: 261671.44449
 }
}
dps_results: {
 key: "TestElemental-Settings-Orc-p1-DefaultTalents-Standard-aoe-FullBuffs-20.0yards-LongSingleTarget"
 value: {
  dps: 39915.78998
  tps: 32541.54293
 }
}
dps_results: {
 key: "TestElemental-Settings-Orc-p1-DefaultTalents-Standard-aoe-FullBuffs-20.0yards-ShortSingleTarget"
 value: {
  dps: 79118.29158
  tps: 48894.93262
 }
}
dps_results: {
 key: "TestElemental-Settings-Orc-p1-DefaultTalents-Standard-aoe-NoBuffs-20.0yards-LongMultiTarget"
 value: {
  dps: 158587.27249
  tps: 243553.73344
 }
}
dps_results: {
 key: "TestElemental-Settings-Orc-p1-DefaultTalents-Standard-aoe-NoBuffs-20.0yards-LongSingleTarget"
 value: {
  dps: 31766.6729
  tps: 26610.27974
 }
}
dps_results: {
 key: "TestElemental-Settings-Orc-p1-DefaultTalents-Standard-aoe-NoBuffs-20.0yards-ShortSingleTarget"
 value: {
  dps: 61695.87644
  tps: 40271.3426
 }
}
dps_results: {
 key: "TestElemental-Settings-Orc-p1-DefaultTalents-Standard-default-FullBuffs-20.0yards-LongMultiTarget"
 value: {
  dps: 136793.8765
  tps: 123263.02617
 }
}
dps_results: {
 key: "TestElemental-Settings-Orc-p1-DefaultTalents-Standard-default-FullBuffs-20.0yards-LongSingleTarget"
 value: {
  dps: 130397.0978
  tps: 98869.21236
 }
}
dps_results: {
 key: "TestElemental-Settings-Orc-p1-DefaultTalents-Standard-default-FullBuffs-20.0yards-ShortSingleTarget"
 value: {
  dps: 197516.18069
  tps: 131845.69704
 }
}
dps_results: {
 key: "TestElemental-Settings-Orc-p1-DefaultTalents-Standard-default-NoBuffs-20.0yards-LongMultiTarget"
 value: {
  dps: 116003.55645
  tps: 109751.22994
 }
}
dps_results: {
 key: "TestElemental-Settings-Orc-p1-DefaultTalents-Standard-default-NoBuffs-20.0yards-LongSingleTarget"
 value: {
  dps: 111380.51712
  tps: 85801.57077
 }
}
dps_results: {
 key: "TestElemental-Settings-Orc-p1-DefaultTalents-Standard-default-NoBuffs-20.0yards-ShortSingleTarget"
 value: {
  dps: 158878.73692
  tps: 111321.27065
 }
}
dps_results: {
 key: "TestElemental-Settings-Orc-p1-TalentsEMPrimal-Standard-aoe-FullBuffs-20.0yards-LongMultiTarget"
 value: {
  dps: 203766.20785
  tps: 262484.36415
 }
}
dps_results: {
 key: "TestElemental-Settings-Orc-p1-TalentsEMPrimal-Standard-aoe-FullBuffs-20.0yards-LongSingleTarget"
 value: {
  dps: 46897.48648
  tps: 33868.96307
 }
}
dps_results: {
 key: "TestElemental-Settings-Orc-p1-TalentsEMPrimal-Standard-aoe-FullBuffs-20.0yards-ShortSingleTarget"
 value: {
  dps: 102174.01861
  tps: 47852.3887
 }
}
dps_results: {
 key: "TestElemental-Settings-Orc-p1-TalentsEMPrimal-Standard-aoe-NoBuffs-20.0yards-LongMultiTarget"
 value: {
  dps: 167620.48999
  tps: 244484.34912
 }
}
dps_results: {
 key: "TestElemental-Settings-Orc-p1-TalentsEMPrimal-Standard-aoe-NoBuffs-20.0yards-LongSingleTarget"
 value: {
  dps: 36806.5979
  tps: 27115.5263
 }
}
dps_results: {
 key: "TestElemental-Settings-Orc-p1-TalentsEMPrimal-Standard-aoe-NoBuffs-20.0yards-ShortSingleTarget"
 value: {
  dps: 79882.18086
  tps: 38858.77375
 }
}
dps_results: {
 key: "TestElemental-Settings-Orc-p1-TalentsEMPrimal-Standard-default-FullBuffs-20.0yards-LongMultiTarget"
 value: {
  dps: 144963.86807
  tps: 125863.89453
 }
}
dps_results: {
 key: "TestElemental-Settings-Orc-p1-TalentsEMPrimal-Standard-default-FullBuffs-20.0yards-LongSingleTarget"
 value: {
  dps: 134574.31463
  tps: 98713.06076
 }
}
dps_results: {
 key: "TestElemental-Settings-Orc-p1-TalentsEMPrimal-Standard-default-FullBuffs-20.0yards-ShortSingleTarget"
 value: {
  dps: 220417.85868
  tps: 131596.93591
 }
}
dps_results: {
 key: "TestElemental-Settings-Orc-p1-TalentsEMPrimal-Standard-default-NoBuffs-20.0yards-LongMultiTarget"
 value: {
  dps: 122410.64404
  tps: 112276.00957
 }
}
dps_results: {
 key: "TestElemental-Settings-Orc-p1-TalentsEMPrimal-Standard-default-NoBuffs-20.0yards-LongSingleTarget"
 value: {
  dps: 113860.62279
  tps: 84833.29302
 }
}
dps_results: {
 key: "TestElemental-Settings-Orc-p1-TalentsEMPrimal-Standard-default-NoBuffs-20.0yards-ShortSingleTarget"
 value: {
  dps: 177566.61882
  tps: 110622.92652
 }
}
dps_results: {
 key: "TestElemental-Settings-Orc-p1-TalentsEchoUnleashed-Standard-aoe-FullBuffs-20.0yards-LongMultiTarget"
 value: {
  dps: 455161.47681
  tps: 326169.93284
 }
}
dps_results: {
 key: "TestElemental-Settings-Orc-p1-TalentsEchoUnleashed-Standard-aoe-FullBuffs-20.0yards-LongSingleTarget"
 value: {
  dps: 39304.59681
  tps: 30802.15124
 }
}
dps_results: {
 key: "TestElemental-Settings-Orc-p1-TalentsEchoUnleashed-Standard-aoe-FullBuffs-20.0yards-ShortSingleTarget"
 value: {
  dps: 76426.60807
  tps: 45572.50835
 }
}
dps_results: {
 key: "TestElemental-Settings-Orc-p1-TalentsEchoUnleashed-Standard-aoe-NoBuffs-20.0yards-LongMultiTarget"
 value: {
  dps: 379325.51545
  tps: 294416.24873
 }
}
dps_results: {
 key: "TestElemental-Settings-Orc-p1-TalentsEchoUnleashed-Standard-aoe-NoBuffs-20.0yards-LongSingleTarget"
 value: {
  dps: 31709.66211
  tps: 25789.89341
 }
}
dps_results: {
 key: "TestElemental-Settings-Orc-p1-TalentsEchoUnleashed-Standard-aoe-NoBuffs-20.0yards-ShortSingleTarget"
 value: {
  dps: 59553.40895
  tps: 37522.70438
 }
}
dps_results: {
 key: "TestElemental-Settings-Orc-p1-TalentsEchoUnleashed-Standard-default-FullBuffs-20.0yards-LongMultiTarget"
 value: {
  dps: 138581.96779
  tps: 123743.93106
 }
}
dps_results: {
 key: "TestElemental-Settings-Orc-p1-TalentsEchoUnleashed-Standard-default-FullBuffs-20.0yards-LongSingleTarget"
 value: {
  dps: 133099.03285
  tps: 98374.54791
 }
}
dps_results: {
 key: "TestElemental-Settings-Orc-p1-TalentsEchoUnleashed-Standard-default-FullBuffs-20.0yards-ShortSingleTarget"
 value: {
  dps: 201088.84123
  tps: 132921.31045
 }
}
dps_results: {
 key: "TestElemental-Settings-Orc-p1-TalentsEchoUnleashed-Standard-default-NoBuffs-20.0yards-LongMultiTarget"
 value: {
  dps: 117840.25465
  tps: 110295.16258
 }
}
dps_results: {
 key: "TestElemental-Settings-Orc-p1-TalentsEchoUnleashed-Standard-default-NoBuffs-20.0yards-LongSingleTarget"
 value: {
  dps: 112792.38729
  tps: 84104.73032
 }
}
dps_results: {
 key: "TestElemental-Settings-Orc-p1-TalentsEchoUnleashed-Standard-default-NoBuffs-20.0yards-ShortSingleTarget"
 value: {
  dps: 158532.78668
  tps: 108858.05811
 }
}
dps_results: {
 key: "TestElemental-Settings-Troll-p1-DefaultTalents-Standard-aoe-FullBuffs-20.0yards-LongMultiTarget"
 value: {
  dps: 191921.86821
  tps: 261779.9336
 }
}
dps_results: {
 key: "TestElemental-Settings-Troll-p1-DefaultTalents-Standard-aoe-FullBuffs-20.0yards-LongSingleTarget"
 value: {
  dps: 39985.75999
  tps: 32684.61696
 }
}
dps_results: {
 key: "TestElemental-Settings-Troll-p1-DefaultTalents-Standard-aoe-FullBuffs-20.0yards-ShortSingleTarget"
 value: {
  dps: 77904.43211
  tps: 47929.71272
 }
}
dps_results: {
 key: "TestElemental-Settings-Troll-p1-DefaultTalents-Standard-aoe-NoBuffs-20.0yards-LongMultiTarget"
 value: {
  dps: 157922.20358
  tps: 243752.81045
 }
}
dps_results: {
 key: "TestElemental-Settings-Troll-p1-DefaultTalents-Standard-aoe-NoBuffs-20.0yards-LongSingleTarget"
 value: {
  dps: 31752.28861
  tps: 26705.95985
 }
}
dps_results: {
 key: "TestElemental-Settings-Troll-p1-DefaultTalents-Standard-aoe-NoBuffs-20.0yards-ShortSingleTarget"
 value: {
  dps: 60677.60215
  tps: 39389.2015
 }
}
dps_results: {
 key: "TestElemental-Settings-Troll-p1-DefaultTalents-Standard-default-FullBuffs-20.0yards-LongMultiTarget"
 value: {
  dps: 136859.21849
  tps: 123859.17206
 }
}
dps_results: {
 key: "TestElemental-Settings-Troll-p1-DefaultTalents-Standard-default-FullBuffs-20.0yards-LongSingleTarget"
 value: {
  dps: 131252.10514
  tps: 99578.99718
 }
}
dps_results: {
 key: "TestElemental-Settings-Troll-p1-DefaultTalents-Standard-default-FullBuffs-20.0yards-ShortSingleTarget"
 value: {
  dps: 198679.98836
  tps: 132784.52586
 }
}
dps_results: {
 key: "TestElemental-Settings-Troll-p1-DefaultTalents-Standard-default-NoBuffs-20.0yards-LongMultiTarget"
 value: {
  dps: 116069.21505
  tps: 110131.35237
 }
}
dps_results: {
 key: "TestElemental-Settings-Troll-p1-DefaultTalents-Standard-default-NoBuffs-20.0yards-LongSingleTarget"
 value: {
  dps: 111330.29202
  tps: 85507.86323
 }
}
dps_results: {
 key: "TestElemental-Settings-Troll-p1-DefaultTalents-Standard-default-NoBuffs-20.0yards-ShortSingleTarget"
 value: {
  dps: 157680.7824
  tps: 109828.56687
 }
}
dps_results: {
 key: "TestElemental-Settings-Troll-p1-TalentsEMPrimal-Standard-aoe-FullBuffs-20.0yards-LongMultiTarget"
 value: {
  dps: 201890.67648
  tps: 261808.44796
 }
}
dps_results: {
 key: "TestElemental-Settings-Troll-p1-TalentsEMPrimal-Standard-aoe-FullBuffs-20.0yards-LongSingleTarget"
 value: {
  dps: 45974.32003
  tps: 32864.28383
 }
}
dps_results: {
 key: "TestElemental-Settings-Troll-p1-TalentsEMPrimal-Standard-aoe-FullBuffs-20.0yards-ShortSingleTarget"
 value: {
  dps: 101755.21052
  tps: 47079.57709
 }
}
dps_results: {
 key: "TestElemental-Settings-Troll-p1-TalentsEMPrimal-Standard-aoe-NoBuffs-20.0yards-LongMultiTarget"
 value: {
  dps: 166061.39054
  tps: 243674.38845
 }
}
dps_results: {
 key: "TestElemental-Settings-Troll-p1-TalentsEMPrimal-Standard-aoe-NoBuffs-20.0yards-LongSingleTarget"
 value: {
  dps: 36435.30101
  tps: 26822.06815
 }
}
dps_results: {
 key: "TestElemental-Settings-Troll-p1-TalentsEMPrimal-Standard-aoe-NoBuffs-20.0yards-ShortSingleTarget"
 value: {
  dps: 79845.97982
  tps: 38983.65299
 }
}
dps_results: {
 key: "TestElemental-Settings-Troll-p1-TalentsEMPrimal-Standard-default-FullBuffs-20.0yards-LongMultiTarget"
 value: {
  dps: 144648.46891
  tps: 125754.73515
 }
}
dps_results: {
 key: "TestElemental-Settings-Troll-p1-TalentsEMPrimal-Standard-default-FullBuffs-20.0yards-LongSingleTarget"
 value: {
  dps: 135004.30241
  tps: 99243.96543
 }
}
dps_results: {
 key: "TestElemental-Settings-Troll-p1-TalentsEMPrimal-Standard-default-FullBuffs-20.0yards-ShortSingleTarget"
 value: {
  dps: 219432.35811
  tps: 131098.67464
 }
}
dps_results: {
 key: "TestElemental-Settings-Troll-p1-TalentsEMPrimal-Standard-default-NoBuffs-20.0yards-LongMultiTarget"
 value: {
  dps: 122751.20816
  tps: 112941.98675
 }
}
dps_results: {
 key: "TestElemental-Settings-Troll-p1-TalentsEMPrimal-Standard-default-NoBuffs-20.0yards-LongSingleTarget"
 value: {
  dps: 114491.43337
  tps: 85579.43256
 }
}
dps_results: {
 key: "TestElemental-Settings-Troll-p1-TalentsEMPrimal-Standard-default-NoBuffs-20.0yards-ShortSingleTarget"
 value: {
  dps: 177987.24811
  tps: 111215.74575
 }
}
dps_results: {
 key: "TestElemental-Settings-Troll-p1-TalentsEchoUnleashed-Standard-aoe-FullBuffs-20.0yards-LongMultiTarget"
 value: {
  dps: 452281.55014
  tps: 325090.25714
 }
}
dps_results: {
 key: "TestElemental-Settings-Troll-p1-TalentsEchoUnleashed-Standard-aoe-FullBuffs-20.0yards-LongSingleTarget"
 value: {
  dps: 39243.28954
  tps: 30848.93055
 }
}
dps_results: {
 key: "TestElemental-Settings-Troll-p1-TalentsEchoUnleashed-Standard-aoe-FullBuffs-20.0yards-ShortSingleTarget"
 value: {
  dps: 75524.94652
  tps: 44997.38279
 }
}
dps_results: {
 key: "TestElemental-Settings-Troll-p1-TalentsEchoUnleashed-Standard-aoe-NoBuffs-20.0yards-LongMultiTarget"
 value: {
  dps: 376963.41976
  tps: 293449.00855
 }
}
dps_results: {
 key: "TestElemental-Settings-Troll-p1-TalentsEchoUnleashed-Standard-aoe-NoBuffs-20.0yards-LongSingleTarget"
 value: {
  dps: 31420.70432
  tps: 25526.71338
 }
}
dps_results: {
 key: "TestElemental-Settings-Troll-p1-TalentsEchoUnleashed-Standard-aoe-NoBuffs-20.0yards-ShortSingleTarget"
 value: {
  dps: 58408.74549
  tps: 36534.87797
 }
}
dps_results: {
 key: "TestElemental-Settings-Troll-p1-TalentsEchoUnleashed-Standard-default-FullBuffs-20.0yards-LongMultiTarget"
 value: {
  dps: 139767.40647
  tps: 124827.51221
 }
}
dps_results: {
 key: "TestElemental-Settings-Troll-p1-TalentsEchoUnleashed-Standard-default-FullBuffs-20.0yards-LongSingleTarget"
 value: {
  dps: 133941.82673
  tps: 98568.88594
 }
}
dps_results: {
 key: "TestElemental-Settings-Troll-p1-TalentsEchoUnleashed-Standard-default-FullBuffs-20.0yards-ShortSingleTarget"
 value: {
  dps: 205616.50967
  tps: 135139.48438
 }
}
dps_results: {
 key: "TestElemental-Settings-Troll-p1-TalentsEchoUnleashed-Standard-default-NoBuffs-20.0yards-LongMultiTarget"
 value: {
  dps: 118488.21883
  tps: 111179.31483
 }
}
dps_results: {
 key: "TestElemental-Settings-Troll-p1-TalentsEchoUnleashed-Standard-default-NoBuffs-20.0yards-LongSingleTarget"
 value: {
  dps: 114015.75771
  tps: 85423.48786
 }
}
dps_results: {
 key: "TestElemental-Settings-Troll-p1-TalentsEchoUnleashed-Standard-default-NoBuffs-20.0yards-ShortSingleTarget"
 value: {
  dps: 161956.36786
  tps: 110585.90348
 }
}
dps_results: {
 key: "TestElemental-SwitchInFrontOfTarget-Default"
 value: {
  dps: 130752.34096
  tps: 99578.99718
 }
}
