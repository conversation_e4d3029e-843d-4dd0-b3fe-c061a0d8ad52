package restoration

import (
	_ "github.com/wowsims/mop/sim/common"
)

func init() {
	RegisterRestorationShaman()
}

// func TestRestoration(t *testing.T) {
// 	core.RunTestSuite(t, t.Name(), core.FullCharacterTestSuiteGenerator([]core.CharacterSuiteConfig{
// 		Class: proto.Class_ClassShaman,
// 		Race:  proto.Race_RaceTroll,

// 		GearSet:     core.GetGearSet("../../../ui/restoration_shaman/gear_sets", "p1"),
// 		Talents:     StandardTalents,
// 		Glyphs:      StandardGlyphs,
// 		Consumes:    FullConsumes,
// 		SpecOptions: core.SpecOptionsCombo{Label: "Standard", SpecOptions: PlayerOptionsStandard},
// 		Rotation:    core.RotationCombo{Label: "Default", Rotation: DefaultRotation},

// 		ItemFilter: core.ItemFilter{
// 			WeaponTypes: []proto.WeaponType{
// 				proto.WeaponType_WeaponTypeAxe,
// 				proto.WeaponType_WeaponTypeDagger,
// 				proto.WeaponType_WeaponTypeFist,
// 				proto.WeaponType_WeaponTypeMace,
// 				proto.WeaponType_WeaponTypeOffHand,
// 				proto.WeaponType_WeaponTypeShield,
// 				proto.WeaponType_WeaponTypeStaff,
// 			},
// 			ArmorType: proto.ArmorType_ArmorTypeMail,
// 			RangedWeaponTypes: []proto.RangedWeaponType{},
// 		},
// 	}))
// }

// func BenchmarkSimulate(b *testing.B) {
// 	rsr := &proto.RaidSimRequest{
// 		Raid: core.SinglePlayerRaidProto(
// 			&proto.Player{
// 				Race:          proto.Race_RaceOrc,
// 				Class:         proto.Class_ClassShaman,
// 				Equipment:     core.GetGearSet("../../../ui/restoration_shaman/gear_sets", "p1").GearSet,
// 				Consumes:      FullConsumes,
// 				Spec:          PlayerOptionsStandard,
// 				Buffs:         core.FullIndividualBuffs,
// 				TalentsString: StandardTalents,
// 				Glyphs:        StandardGlyphs,
// 			},
// 			core.FullPartyBuffs,
// 			core.FullRaidBuffs,
// 			core.FullDebuffs),
// 		Encounter: &proto.Encounter{
// 			Duration: 300,
// 			Targets: []*proto.Target{
// 				core.NewDefaultTarget(),
// 			},
// 		},
// 		SimOptions: core.AverageDefaultSimTestOptions,
// 	}

// 	core.RaidBenchmark(b, rsr)
// }

// var StandardTalents = "-3020503-50005331335310501122331251"
// var StandardGlyphs = &proto.Glyphs{
// 	Major1: int32(proto.ShamanMajorGlyph_GlyphOfChainHeal),
// 	Major2: int32(proto.ShamanMajorGlyph_GlyphOfEarthShield),
// 	Major3: int32(proto.ShamanMajorGlyph_GlyphOfEarthlivingWeapon),
// }

// var BasicTotems = &proto.ShamanTotems{
// 	Earth: proto.EarthTotem_TremorTotem,
// 	Air:   proto.AirTotem_WrathOfAirTotem,
// 	Water: proto.WaterTotem_ManaSpringTotem,
// 	Fire:  proto.FireTotem_FlametongueTotem,
// }

// var restoShamOptions = &proto.RestorationShaman_Options{
// 	Shield: proto.ShamanShield_WaterShield,
// 	Totems: BasicTotems,
// }
// var PlayerOptionsStandard = &proto.Player_RestorationShaman{
// 	RestorationShaman: &proto.RestorationShaman{
// 		Options: restoShamOptions,
// 	},
// }

// var FullConsumes = &proto.Consumes{
// 	Flask:           proto.Flask_FlaskOfBlindingLight,
// 	Food:            proto.Food_FoodBlackenedBasilisk,
// 	DefaultPotion:   proto.Potions_SuperManaPotion,
// 	PrepopPotion:    proto.Potions_DestructionPotion,
// 	DefaultConjured: proto.Conjured_ConjuredDarkRune,
// }

// var DefaultRotation = core.APLRotationFromJsonString(`{
// 	"type": "TypeAPL",
// 	"priorityList": [
// 		{"action":{"autocastOtherCooldowns":{}}}
// 	]
// }`)
